/**
 * 云端API接口服务
 * 负责与云端服务器进行数据交互
 */

import { getEncryption } from '../../utils/storage/encryption.js';

class CloudApiService {
  constructor() {
    this.baseUrl = 'https://api.healthreport.com'; // 云端API基础URL
    this.apiVersion = 'v1';
    this.timeout = 30000; // 30秒超时
    this.encryption = getEncryption();
    this.authToken = null;
    this.refreshToken = null;
  }

  /**
   * 设置认证令牌
   * @param {string} token 访问令牌
   * @param {string} refresh 刷新令牌
   */
  setAuthTokens(token, refresh = null) {
    this.authToken = token;
    this.refreshToken = refresh;
  }

  /**
   * 获取完整的API URL
   * @param {string} endpoint 
   * @returns {string}
   */
  getApiUrl(endpoint) {
    return `${this.baseUrl}/${this.apiVersion}${endpoint}`;
  }

  /**
   * 发送HTTP请求
   * @param {string} method 
   * @param {string} endpoint 
   * @param {Object} data 
   * @param {Object} options 
   * @returns {Promise<Object>}
   */
  async request(method, endpoint, data = null, options = {}) {
    const url = this.getApiUrl(endpoint);
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers
    };

    // 添加认证头
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    const requestOptions = {
      method: method.toUpperCase(),
      headers,
      timeout: options.timeout || this.timeout
    };

    if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      requestOptions.data = JSON.stringify(data);
    }

    try {
      const response = await uni.request({
        url,
        ...requestOptions
      });

      // 处理HTTP错误状态
      if (response.statusCode >= 400) {
        throw new Error(`HTTP ${response.statusCode}: ${response.data?.message || '请求失败'}`);
      }

      return response.data;
    } catch (error) {
      console.error('API请求失败:', error);
      
      // 处理认证失败，尝试刷新令牌
      if (error.statusCode === 401 && this.refreshToken) {
        try {
          await this.refreshAuthToken();
          // 重新发送原始请求
          return await this.request(method, endpoint, data, options);
        } catch (refreshError) {
          console.error('令牌刷新失败:', refreshError);
          throw new Error('认证失败，请重新登录');
        }
      }

      throw error;
    }
  }

  /**
   * 刷新认证令牌
   * @returns {Promise<Object>}
   */
  async refreshAuthToken() {
    if (!this.refreshToken) {
      throw new Error('没有刷新令牌');
    }

    const response = await uni.request({
      url: this.getApiUrl('/auth/refresh'),
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      data: JSON.stringify({
        refresh_token: this.refreshToken
      })
    });

    if (response.statusCode !== 200) {
      throw new Error('令牌刷新失败');
    }

    this.setAuthTokens(response.data.access_token, response.data.refresh_token);
    return response.data;
  }

  /**
   * 用户认证
   * @param {string} username 
   * @param {string} password 
   * @returns {Promise<Object>}
   */
  async authenticate(username, password) {
    const response = await this.request('POST', '/auth/login', {
      username,
      password
    });

    if (response.access_token) {
      this.setAuthTokens(response.access_token, response.refresh_token);
    }

    return response;
  }

  /**
   * 上传用户数据
   * @param {Object} userData 
   * @returns {Promise<Object>}
   */
  async uploadUserData(userData) {
    // 加密敏感数据
    const encryptedData = await this.encryptSensitiveData(userData);
    
    return await this.request('POST', '/sync/user', {
      data: encryptedData,
      timestamp: Date.now(),
      checksum: await this.calculateChecksum(encryptedData)
    });
  }

  /**
   * 上传健康报告数据
   * @param {Array} reports 
   * @returns {Promise<Object>}
   */
  async uploadHealthReports(reports) {
    const encryptedReports = [];
    
    for (const report of reports) {
      const encryptedReport = await this.encryptSensitiveData(report);
      encryptedReports.push(encryptedReport);
    }

    return await this.request('POST', '/sync/reports', {
      data: encryptedReports,
      timestamp: Date.now(),
      count: encryptedReports.length
    });
  }

  /**
   * 上传健康指标数据
   * @param {Array} indicators 
   * @returns {Promise<Object>}
   */
  async uploadHealthIndicators(indicators) {
    return await this.request('POST', '/sync/indicators', {
      data: indicators,
      timestamp: Date.now(),
      count: indicators.length
    });
  }

  /**
   * 下载用户数据
   * @param {number} userId 
   * @param {number} lastSyncTime 
   * @returns {Promise<Object>}
   */
  async downloadUserData(userId, lastSyncTime = 0) {
    const response = await this.request('GET', `/sync/user/${userId}`, null, {
      headers: {
        'If-Modified-Since': new Date(lastSyncTime).toISOString()
      }
    });

    // 解密敏感数据
    if (response.data) {
      response.data = await this.decryptSensitiveData(response.data);
    }

    return response;
  }

  /**
   * 下载健康报告数据
   * @param {number} userId 
   * @param {number} lastSyncTime 
   * @returns {Promise<Object>}
   */
  async downloadHealthReports(userId, lastSyncTime = 0) {
    const response = await this.request('GET', `/sync/reports/${userId}`, null, {
      headers: {
        'If-Modified-Since': new Date(lastSyncTime).toISOString()
      }
    });

    // 解密报告数据
    if (response.data && Array.isArray(response.data)) {
      const decryptedReports = [];
      for (const report of response.data) {
        const decryptedReport = await this.decryptSensitiveData(report);
        decryptedReports.push(decryptedReport);
      }
      response.data = decryptedReports;
    }

    return response;
  }

  /**
   * 下载健康指标数据
   * @param {number} userId 
   * @param {number} lastSyncTime 
   * @returns {Promise<Object>}
   */
  async downloadHealthIndicators(userId, lastSyncTime = 0) {
    return await this.request('GET', `/sync/indicators/${userId}`, null, {
      headers: {
        'If-Modified-Since': new Date(lastSyncTime).toISOString()
      }
    });
  }

  /**
   * 删除云端数据
   * @param {string} dataType 
   * @param {number} recordId 
   * @returns {Promise<Object>}
   */
  async deleteCloudData(dataType, recordId) {
    return await this.request('DELETE', `/sync/${dataType}/${recordId}`);
  }

  /**
   * 获取数据版本信息
   * @param {number} userId 
   * @returns {Promise<Object>}
   */
  async getDataVersions(userId) {
    return await this.request('GET', `/sync/versions/${userId}`);
  }

  /**
   * 上传文件（图片等）
   * @param {string} filePath 
   * @param {string} fileName 
   * @returns {Promise<Object>}
   */
  async uploadFile(filePath, fileName) {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: this.getApiUrl('/files/upload'),
        filePath,
        name: 'file',
        formData: {
          fileName,
          timestamp: Date.now()
        },
        header: {
          'Authorization': `Bearer ${this.authToken}`
        },
        success: (response) => {
          try {
            const data = JSON.parse(response.data);
            resolve(data);
          } catch (error) {
            reject(new Error('文件上传响应解析失败'));
          }
        },
        fail: (error) => {
          reject(new Error(`文件上传失败: ${error.errMsg}`));
        }
      });
    });
  }

  /**
   * 下载文件
   * @param {string} fileUrl 
   * @param {string} savePath 
   * @returns {Promise<string>}
   */
  async downloadFile(fileUrl, savePath) {
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: fileUrl,
        header: {
          'Authorization': `Bearer ${this.authToken}`
        },
        success: (response) => {
          if (response.statusCode === 200) {
            // 保存文件到指定路径
            uni.saveFile({
              tempFilePath: response.tempFilePath,
              filePath: savePath,
              success: () => resolve(savePath),
              fail: (error) => reject(new Error(`文件保存失败: ${error.errMsg}`))
            });
          } else {
            reject(new Error(`文件下载失败: HTTP ${response.statusCode}`));
          }
        },
        fail: (error) => {
          reject(new Error(`文件下载失败: ${error.errMsg}`));
        }
      });
    });
  }

  /**
   * 加密敏感数据
   * @param {Object} data 
   * @returns {Promise<Object>}
   */
  async encryptSensitiveData(data) {
    const sensitiveFields = ['password_hash', 'salt', 'ocr_text', 'notes'];
    const encryptedData = { ...data };

    for (const field of sensitiveFields) {
      if (encryptedData[field]) {
        try {
          const encryptionKey = this.encryption.generateKey();
          encryptedData[field] = await this.encryption.encrypt(encryptedData[field], encryptionKey);
          encryptedData[`${field}_key`] = encryptionKey;
        } catch (error) {
          console.error(`加密字段 ${field} 失败:`, error);
        }
      }
    }

    return encryptedData;
  }

  /**
   * 解密敏感数据
   * @param {Object} data 
   * @returns {Promise<Object>}
   */
  async decryptSensitiveData(data) {
    const sensitiveFields = ['password_hash', 'salt', 'ocr_text', 'notes'];
    const decryptedData = { ...data };

    for (const field of sensitiveFields) {
      if (decryptedData[field] && decryptedData[`${field}_key`]) {
        try {
          decryptedData[field] = await this.encryption.decrypt(
            decryptedData[field], 
            decryptedData[`${field}_key`]
          );
          delete decryptedData[`${field}_key`];
        } catch (error) {
          console.error(`解密字段 ${field} 失败:`, error);
          decryptedData[field] = '[解密失败]';
        }
      }
    }

    return decryptedData;
  }

  /**
   * 计算数据校验和
   * @param {Object} data 
   * @returns {Promise<string>}
   */
  async calculateChecksum(data) {
    const dataString = JSON.stringify(data);
    return await this.encryption.hash(dataString);
  }

  /**
   * 检查网络连接
   * @returns {Promise<boolean>}
   */
  async checkNetworkConnection() {
    try {
      const response = await uni.request({
        url: this.getApiUrl('/health'),
        method: 'GET',
        timeout: 5000
      });
      return response.statusCode === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取服务器时间
   * @returns {Promise<number>}
   */
  async getServerTime() {
    const response = await this.request('GET', '/time');
    return response.timestamp;
  }
}

// 导出单例实例
export const cloudApiService = new CloudApiService();
export default cloudApiService;