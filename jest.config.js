module.exports = {
  testEnvironment: 'node',
  roots: ['<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.{js,jsx,ts,tsx}',
    '**/*.(test|spec).{js,jsx,ts,tsx}'
  ],
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json', 'node', 'vue'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  collectCoverageFrom: [
    'utils/**/*.{js,jsx,ts,tsx}',
    'services/**/*.{js,jsx,ts,tsx}',
    'stores/**/*.{js,jsx,ts,tsx}',
    'components/**/*.{js,jsx,ts,tsx,vue}',
    'pages/**/*.{js,jsx,ts,tsx,vue}',
    '!utils/**/*.d.ts',
    '!utils/**/index.js'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  preset: '@babel/preset-env',
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^uni-(.*)$': '<rootDir>/uni.promisify.adaptor.js'
  },

  globals: {
    'vue-jest': {
      pug: {
        doctype: 'html'
      }
    }
  }
}