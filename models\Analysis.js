/**
 * 分析结果数据模型
 * 支持趋势分析和健康摘要
 */

const { BaseModel } = require('../core/base/BaseModel.js')
const { Constants } = require('../types/index.js')

class Analysis extends BaseModel {
  /**
   * 初始化分析属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    // 基本信息
    this.userId = data.userId || ''
    this.type = data.type || 'trend' // trend | comparison | summary
    this.title = data.title || ''
    this.description = data.description || ''
    
    // 时间范围
    this.timeRange = {
      start: data.timeRange?.start ? new Date(data.timeRange.start) : null,
      end: data.timeRange?.end ? new Date(data.timeRange.end) : null,
      ...data.timeRange
    }
    
    // 分析项目
    this.items = data.items || [] // 分析的检查项目名称列表
    this.categories = data.categories || [] // 分析的分类列表
    
    // 分析结果
    this.results = {
      trends: [],
      comparisons: [],
      summary: '',
      recommendations: [],
      statistics: {},
      ...data.results
    }
    
    // 处理趋势数据
    if (data.results?.trends) {
      this.results.trends = data.results.trends.map(trend => new TrendData(trend))
    }
    
    // 处理比较数据
    if (data.results?.comparisons) {
      this.results.comparisons = data.results.comparisons.map(comp => new ComparisonData(comp))
    }
    
    // 元数据
    this.metadata = {
      reportCount: 0,
      dataPoints: 0,
      confidence: 1.0,
      generatedAt: new Date(),
      ...data.metadata
    }
    
    // 状态信息
    this.status = data.status || 'completed' // pending | processing | completed | failed
    this.isPublic = data.isPublic || false
    this.tags = data.tags || []
  }
  
  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      userId: {
        required: true,
        type: 'string',
        message: '用户ID是必填项'
      },
      type: {
        required: true,
        type: 'string',
        validator: (value) => {
          const validTypes = ['trend', 'comparison', 'summary']
          if (!validTypes.includes(value)) {
            return '分析类型无效'
          }
          return true
        }
      },
      timeRange: {
        required: false,
        validator: (value) => {
          if (value && value.start && value.end) {
            if (new Date(value.start) > new Date(value.end)) {
              return '时间范围无效：开始时间不能晚于结束时间'
            }
          }
          return true
        }
      },
      items: {
        required: false,
        validator: (value) => {
          if (!Array.isArray(value)) {
            return '分析项目必须是数组'
          }
          return true
        }
      }
    }
  }
  
  /**
   * 添加趋势数据
   * @param {Object|TrendData} trendData - 趋势数据
   */
  addTrend(trendData) {
    const trend = trendData instanceof TrendData ? trendData : new TrendData(trendData)
    
    // 验证趋势数据
    const validation = trend.validate()
    if (!validation.isValid) {
      throw new Error(`趋势数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    this.results.trends.push(trend)
    this.updatedAt = new Date()
  }
  
  /**
   * 添加比较数据
   * @param {Object|ComparisonData} comparisonData - 比较数据
   */
  addComparison(comparisonData) {
    const comparison = comparisonData instanceof ComparisonData ? comparisonData : new ComparisonData(comparisonData)
    
    // 验证比较数据
    const validation = comparison.validate()
    if (!validation.isValid) {
      throw new Error(`比较数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    this.results.comparisons.push(comparison)
    this.updatedAt = new Date()
  }
  
  /**
   * 设置分析摘要
   * @param {String} summary - 摘要内容
   */
  setSummary(summary) {
    this.results.summary = summary
    this.updatedAt = new Date()
  }
  
  /**
   * 添加建议
   * @param {String} recommendation - 建议内容
   */
  addRecommendation(recommendation) {
    if (recommendation && !this.results.recommendations.includes(recommendation)) {
      this.results.recommendations.push(recommendation)
      this.updatedAt = new Date()
    }
  }
  
  /**
   * 设置统计数据
   * @param {Object} statistics - 统计数据
   */
  setStatistics(statistics) {
    this.results.statistics = {
      ...this.results.statistics,
      ...statistics
    }
    this.updatedAt = new Date()
  }
  
  /**
   * 更新元数据
   * @param {Object} metadata - 元数据
   */
  updateMetadata(metadata) {
    this.metadata = {
      ...this.metadata,
      ...metadata
    }
    this.updatedAt = new Date()
  }
  
  /**
   * 设置分析状态
   * @param {String} status - 状态
   */
  setStatus(status) {
    const validStatuses = ['pending', 'processing', 'completed', 'failed']
    if (validStatuses.includes(status)) {
      this.status = status
      this.updatedAt = new Date()
    }
  }
  
  /**
   * 根据项目名称获取趋势数据
   * @param {String} itemName - 项目名称
   * @returns {TrendData|null} 趋势数据或null
   */
  getTrendByItem(itemName) {
    return this.results.trends.find(trend => trend.item === itemName) || null
  }
  
  /**
   * 获取异常趋势
   * @returns {Array} 异常趋势列表
   */
  getAbnormalTrends() {
    return this.results.trends.filter(trend => trend.hasAbnormalData())
  }
  
  /**
   * 获取改善趋势
   * @returns {Array} 改善趋势列表
   */
  getImprovingTrends() {
    return this.results.trends.filter(trend => trend.trend === 'improving')
  }
  
  /**
   * 获取恶化趋势
   * @returns {Array} 恶化趋势列表
   */
  getWorseningTrends() {
    return this.results.trends.filter(trend => trend.trend === 'worsening')
  }
  
  /**
   * 计算总体健康评分
   * @returns {Number} 健康评分 (0-100)
   */
  calculateHealthScore() {
    if (this.results.trends.length === 0) {
      return 50 // 默认中等分数
    }
    
    let totalScore = 0
    let weightSum = 0
    
    for (const trend of this.results.trends) {
      const trendScore = trend.calculateTrendScore()
      const weight = trend.importance || 1
      
      totalScore += trendScore * weight
      weightSum += weight
    }
    
    return Math.round(totalScore / weightSum)
  }
  
  /**
   * 生成健康等级
   * @returns {String} 健康等级
   */
  getHealthGrade() {
    const score = this.calculateHealthScore()
    
    if (score >= 90) return 'excellent'
    if (score >= 80) return 'good'
    if (score >= 70) return 'fair'
    if (score >= 60) return 'poor'
    return 'critical'
  }
  
  /**
   * 获取健康等级显示名称
   * @returns {String} 等级显示名称
   */
  getHealthGradeDisplay() {
    const gradeNames = {
      excellent: '优秀',
      good: '良好',
      fair: '一般',
      poor: '较差',
      critical: '危险'
    }
    
    return gradeNames[this.getHealthGrade()] || '未知'
  }
  
  /**
   * 生成分析报告
   * @returns {Object} 分析报告
   */
  generateReport() {
    const healthScore = this.calculateHealthScore()
    const healthGrade = this.getHealthGrade()
    const abnormalTrends = this.getAbnormalTrends()
    const improvingTrends = this.getImprovingTrends()
    const worseningTrends = getWorseningTrends()
    
    return {
      title: this.title || this.generateTitle(),
      summary: this.results.summary,
      healthScore,
      healthGrade: this.getHealthGradeDisplay(),
      keyFindings: this.generateKeyFindings(),
      trends: {
        total: this.results.trends.length,
        abnormal: abnormalTrends.length,
        improving: improvingTrends.length,
        worsening: worseningTrends.length
      },
      recommendations: this.results.recommendations,
      metadata: this.metadata,
      generatedAt: new Date()
    }
  }
  
  /**
   * 生成标题
   * @returns {String} 标题
   */
  generateTitle() {
    const typeNames = {
      trend: '趋势分析',
      comparison: '对比分析',
      summary: '健康摘要'
    }
    
    const typeName = typeNames[this.type] || '分析'
    const dateStr = new Date().toLocaleDateString('zh-CN')
    
    return `${dateStr} ${typeName}报告`
  }
  
  /**
   * 生成关键发现
   * @returns {Array} 关键发现列表
   */
  generateKeyFindings() {
    const findings = []
    
    // 异常趋势发现
    const abnormalTrends = this.getAbnormalTrends()
    if (abnormalTrends.length > 0) {
      findings.push(`发现${abnormalTrends.length}项指标存在异常`)
    }
    
    // 恶化趋势发现
    const worseningTrends = this.getWorseningTrends()
    if (worseningTrends.length > 0) {
      const items = worseningTrends.map(t => t.item).join('、')
      findings.push(`${items}呈恶化趋势`)
    }
    
    // 改善趋势发现
    const improvingTrends = this.getImprovingTrends()
    if (improvingTrends.length > 0) {
      const items = improvingTrends.map(t => t.item).join('、')
      findings.push(`${items}呈改善趋势`)
    }
    
    // 健康评分发现
    const healthScore = this.calculateHealthScore()
    if (healthScore >= 90) {
      findings.push('整体健康状况优秀')
    } else if (healthScore < 60) {
      findings.push('整体健康状况需要关注')
    }
    
    return findings
  }
  
  /**
   * 添加标签
   * @param {String} tag - 标签
   */
  addTag(tag) {
    if (tag && !this.tags.includes(tag)) {
      this.tags.push(tag)
      this.updatedAt = new Date()
    }
  }
  
  /**
   * 移除标签
   * @param {String} tag - 标签
   */
  removeTag(tag) {
    const index = this.tags.indexOf(tag)
    if (index > -1) {
      this.tags.splice(index, 1)
      this.updatedAt = new Date()
    }
  }
  
  /**
   * 检查是否过期
   * @param {Number} days - 过期天数
   * @returns {Boolean} 是否过期
   */
  isExpired(days = 30) {
    const expiryDate = new Date(this.createdAt)
    expiryDate.setDate(expiryDate.getDate() + days)
    return new Date() > expiryDate
  }
  
  /**
   * 获取可序列化的属性列表
   * @returns {Array} 属性名列表
   */
  getSerializableProperties() {
    return [
      'id', 'userId', 'type', 'title', 'description', 'timeRange',
      'items', 'categories', 'results', 'metadata', 'status',
      'isPublic', 'tags', 'createdAt', 'updatedAt'
    ]
  }
  
  /**
   * 转换为JSON对象
   * @returns {Object} JSON对象
   */
  toJSON() {
    const json = super.toJSON()
    
    // 转换趋势数据为JSON
    if (json.results && json.results.trends) {
      json.results.trends = json.results.trends.map(trend => 
        trend instanceof TrendData ? trend.toJSON() : trend
      )
    }
    
    // 转换比较数据为JSON
    if (json.results && json.results.comparisons) {
      json.results.comparisons = json.results.comparisons.map(comp => 
        comp instanceof ComparisonData ? comp.toJSON() : comp
      )
    }
    
    return json
  }
  
  /**
   * 从JSON创建分析实例
   * @param {Object} json - JSON对象
   * @returns {Analysis} 分析实例
   */
  static fromJSON(json) {
    return new Analysis(json)
  }
  
  /**
   * 创建新分析
   * @param {Object} analysisData - 分析数据
   * @returns {Analysis} 分析实例
   */
  static create(analysisData) {
    const analysis = new Analysis(analysisData)
    
    // 验证必填字段
    if (!analysis.userId) {
      throw new Error('用户ID是必填项')
    }
    
    // 验证数据
    const validation = analysis.validate()
    if (!validation.isValid) {
      throw new Error(`分析数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    return analysis
  }
}

/**
 * 趋势数据模型
 */
class TrendData extends BaseModel {
  /**
   * 初始化趋势属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    this.item = data.item || '' // 检查项目名称
    this.category = data.category || '' // 项目分类
    this.unit = data.unit || '' // 单位
    this.data = (data.data || []).map(point => new DataPoint(point)) // 数据点
    this.trend = data.trend || 'stable' // up | down | stable | improving | worsening
    this.trendStrength = data.trendStrength || 0 // 趋势强度 (-1 到 1)
    this.correlation = data.correlation || 0 // 相关系数
    this.significance = data.significance || 0 // 显著性
    this.importance = data.importance || 1 // 重要性权重
    this.referenceRange = data.referenceRange || null // 参考范围
    this.analysis = data.analysis || '' // 分析说明
  }
  
  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      item: {
        required: true,
        type: 'string',
        minLength: 1,
        message: '检查项目名称是必填项'
      },
      data: {
        required: true,
        validator: (value) => {
          if (!Array.isArray(value)) {
            return '数据点必须是数组'
          }
          
          if (value.length < 2) {
            return '至少需要2个数据点才能进行趋势分析'
          }
          
          for (let i = 0; i < value.length; i++) {
            const point = value[i]
            if (!(point instanceof DataPoint)) {
              return `数据点[${i}]类型不正确`
            }
            
            const pointValidation = point.validate()
            if (!pointValidation.isValid) {
              return `数据点[${i}]验证失败: ${pointValidation.errors.map(e => e.message).join(', ')}`
            }
          }
          
          return true
        }
      },
      trend: {
        required: false,
        type: 'string',
        validator: (value) => {
          const validTrends = ['up', 'down', 'stable', 'improving', 'worsening']
          if (value && !validTrends.includes(value)) {
            return '趋势类型无效'
          }
          return true
        }
      }
    }
  }
  
  /**
   * 添加数据点
   * @param {Object|DataPoint} pointData - 数据点
   */
  addDataPoint(pointData) {
    const point = pointData instanceof DataPoint ? pointData : new DataPoint(pointData)
    
    // 验证数据点
    const validation = point.validate()
    if (!validation.isValid) {
      throw new Error(`数据点验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    this.data.push(point)
    this.data.sort((a, b) => new Date(a.date) - new Date(b.date)) // 按时间排序
    this.updatedAt = new Date()
  }
  
  /**
   * 获取最新数据点
   * @returns {DataPoint|null} 最新数据点
   */
  getLatestDataPoint() {
    return this.data.length > 0 ? this.data[this.data.length - 1] : null
  }
  
  /**
   * 获取最早数据点
   * @returns {DataPoint|null} 最早数据点
   */
  getEarliestDataPoint() {
    return this.data.length > 0 ? this.data[0] : null
  }
  
  /**
   * 获取异常数据点
   * @returns {Array} 异常数据点列表
   */
  getAbnormalDataPoints() {
    return this.data.filter(point => point.isAbnormal)
  }
  
  /**
   * 检查是否有异常数据
   * @returns {Boolean} 是否有异常
   */
  hasAbnormalData() {
    return this.data.some(point => point.isAbnormal)
  }
  
  /**
   * 计算平均值
   * @returns {Number} 平均值
   */
  getAverageValue() {
    if (this.data.length === 0) return 0
    
    const sum = this.data.reduce((total, point) => total + point.value, 0)
    return sum / this.data.length
  }
  
  /**
   * 计算最大值
   * @returns {Number} 最大值
   */
  getMaxValue() {
    if (this.data.length === 0) return 0
    return Math.max(...this.data.map(point => point.value))
  }
  
  /**
   * 计算最小值
   * @returns {Number} 最小值
   */
  getMinValue() {
    if (this.data.length === 0) return 0
    return Math.min(...this.data.map(point => point.value))
  }
  
  /**
   * 计算变化幅度
   * @returns {Number} 变化幅度（百分比）
   */
  getChangePercentage() {
    if (this.data.length < 2) return 0
    
    const earliest = this.getEarliestDataPoint()
    const latest = this.getLatestDataPoint()
    
    if (earliest.value === 0) return 0
    
    return ((latest.value - earliest.value) / earliest.value) * 100
  }
  
  /**
   * 计算趋势评分
   * @returns {Number} 趋势评分 (0-100)
   */
  calculateTrendScore() {
    let score = 50 // 基础分数
    
    // 根据趋势类型调整分数
    switch (this.trend) {
      case 'improving':
        score += 30
        break
      case 'stable':
        score += 10
        break
      case 'worsening':
        score -= 30
        break
      case 'up':
      case 'down':
        // 根据是否有异常数据调整
        score += this.hasAbnormalData() ? -20 : 10
        break
    }
    
    // 根据异常数据比例调整分数
    const abnormalRatio = this.getAbnormalDataPoints().length / this.data.length
    score -= abnormalRatio * 40
    
    // 根据趋势强度调整分数
    score += this.trendStrength * 20
    
    return Math.max(0, Math.min(100, Math.round(score)))
  }
  
  /**
   * 生成趋势描述
   * @returns {String} 趋势描述
   */
  generateDescription() {
    const latest = this.getLatestDataPoint()
    const earliest = this.getEarliestDataPoint()
    const changePercentage = this.getChangePercentage()
    const abnormalCount = this.getAbnormalDataPoints().length
    
    let description = `${this.item}在分析期间`
    
    if (Math.abs(changePercentage) < 5) {
      description += '保持相对稳定'
    } else if (changePercentage > 0) {
      description += `上升了${Math.abs(changePercentage).toFixed(1)}%`
    } else {
      description += `下降了${Math.abs(changePercentage).toFixed(1)}%`
    }
    
    if (abnormalCount > 0) {
      description += `，其中${abnormalCount}次检查结果异常`
    }
    
    if (latest && latest.isAbnormal) {
      description += '，最近一次检查结果异常，建议关注'
    }
    
    return description
  }
  
  /**
   * 转换为JSON对象
   * @returns {Object} JSON对象
   */
  toJSON() {
    const json = super.toJSON()
    
    // 转换数据点为JSON
    json.data = this.data.map(point => point.toJSON())
    
    return json
  }
  
  /**
   * 从JSON创建趋势数据实例
   * @param {Object} json - JSON对象
   * @returns {TrendData} 趋势数据实例
   */
  static fromJSON(json) {
    return new TrendData(json)
  }
}

/**
 * 数据点模型
 */
class DataPoint extends BaseModel {
  /**
   * 初始化数据点属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    this.date = data.date ? new Date(data.date) : new Date()
    this.value = data.value || 0
    this.isAbnormal = data.isAbnormal || false
    this.reportId = data.reportId || '' // 关联的报告ID
    this.notes = data.notes || ''
    this.confidence = data.confidence || 1.0 // 数据置信度
  }
  
  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      date: {
        required: true,
        validator: (value) => {
          if (!(value instanceof Date)) {
            return '日期格式不正确'
          }
          if (value > new Date()) {
            return '日期不能是未来时间'
          }
          return true
        }
      },
      value: {
        required: true,
        type: 'number',
        validator: (value) => {
          if (isNaN(value) || !isFinite(value)) {
            return '数值格式不正确'
          }
          return true
        }
      }
    }
  }
  
  /**
   * 从JSON创建数据点实例
   * @param {Object} json - JSON对象
   * @returns {DataPoint} 数据点实例
   */
  static fromJSON(json) {
    return new DataPoint(json)
  }
}

/**
 * 比较数据模型
 */
class ComparisonData extends BaseModel {
  /**
   * 初始化比较属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    this.item = data.item || ''
    this.baselineValue = data.baselineValue || 0
    this.currentValue = data.currentValue || 0
    this.baselineDate = data.baselineDate ? new Date(data.baselineDate) : null
    this.currentDate = data.currentDate ? new Date(data.currentDate) : null
    this.change = data.change || 0
    this.changePercentage = data.changePercentage || 0
    this.significance = data.significance || 'none' // none | low | medium | high
    this.direction = data.direction || 'stable' // up | down | stable
    this.analysis = data.analysis || ''
  }
  
  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      item: {
        required: true,
        type: 'string',
        minLength: 1,
        message: '比较项目名称是必填项'
      },
      baselineValue: {
        required: true,
        type: 'number',
        message: '基线值是必填项'
      },
      currentValue: {
        required: true,
        type: 'number',
        message: '当前值是必填项'
      }
    }
  }
  
  /**
   * 计算变化
   */
  calculateChange() {
    this.change = this.currentValue - this.baselineValue
    
    if (this.baselineValue !== 0) {
      this.changePercentage = (this.change / this.baselineValue) * 100
    } else {
      this.changePercentage = 0
    }
    
    // 确定方向
    if (Math.abs(this.changePercentage) < 5) {
      this.direction = 'stable'
    } else if (this.change > 0) {
      this.direction = 'up'
    } else {
      this.direction = 'down'
    }
    
    this.updatedAt = new Date()
  }
  
  /**
   * 从JSON创建比较数据实例
   * @param {Object} json - JSON对象
   * @returns {ComparisonData} 比较数据实例
   */
  static fromJSON(json) {
    return new ComparisonData(json)
  }
}

module.exports = { Analysis, TrendData, DataPoint, ComparisonData }