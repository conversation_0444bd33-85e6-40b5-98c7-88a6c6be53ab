/**
 * 用户注册功能集成测试
 * 测试用户注册页面组件和表单验证、手机号验证码发送和验证逻辑
 */

import { createPinia, setActivePinia } from 'pinia'
import authService from '../../services/auth/authService.js'
import { useUserStore } from '../../stores/user.js'
import { User } from '../../models/User.js'

// Mock uni-app API
global.uni = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn().mockReturnValue(null),
  removeStorageSync: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  navigateTo: jest.fn(),
  reLaunch: jest.fn(),
  request: jest.fn()
}

describe('用户注册功能测试', () => {
  let pinia
  let userStore

  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks()
    
    // 创建新的pinia实例
    pinia = createPinia()
    setActivePinia(pinia)
    userStore = useUserStore()
  })

  describe('表单验证测试', () => {
    test('应该验证手机号格式', () => {
      // 测试有效手机号
      expect(authService.validatePhone('13800138000')).toBe(true)
      expect(authService.validatePhone('15912345678')).toBe(true)
      
      // 测试无效手机号
      expect(authService.validatePhone('1380013800')).toBe(false) // 少一位
      expect(authService.validatePhone('138001380001')).toBe(false) // 多一位
      expect(authService.validatePhone('12800138000')).toBe(false) // 第二位不是3-9
      expect(authService.validatePhone('abc12345678')).toBe(false) // 包含字母
      expect(authService.validatePhone('')).toBe(false) // 空字符串
    })

    test('应该验证密码格式', () => {
      // 测试有效密码
      expect(authService.validatePassword('abc12345')).toBe(true)
      expect(authService.validatePassword('Test123456')).toBe(true)
      expect(authService.validatePassword('myPassword1')).toBe(true)
      
      // 测试无效密码
      expect(authService.validatePassword('12345678')).toBe(false) // 只有数字
      expect(authService.validatePassword('abcdefgh')).toBe(false) // 只有字母
      expect(authService.validatePassword('abc123')).toBe(false) // 少于8位
      expect(authService.validatePassword('')).toBe(false) // 空字符串
    })

    test('应该验证验证码格式', () => {
      // 测试有效验证码
      expect(authService.validateVerificationCode('123456')).toBe(true)
      expect(authService.validateVerificationCode('000000')).toBe(true)
      
      // 测试无效验证码
      expect(authService.validateVerificationCode('12345')).toBe(false) // 少于6位
      expect(authService.validateVerificationCode('1234567')).toBe(false) // 多于6位
      expect(authService.validateVerificationCode('12345a')).toBe(false) // 包含字母
      expect(authService.validateVerificationCode('')).toBe(false) // 空字符串
    })
  })

  describe('验证码发送测试', () => {
    test('应该成功发送验证码', async () => {
      const phone = '13800138001'
      const result = await authService.sendVerificationCode(phone, 'register')
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('验证码已发送')
      expect(result.data).toHaveProperty('codeId')
      expect(result.data).toHaveProperty('expiry')
    })

    test('应该拒绝无效手机号', async () => {
      const invalidPhone = '1234567890'
      const result = await authService.sendVerificationCode(invalidPhone, 'register')
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('手机号格式不正确')
    })

    test('应该限制验证码发送频率', async () => {
      const phone = '13800138002'
      
      // 模拟上次发送时间为30秒前
      uni.getStorageSync.mockReturnValue(Date.now() - 30000)
      
      const result = await authService.sendVerificationCode(phone, 'register')
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('验证码发送过于频繁，请稍后再试')
    })
  })

  describe('用户注册测试', () => {
    test('应该成功注册新用户', async () => {
      const registerData = {
        phone: '13800138003',
        code: '123456',
        password: 'test123456',
        nickname: '测试用户'
      }
      
      const result = await authService.registerWithPhone(registerData)
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('注册成功')
      expect(result.data).toHaveProperty('token')
      expect(result.data).toHaveProperty('refreshToken')
      expect(result.data).toHaveProperty('tokenExpiry')
      expect(result.data).toHaveProperty('userInfo')
      expect(result.data.userInfo.phone).toBe(registerData.phone)
      expect(result.data.userInfo.nickname).toBe(registerData.nickname)
    })

    test('应该拒绝无效的注册数据', async () => {
      // 测试缺少必填字段
      const incompleteData = {
        phone: '13800138004',
        code: '123456'
        // 缺少password
      }
      
      const result = await authService.registerWithPhone(incompleteData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('请填写完整信息')
    })

    test('应该拒绝无效手机号注册', async () => {
      const invalidPhoneData = {
        phone: '1234567890',
        code: '123456',
        password: 'test123456'
      }
      
      const result = await authService.registerWithPhone(invalidPhoneData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('手机号格式不正确')
    })

    test('应该拒绝无效密码注册', async () => {
      const invalidPasswordData = {
        phone: '13800138005',
        code: '123456',
        password: '123456' // 只有数字，不符合要求
      }
      
      const result = await authService.registerWithPhone(invalidPasswordData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('密码格式不正确，至少8位包含字母和数字')
    })

    test('应该拒绝错误的验证码', async () => {
      const wrongCodeData = {
        phone: '13800138006',
        code: '654321', // 错误的验证码
        password: 'test123456'
      }
      
      const result = await authService.registerWithPhone(wrongCodeData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('验证码错误')
    })

    test('应该拒绝已存在的手机号注册', async () => {
      const existingPhoneData = {
        phone: '13800138000', // 这个号码在mock中设置为已存在
        code: '123456',
        password: 'test123456'
      }
      
      const result = await authService.registerWithPhone(existingPhoneData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('该手机号已注册，请直接登录')
    })

    test('注册成功后应该更新用户状态', async () => {
      const registerData = {
        phone: '13800138007',
        code: '123456',
        password: 'test123456',
        nickname: '新用户'
      }
      
      const result = await authService.registerWithPhone(registerData)
      expect(result.success).toBe(true)
      
      // 模拟登录过程
      await userStore.login(result.data)
      
      expect(userStore.isAuthenticated).toBe(true)
      expect(userStore.userInfo.phone).toBe(registerData.phone)
      expect(userStore.userInfo.nickname).toBe(registerData.nickname)
      expect(userStore.auth.token).toBe(result.data.token)
    })
  })

  describe('用户模型验证测试', () => {
    test('应该创建有效的用户模型', () => {
      const userData = {
        id: 'user_123',
        phone: '13800138008',
        nickname: '测试用户',
        avatar: '',
        gender: 'male',
        birthday: new Date('1990-01-01'),
        height: 175,
        weight: 70,
        createdAt: new Date(),
        updatedAt: new Date(),
        settings: {
          biometricEnabled: false,
          autoSync: true,
          notificationEnabled: true,
          theme: 'light'
        }
      }
      
      const user = new User(userData)
      
      expect(user.isValid()).toBe(true)
      expect(user.phone).toBe(userData.phone)
      expect(user.nickname).toBe(userData.nickname)
      expect(user.getDisplayName()).toBe(userData.nickname)
    })

    test('应该验证用户数据完整性', () => {
      const invalidUserData = {
        // 缺少必填的id和phone字段
        nickname: '测试用户'
      }
      
      const user = new User(invalidUserData)
      
      expect(user.isValid()).toBe(false)
      const errors = user.getValidationErrors()
      expect(errors.some(e => e.field === 'phone')).toBe(true)
    })
  })

  describe('集成测试', () => {
    test('完整的注册流程测试', async () => {
      const phone = '13800138009'
      const code = '123456'
      const password = 'test123456'
      const nickname = '集成测试用户'
      
      // 1. 发送验证码
      const sendCodeResult = await authService.sendVerificationCode(phone, 'register')
      expect(sendCodeResult.success).toBe(true)
      
      // 2. 注册用户
      const registerResult = await authService.registerWithPhone({
        phone,
        code,
        password,
        nickname
      })
      expect(registerResult.success).toBe(true)
      
      // 3. 登录到用户状态
      await userStore.login(registerResult.data)
      
      // 4. 验证最终状态
      expect(userStore.isAuthenticated).toBe(true)
      expect(userStore.userInfo.phone).toBe(phone)
      expect(userStore.userInfo.nickname).toBe(nickname)
      expect(userStore.auth.token).toBeTruthy()
      expect(userStore.auth.refreshToken).toBeTruthy()
      
      // 5. 验证本地存储
      expect(uni.setStorageSync).toHaveBeenCalledWith('user_info', expect.objectContaining({
        phone,
        nickname
      }))
      expect(uni.setStorageSync).toHaveBeenCalledWith('auth_token', expect.any(String))
    })
  })
})