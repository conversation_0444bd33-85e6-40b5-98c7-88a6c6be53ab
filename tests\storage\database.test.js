/**
 * 数据库操作单元测试
 */

import { getDatabase } from '../../utils/storage/database.js';
import { ALL_TABLES } from '../../utils/storage/schema.js';

// Mock uni-app API
global.uni = {
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn()
};

describe('Database', () => {
  let db;

  beforeEach(() => {
    // 重置mock
    jest.clearAllMocks();
    
    // 创建新的数据库实例
    db = getDatabase();
    db.isInitialized = false;
    db.tables.clear();
    
    // Mock存储返回空数据
    global.uni.getStorageSync.mockReturnValue(null);
  });

  describe('初始化', () => {
    test('应该成功初始化数据库', async () => {
      global.uni.getStorageSync.mockReturnValue(0); // 数据库版本
      
      const result = await db.initialize();
      
      expect(result).toBe(true);
      expect(db.isInitialized).toBe(true);
      expect(global.uni.setStorageSync).toHaveBeenCalledWith('heath_report_db_version', 1);
    });

    test('应该创建所有必需的表', async () => {
      await db.initialize();
      
      ALL_TABLES.forEach(table => {
        expect(db.tables.has(table.name)).toBe(true);
      });
    });

    test('应该处理数据库版本升级', async () => {
      global.uni.getStorageSync.mockReturnValue(0); // 旧版本
      
      await db.initialize();
      
      expect(global.uni.setStorageSync).toHaveBeenCalledWith('heath_report_db_version', 1);
    });
  });

  describe('数据操作', () => {
    beforeEach(async () => {
      await db.initialize();
    });

    test('应该能够插入数据', async () => {
      const testData = {
        username: 'testuser',
        email: '<EMAIL>'
      };

      const result = await db.insert('users', testData);

      expect(result).toMatchObject({
        id: 1,
        username: 'testuser',
        email: '<EMAIL>'
      });
      expect(result.created_at).toBeDefined();
      expect(result.updated_at).toBeDefined();
    });

    test('应该能够查询数据', async () => {
      // 插入测试数据
      await db.insert('users', { username: 'user1', age: 25 });
      await db.insert('users', { username: 'user2', age: 30 });

      // 查询所有数据
      const allUsers = db.query('users');
      expect(allUsers).toHaveLength(2);

      // 条件查询
      const user1 = db.query('users', { username: 'user1' });
      expect(user1).toHaveLength(1);
      expect(user1[0].username).toBe('user1');

      // 范围查询
      const youngUsers = db.query('users', { age: { $lt: 30 } });
      expect(youngUsers).toHaveLength(1);
      expect(youngUsers[0].age).toBe(25);
    });

    test('应该能够更新数据', async () => {
      // 插入测试数据
      const user = await db.insert('users', { username: 'testuser', age: 25 });

      // 更新数据
      const updatedCount = await db.update('users', { id: user.id }, { age: 26 });

      expect(updatedCount).toBe(1);

      // 验证更新结果
      const updatedUser = db.query('users', { id: user.id })[0];
      expect(updatedUser.age).toBe(26);
      expect(updatedUser.updated_at).not.toBe(user.updated_at);
    });

    test('应该能够删除数据', async () => {
      // 插入测试数据
      const user = await db.insert('users', { username: 'testuser' });

      // 删除数据
      const deletedCount = await db.delete('users', { id: user.id });

      expect(deletedCount).toBe(1);

      // 验证删除结果
      const remainingUsers = db.query('users');
      expect(remainingUsers).toHaveLength(0);
    });

    test('应该支持排序查询', async () => {
      // 插入测试数据
      await db.insert('users', { username: 'user1', age: 30 });
      await db.insert('users', { username: 'user2', age: 25 });
      await db.insert('users', { username: 'user3', age: 35 });

      // 按年龄升序排序
      const usersAsc = db.query('users', {}, { orderBy: 'age ASC' });
      expect(usersAsc[0].age).toBe(25);
      expect(usersAsc[2].age).toBe(35);

      // 按年龄降序排序
      const usersDesc = db.query('users', {}, { orderBy: 'age DESC' });
      expect(usersDesc[0].age).toBe(35);
      expect(usersDesc[2].age).toBe(25);
    });

    test('应该支持分页查询', async () => {
      // 插入测试数据
      for (let i = 1; i <= 10; i++) {
        await db.insert('users', { username: `user${i}`, age: 20 + i });
      }

      // 分页查询
      const page1 = db.query('users', {}, { limit: 3, offset: 0 });
      const page2 = db.query('users', {}, { limit: 3, offset: 3 });

      expect(page1).toHaveLength(3);
      expect(page2).toHaveLength(3);
      expect(page1[0].id).not.toBe(page2[0].id);
    });

    test('应该能够清空表', async () => {
      // 插入测试数据
      await db.insert('users', { username: 'user1' });
      await db.insert('users', { username: 'user2' });

      // 清空表
      await db.truncate('users');

      // 验证表已清空
      const users = db.query('users');
      expect(users).toHaveLength(0);
    });
  });

  describe('ID生成', () => {
    beforeEach(async () => {
      await db.initialize();
    });

    test('应该生成递增的ID', async () => {
      const user1 = await db.insert('users', { username: 'user1' });
      const user2 = await db.insert('users', { username: 'user2' });

      expect(user1.id).toBe(1);
      expect(user2.id).toBe(2);
    });

    test('空表应该从1开始生成ID', () => {
      const id = db.generateId('users');
      expect(id).toBe(1);
    });
  });

  describe('错误处理', () => {
    test('未初始化时应该抛出错误', () => {
      expect(() => {
        db.query('users');
      }).toThrow('数据库未初始化');
    });

    test('应该处理存储错误', async () => {
      await db.initialize();
      
      // Mock存储失败
      global.uni.setStorageSync.mockImplementation(() => {
        throw new Error('存储失败');
      });

      await expect(db.insert('users', { username: 'test' })).rejects.toThrow();
    });
  });

  describe('统计信息', () => {
    beforeEach(async () => {
      await db.initialize();
    });

    test('应该返回正确的表统计信息', async () => {
      // 插入测试数据
      await db.insert('users', { username: 'user1' });
      await db.insert('users', { username: 'user2' });

      const stats = db.getTableStats('users');

      expect(stats.tableName).toBe('users');
      expect(stats.recordCount).toBe(2);
      expect(stats.lastUpdated).toBeDefined();
    });

    test('空表应该返回正确的统计信息', () => {
      const stats = db.getTableStats('users');

      expect(stats.tableName).toBe('users');
      expect(stats.recordCount).toBe(0);
      expect(stats.lastUpdated).toBeNull();
    });
  });
});