import { defineStore } from 'pinia'

export const useReportStore = defineStore('report', {
  state: () => ({
    // 报告列表
    reports: [],
    
    // 当前查看的报告
    currentReport: null,
    
    // 筛选条件
    filter: {
      timeRange: {
        start: null,
        end: null
      },
      category: '', // 检查项目类别
      hospital: '', // 医院名称
      abnormalOnly: false // 仅显示异常指标
    },
    
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
      hasMore: true
    },
    
    // 加载状态
    loading: {
      list: false,
      detail: false,
      upload: false,
      delete: false
    },
    
    // 统计信息
    statistics: {
      totalReports: 0,
      abnormalCount: 0,
      lastReportDate: null,
      categories: []
    }
  }),
  
  getters: {
    // 过滤后的报告列表
    filteredReports: (state) => {
      let filtered = [...state.reports]
      
      // 时间范围筛选
      if (state.filter.timeRange.start && state.filter.timeRange.end) {
        filtered = filtered.filter(report => {
          const reportDate = new Date(report.reportDate)
          return reportDate >= state.filter.timeRange.start && 
                 reportDate <= state.filter.timeRange.end
        })
      }
      
      // 类别筛选
      if (state.filter.category) {
        filtered = filtered.filter(report => 
          report.indicators.some(indicator => 
            indicator.category === state.filter.category
          )
        )
      }
      
      // 医院筛选
      if (state.filter.hospital) {
        filtered = filtered.filter(report => 
          report.hospitalName.includes(state.filter.hospital)
        )
      }
      
      // 仅显示异常指标
      if (state.filter.abnormalOnly) {
        filtered = filtered.filter(report => 
          report.indicators.some(indicator => indicator.isAbnormal)
        )
      }
      
      return filtered.sort((a, b) => 
        new Date(b.reportDate) - new Date(a.reportDate)
      )
    },
    
    // 是否有异常指标
    hasAbnormalIndicators: (state) => {
      return state.reports.some(report => 
        report.indicators.some(indicator => indicator.isAbnormal)
      )
    },
    
    // 最近的报告
    latestReport: (state) => {
      if (state.reports.length === 0) return null
      return state.reports.reduce((latest, current) => 
        new Date(current.reportDate) > new Date(latest.reportDate) ? current : latest
      )
    },
    
    // 指标类别统计
    categoryStats: (state) => {
      const stats = {}
      state.reports.forEach(report => {
        report.indicators.forEach(indicator => {
          if (!stats[indicator.category]) {
            stats[indicator.category] = {
              total: 0,
              abnormal: 0,
              name: indicator.category
            }
          }
          stats[indicator.category].total++
          if (indicator.isAbnormal) {
            stats[indicator.category].abnormal++
          }
        })
      })
      return Object.values(stats)
    }
  },
  
  actions: {
    // 设置报告列表
    setReports(reports) {
      this.reports = reports
      this.updateStatistics()
    },
    
    // 添加报告
    addReport(report) {
      this.reports.unshift(report)
      this.updateStatistics()
      // 持久化存储
      this.saveToLocal()
    },
    
    // 更新报告
    updateReport(reportId, updatedData) {
      const index = this.reports.findIndex(r => r.id === reportId)
      if (index !== -1) {
        this.reports[index] = { ...this.reports[index], ...updatedData }
        this.updateStatistics()
        this.saveToLocal()
      }
    },
    
    // 删除报告
    deleteReport(reportId) {
      this.reports = this.reports.filter(r => r.id !== reportId)
      if (this.currentReport && this.currentReport.id === reportId) {
        this.currentReport = null
      }
      this.updateStatistics()
      this.saveToLocal()
    },
    
    // 设置当前报告
    setCurrentReport(report) {
      this.currentReport = report
    },
    
    // 更新筛选条件
    updateFilter(filterData) {
      this.filter = { ...this.filter, ...filterData }
    },
    
    // 清除筛选条件
    clearFilter() {
      this.filter = {
        timeRange: { start: null, end: null },
        category: '',
        hospital: '',
        abnormalOnly: false
      }
    },
    
    // 设置加载状态
    setLoading(type, status) {
      this.loading[type] = status
    },
    
    // 更新统计信息
    updateStatistics() {
      this.statistics.totalReports = this.reports.length
      this.statistics.abnormalCount = this.reports.filter(report => 
        report.indicators.some(indicator => indicator.isAbnormal)
      ).length
      
      if (this.reports.length > 0) {
        this.statistics.lastReportDate = this.reports.reduce((latest, current) => 
          new Date(current.reportDate) > new Date(latest.reportDate) ? current : latest
        ).reportDate
      }
      
      // 统计类别
      const categories = new Set()
      this.reports.forEach(report => {
        report.indicators.forEach(indicator => {
          categories.add(indicator.category)
        })
      })
      this.statistics.categories = Array.from(categories)
    },
    
    // 保存到本地存储
    saveToLocal() {
      try {
        uni.setStorageSync('health_reports', this.reports)
        uni.setStorageSync('report_statistics', this.statistics)
      } catch (error) {
        console.error('保存报告数据失败:', error)
      }
    },
    
    // 从本地存储加载
    loadFromLocal() {
      try {
        const savedReports = uni.getStorageSync('health_reports')
        const savedStatistics = uni.getStorageSync('report_statistics')
        
        if (savedReports && Array.isArray(savedReports)) {
          this.reports = savedReports
        }
        
        if (savedStatistics) {
          this.statistics = { ...this.statistics, ...savedStatistics }
        }
        
        this.updateStatistics()
      } catch (error) {
        console.error('加载报告数据失败:', error)
      }
    },
    
    // 初始化报告数据
    async initReports() {
      try {
        this.setLoading('list', true)
        
        // 从本地存储加载数据
        this.loadFromLocal()
        
        // 如果有网络连接，尝试同步云端数据
        // 这里可以调用同步服务
        
      } catch (error) {
        console.error('初始化报告数据失败:', error)
      } finally {
        this.setLoading('list', false)
      }
    }
  }
})