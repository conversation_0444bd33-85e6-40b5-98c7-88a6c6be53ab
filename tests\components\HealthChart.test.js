// 简化的图表组件测试
const HealthChart = require('@/components/common/HealthChart.vue')

// Mock uni-app API
global.uni = {
  createCanvasContext: jest.fn(() => ({
    clearRect: jest.fn(),
    setFillStyle: jest.fn(),
    fillRect: jest.fn(),
    beginPath: jest.fn(),
    setStrokeStyle: jest.fn(),
    setLineWidth: jest.fn(),
    moveTo: jest.fn(),
    lineTo: jest.fn(),
    stroke: jest.fn(),
    arc: jest.fn(),
    fill: jest.fn(),
    setFontSize: jest.fn(),
    fillText: jest.fn(),
    draw: jest.fn()
  })),
  createSelectorQuery: jest.fn(() => ({
    in: jest.fn(() => ({
      select: jest.fn(() => ({
        fields: jest.fn(() => ({
          exec: jest.fn()
        }))
      }))
    }))
  })),
  getSystemInfoSync: jest.fn(() => ({
    pixelRatio: 2
  }))
}

describe('HealthChart', () => {
  let wrapper
  
  beforeEach(() => {
    wrapper = mount(HealthChart, {
      props: {
        canvasId: 'testChart',
        chartData: [
          { date: '2024-01-01', value: 120 },
          { date: '2024-02-01', value: 135 },
          { date: '2024-03-01', value: 125 }
        ],
        chartType: 'line',
        width: 350,
        height: 250,
        normalRange: { min: 90, max: 140 }
      }
    })
  })
  
  afterEach(() => {
    wrapper.unmount()
  })
  
  it('应该正确渲染组件', () => {
    expect(wrapper.find('.health-chart').exists()).toBe(true)
    expect(wrapper.find('.chart-canvas').exists()).toBe(true)
  })
  
  it('应该使用正确的canvas ID', () => {
    const canvas = wrapper.find('.chart-canvas')
    expect(canvas.attributes('canvas-id')).toBe('testChart')
    expect(canvas.attributes('id')).toBe('testChart')
  })
  
  it('应该设置正确的canvas尺寸', () => {
    const canvas = wrapper.find('.chart-canvas')
    expect(canvas.element.style.width).toBe('350px')
    expect(canvas.element.style.height).toBe('250px')
  })
  
  it('应该在mounted时初始化图表', () => {
    expect(uni.createCanvasContext).toHaveBeenCalledWith('testChart', wrapper.vm)
  })
  
  it('应该正确判断异常值', () => {
    // 正常值
    expect(wrapper.vm.isAbnormalValue(120)).toBe(false)
    
    // 异常值 - 过低
    expect(wrapper.vm.isAbnormalValue(80)).toBe(true)
    
    // 异常值 - 过高
    expect(wrapper.vm.isAbnormalValue(150)).toBe(true)
    
    // 边界值
    expect(wrapper.vm.isAbnormalValue(90)).toBe(false)
    expect(wrapper.vm.isAbnormalValue(140)).toBe(false)
  })
  
  it('应该在没有正常范围时返回false', async () => {
    await wrapper.setProps({
      normalRange: { min: null, max: null }
    })
    
    expect(wrapper.vm.isAbnormalValue(80)).toBe(false)
    expect(wrapper.vm.isAbnormalValue(150)).toBe(false)
  })
  
  it('应该在chartData变化时重新绘制图表', async () => {
    const drawChartSpy = jest.spyOn(wrapper.vm, 'drawChart')
    
    await wrapper.setProps({
      chartData: [
        { date: '2024-01-01', value: 110 },
        { date: '2024-02-01', value: 115 }
      ]
    })
    
    expect(drawChartSpy).toHaveBeenCalled()
  })
  
  it('应该正确处理空数据', async () => {
    await wrapper.setProps({
      chartData: []
    })
    
    const drawChartSpy = jest.spyOn(wrapper.vm, 'drawChart')
    wrapper.vm.drawChart()
    
    expect(drawChartSpy).toHaveBeenCalled()
  })
  
  it('应该支持不同的图表类型', async () => {
    // 测试折线图
    expect(wrapper.vm.chartType).toBe('line')
    
    // 切换到柱状图
    await wrapper.setProps({
      chartType: 'bar'
    })
    
    expect(wrapper.vm.chartType).toBe('bar')
  })
  
  it('应该正确处理触摸事件', () => {
    const mockTouchEvent = {
      touches: [
        { clientX: 100, clientY: 100 }
      ]
    }
    
    wrapper.vm.handleTouchStart(mockTouchEvent)
    expect(wrapper.vm.touchStartX).toBe(100)
    expect(wrapper.vm.touchStartY).toBe(100)
  })
  
  it('应该支持单指滑动', () => {
    const startEvent = {
      touches: [{ clientX: 100, clientY: 100 }]
    }
    
    const moveEvent = {
      touches: [{ clientX: 120, clientY: 100 }]
    }
    
    wrapper.vm.handleTouchStart(startEvent)
    const initialOffsetX = wrapper.vm.offsetX
    
    wrapper.vm.handleTouchMove(moveEvent)
    
    expect(wrapper.vm.offsetX).toBe(initialOffsetX + 20)
  })
  
  it('应该支持双指缩放', () => {
    const moveEvent = {
      touches: [
        { clientX: 100, clientY: 100 },
        { clientX: 200, clientY: 100 }
      ]
    }
    
    wrapper.vm.lastDistance = 100
    wrapper.vm.handleTouchMove(moveEvent)
    
    // 双指距离为100，应该触发缩放逻辑
    expect(wrapper.vm.lastDistance).toBe(100)
  })
  
  it('应该在touchend时清理状态', () => {
    wrapper.vm.lastDistance = 100
    wrapper.vm.handleTouchEnd()
    
    expect(wrapper.vm.lastDistance).toBeNull()
  })
  
  it('应该限制缩放范围', () => {
    wrapper.vm.scale = 0.3
    const moveEvent = {
      touches: [
        { clientX: 100, clientY: 100 },
        { clientX: 150, clientY: 100 }
      ]
    }
    
    wrapper.vm.lastDistance = 100
    wrapper.vm.handleTouchMove(moveEvent)
    
    // 缩放应该被限制在0.5-3之间
    expect(wrapper.vm.scale).toBeGreaterThanOrEqual(0.5)
    expect(wrapper.vm.scale).toBeLessThanOrEqual(3)
  })
  
  describe('绘图方法测试', () => {
    beforeEach(() => {
      wrapper.vm.ctx = {
        clearRect: jest.fn(),
        setFillStyle: jest.fn(),
        fillRect: jest.fn(),
        beginPath: jest.fn(),
        setStrokeStyle: jest.fn(),
        setLineWidth: jest.fn(),
        moveTo: jest.fn(),
        lineTo: jest.fn(),
        stroke: jest.fn(),
        arc: jest.fn(),
        fill: jest.fn(),
        setFontSize: jest.fn(),
        fillText: jest.fn(),
        draw: jest.fn()
      }
    })
    
    it('应该正确清理画布', () => {
      wrapper.vm.clearCanvas()
      
      expect(wrapper.vm.ctx.clearRect).toHaveBeenCalledWith(0, 0, 350, 250)
      expect(wrapper.vm.ctx.setFillStyle).toHaveBeenCalledWith('#ffffff')
      expect(wrapper.vm.ctx.fillRect).toHaveBeenCalledWith(0, 0, 350, 250)
    })
    
    it('应该绘制坐标轴', () => {
      wrapper.vm.drawAxes(40, 270, 170)
      
      expect(wrapper.vm.ctx.beginPath).toHaveBeenCalled()
      expect(wrapper.vm.ctx.setStrokeStyle).toHaveBeenCalledWith('#CCCCCC')
      expect(wrapper.vm.ctx.setLineWidth).toHaveBeenCalledWith(1)
      expect(wrapper.vm.ctx.moveTo).toHaveBeenCalled()
      expect(wrapper.vm.ctx.lineTo).toHaveBeenCalled()
      expect(wrapper.vm.ctx.stroke).toHaveBeenCalled()
    })
    
    it('应该绘制网格线', () => {
      wrapper.vm.drawGrid(40, 270, 170)
      
      expect(wrapper.vm.ctx.beginPath).toHaveBeenCalled()
      expect(wrapper.vm.ctx.setStrokeStyle).toHaveBeenCalledWith('#F0F0F0')
      expect(wrapper.vm.ctx.setLineWidth).toHaveBeenCalledWith(0.5)
    })
    
    it('应该绘制正常值范围', () => {
      wrapper.vm.drawNormalRange(40, 270, 170, 100, 50)
      
      expect(wrapper.vm.ctx.setFillStyle).toHaveBeenCalledWith('rgba(0, 122, 255, 0.1)')
      expect(wrapper.vm.ctx.fillRect).toHaveBeenCalled()
    })
    
    it('应该绘制图例', () => {
      wrapper.vm.drawLegend()
      
      expect(wrapper.vm.ctx.setFillStyle).toHaveBeenCalledWith('#007AFF')
      expect(wrapper.vm.ctx.fillRect).toHaveBeenCalled()
      expect(wrapper.vm.ctx.fillText).toHaveBeenCalledWith('正常值', 25, expect.any(Number))
      expect(wrapper.vm.ctx.fillText).toHaveBeenCalledWith('异常值', 95, expect.any(Number))
    })
  })
  
  describe('数据处理测试', () => {
    it('应该正确处理有效的图表数据', () => {
      const validData = [
        { date: '2024-01-01', value: 120 },
        { date: '2024-02-01', value: 135 },
        { date: '2024-03-01', value: 125 }
      ]
      
      expect(validData.every(item => 
        item.date && typeof item.value === 'number'
      )).toBe(true)
    })
    
    it('应该处理包含异常值的数据', () => {
      const dataWithAbnormal = [
        { date: '2024-01-01', value: 80 },  // 异常低值
        { date: '2024-02-01', value: 120 }, // 正常值
        { date: '2024-03-01', value: 160 }  // 异常高值
      ]
      
      expect(wrapper.vm.isAbnormalValue(dataWithAbnormal[0].value)).toBe(true)
      expect(wrapper.vm.isAbnormalValue(dataWithAbnormal[1].value)).toBe(false)
      expect(wrapper.vm.isAbnormalValue(dataWithAbnormal[2].value)).toBe(true)
    })
  })
})