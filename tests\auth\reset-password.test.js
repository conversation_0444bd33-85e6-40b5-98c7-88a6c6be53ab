/**
 * 密码重置功能端到端测试
 * 测试密码重置的完整流程，包括手机验证、密码重置和安全验证
 */

// Mock uni API before importing authService
global.uni = {
  getStorageSync: jest.fn(() => null),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  clearStorageSync: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  request: jest.fn()
}

const authService = require('../../services/auth/authService.js')

describe('密码重置功能测试', () => {
  // 测试数据
  const testData = {
    validPhone: '13800138000', // 已注册的手机号
    unregisteredPhone: '18888888888', // 未注册的手机号
    invalidPhone: '1234567890', // 无效手机号
    validCode: '123456', // 有效验证码
    invalidCode: '000000', // 无效验证码
    validPassword: 'Test123456', // 有效密码
weakPassword: 'password123', // 弱密码
    shortPassword: '123', // 过短密码
    noLetterPassword: '12345678', // 无字母密码
    noNumberPassword: 'testpassword' // 无数字密码
  }

  beforeEach(() => {
    // 清理本地存储
    uni.clearStorageSync()
    
    // 模拟uni对象
    global.uni = {
      getStorageSync: jest.fn(() => null),
      setStorageSync: jest.fn(),
      removeStorageSync: jest.fn(),
      clearStorageSync: jest.fn(),
      showToast: jest.fn(),
      showModal: jest.fn()
    }
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('发送验证码测试', () => {
    test('应该成功发送验证码到已注册手机号', async () => {
      const result = await authService.sendVerificationCode(testData.validPhone, 'reset')
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('验证码已发送')
      expect(result.data).toBeDefined()
    })

    test('应该拒绝向无效手机号发送验证码', async () => {
      const result = await authService.sendVerificationCode(testData.invalidPhone, 'reset')
      
      expect(result.success).toBe(false)
      expect(result.message).toContain('手机号格式不正确')
    })

    test('应该限制验证码发送频率', async () => {
      // 模拟上次发送时间为30秒前
      const lastSendTime = Date.now() - 30000
      uni.getStorageSync.mockReturnValue(lastSendTime)
      
      const result = await authService.sendVerificationCode(testData.validPhone, 'reset')
      
      expect(result.success).toBe(false)
      expect(result.message).toContain('验证码发送过于频繁')
    })

    test('应该记录验证码发送时间', async () => {
      // 确保setStorageSync正常工作
      uni.setStorageSync.mockImplementation(() => {})
      
      await authService.sendVerificationCode(testData.validPhone, 'reset')
      
      expect(uni.setStorageSync).toHaveBeenCalledWith(
        `last_send_code_${testData.validPhone}`,
        expect.any(Number)
      )
    })
  })

  describe('密码重置验证测试', () => {
    test('应该验证所有必填字段', async () => {
      const result = await authService.resetPassword({})
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('请填写完整信息')
    })

    test('应该验证手机号格式', async () => {
      const result = await authService.resetPassword({
        phone: testData.invalidPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('手机号格式不正确')
    })

    test('应该验证验证码格式', async () => {
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: '123', // 格式错误的验证码
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('验证码格式不正确')
    })

    test('应该验证密码格式', async () => {
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.shortPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toContain('密码格式不正确')
    })

    test('应该检查手机号是否已注册', async () => {
      const result = await authService.resetPassword({
        phone: testData.unregisteredPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('该手机号未注册，请先注册')
    })
  })

  describe('密码重置流程测试', () => {
    test('应该成功重置密码', async () => {
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('密码重置成功')
      expect(result.data).toBeDefined()
    })

    test('应该拒绝错误的验证码', async () => {
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.invalidCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toContain('验证码错误')
    })

    test('应该拒绝弱密码', async () => {
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.weakPassword
      })
      
      expect(result.success).toBe(false)
expect(result.message).toContain('密码强度过低')
    })

    test('应该清除认证数据', async () => {
      await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(uni.removeStorageSync).toHaveBeenCalledWith('auth_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('refresh_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('token_expiry')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('user_info')
    })
  })

  describe('安全性测试', () => {
    test('应该限制重置频率', async () => {
      // 模拟上次重置尝试为30秒前
      const lastAttempt = Date.now() - 30000
      uni.getStorageSync.mockReturnValue(lastAttempt)
      
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toContain('重置密码过于频繁')
    })

    test('应该记录安全事件', async () => {
      // 模拟失败的重置尝试
      await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.invalidCode,
        newPassword: testData.validPassword
      })
      
      expect(uni.setStorageSync).toHaveBeenCalledWith(
        'security_logs',
        expect.arrayContaining([
          expect.objectContaining({
            type: 'password_reset_failed',
            data: expect.objectContaining({
              phone: testData.validPhone,
              error: expect.any(String),
              timestamp: expect.any(Number)
            })
          })
        ])
      )
    })

    test('应该清除重置尝试记录（成功时）', async () => {
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(true)
      expect(uni.removeStorageSync).toHaveBeenCalledWith(`reset_attempt_${testData.validPhone}`)
    })
  })

  describe('密码强度验证测试', () => {
    const passwordTests = [
      { password: 'Test123456', expected: true, description: '有效密码' },
      { password: '12345678', expected: false, description: '纯数字密码' },
      { password: 'testpassword', expected: false, description: '纯字母密码' },
      { password: 'Test123', expected: false, description: '过短密码' },
      { password: 'Test123456789012345678901', expected: false, description: '过长密码' },
      { password: 'password123', expected: false, description: '常见弱密码' },
      { password: '111111111', expected: false, description: '重复数字密码' }
    ]

    passwordTests.forEach(({ password, expected, description }) => {
      test(`应该正确验证${description}`, async () => {
        const result = await authService.resetPassword({
          phone: testData.validPhone,
          code: testData.validCode,
          newPassword: password
        })
        
        expect(result.success).toBe(expected)
        if (!expected) {
          expect(result.message).toContain('密码')
        }
      })
    })
  })

  describe('错误处理测试', () => {
    test('应该处理网络错误', async () => {
      // 模拟网络错误 - 需要在所有请求中都失败
      const originalRequest = authService.request
      const originalCheckPhoneExists = authService.checkPhoneExists
      
      authService.request = jest.fn().mockRejectedValue(new Error('网络连接失败'))
      authService.checkPhoneExists = jest.fn().mockResolvedValue(true)
      
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('网络连接失败')
      
      // 恢复原方法
      authService.request = originalRequest
      authService.checkPhoneExists = originalCheckPhoneExists
    })

    test('应该处理服务器错误', async () => {
      // 模拟服务器错误
      const originalRequest = authService.request
      const originalCheckPhoneExists = authService.checkPhoneExists
      
      authService.request = jest.fn().mockRejectedValue(new Error('服务器内部错误'))
      authService.checkPhoneExists = jest.fn().mockResolvedValue(true)
      
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: testData.validPassword
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('服务器内部错误')
      
      // 恢复原方法
      authService.request = originalRequest
      authService.checkPhoneExists = originalCheckPhoneExists
    })
  })

  describe('边界条件测试', () => {
    test('应该处理空字符串输入', async () => {
      const result = await authService.resetPassword({
        phone: '',
        code: '',
        newPassword: ''
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('请填写完整信息')
    })

    test('应该处理null和undefined输入', async () => {
      const result = await authService.resetPassword({
        phone: null,
        code: undefined,
        newPassword: null
      })
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('请填写完整信息')
    })

    test('应该处理特殊字符', async () => {
      const result = await authService.resetPassword({
        phone: testData.validPhone,
        code: testData.validCode,
        newPassword: 'Test123@#$'
      })
      
      expect(result.success).toBe(true)
    })
  })
})