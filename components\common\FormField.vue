<template>
	<view class="form-field" :class="{ 'field-error': hasError, 'field-success': isValid && value }">
		<!-- 字段标签 -->
		<view class="field-label" v-if="label">
			<text class="label-text">{{ label }}</text>
			<text class="required-mark" v-if="required">*</text>
		</view>
		
		<!-- 输入框 -->
		<view class="field-input">
			<input
				v-if="type === 'text' || type === 'number' || type === 'password'"
				class="input-control"
				:type="type"
				:placeholder="placeholder"
				:value="value"
				:disabled="disabled"
				:maxlength="maxlength"
				@input="onInput"
				@blur="onBlur"
				@focus="onFocus"
			/>
			
			<textarea
				v-else-if="type === 'textarea'"
				class="textarea-control"
				:placeholder="placeholder"
				:value="value"
				:disabled="disabled"
				:maxlength="maxlength"
				@input="onInput"
				@blur="onBlur"
				@focus="onFocus"
			></textarea>
			
			<picker
				v-else-if="type === 'date'"
				mode="date"
				:value="value"
				:disabled="disabled"
				@change="onDateChange"
			>
				<view class="picker-control" :class="{ 'placeholder': !value }">
					{{ value || placeholder }}
				</view>
			</picker>
			
			<picker
				v-else-if="type === 'selector'"
				mode="selector"
				:range="options"
				:range-key="optionKey"
				:value="selectorIndex"
				:disabled="disabled"
				@change="onSelectorChange"
			>
				<view class="picker-control" :class="{ 'placeholder': selectorIndex === -1 }">
					{{ selectedOptionText || placeholder }}
				</view>
			</picker>
			
			<!-- 输入状态图标 -->
			<view class="field-icon" v-if="showIcon">
				<text class="icon-success" v-if="isValid && value">✓</text>
				<text class="icon-error" v-if="hasError">✗</text>
				<text class="icon-loading" v-if="isValidating">⟳</text>
			</view>
		</view>
		
		<!-- 实时提示信息 -->
		<view class="field-message" v-if="showMessage">
			<text class="message-text" :class="messageClass">{{ currentMessage }}</text>
		</view>
		
		<!-- 字符计数 -->
		<view class="field-counter" v-if="showCounter && maxlength">
			<text class="counter-text">{{ (value || '').length }}/{{ maxlength }}</text>
		</view>
	</view>
</template>

<script>
	import validator from '../../utils/validation/index.js'
	
	export default {
		name: 'FormField',
		props: {
			// 基础属性
			label: String,
			type: {
				type: String,
				default: 'text'
			},
			value: [String, Number],
			placeholder: String,
			disabled: Boolean,
			required: Boolean,
			maxlength: Number,
			
			// 验证相关
			validationCategory: String,
			validationField: String,
			customRules: Object,
			realTimeValidation: {
				type: Boolean,
				default: true
			},
			
			// 选择器相关
			options: Array,
			optionKey: String,
			
			// 显示控制
			showIcon: {
				type: Boolean,
				default: true
			},
			showMessage: {
				type: Boolean,
				default: true
			},
			showCounter: Boolean
		},
		
		data() {
			return {
				isValidating: false,
				isValid: false,
				hasError: false,
				errorMessage: '',
				successMessage: '',
				isFocused: false
			}
		},
		
		computed: {
			// 当前显示的消息
			currentMessage() {
				if (this.hasError) return this.errorMessage
				if (this.isValid && this.value) return this.successMessage
				return ''
			},
			
			// 消息样式类
			messageClass() {
				if (this.hasError) return 'message-error'
				if (this.isValid && this.value) return 'message-success'
				return ''
			},
			
			// 选择器当前索引
			selectorIndex() {
				if (!this.options || !this.value) return -1
				return this.options.findIndex(option => {
					if (this.optionKey) {
						return option[this.optionKey] === this.value
					}
					return option === this.value
				})
			},
			
			// 选择器当前文本
			selectedOptionText() {
				if (this.selectorIndex === -1) return ''
				const option = this.options[this.selectorIndex]
				return this.optionKey ? option[this.optionKey] : option
			}
		},
		
		watch: {
			value: {
				handler(newValue) {
					if (this.realTimeValidation && this.validationCategory && this.validationField) {
						this.validateField(newValue)
					}
				},
				immediate: true
			}
		},
		
		methods: {
			// 输入事件处理
			onInput(e) {
				const value = e.detail.value
				this.$emit('input', value)
				this.$emit('change', value)
			},
			
			// 失焦事件处理
			onBlur(e) {
				this.isFocused = false
				const value = e.detail.value
				
				// 失焦时进行验证
				if (this.validationCategory && this.validationField) {
					this.validateField(value, false)
				}
				
				this.$emit('blur', value)
			},
			
			// 聚焦事件处理
			onFocus(e) {
				this.isFocused = true
				this.$emit('focus', e.detail.value)
			},
			
			// 日期选择处理
			onDateChange(e) {
				const value = e.detail.value
				this.$emit('input', value)
				this.$emit('change', value)
			},
			
			// 选择器变化处理
			onSelectorChange(e) {
				const index = e.detail.value
				const option = this.options[index]
				const value = this.optionKey ? option[this.optionKey] : option
				
				this.$emit('input', value)
				this.$emit('change', value)
			},
			
			// 字段验证
			validateField(value, isRealtime = true) {
				if (!this.validationCategory || !this.validationField) return
				
				if (isRealtime && this.realTimeValidation) {
					// 实时验证（防抖）
					this.isValidating = true
					validator.validateRealtime(
						this.validationCategory,
						this.validationField,
						value,
						this.handleValidationResult,
						300
					)
				} else {
					// 立即验证
					const result = validator.validateField(
						this.validationCategory,
						this.validationField,
						value,
						this.customRules
					)
					this.handleValidationResult(result)
				}
			},
			
			// 处理验证结果
			handleValidationResult(result) {
				this.isValidating = false
				this.isValid = result.isValid
				this.hasError = !result.isValid
				
				if (result.isValid) {
					this.errorMessage = ''
					this.successMessage = this.getSuccessMessage()
				} else {
					this.errorMessage = result.message
					this.successMessage = ''
				}
				
				// 触发验证结果事件
				this.$emit('validation', {
					field: this.validationField,
					isValid: result.isValid,
					message: result.message,
					errors: result.errors
				})
			},
			
			// 获取成功提示消息
			getSuccessMessage() {
				const successMessages = {
					phone: '手机号格式正确',
					email: '邮箱格式正确',
					password: '密码强度良好',
					name: '姓名格式正确'
				}
				
				return successMessages[this.validationField] || '输入正确'
			},
			
			// 手动触发验证
			validate() {
				if (this.validationCategory && this.validationField) {
					this.validateField(this.value, false)
					return {
						isValid: this.isValid,
						message: this.errorMessage
					}
				}
				return { isValid: true, message: '' }
			},
			
			// 清除验证状态
			clearValidation() {
				this.isValid = false
				this.hasError = false
				this.errorMessage = ''
				this.successMessage = ''
				this.isValidating = false
			}
		}
	}
</script>

<style scoped>
	.form-field {
		margin-bottom: 20px;
	}
	
	.field-label {
		display: flex;
		align-items: center;
		margin-bottom: 8px;
	}
	
	.label-text {
		font-size: 14px;
		color: #333333;
		font-weight: 500;
	}
	
	.required-mark {
		color: #FF3B30;
		margin-left: 4px;
		font-size: 14px;
	}
	
	.field-input {
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.input-control,
	.textarea-control {
		flex: 1;
		padding: 12px 15px;
		border: 1px solid #E5E5EA;
		border-radius: 6px;
		font-size: 14px;
		background-color: #FFFFFF;
		transition: all 0.3s ease;
	}
	
	.input-control:focus,
	.textarea-control:focus {
		border-color: #007AFF;
		outline: none;
	}
	
	.textarea-control {
		min-height: 80px;
		resize: vertical;
	}
	
	.picker-control {
		flex: 1;
		padding: 12px 15px;
		border: 1px solid #E5E5EA;
		border-radius: 6px;
		font-size: 14px;
		background-color: #FFFFFF;
		color: #333333;
	}
	
	.picker-control.placeholder {
		color: #C7C7CC;
	}
	
	.field-icon {
		position: absolute;
		right: 15px;
		top: 50%;
		transform: translateY(-50%);
		width: 20px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.icon-success {
		color: #34C759;
		font-size: 16px;
		font-weight: bold;
	}
	
	.icon-error {
		color: #FF3B30;
		font-size: 16px;
		font-weight: bold;
	}
	
	.icon-loading {
		color: #007AFF;
		font-size: 16px;
		animation: spin 1s linear infinite;
	}
	
	@keyframes spin {
		from { transform: translateY(-50%) rotate(0deg); }
		to { transform: translateY(-50%) rotate(360deg); }
	}
	
	.field-message {
		margin-top: 6px;
		min-height: 18px;
	}
	
	.message-text {
		font-size: 12px;
		line-height: 1.4;
	}
	
	.message-error {
		color: #FF3B30;
	}
	
	.message-success {
		color: #34C759;
	}
	
	.field-counter {
		margin-top: 6px;
		text-align: right;
	}
	
	.counter-text {
		font-size: 12px;
		color: #8E8E93;
	}
	
	/* 字段状态样式 */
	.field-error .input-control,
	.field-error .textarea-control,
	.field-error .picker-control {
		border-color: #FF3B30;
		background-color: rgba(255, 59, 48, 0.05);
	}
	
	.field-success .input-control,
	.field-success .textarea-control,
	.field-success .picker-control {
		border-color: #34C759;
		background-color: rgba(52, 199, 89, 0.05);
	}
	
	/* 禁用状态 */
	.input-control:disabled,
	.textarea-control:disabled,
	.picker-control:disabled {
		background-color: #F2F2F7;
		color: #8E8E93;
		cursor: not-allowed;
	}
</style>