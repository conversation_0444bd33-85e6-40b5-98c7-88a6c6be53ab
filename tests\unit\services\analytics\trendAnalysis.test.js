/**
 * 趋势分析服务测试
 * 测试需求 4.1 和 4.2 的实现
 */

import trendAnalysisService from '@/services/analytics/trendAnalysis.js'

describe('TrendAnalysisService', () => {
  // 测试数据
  const mockDataPoints = [
    { date: '2024-01-01', value: '5.2', isAbnormal: false },
    { date: '2024-01-15', value: '5.5', isAbnormal: false },
    { date: '2024-02-01', value: '5.8', isAbnormal: false },
    { date: '2024-02-15', value: '6.1', isAbnormal: true },
    { date: '2024-03-01', value: '6.4', isAbnormal: true }
  ]

  const mockStableDataPoints = [
    { date: '2024-01-01', value: '5.0', isAbnormal: false },
    { date: '2024-01-15', value: '5.1', isAbnormal: false },
    { date: '2024-02-01', value: '4.9', isAbnormal: false },
    { date: '2024-02-15', value: '5.0', isAbnormal: false },
    { date: '2024-03-01', value: '5.1', isAbnormal: false }
  ]

  const mockDecreasingDataPoints = [
    { date: '2024-01-01', value: '6.5', isAbnormal: true },
    { date: '2024-01-15', value: '6.2', isAbnormal: true },
    { date: '2024-02-01', value: '5.8', isAbnormal: false },
    { date: '2024-02-15', value: '5.5', isAbnormal: false },
    { date: '2024-03-01', value: '5.2', isAbnormal: false }
  ]

  describe('analyzeTrend', () => {
    test('应该正确分析上升趋势', () => {
      const result = trendAnalysisService.analyzeTrend(mockDataPoints)
      
      expect(result.trend).toBe('increasing')
      expect(result.direction).toBe('increasing')
      expect(result.slope).toBeGreaterThan(0)
      expect(result.changeRate).toBeGreaterThan(0)
      expect(result.summary).toContain('上升趋势')
      expect(result.dataPoints).toHaveLength(5)
    })

    test('应该正确分析稳定趋势', () => {
      const result = trendAnalysisService.analyzeTrend(mockStableDataPoints)
      
      expect(result.trend).toBe('stable')
      expect(result.direction).toBe('stable')
      expect(Math.abs(result.changeRate)).toBeLessThan(5)
      expect(result.summary).toContain('稳定')
    })

    test('应该正确分析下降趋势', () => {
      const result = trendAnalysisService.analyzeTrend(mockDecreasingDataPoints)
      
      expect(result.trend).toBe('decreasing')
      expect(result.direction).toBe('decreasing')
      expect(result.slope).toBeLessThan(0)
      expect(result.changeRate).toBeLessThan(0)
      expect(result.summary).toContain('下降趋势')
    })

    test('应该处理数据不足的情况', () => {
      const result = trendAnalysisService.analyzeTrend([])
      
      expect(result.trend).toBe('insufficient_data')
      expect(result.direction).toBeNull()
      expect(result.slope).toBe(0)
      expect(result.correlation).toBe(0)
      expect(result.summary).toContain('数据点不足')
    })

    test('应该处理单个数据点', () => {
      const singlePoint = [{ date: '2024-01-01', value: '5.2', isAbnormal: false }]
      const result = trendAnalysisService.analyzeTrend(singlePoint)
      
      expect(result.trend).toBe('insufficient_data')
      expect(result.summary).toContain('数据点不足')
    })
  })

  describe('sortDataByDate', () => {
    test('应该按日期正确排序数据点', () => {
      const unsortedData = [
        { date: '2024-02-01', value: '5.8' },
        { date: '2024-01-01', value: '5.2' },
        { date: '2024-03-01', value: '6.4' }
      ]
      
      const sorted = trendAnalysisService.sortDataByDate(unsortedData)
      
      expect(sorted[0].date).toBe('2024-01-01')
      expect(sorted[1].date).toBe('2024-02-01')
      expect(sorted[2].date).toBe('2024-03-01')
      expect(sorted[0]).toHaveProperty('timestamp')
    })
  })

  describe('calculateLinearRegression', () => {
    test('应该正确计算线性回归', () => {
      const sortedData = trendAnalysisService.sortDataByDate(mockDataPoints)
      const regression = trendAnalysisService.calculateLinearRegression(sortedData)
      
      expect(regression).toHaveProperty('slope')
      expect(regression).toHaveProperty('intercept')
      expect(regression).toHaveProperty('correlation')
      expect(regression).toHaveProperty('points')
      expect(regression.slope).toBeGreaterThan(0) // 上升趋势
      expect(regression.correlation).toBeGreaterThan(0.8) // 强相关性
    })

    test('应该处理空数据', () => {
      const regression = trendAnalysisService.calculateLinearRegression([])
      
      expect(regression.slope).toBe(0)
      expect(regression.intercept).toBe(0)
      expect(regression.correlation).toBe(0)
    })
  })

  describe('calculateChangeRate', () => {
    test('应该正确计算变化率', () => {
      const sortedData = trendAnalysisService.sortDataByDate(mockDataPoints)
      const changeRate = trendAnalysisService.calculateChangeRate(sortedData)
      
      // 从5.2到6.4，变化率应该约为23%
      expect(changeRate).toBeCloseTo(23.08, 1)
    })

    test('应该处理零值', () => {
      const zeroData = [
        { date: '2024-01-01', value: '0' },
        { date: '2024-02-01', value: '5.0' }
      ]
      const changeRate = trendAnalysisService.calculateChangeRate(zeroData)
      
      expect(changeRate).toBe(0) // 避免除零错误
    })
  })

  describe('determineTrendDirection', () => {
    test('应该正确确定趋势方向', () => {
      expect(trendAnalysisService.determineTrendDirection(0.1, 10)).toBe('increasing')
      expect(trendAnalysisService.determineTrendDirection(-0.1, -10)).toBe('decreasing')
      expect(trendAnalysisService.determineTrendDirection(0.01, 2)).toBe('stable')
      expect(trendAnalysisService.determineTrendDirection(0.1, -10)).toBe('fluctuating')
    })
  })

  describe('calculateTimeSpan', () => {
    test('应该正确计算时间跨度', () => {
      const dataPoints = [
        { date: '2024-01-01' },
        { date: '2024-01-15' }
      ]
      const timeSpan = trendAnalysisService.calculateTimeSpan(dataPoints)
      
      expect(timeSpan).toBe('14天')
    })

    test('应该处理月份跨度', () => {
      const dataPoints = [
        { date: '2024-01-01' },
        { date: '2024-03-01' }
      ]
      const timeSpan = trendAnalysisService.calculateTimeSpan(dataPoints)
      
      expect(timeSpan).toBe('2个月')
    })

    test('应该处理年份跨度', () => {
      const dataPoints = [
        { date: '2023-01-01' },
        { date: '2024-06-01' }
      ]
      const timeSpan = trendAnalysisService.calculateTimeSpan(dataPoints)
      
      expect(timeSpan).toBe('1年5个月')
    })
  })

  describe('analyzeMultipleIndicators', () => {
    test('应该正确分析多个指标', () => {
      const indicatorsData = {
        '血糖': mockDataPoints,
        '血压': mockStableDataPoints
      }
      
      const result = trendAnalysisService.analyzeMultipleIndicators(indicatorsData)
      
      expect(result).toHaveProperty('indicators')
      expect(result).toHaveProperty('correlations')
      expect(result).toHaveProperty('summary')
      expect(result.indicators).toHaveProperty('血糖')
      expect(result.indicators).toHaveProperty('血压')
      expect(result.indicators['血糖'].trend).toBe('increasing')
      expect(result.indicators['血压'].trend).toBe('stable')
    })

    test('应该计算指标间相关性', () => {
      const indicatorsData = {
        '指标1': mockDataPoints,
        '指标2': mockDataPoints // 相同数据，应该有强相关性
      }
      
      const result = trendAnalysisService.analyzeMultipleIndicators(indicatorsData)
      
      expect(result.correlations).toHaveProperty('指标1_指标2')
      expect(Math.abs(result.correlations['指标1_指标2'])).toBeGreaterThan(0.9)
    })
  })

  describe('calculatePearsonCorrelation', () => {
    test('应该正确计算皮尔逊相关系数', () => {
      const x = [1, 2, 3, 4, 5]
      const y = [2, 4, 6, 8, 10] // 完全正相关
      
      const correlation = trendAnalysisService.calculatePearsonCorrelation(x, y)
      
      expect(correlation).toBeCloseTo(1, 2)
    })

    test('应该处理负相关', () => {
      const x = [1, 2, 3, 4, 5]
      const y = [10, 8, 6, 4, 2] // 完全负相关
      
      const correlation = trendAnalysisService.calculatePearsonCorrelation(x, y)
      
      expect(correlation).toBeCloseTo(-1, 2)
    })

    test('应该处理无相关性', () => {
      const x = [1, 2, 3, 4, 5]
      const y = [3, 3, 3, 3, 3] // 无变化
      
      const correlation = trendAnalysisService.calculatePearsonCorrelation(x, y)
      
      expect(correlation).toBe(0)
    })
  })

  describe('findCommonDates', () => {
    test('应该找到共同日期', () => {
      const data1 = [
        { date: '2024-01-01', value: '5.0' },
        { date: '2024-01-15', value: '5.2' },
        { date: '2024-02-01', value: '5.4' }
      ]
      
      const data2 = [
        { date: '2024-01-01', value: '120' },
        { date: '2024-01-10', value: '125' },
        { date: '2024-02-01', value: '130' }
      ]
      
      const commonDates = trendAnalysisService.findCommonDates(data1, data2)
      
      expect(commonDates).toHaveLength(2)
      expect(commonDates).toContain('2024-01-01')
      expect(commonDates).toContain('2024-02-01')
    })
  })

  describe('generateMultiIndicatorSummary', () => {
    test('应该生成正确的多指标摘要', () => {
      const results = {
        '血糖': { trend: 'increasing' },
        '血压': { trend: 'stable' },
        '胆固醇': { trend: 'decreasing' }
      }
      
      const correlations = {
        '血糖_血压': 0.8,
        '血糖_胆固醇': 0.3,
        '血压_胆固醇': 0.2
      }
      
      const summary = trendAnalysisService.generateMultiIndicatorSummary(results, correlations)
      
      expect(summary).toContain('分析了3个健康指标')
      expect(summary).toContain('1个指标呈上升趋势')
      expect(summary).toContain('1个指标呈下降趋势')
      expect(summary).toContain('1个指标保持稳定')
      expect(summary).toContain('1对指标存在强相关性')
    })
  })
})