/**
 * 数据加密和安全传输工具
 */

import CryptoJS from 'crypto-js'

class SecurityManager {
  constructor() {
    // 应用密钥（实际项目中应从安全配置获取）
    this.appSecret = 'health-report-app-secret-key-2024'
    this.apiBaseUrl = 'https://api.healthreport.com' // 确保使用HTTPS
  }

  /**
   * AES加密
   */
  encrypt(data, key = null) {
    try {
      const secretKey = key || this.appSecret
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), secretKey).toString()
      return encrypted
    } catch (error) {
      console.error('数据加密失败:', error)
      throw new Error('数据加密失败')
    }
  }

  /**
   * AES解密
   */
  decrypt(encryptedData, key = null) {
    try {
      const secretKey = key || this.appSecret
      const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey)
      const decrypted = bytes.toString(CryptoJS.enc.Utf8)
      return JSON.parse(decrypted)
    } catch (error) {
      console.error('数据解密失败:', error)
      throw new Error('数据解密失败')
    }
  }

  /**
   * 生成随机密钥
   */
  generateRandomKey(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 生成数据签名
   */
  generateSignature(data, timestamp = null) {
    const ts = timestamp || Date.now()
    const dataString = typeof data === 'string' ? data : JSON.stringify(data)
    const signString = `${dataString}${ts}${this.appSecret}`
    return CryptoJS.SHA256(signString).toString()
  }

  /**
   * 验证数据签名
   */
  verifySignature(data, signature, timestamp) {
    const expectedSignature = this.generateSignature(data, timestamp)
    return signature === expectedSignature
  }

  /**
   * 安全的HTTP请求
   */
  async secureRequest(options) {
    const {
      url,
      method = 'GET',
      data = null,
      headers = {},
      timeout = 10000,
      encrypt = true
    } = options

    try {
      // 确保使用HTTPS
      const secureUrl = url.startsWith('http://') ? url.replace('http://', 'https://') : url
      
      // 生成时间戳和签名
      const timestamp = Date.now()
      let requestData = data
      
      if (encrypt && data) {
        // 加密请求数据
        requestData = {
          encrypted: this.encrypt(data),
          timestamp
        }
        requestData.signature = this.generateSignature(requestData.encrypted, timestamp)
      }

      // 设置安全头部
      const secureHeaders = {
        'Content-Type': 'application/json',
        'X-Timestamp': timestamp.toString(),
        'X-App-Version': uni.getSystemInfoSync().appVersion || '1.0.0',
        'X-Platform': uni.getSystemInfoSync().platform,
        ...headers
      }

      // 发起请求
      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: secureUrl,
          method,
          data: requestData,
          header: secureHeaders,
          timeout,
          success: resolve,
          fail: reject
        })
      })

      // 验证响应
      if (response.statusCode !== 200) {
        throw new Error(`HTTP ${response.statusCode}: ${response.data?.message || '请求失败'}`)
      }

      // 解密响应数据
      let responseData = response.data
      if (encrypt && responseData.encrypted) {
        // 验证响应签名
        if (!this.verifySignature(responseData.encrypted, responseData.signature, responseData.timestamp)) {
          throw new Error('响应数据签名验证失败')
        }
        
        responseData = this.decrypt(responseData.encrypted)
      }

      return responseData
    } catch (error) {
      console.error('安全请求失败:', error)
      throw error
    }
  }

  /**
   * 敏感数据本地加密存储
   */
  setSecureStorage(key, data) {
    try {
      const encrypted = this.encrypt(data)
      uni.setStorageSync(`secure_${key}`, encrypted)
    } catch (error) {
      console.error('安全存储失败:', error)
      throw new Error('数据存储失败')
    }
  }

  /**
   * 敏感数据本地解密读取
   */
  getSecureStorage(key) {
    try {
      const encrypted = uni.getStorageSync(`secure_${key}`)
      if (!encrypted) return null
      
      return this.decrypt(encrypted)
    } catch (error) {
      console.error('安全读取失败:', error)
      return null
    }
  }

  /**
   * 清除敏感数据
   */
  clearSecureStorage(key) {
    try {
      uni.removeStorageSync(`secure_${key}`)
    } catch (error) {
      console.error('清除安全存储失败:', error)
    }
  }

  /**
   * 密码强度检测
   */
  checkPasswordStrength(password) {
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    }

    const score = Object.values(checks).filter(Boolean).length
    
    let strength = 'weak'
    let message = '密码强度较弱'
    
    if (score >= 4) {
      strength = 'strong'
      message = '密码强度很强'
    } else if (score >= 3) {
      strength = 'medium'
      message = '密码强度中等'
    }

    return {
      strength,
      score,
      message,
      checks
    }
  }

  /**
   * 生成安全的随机密码
   */
  generateSecurePassword(length = 12) {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const special = '!@#$%^&*(),.?":{}|<>'
    
    const allChars = lowercase + uppercase + numbers + special
    let password = ''
    
    // 确保包含各种字符类型
    password += lowercase[Math.floor(Math.random() * lowercase.length)]
    password += uppercase[Math.floor(Math.random() * uppercase.length)]
    password += numbers[Math.floor(Math.random() * numbers.length)]
    password += special[Math.floor(Math.random() * special.length)]
    
    // 填充剩余长度
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }
    
    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }

  /**
   * 设备指纹生成
   */
  generateDeviceFingerprint() {
    const systemInfo = uni.getSystemInfoSync()
    
    const fingerprint = {
      platform: systemInfo.platform,
      system: systemInfo.system,
      model: systemInfo.model,
      brand: systemInfo.brand,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      pixelRatio: systemInfo.pixelRatio,
      language: systemInfo.language,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion
    }
    
    // 生成设备指纹哈希
    const fingerprintString = JSON.stringify(fingerprint)
    return CryptoJS.SHA256(fingerprintString).toString()
  }

  /**
   * 检测应用完整性
   */
  async checkAppIntegrity() {
    try {
      // 检查关键文件是否被篡改
      const criticalFiles = [
        'app.js',
        'manifest.json',
        'pages.json'
      ]
      
      // 这里应该检查文件哈希值
      // 实际实现需要在打包时生成文件哈希清单
      
      return {
        isIntact: true,
        message: '应用完整性检查通过'
      }
    } catch (error) {
      console.error('应用完整性检查失败:', error)
      return {
        isIntact: false,
        message: '应用完整性检查失败'
      }
    }
  }
}

// 创建全局安全管理器实例
const securityManager = new SecurityManager()

export default securityManager
export { SecurityManager }