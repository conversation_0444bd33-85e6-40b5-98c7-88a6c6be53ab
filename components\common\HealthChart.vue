<template>
  <view class="health-chart">
    <canvas 
      :canvas-id="canvasId" 
      :id="canvasId"
      class="chart-canvas"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    ></canvas>
  </view>
</template>

<script>
import UChartsAdapter from '@/utils/charts/uCharts.js'

export default {
  name: 'HealthChart',
  props: {
    canvasId: {
      type: String,
      default: 'healthChart'
    },
    chartData: {
      type: Array,
      default: () => []
    },
    chartType: {
      type: String,
      default: 'line' // line, bar
    },
    width: {
      type: Number,
      default: 350
    },
    height: {
      type: Number,
      default: 250
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    normalRange: {
      type: Object,
      default: () => ({ min: null, max: null })
    }
  },
  data() {
    return {
      ctx: null,
      chart: null,
      touchStartX: 0,
      touchStartY: 0,
      scale: 1,
      offsetX: 0,
      offsetY: 0
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    chartData: {
      handler() {
        this.drawChart()
      },
      deep: true
    }
  },
  methods: {
    async initChart() {
      try {
        // 初始化uCharts适配器
        this.chart = new UChartsAdapter({
          canvasId: this.canvasId,
          width: this.width,
          height: this.height,
          type: this.chartType,
          padding: [40, 40, 40, 40]
        })
        
        // 初始化canvas上下文
        await this.chart.init()
        this.ctx = this.chart.ctx
        
        // 绘制图表
        this.$nextTick(() => {
          this.drawChart()
        })
      } catch (error) {
        console.error('图表初始化失败:', error)
      }
    },
    
    drawChart() {
      if (!this.chart || !this.chartData.length) return
      
      const options = {
        normalRange: this.normalRange,
        showGrid: this.showGrid,
        showLegend: this.showLegend,
        colors: ['#007AFF', '#FF3B30', '#34C759', '#FF9500']
      }
      
      this.chart.updateData(this.chartData, options)
    },
    
    clearCanvas() {
      this.ctx.clearRect(0, 0, this.width, this.height)
      this.ctx.setFillStyle('#ffffff')
      this.ctx.fillRect(0, 0, this.width, this.height)
    },
    
    drawLineChart() {
      const padding = 40
      const chartWidth = this.width - padding * 2
      const chartHeight = this.height - padding * 2
      
      if (!this.chartData.length) return
      
      // 计算数据范围
      const values = this.chartData.map(item => item.value)
      const minValue = Math.min(...values)
      const maxValue = Math.max(...values)
      const valueRange = maxValue - minValue || 1
      
      // 绘制坐标轴
      this.drawAxes(padding, chartWidth, chartHeight)
      
      // 绘制正常值范围背景
      if (this.normalRange.min !== null && this.normalRange.max !== null) {
        this.drawNormalRange(padding, chartWidth, chartHeight, minValue, valueRange)
      }
      
      // 绘制折线
      this.ctx.beginPath()
      this.ctx.setStrokeStyle('#007AFF')
      this.ctx.setLineWidth(2)
      
      this.chartData.forEach((item, index) => {
        const x = padding + (index / (this.chartData.length - 1)) * chartWidth
        const y = padding + chartHeight - ((item.value - minValue) / valueRange) * chartHeight
        
        if (index === 0) {
          this.ctx.moveTo(x, y)
        } else {
          this.ctx.lineTo(x, y)
        }
      })
      
      this.ctx.stroke()
      
      // 绘制数据点
      this.chartData.forEach((item, index) => {
        const x = padding + (index / (this.chartData.length - 1)) * chartWidth
        const y = padding + chartHeight - ((item.value - minValue) / valueRange) * chartHeight
        
        // 判断是否为异常值
        const isAbnormal = this.isAbnormalValue(item.value)
        
        this.ctx.beginPath()
        this.ctx.arc(x, y, 4, 0, 2 * Math.PI)
        this.ctx.setFillStyle(isAbnormal ? '#FF3B30' : '#007AFF')
        this.ctx.fill()
        
        // 显示数值
        this.ctx.setFillStyle('#333333')
        this.ctx.setFontSize(10)
        this.ctx.fillText(item.value.toString(), x - 10, y - 10)
      })
    },
    
    drawBarChart() {
      const padding = 40
      const chartWidth = this.width - padding * 2
      const chartHeight = this.height - padding * 2
      
      if (!this.chartData.length) return
      
      const values = this.chartData.map(item => item.value)
      const maxValue = Math.max(...values)
      const barWidth = chartWidth / this.chartData.length * 0.6
      const barSpacing = chartWidth / this.chartData.length * 0.4
      
      // 绘制坐标轴
      this.drawAxes(padding, chartWidth, chartHeight)
      
      // 绘制柱状图
      this.chartData.forEach((item, index) => {
        const x = padding + index * (barWidth + barSpacing) + barSpacing / 2
        const barHeight = (item.value / maxValue) * chartHeight
        const y = padding + chartHeight - barHeight
        
        const isAbnormal = this.isAbnormalValue(item.value)
        
        this.ctx.setFillStyle(isAbnormal ? '#FF3B30' : '#007AFF')
        this.ctx.fillRect(x, y, barWidth, barHeight)
        
        // 显示数值
        this.ctx.setFillStyle('#333333')
        this.ctx.setFontSize(10)
        this.ctx.fillText(item.value.toString(), x + barWidth / 2 - 10, y - 5)
      })
    },
    
    drawAxes(padding, chartWidth, chartHeight) {
      this.ctx.beginPath()
      this.ctx.setStrokeStyle('#CCCCCC')
      this.ctx.setLineWidth(1)
      
      // Y轴
      this.ctx.moveTo(padding, padding)
      this.ctx.lineTo(padding, padding + chartHeight)
      
      // X轴
      this.ctx.moveTo(padding, padding + chartHeight)
      this.ctx.lineTo(padding + chartWidth, padding + chartHeight)
      
      this.ctx.stroke()
    },
    
    drawGrid(padding = 40, chartWidth, chartHeight) {
      if (!chartWidth) chartWidth = this.width - padding * 2
      if (!chartHeight) chartHeight = this.height - padding * 2
      
      this.ctx.beginPath()
      this.ctx.setStrokeStyle('#F0F0F0')
      this.ctx.setLineWidth(0.5)
      
      // 绘制水平网格线
      for (let i = 1; i < 5; i++) {
        const y = padding + (chartHeight / 5) * i
        this.ctx.moveTo(padding, y)
        this.ctx.lineTo(padding + chartWidth, y)
      }
      
      // 绘制垂直网格线
      const gridCount = Math.min(this.chartData.length, 10)
      for (let i = 1; i < gridCount; i++) {
        const x = padding + (chartWidth / gridCount) * i
        this.ctx.moveTo(x, padding)
        this.ctx.lineTo(x, padding + chartHeight)
      }
      
      this.ctx.stroke()
    },
    
    drawNormalRange(padding, chartWidth, chartHeight, minValue, valueRange) {
      const normalMinY = padding + chartHeight - ((this.normalRange.min - minValue) / valueRange) * chartHeight
      const normalMaxY = padding + chartHeight - ((this.normalRange.max - minValue) / valueRange) * chartHeight
      
      this.ctx.setFillStyle('rgba(0, 122, 255, 0.1)')
      this.ctx.fillRect(padding, normalMaxY, chartWidth, normalMinY - normalMaxY)
      
      // 绘制正常范围边界线
      this.ctx.beginPath()
      this.ctx.setStrokeStyle('rgba(0, 122, 255, 0.3)')
      this.ctx.setLineWidth(1)
      this.ctx.moveTo(padding, normalMinY)
      this.ctx.lineTo(padding + chartWidth, normalMinY)
      this.ctx.moveTo(padding, normalMaxY)
      this.ctx.lineTo(padding + chartWidth, normalMaxY)
      this.ctx.stroke()
    },
    
    drawLegend() {
      // 简单的图例实现
      const legendY = this.height - 20
      
      this.ctx.setFillStyle('#007AFF')
      this.ctx.fillRect(10, legendY, 10, 10)
      this.ctx.setFillStyle('#333333')
      this.ctx.setFontSize(12)
      this.ctx.fillText('正常值', 25, legendY + 8)
      
      this.ctx.setFillStyle('#FF3B30')
      this.ctx.fillRect(80, legendY, 10, 10)
      this.ctx.fillText('异常值', 95, legendY + 8)
    },
    
    isAbnormalValue(value) {
      if (this.normalRange.min === null || this.normalRange.max === null) {
        return false
      }
      return value < this.normalRange.min || value > this.normalRange.max
    },
    
    // 触摸事件处理 - 支持缩放和滑动
    handleTouchStart(e) {
      this.touchStartX = e.touches[0].clientX
      this.touchStartY = e.touches[0].clientY
    },
    
    handleTouchMove(e) {
      if (e.touches.length === 1) {
        // 单指滑动
        const deltaX = e.touches[0].clientX - this.touchStartX
        this.offsetX += deltaX
        this.touchStartX = e.touches[0].clientX
        this.drawChart()
      } else if (e.touches.length === 2) {
        // 双指缩放
        const touch1 = e.touches[0]
        const touch2 = e.touches[1]
        const distance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) + 
          Math.pow(touch2.clientY - touch1.clientY, 2)
        )
        
        if (this.lastDistance) {
          const scaleChange = distance / this.lastDistance
          this.scale *= scaleChange
          this.scale = Math.max(0.5, Math.min(3, this.scale)) // 限制缩放范围
          this.drawChart()
        }
        this.lastDistance = distance
      }
    },
    
    handleTouchEnd() {
      this.lastDistance = null
    }
  }
}
</script>

<style scoped>
.health-chart {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-canvas {
  width: 350px;
  height: 250px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}
</style>