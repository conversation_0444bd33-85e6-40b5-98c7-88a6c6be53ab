<template>
  <view class="virtual-list" :style="{ height: containerHeight + 'px' }">
    <!-- 滚动容器 -->
    <scroll-view
      class="scroll-container"
      :scroll-y="true"
      :scroll-top="scrollTop"
      :style="{ height: containerHeight + 'px' }"
      @scroll="handleScroll"
      @scrolltolower="handleScrollToLower"
    >
      <!-- 占位容器，用于撑开滚动高度 -->
      <view 
        class="placeholder" 
        :style="{ height: totalHeight + 'px', paddingTop: offsetY + 'px' }"
      >
        <!-- 可见项目列表 -->
        <view class="visible-items">
          <view
            v-for="(item, index) in visibleItems"
            :key="getItemKey(item, startIndex + index)"
            class="list-item"
            :style="{ height: itemHeight + 'px' }"
          >
            <slot :item="item" :index="startIndex + index"></slot>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 加载更多指示器 -->
    <view v-if="loading" class="loading-indicator">
      <uni-load-more :status="loadingStatus"></uni-load-more>
    </view>
  </view>
</template>

<script>
export default {
  name: 'VirtualList',
  props: {
    // 数据列表
    items: {
      type: Array,
      default: () => []
    },
    // 每项高度
    itemHeight: {
      type: Number,
      default: 80
    },
    // 容器高度
    containerHeight: {
      type: Number,
      default: 600
    },
    // 缓冲区大小（额外渲染的项目数）
    bufferSize: {
      type: Number,
      default: 5
    },
    // 是否正在加载
    loading: {
      type: Boolean,
      default: false
    },
    // 是否还有更多数据
    hasMore: {
      type: Boolean,
      default: true
    },
    // 获取项目唯一键的函数
    keyField: {
      type: String,
      default: 'id'
    }
  },
  
  data() {
    return {
      scrollTop: 0,
      startIndex: 0,
      endIndex: 0
    }
  },
  
  computed: {
    // 总高度
    totalHeight() {
      return this.items.length * this.itemHeight
    },
    
    // 可见区域可容纳的项目数
    visibleCount() {
      return Math.ceil(this.containerHeight / this.itemHeight)
    },
    
    // 实际渲染的项目数（包含缓冲区）
    renderCount() {
      return this.visibleCount + this.bufferSize * 2
    },
    
    // 可见项目列表
    visibleItems() {
      return this.items.slice(this.startIndex, this.endIndex + 1)
    },
    
    // 偏移量
    offsetY() {
      return this.startIndex * this.itemHeight
    },
    
    // 加载状态
    loadingStatus() {
      if (this.loading) return 'loading'
      if (!this.hasMore) return 'noMore'
      return 'more'
    }
  },
  
  watch: {
    items: {
      handler() {
        this.updateVisibleRange()
      },
      immediate: true
    }
  },
  
  methods: {
    // 处理滚动事件
    handleScroll(e) {
      const scrollTop = e.detail.scrollTop
      this.scrollTop = scrollTop
      this.updateVisibleRange()
    },
    
    // 更新可见范围
    updateVisibleRange() {
      if (this.items.length === 0) {
        this.startIndex = 0
        this.endIndex = 0
        return
      }
      
      // 计算开始索引
      const scrollTop = this.scrollTop
      let startIndex = Math.floor(scrollTop / this.itemHeight)
      startIndex = Math.max(0, startIndex - this.bufferSize)
      
      // 计算结束索引
      let endIndex = startIndex + this.renderCount - 1
      endIndex = Math.min(endIndex, this.items.length - 1)
      
      this.startIndex = startIndex
      this.endIndex = endIndex
    },
    
    // 处理滚动到底部
    handleScrollToLower() {
      if (!this.loading && this.hasMore) {
        this.$emit('loadMore')
      }
    },
    
    // 获取项目键值
    getItemKey(item, index) {
      if (typeof this.keyField === 'function') {
        return this.keyField(item, index)
      }
      return item[this.keyField] || index
    },
    
    // 滚动到指定位置
    scrollTo(index) {
      const scrollTop = index * this.itemHeight
      this.scrollTop = scrollTop
      this.updateVisibleRange()
    },
    
    // 滚动到顶部
    scrollToTop() {
      this.scrollTo(0)
    },
    
    // 刷新列表
    refresh() {
      this.scrollToTop()
      this.updateVisibleRange()
    }
  }
}
</script>

<style lang="scss" scoped>
.virtual-list {
  position: relative;
  overflow: hidden;
}

.scroll-container {
  width: 100%;
  height: 100%;
}

.placeholder {
  position: relative;
  width: 100%;
}

.visible-items {
  position: relative;
}

.list-item {
  width: 100%;
  box-sizing: border-box;
}

.loading-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  text-align: center;
  background-color: #f8f8f8;
}
</style>