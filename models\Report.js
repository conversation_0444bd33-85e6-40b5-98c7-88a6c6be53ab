/**
 * 报告数据模型
 * 包含检查项目、医院信息等完整结构
 */

const { BaseModel } = require('../core/base/BaseModel.js')
const { Constants } = require('../types/index.js')

class Report extends BaseModel {
  /**
   * 初始化报告属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    // 基本信息
    this.userId = data.userId || ''
    this.title = data.title || ''
    this.hospital = data.hospital || ''
    this.doctor = data.doctor || ''
    this.department = data.department || '' // 科室
    
    // 时间信息
    this.checkDate = data.checkDate ? new Date(data.checkDate) : new Date()
    this.reportDate = data.reportDate ? new Date(data.reportDate) : new Date()
    
    // 图片信息
    this.originalImage = data.originalImage || ''
    this.processedImages = data.processedImages || [] // 处理后的图片
    
    // 检查项目
    this.items = (data.items || []).map(item => new ReportItem(item))
    
    // 分类和标签
    this.category = data.category || Constants.REPORT_CATEGORIES.OTHER
    this.tags = data.tags || []
    
    // 备注和总结
    this.notes = data.notes || ''
    this.summary = data.summary || '' // 报告总结
    this.conclusion = data.conclusion || '' // 检查结论
    
    // 状态信息
    this.syncStatus = data.syncStatus || Constants.SYNC_STATUS.LOCAL
    this.isDeleted = data.isDeleted || false
    this.version = data.version || 1
    
    // OCR相关信息
    this.ocrResult = data.ocrResult || null // 原始OCR结果
    this.ocrConfidence = data.ocrConfidence || 0 // OCR置信度
    this.isManuallyEdited = data.isManuallyEdited || false // 是否手动编辑过
  }
  
  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      userId: {
        required: true,
        type: 'string',
        message: '用户ID是必填项'
      },
      title: {
        required: false,
        type: 'string',
        maxLength: 100,
        message: '报告标题不能超过100个字符'
      },
      hospital: {
        required: true,
        type: 'string',
        minLength: 1,
        maxLength: 100,
        message: '医院名称是必填项，且不能超过100个字符'
      },
      doctor: {
        required: false,
        type: 'string',
        maxLength: 50,
        message: '医生姓名不能超过50个字符'
      },
      checkDate: {
        required: true,
        validator: (value) => {
          if (!(value instanceof Date)) {
            return '检查日期格式不正确'
          }
          if (value > new Date()) {
            return '检查日期不能是未来日期'
          }
          return true
        }
      },
      reportDate: {
        required: true,
        validator: (value) => {
          if (!(value instanceof Date)) {
            return '报告日期格式不正确'
          }
          if (value > new Date()) {
            return '报告日期不能是未来日期'
          }
          return true
        }
      },
      category: {
        required: false,
        type: 'string',
        validator: (value) => {
          if (value && !Object.values(Constants.REPORT_CATEGORIES).includes(value)) {
            return '报告分类无效'
          }
          return true
        }
      },
      items: {
        required: false,
        validator: (value) => {
          if (!Array.isArray(value)) {
            return '检查项目必须是数组'
          }
          
          for (let i = 0; i < value.length; i++) {
            const item = value[i]
            if (!(item instanceof ReportItem)) {
              return `检查项目[${i}]类型不正确`
            }
            
            const itemValidation = item.validate()
            if (!itemValidation.isValid) {
              return `检查项目[${i}]验证失败: ${itemValidation.errors.map(e => e.message).join(', ')}`
            }
          }
          
          return true
        }
      }
    }
  }
  
  /**
   * 添加检查项目
   * @param {Object|ReportItem} itemData - 项目数据
   */
  addItem(itemData) {
    const item = itemData instanceof ReportItem ? itemData : new ReportItem(itemData)
    
    // 验证项目数据
    const validation = item.validate()
    if (!validation.isValid) {
      throw new Error(`检查项目验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    this.items.push(item)
    this.updatedAt = new Date()
  }
  
  /**
   * 移除检查项目
   * @param {String} itemId - 项目ID
   * @returns {Boolean} 是否成功移除
   */
  removeItem(itemId) {
    const index = this.items.findIndex(item => item.id === itemId)
    if (index > -1) {
      this.items.splice(index, 1)
      this.updatedAt = new Date()
      return true
    }
    return false
  }
  
  /**
   * 更新检查项目
   * @param {String} itemId - 项目ID
   * @param {Object} updateData - 更新数据
   * @returns {Boolean} 是否成功更新
   */
  updateItem(itemId, updateData) {
    const item = this.items.find(item => item.id === itemId)
    if (item) {
      item.update(updateData)
      this.updatedAt = new Date()
      return true
    }
    return false
  }
  
  /**
   * 根据名称查找检查项目
   * @param {String} name - 项目名称
   * @returns {ReportItem|null} 找到的项目或null
   */
  findItemByName(name) {
    return this.items.find(item => item.name === name) || null
  }
  
  /**
   * 根据分类获取检查项目
   * @param {String} category - 项目分类
   * @returns {Array} 匹配的项目列表
   */
  getItemsByCategory(category) {
    return this.items.filter(item => item.category === category)
  }
  
  /**
   * 获取异常检查项目
   * @returns {Array} 异常项目列表
   */
  getAbnormalItems() {
    return this.items.filter(item => item.isAbnormal)
  }
  
  /**
   * 检查是否有异常项目
   * @returns {Boolean} 是否有异常
   */
  hasAbnormalItems() {
    return this.items.some(item => item.isAbnormal)
  }
  
  /**
   * 获取异常项目数量
   * @returns {Number} 异常项目数量
   */
  getAbnormalItemCount() {
    return this.items.filter(item => item.isAbnormal).length
  }
  
  /**
   * 添加标签
   * @param {String} tag - 标签
   */
  addTag(tag) {
    if (tag && !this.tags.includes(tag)) {
      this.tags.push(tag)
      this.updatedAt = new Date()
    }
  }
  
  /**
   * 移除标签
   * @param {String} tag - 标签
   */
  removeTag(tag) {
    const index = this.tags.indexOf(tag)
    if (index > -1) {
      this.tags.splice(index, 1)
      this.updatedAt = new Date()
    }
  }
  
  /**
   * 设置同步状态
   * @param {String} status - 同步状态
   */
  setSyncStatus(status) {
    if (Object.values(Constants.SYNC_STATUS).includes(status)) {
      this.syncStatus = status
      this.updatedAt = new Date()
    }
  }
  
  /**
   * 标记为已删除
   */
  markAsDeleted() {
    this.isDeleted = true
    this.updatedAt = new Date()
  }
  
  /**
   * 恢复删除
   */
  restore() {
    this.isDeleted = false
    this.updatedAt = new Date()
  }
  
  /**
   * 增加版本号
   */
  incrementVersion() {
    this.version += 1
    this.updatedAt = new Date()
  }
  
  /**
   * 设置OCR结果
   * @param {Object} ocrResult - OCR结果
   * @param {Number} confidence - 置信度
   */
  setOCRResult(ocrResult, confidence = 0) {
    this.ocrResult = ocrResult
    this.ocrConfidence = confidence
    this.updatedAt = new Date()
  }
  
  /**
   * 标记为手动编辑
   */
  markAsManuallyEdited() {
    this.isManuallyEdited = true
    this.updatedAt = new Date()
  }
  
  /**
   * 生成报告摘要
   * @returns {String} 报告摘要
   */
  generateSummary() {
    const totalItems = this.items.length
    const abnormalItems = this.getAbnormalItemCount()
    const normalItems = totalItems - abnormalItems
    
    let summary = `本次检查共${totalItems}项，其中正常${normalItems}项`
    
    if (abnormalItems > 0) {
      summary += `，异常${abnormalItems}项`
    }
    
    if (this.conclusion) {
      summary += `。${this.conclusion}`
    }
    
    return summary
  }
  
  /**
   * 获取报告标题（如果没有设置则自动生成）
   * @returns {String} 报告标题
   */
  getTitle() {
    if (this.title) {
      return this.title
    }
    
    // 自动生成标题
    const dateStr = this.checkDate.toLocaleDateString('zh-CN')
    const categoryName = this.getCategoryDisplayName()
    
    return `${dateStr} ${this.hospital} ${categoryName}报告`
  }
  
  /**
   * 获取分类显示名称
   * @returns {String} 分类显示名称
   */
  getCategoryDisplayName() {
    const categoryNames = {
      [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE]: '血常规',
      [Constants.REPORT_CATEGORIES.BIOCHEMISTRY]: '生化',
      [Constants.REPORT_CATEGORIES.IMMUNOLOGY]: '免疫',
      [Constants.REPORT_CATEGORIES.URINE_ROUTINE]: '尿常规',
      [Constants.REPORT_CATEGORIES.IMAGING]: '影像',
      [Constants.REPORT_CATEGORIES.OTHER]: '其他'
    }
    
    return categoryNames[this.category] || '检查'
  }
  
  /**
   * 检查数据完整性
   * @returns {Object} 完整性检查结果
   */
  checkDataIntegrity() {
    const issues = []
    
    if (!this.hospital) {
      issues.push('缺少医院信息')
    }
    
    if (this.items.length === 0) {
      issues.push('没有检查项目')
    }
    
    if (!this.originalImage) {
      issues.push('缺少原始图片')
    }
    
    if (this.ocrConfidence < 0.5 && !this.isManuallyEdited) {
      issues.push('OCR识别置信度较低，建议手动核对')
    }
    
    return {
      isComplete: issues.length === 0,
      issues
    }
  }
  
  /**
   * 获取可序列化的属性列表
   * @returns {Array} 属性名列表
   */
  getSerializableProperties() {
    return [
      'id', 'userId', 'title', 'hospital', 'doctor', 'department',
      'checkDate', 'reportDate', 'originalImage', 'processedImages',
      'items', 'category', 'tags', 'notes', 'summary', 'conclusion',
      'syncStatus', 'isDeleted', 'version', 'ocrResult', 'ocrConfidence',
      'isManuallyEdited', 'createdAt', 'updatedAt'
    ]
  }
  
  /**
   * 转换为JSON对象
   * @returns {Object} JSON对象
   */
  toJSON() {
    const json = super.toJSON()
    
    // 转换检查项目为JSON
    json.items = this.items.map(item => item.toJSON())
    
    return json
  }
  
  /**
   * 从JSON创建报告实例
   * @param {Object} json - JSON对象
   * @returns {Report} 报告实例
   */
  static fromJSON(json) {
    return new Report(json)
  }
  
  /**
   * 创建新报告
   * @param {Object} reportData - 报告数据
   * @returns {Report} 报告实例
   */
  static create(reportData) {
    const report = new Report(reportData)
    
    // 验证必填字段
    if (!report.userId) {
      throw new Error('用户ID是必填项')
    }
    
    if (!report.hospital) {
      throw new Error('医院名称是必填项')
    }
    
    // 验证数据
    const validation = report.validate()
    if (!validation.isValid) {
      throw new Error(`报告数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    return report
  }
}

/**
 * 检查项目模型
 */
class ReportItem extends BaseModel {
  /**
   * 初始化项目属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    this.name = data.name || '' // 项目名称
    this.value = data.value || '' // 检查结果
    this.unit = data.unit || '' // 单位
    this.referenceRange = data.referenceRange || '' // 参考范围
    this.isAbnormal = data.isAbnormal || false // 是否异常
    this.category = data.category || Constants.REPORT_CATEGORIES.OTHER // 分类
    this.description = data.description || '' // 描述
    this.notes = data.notes || '' // 备注
    
    // 数值相关
    this.numericValue = data.numericValue || null // 数值
    this.minReference = data.minReference || null // 参考值下限
    this.maxReference = data.maxReference || null // 参考值上限
    
    // 状态信息
    this.confidence = data.confidence || 1.0 // 识别置信度
    this.isManuallyEdited = data.isManuallyEdited || false // 是否手动编辑
  }
  
  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      name: {
        required: true,
        type: 'string',
        minLength: 1,
        maxLength: 100,
        message: '项目名称是必填项，且不能超过100个字符'
      },
      value: {
        required: true,
        type: 'string',
        minLength: 1,
        message: '检查结果是必填项'
      },
      category: {
        required: false,
        type: 'string',
        validator: (value) => {
          if (value && !Object.values(Constants.REPORT_CATEGORIES).includes(value)) {
            return '项目分类无效'
          }
          return true
        }
      },
      numericValue: {
        required: false,
        type: 'number',
        validator: (value) => {
          if (value !== null && (isNaN(value) || !isFinite(value))) {
            return '数值格式不正确'
          }
          return true
        }
      }
    }
  }
  
  /**
   * 设置数值和参考范围
   * @param {Number} value - 数值
   * @param {Number} min - 参考值下限
   * @param {Number} max - 参考值上限
   */
  setNumericValue(value, min = null, max = null) {
    this.numericValue = value
    this.minReference = min
    this.maxReference = max
    
    // 自动判断是否异常
    if (min !== null && max !== null) {
      this.isAbnormal = value < min || value > max
    }
    
    this.updatedAt = new Date()
  }
  
  /**
   * 解析参考范围字符串
   * @param {String} rangeStr - 参考范围字符串
   * @returns {Object} 解析结果
   */
  parseReferenceRange(rangeStr) {
    if (!rangeStr) return { min: null, max: null }
    
    // 匹配各种格式的参考范围
    const patterns = [
      /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/, // 3.5-5.5
      /(\d+\.?\d*)\s*-\s*(\d+\.?\d*)/, // 3.5 - 5.5
      /<\s*(\d+\.?\d*)/, // <5.5
      />\s*(\d+\.?\d*)/, // >3.5
      /≤\s*(\d+\.?\d*)/, // ≤5.5
      /≥\s*(\d+\.?\d*)/ // ≥3.5
    ]
    
    for (const pattern of patterns) {
      const match = rangeStr.match(pattern)
      if (match) {
        if (match.length === 3) {
          // 范围格式
          return {
            min: parseFloat(match[1]),
            max: parseFloat(match[2])
          }
        } else if (match.length === 2) {
          // 单边界格式
          const value = parseFloat(match[1])
          if (rangeStr.includes('<') || rangeStr.includes('≤')) {
            return { min: null, max: value }
          } else {
            return { min: value, max: null }
          }
        }
      }
    }
    
    return { min: null, max: null }
  }
  
  /**
   * 自动设置参考范围
   * @param {String} rangeStr - 参考范围字符串
   */
  setReferenceRange(rangeStr) {
    this.referenceRange = rangeStr
    const { min, max } = this.parseReferenceRange(rangeStr)
    this.minReference = min
    this.maxReference = max
    
    // 如果有数值，重新判断是否异常
    if (this.numericValue !== null && (min !== null || max !== null)) {
      this.isAbnormal = (min !== null && this.numericValue < min) || 
                       (max !== null && this.numericValue > max)
    }
    
    this.updatedAt = new Date()
  }
  
  /**
   * 标记为异常
   * @param {Boolean} isAbnormal - 是否异常
   */
  setAbnormal(isAbnormal) {
    this.isAbnormal = isAbnormal
    this.updatedAt = new Date()
  }
  
  /**
   * 标记为手动编辑
   */
  markAsManuallyEdited() {
    this.isManuallyEdited = true
    this.updatedAt = new Date()
  }
  
  /**
   * 获取异常程度
   * @returns {String} 异常程度 (normal|mild|moderate|severe)
   */
  getAbnormalSeverity() {
    if (!this.isAbnormal || this.numericValue === null) {
      return 'normal'
    }
    
    const { minReference, maxReference, numericValue } = this
    
    if (minReference !== null && numericValue < minReference) {
      const deviation = (minReference - numericValue) / minReference
      if (deviation > 0.5) return 'severe'
      if (deviation > 0.2) return 'moderate'
      return 'mild'
    }
    
    if (maxReference !== null && numericValue > maxReference) {
      const deviation = (numericValue - maxReference) / maxReference
      if (deviation > 0.5) return 'severe'
      if (deviation > 0.2) return 'moderate'
      return 'mild'
    }
    
    return 'mild'
  }
  
  /**
   * 获取显示值
   * @returns {String} 显示值
   */
  getDisplayValue() {
    let displayValue = this.value
    
    if (this.unit) {
      displayValue += ` ${this.unit}`
    }
    
    return displayValue
  }
  
  /**
   * 获取完整显示信息
   * @returns {String} 完整显示信息
   */
  getFullDisplayText() {
    let text = `${this.name}: ${this.getDisplayValue()}`
    
    if (this.referenceRange) {
      text += ` (参考值: ${this.referenceRange})`
    }
    
    if (this.isAbnormal) {
      text += ' [异常]'
    }
    
    return text
  }
  
  /**
   * 从JSON创建项目实例
   * @param {Object} json - JSON对象
   * @returns {ReportItem} 项目实例
   */
  static fromJSON(json) {
    return new ReportItem(json)
  }
  
  /**
   * 创建新项目
   * @param {Object} itemData - 项目数据
   * @returns {ReportItem} 项目实例
   */
  static create(itemData) {
    const item = new ReportItem(itemData)
    
    // 验证必填字段
    if (!item.name) {
      throw new Error('项目名称是必填项')
    }
    
    if (!item.value) {
      throw new Error('检查结果是必填项')
    }
    
    // 验证数据
    const validation = item.validate()
    if (!validation.isValid) {
      throw new Error(`项目数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    return item
  }
}

module.exports = { Report, ReportItem }