/**
 * 用户数据访问层
 * 实现用户相关的数据库操作
 */

const { BaseRepository } = require('../base/BaseRepository.js')
const { IUserRepository } = require('../interfaces/IRepository.js')
const { User } = require('../../models/User.js')
const { Constants } = require('../../types/index.js')
const { logger } = require('../logger/Logger.js')

class UserRepository extends BaseRepository {
  constructor(storageManager) {
    super(storageManager, User)
  }
  
  /**
   * 获取表名
   * @returns {String} 表名
   */
  getTableName() {
    return 'users'
  }
  
  /**
   * 根据手机号查找用户
   * @param {String} phone - 手机号
   * @returns {Promise<User|null>} 用户信息或null
   */
  async findByPhone(phone) {
    try {
      logger.debug('根据手机号查找用户', { phone })
      
      const users = await this.findBy({ phone })
      return users.length > 0 ? users[0] : null
    } catch (error) {
      throw this.handleError(error, 'findByPhone')
    }
  }
  
  /**
   * 验证用户凭据
   * @param {String} phone - 手机号
   * @param {String} password - 密码
   * @returns {Promise<User|null>} 用户信息或null
   */
  async validateCredentials(phone, password) {
    try {
      logger.debug('验证用户凭据', { phone })
      
      const user = await this.findByPhone(phone)
      if (!user) {
        return null
      }
      
      // 验证密码
      const isValid = await user.validatePassword(password)
      return isValid ? user : null
    } catch (error) {
      throw this.handleError(error, 'validateCredentials')
    }
  }
  
  /**
   * 更新用户设置
   * @param {String} userId - 用户ID
   * @param {Object} settings - 设置信息
   * @returns {Promise<User>} 更新后的用户信息
   */
  async updateSettings(userId, settings) {
    try {
      logger.debug('更新用户设置', { userId, settings })
      
      const user = await this.findById(userId)
      if (!user) {
        throw new Error(`用户不存在: ${userId}`)
      }
      
      // 合并设置
      const updatedSettings = {
        ...user.settings,
        ...settings
      }
      
      return await this.update(userId, { settings: updatedSettings })
    } catch (error) {
      throw this.handleError(error, 'updateSettings')
    }
  }
  
  /**
   * 更新用户头像
   * @param {String} userId - 用户ID
   * @param {String} avatarUrl - 头像URL
   * @returns {Promise<User>} 更新后的用户信息
   */
  async updateAvatar(userId, avatarUrl) {
    try {
      logger.debug('更新用户头像', { userId, avatarUrl })
      
      return await this.update(userId, { avatar: avatarUrl })
    } catch (error) {
      throw this.handleError(error, 'updateAvatar')
    }
  }
  
  /**
   * 更新用户密码
   * @param {String} userId - 用户ID
   * @param {String} newPassword - 新密码
   * @returns {Promise<User>} 更新后的用户信息
   */
  async updatePassword(userId, newPassword) {
    try {
      logger.debug('更新用户密码', { userId })
      
      const user = await this.findById(userId)
      if (!user) {
        throw new Error(`用户不存在: ${userId}`)
      }
      
      // 加密新密码
      const hashedPassword = await user.hashPassword(newPassword)
      
      return await this.update(userId, { password: hashedPassword })
    } catch (error) {
      throw this.handleError(error, 'updatePassword')
    }
  }
  
  /**
   * 更新用户基本信息
   * @param {String} userId - 用户ID
   * @param {Object} profileData - 基本信息
   * @returns {Promise<User>} 更新后的用户信息
   */
  async updateProfile(userId, profileData) {
    try {
      logger.debug('更新用户基本信息', { userId, profileData })
      
      const allowedFields = ['nickname', 'gender', 'birthday', 'height', 'weight']
      const updateData = {}
      
      // 只允许更新指定字段
      for (const field of allowedFields) {
        if (profileData.hasOwnProperty(field)) {
          updateData[field] = profileData[field]
        }
      }
      
      if (Object.keys(updateData).length === 0) {
        throw new Error('没有有效的更新字段')
      }
      
      return await this.update(userId, updateData)
    } catch (error) {
      throw this.handleError(error, 'updateProfile')
    }
  }
  
  /**
   * 检查手机号是否已存在
   * @param {String} phone - 手机号
   * @param {String} excludeUserId - 排除的用户ID（用于更新时检查）
   * @returns {Promise<Boolean>} 是否已存在
   */
  async isPhoneExists(phone, excludeUserId = null) {
    try {
      logger.debug('检查手机号是否已存在', { phone, excludeUserId })
      
      const conditions = { phone }
      const users = await this.findBy(conditions)
      
      if (excludeUserId) {
        return users.some(user => user.id !== excludeUserId)
      }
      
      return users.length > 0
    } catch (error) {
      throw this.handleError(error, 'isPhoneExists')
    }
  }
  
  /**
   * 获取用户统计信息
   * @param {String} userId - 用户ID
   * @returns {Promise<Object>} 统计信息
   */
  async getUserStats(userId) {
    try {
      logger.debug('获取用户统计信息', { userId })
      
      const user = await this.findById(userId)
      if (!user) {
        throw new Error(`用户不存在: ${userId}`)
      }
      
      // 这里可以添加更多统计信息的计算
      // 比如报告数量、最后登录时间等
      return {
        userId: user.id,
        registrationDate: user.createdAt,
        lastUpdated: user.updatedAt,
        profileCompleteness: this.calculateProfileCompleteness(user)
      }
    } catch (error) {
      throw this.handleError(error, 'getUserStats')
    }
  }
  
  /**
   * 计算用户资料完整度
   * @param {User} user - 用户对象
   * @returns {Number} 完整度百分比
   */
  calculateProfileCompleteness(user) {
    const requiredFields = ['nickname', 'gender', 'birthday', 'height', 'weight']
    const completedFields = requiredFields.filter(field => 
      user[field] !== null && user[field] !== undefined && user[field] !== ''
    )
    
    return Math.round((completedFields.length / requiredFields.length) * 100)
  }
  
  /**
   * 软删除用户（标记为已删除而不是物理删除）
   * @param {String} userId - 用户ID
   * @returns {Promise<Boolean>} 删除是否成功
   */
  async softDelete(userId) {
    try {
      logger.debug('软删除用户', { userId })
      
      await this.update(userId, {
        isDeleted: true,
        deletedAt: new Date()
      })
      
      return true
    } catch (error) {
      throw this.handleError(error, 'softDelete')
    }
  }
  
  /**
   * 恢复已删除的用户
   * @param {String} userId - 用户ID
   * @returns {Promise<User>} 恢复后的用户信息
   */
  async restore(userId) {
    try {
      logger.debug('恢复已删除的用户', { userId })
      
      return await this.update(userId, {
        isDeleted: false,
        deletedAt: null
      })
    } catch (error) {
      throw this.handleError(error, 'restore')
    }
  }
  
  /**
   * 查找活跃用户（未删除的用户）
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 活跃用户列表
   */
  async findActiveUsers(options = {}) {
    try {
      logger.debug('查找活跃用户', { options })
      
      const conditions = {
        $or: [
          { isDeleted: false },
          { isDeleted: { $exists: false } }
        ]
      }
      
      return await this.findBy(conditions, options)
    } catch (error) {
      throw this.handleError(error, 'findActiveUsers')
    }
  }
  
  /**
   * 清理已删除用户的数据
   * @param {Number} daysToKeep - 保留天数
   * @returns {Promise<Number>} 清理的用户数量
   */
  async cleanupDeletedUsers(daysToKeep = 30) {
    try {
      logger.debug('清理已删除用户的数据', { daysToKeep })
      
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
      
      const conditions = {
        isDeleted: true,
        deletedAt: { $lt: cutoffDate }
      }
      
      const deletedUsers = await this.findBy(conditions)
      let cleanedCount = 0
      
      for (const user of deletedUsers) {
        await this.delete(user.id) // 物理删除
        cleanedCount++
      }
      
      logger.info('清理已删除用户完成', { cleanedCount })
      return cleanedCount
    } catch (error) {
      throw this.handleError(error, 'cleanupDeletedUsers')
    }
  }
}

module.exports = { UserRepository }