// 指标对比页面测试
describe('CompareAnalysis 页面测试', () => {
  // Mock uni-app API
  global.uni = {
    navigateBack: jest.fn(),
    showToast: jest.fn()
  }

  // 模拟页面核心方法
  const compareAnalysisMethods = {
    // 模拟数据
    availableIndicators: [
      { key: 'blood_pressure_systolic', name: '收缩压', unit: 'mmHg', normalRange: { min: 90, max: 140 } },
      { key: 'blood_pressure_diastolic', name: '舒张压', unit: 'mmHg', normalRange: { min: 60, max: 90 } },
      { key: 'heart_rate', name: '心率', unit: 'bpm', normalRange: { min: 60, max: 100 } },
      { key: 'blood_glucose', name: '血糖', unit: 'mmol/L', normalRange: { min: 3.9, max: 6.1 } }
    ],

    healthReports: [
      {
        id: 1,
        date: '2024-01-01',
        indicators: {
          blood_pressure_systolic: 120,
          blood_pressure_diastolic: 80,
          heart_rate: 72,
          blood_glucose: 5.2
        }
      },
      {
        id: 2,
        date: '2024-02-01',
        indicators: {
          blood_pressure_systolic: 135,
          blood_pressure_diastolic: 85,
          heart_rate: 78,
          blood_glucose: 5.8
        }
      },
      {
        id: 3,
        date: '2024-03-01',
        indicators: {
          blood_pressure_systolic: 145,
          blood_pressure_diastolic: 92,
          heart_rate: 82,
          blood_glucose: 6.5
        }
      }
    ],

    // 时间对比数据更新
    updateTimeCompareData(selectedIndicator, selectedTimePoints) {
      const indicatorKey = selectedIndicator
      
      return selectedTimePoints.map(point => {
        const report = this.healthReports.find(r => r.date === point.date)
        return {
          date: this.formatDisplayDate(point.date),
          value: report ? (report.indicators[indicatorKey] || 0) : 0
        }
      })
    },

    // 指标对比数据更新
    updateIndicatorCompareData(compareDate, selectedIndicators) {
      const report = this.healthReports.find(r => r.date === compareDate)
      
      if (!report) return []
      
      return selectedIndicators.map(indicatorKey => {
        const indicator = this.availableIndicators.find(ind => ind.key === indicatorKey)
        return {
          date: indicator.name,
          value: report.indicators[indicatorKey] || 0
        }
      })
    },

    // 生成时间分析
    generateTimeAnalysis(timeCompareData, currentIndicator) {
      if (timeCompareData.length < 2) return []

      const firstValue = timeCompareData[0].value
      const lastValue = timeCompareData[timeCompareData.length - 1].value
      const change = lastValue - firstValue
      const changePercent = ((change / firstValue) * 100).toFixed(1)
      
      let trend = 'stable'
      let description = ''
      let suggestion = ''
      
      if (Math.abs(change) > firstValue * 0.1) { // 变化超过10%
        trend = change > 0 ? 'up' : 'down'
        description = `${currentIndicator.name}从${firstValue}${currentIndicator.unit}变化到${lastValue}${currentIndicator.unit}，变化幅度${changePercent}%`
        
        if (this.isAbnormalValueForIndicator(lastValue, currentIndicator)) {
          suggestion = '当前数值异常，建议咨询医生'
        } else if (Math.abs(parseFloat(changePercent)) > 20) {
          suggestion = '变化幅度较大，建议持续关注'
        }
      } else {
        description = `${currentIndicator.name}保持相对稳定，变化幅度${changePercent}%`
      }
      
      return [{
        title: '趋势变化',
        trend,
        description,
        suggestion
      }]
    },

    // 生成指标分析
    generateIndicatorAnalysis(indicatorCompareData) {
      const abnormalCount = indicatorCompareData.filter(item => {
        const indicator = this.availableIndicators.find(ind => ind.name === item.date)
        return indicator && this.isAbnormalValueForIndicator(item.value, indicator)
      }).length
      
      let description = ''
      let suggestion = ''
      let trend = 'stable'
      
      if (abnormalCount === 0) {
        description = '所有选中指标均在正常范围内'
        trend = 'stable'
      } else {
        description = `${abnormalCount}项指标超出正常范围`
        trend = 'up'
        suggestion = '建议关注异常指标，必要时咨询医生'
      }
      
      return [{
        title: '指标状态',
        trend,
        description,
        suggestion
      }]
    },

    // 判断异常值
    isAbnormalValueForIndicator(value, indicator) {
      const { min, max } = indicator.normalRange
      if (min === null || max === null) return false
      return value < min || value > max
    },

    // 格式化显示日期
    formatDisplayDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  }

  describe('数据更新功能', () => {
    it('应该正确更新时间对比数据', () => {
      const selectedTimePoints = [
        { date: '2024-01-01' },
        { date: '2024-02-01' }
      ]
      
      const timeCompareData = compareAnalysisMethods.updateTimeCompareData(
        'blood_pressure_systolic', 
        selectedTimePoints
      )
      
      expect(timeCompareData.length).toBe(2)
      expect(timeCompareData[0].value).toBe(120)
      expect(timeCompareData[1].value).toBe(135)
    })

    it('应该正确更新指标对比数据', () => {
      const selectedIndicators = ['blood_pressure_systolic', 'heart_rate']
      
      const indicatorCompareData = compareAnalysisMethods.updateIndicatorCompareData(
        '2024-01-01',
        selectedIndicators
      )
      
      expect(indicatorCompareData.length).toBe(2)
      expect(indicatorCompareData[0].date).toBe('收缩压')
      expect(indicatorCompareData[0].value).toBe(120)
      expect(indicatorCompareData[1].date).toBe('心率')
      expect(indicatorCompareData[1].value).toBe(72)
    })

    it('应该处理不存在的日期', () => {
      const selectedIndicators = ['blood_pressure_systolic']
      
      const indicatorCompareData = compareAnalysisMethods.updateIndicatorCompareData(
        '2024-12-01', // 不存在的日期
        selectedIndicators
      )
      
      expect(indicatorCompareData.length).toBe(0)
    })
  })

  describe('分析生成功能', () => {
    it('应该正确生成时间分析 - 上升趋势', () => {
      const timeCompareData = [
        { date: '1月1日', value: 120 },
        { date: '3月1日', value: 145 }
      ]
      
      const currentIndicator = compareAnalysisMethods.availableIndicators[0] // 收缩压
      
      const analysis = compareAnalysisMethods.generateTimeAnalysis(timeCompareData, currentIndicator)
      
      expect(analysis.length).toBe(1)
      expect(analysis[0].trend).toBe('up')
      expect(analysis[0].description).toContain('收缩压从120mmHg变化到145mmHg')
      expect(analysis[0].suggestion).toContain('当前数值异常')
    })

    it('应该正确生成时间分析 - 稳定趋势', () => {
      const timeCompareData = [
        { date: '1月1日', value: 120 },
        { date: '2月1日', value: 125 }
      ]
      
      const currentIndicator = compareAnalysisMethods.availableIndicators[0] // 收缩压
      
      const analysis = compareAnalysisMethods.generateTimeAnalysis(timeCompareData, currentIndicator)
      
      expect(analysis.length).toBe(1)
      expect(analysis[0].trend).toBe('stable')
      expect(analysis[0].description).toContain('保持相对稳定')
    })

    it('应该正确生成指标分析 - 有异常', () => {
      const indicatorCompareData = [
        { date: '收缩压', value: 150 }, // 异常高值
        { date: '心率', value: 72 }     // 正常值
      ]
      
      const analysis = compareAnalysisMethods.generateIndicatorAnalysis(indicatorCompareData)
      
      expect(analysis.length).toBe(1)
      expect(analysis[0].trend).toBe('up')
      expect(analysis[0].description).toContain('1项指标超出正常范围')
      expect(analysis[0].suggestion).toContain('建议关注异常指标')
    })

    it('应该正确生成指标分析 - 全部正常', () => {
      const indicatorCompareData = [
        { date: '收缩压', value: 120 },
        { date: '心率', value: 72 }
      ]
      
      const analysis = compareAnalysisMethods.generateIndicatorAnalysis(indicatorCompareData)
      
      expect(analysis.length).toBe(1)
      expect(analysis[0].trend).toBe('stable')
      expect(analysis[0].description).toBe('所有选中指标均在正常范围内')
    })
  })

  describe('异常值判断', () => {
    it('应该正确判断收缩压异常值', () => {
      const indicator = compareAnalysisMethods.availableIndicators[0] // 收缩压
      
      expect(compareAnalysisMethods.isAbnormalValueForIndicator(80, indicator)).toBe(true)  // 偏低
      expect(compareAnalysisMethods.isAbnormalValueForIndicator(150, indicator)).toBe(true) // 偏高
      expect(compareAnalysisMethods.isAbnormalValueForIndicator(120, indicator)).toBe(false) // 正常
    })

    it('应该正确判断心率异常值', () => {
      const indicator = compareAnalysisMethods.availableIndicators[2] // 心率
      
      expect(compareAnalysisMethods.isAbnormalValueForIndicator(50, indicator)).toBe(true)  // 偏低
      expect(compareAnalysisMethods.isAbnormalValueForIndicator(110, indicator)).toBe(true) // 偏高
      expect(compareAnalysisMethods.isAbnormalValueForIndicator(75, indicator)).toBe(false) // 正常
    })
  })

  describe('日期格式化', () => {
    it('应该正确格式化日期', () => {
      expect(compareAnalysisMethods.formatDisplayDate('2024-03-15')).toBe('3月15日')
      expect(compareAnalysisMethods.formatDisplayDate('2024-12-01')).toBe('12月1日')
    })
  })

  describe('边界情况处理', () => {
    it('应该处理空的时间对比数据', () => {
      const analysis = compareAnalysisMethods.generateTimeAnalysis([], {})
      expect(analysis.length).toBe(0)
    })

    it('应该处理单个数据点的时间对比', () => {
      const timeCompareData = [{ date: '1月1日', value: 120 }]
      const analysis = compareAnalysisMethods.generateTimeAnalysis(timeCompareData, {})
      expect(analysis.length).toBe(0)
    })

    it('应该处理空的指标对比数据', () => {
      const analysis = compareAnalysisMethods.generateIndicatorAnalysis([])
      expect(analysis.length).toBe(1)
      expect(analysis[0].description).toBe('所有选中指标均在正常范围内')
    })
  })

  describe('数据查找功能', () => {
    it('应该能找到指定日期的报告', () => {
      const report = compareAnalysisMethods.healthReports.find(r => r.date === '2024-01-01')
      expect(report).toBeDefined()
      expect(report.indicators.blood_pressure_systolic).toBe(120)
    })

    it('应该能找到指定指标', () => {
      const indicator = compareAnalysisMethods.availableIndicators.find(ind => ind.key === 'heart_rate')
      expect(indicator).toBeDefined()
      expect(indicator.name).toBe('心率')
      expect(indicator.unit).toBe('bpm')
    })
  })
})