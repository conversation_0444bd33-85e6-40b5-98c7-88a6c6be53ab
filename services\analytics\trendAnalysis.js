/**
 * 健康指标趋势分析服务
 * 实现需求 4.1: 生成各项健康指标随时间变化的折线图
 * 实现需求 4.2: 显示特定检查项目在不同时间点的数值对比
 */

import { formatDate, parseDate } from '@/utils/dateUtils'

/**
 * 趋势分析服务类
 */
class TrendAnalysisService {
  /**
   * 分析单个指标的趋势
   * @param {Array} dataPoints - 数据点数组 [{date, value, isAbnormal}]
   * @param {Object} options - 分析选项
   * @returns {Object} 趋势分析结果
   */
  analyzeTrend(dataPoints, options = {}) {
    if (!dataPoints || dataPoints.length < 2) {
      return {
        trend: 'insufficient_data',
        direction: null,
        slope: 0,
        correlation: 0,
        changeRate: 0,
        summary: '数据点不足，无法分析趋势'
      }
    }

    // 按日期排序
    const sortedData = this.sortDataByDate(dataPoints)
    
    // 计算线性回归
    const regression = this.calculateLinearRegression(sortedData)
    
    // 计算变化率
    const changeRate = this.calculateChangeRate(sortedData)
    
    // 确定趋势方向
    const direction = this.determineTrendDirection(regression.slope, changeRate)
    
    // 生成趋势摘要
    const summary = this.generateTrendSummary(direction, changeRate, sortedData)

    return {
      trend: direction,
      direction: direction,
      slope: regression.slope,
      correlation: regression.correlation,
      changeRate: changeRate,
      summary: summary,
      dataPoints: sortedData,
      regression: regression
    }
  }

  /**
   * 分析多个指标的趋势对比
   * @param {Object} indicatorsData - 多个指标的数据 {indicator1: dataPoints[], indicator2: dataPoints[]}
   * @param {Object} options - 分析选项
   * @returns {Object} 多指标趋势分析结果
   */
  analyzeMultipleIndicators(indicatorsData, options = {}) {
    const results = {}
    const correlations = {}

    // 分析每个指标的趋势
    Object.keys(indicatorsData).forEach(indicator => {
      results[indicator] = this.analyzeTrend(indicatorsData[indicator], options)
    })

    // 计算指标间的相关性
    const indicators = Object.keys(indicatorsData)
    for (let i = 0; i < indicators.length; i++) {
      for (let j = i + 1; j < indicators.length; j++) {
        const indicator1 = indicators[i]
        const indicator2 = indicators[j]
        const correlation = this.calculateIndicatorCorrelation(
          indicatorsData[indicator1],
          indicatorsData[indicator2]
        )
        correlations[`${indicator1}_${indicator2}`] = correlation
      }
    }

    return {
      indicators: results,
      correlations: correlations,
      summary: this.generateMultiIndicatorSummary(results, correlations)
    }
  }

  /**
   * 按日期排序数据点
   * @param {Array} dataPoints - 数据点数组
   * @returns {Array} 排序后的数据点
   */
  sortDataByDate(dataPoints) {
    return dataPoints
      .map(point => ({
        ...point,
        timestamp: new Date(point.date).getTime()
      }))
      .sort((a, b) => a.timestamp - b.timestamp)
  }

  /**
   * 计算线性回归
   * @param {Array} dataPoints - 排序后的数据点
   * @returns {Object} 回归分析结果
   */
  calculateLinearRegression(dataPoints) {
    const n = dataPoints.length
    if (n < 2) return { slope: 0, intercept: 0, correlation: 0 }

    // 将时间戳转换为相对天数
    const firstTimestamp = dataPoints[0].timestamp
    const points = dataPoints.map(point => ({
      x: (point.timestamp - firstTimestamp) / (1000 * 60 * 60 * 24), // 转换为天数
      y: parseFloat(point.value) || 0
    }))

    // 计算均值
    const meanX = points.reduce((sum, p) => sum + p.x, 0) / n
    const meanY = points.reduce((sum, p) => sum + p.y, 0) / n

    // 计算斜率和截距
    let numerator = 0
    let denominatorX = 0
    let denominatorY = 0

    points.forEach(point => {
      const deltaX = point.x - meanX
      const deltaY = point.y - meanY
      numerator += deltaX * deltaY
      denominatorX += deltaX * deltaX
      denominatorY += deltaY * deltaY
    })

    const slope = denominatorX === 0 ? 0 : numerator / denominatorX
    const intercept = meanY - slope * meanX

    // 计算相关系数
    const correlation = (denominatorX === 0 || denominatorY === 0) 
      ? 0 
      : numerator / Math.sqrt(denominatorX * denominatorY)

    return {
      slope,
      intercept,
      correlation: Math.abs(correlation),
      points
    }
  }

  /**
   * 计算变化率
   * @param {Array} dataPoints - 排序后的数据点
   * @returns {number} 变化率百分比
   */
  calculateChangeRate(dataPoints) {
    if (dataPoints.length < 2) return 0

    const firstValue = parseFloat(dataPoints[0].value) || 0
    const lastValue = parseFloat(dataPoints[dataPoints.length - 1].value) || 0

    if (firstValue === 0) return 0

    return ((lastValue - firstValue) / firstValue) * 100
  }

  /**
   * 确定趋势方向
   * @param {number} slope - 回归斜率
   * @param {number} changeRate - 变化率
   * @returns {string} 趋势方向
   */
  determineTrendDirection(slope, changeRate) {
    const threshold = 0.05 // 5%的变化阈值

    if (Math.abs(changeRate) < threshold) {
      return 'stable'
    } else if (slope > 0 && changeRate > threshold) {
      return 'increasing'
    } else if (slope < 0 && changeRate < -threshold) {
      return 'decreasing'
    } else {
      return 'fluctuating'
    }
  }

  /**
   * 生成趋势摘要
   * @param {string} direction - 趋势方向
   * @param {number} changeRate - 变化率
   * @param {Array} dataPoints - 数据点
   * @returns {string} 趋势摘要
   */
  generateTrendSummary(direction, changeRate, dataPoints) {
    const timeSpan = this.calculateTimeSpan(dataPoints)
    const changeRateAbs = Math.abs(changeRate).toFixed(1)

    switch (direction) {
      case 'increasing':
        return `在过去${timeSpan}内，该指标呈上升趋势，增长了${changeRateAbs}%`
      case 'decreasing':
        return `在过去${timeSpan}内，该指标呈下降趋势，下降了${changeRateAbs}%`
      case 'stable':
        return `在过去${timeSpan}内，该指标保持相对稳定，变化幅度为${changeRateAbs}%`
      case 'fluctuating':
        return `在过去${timeSpan}内，该指标波动较大，总体变化为${changeRate > 0 ? '+' : ''}${changeRate.toFixed(1)}%`
      default:
        return '趋势分析结果不明确'
    }
  }

  /**
   * 计算时间跨度
   * @param {Array} dataPoints - 数据点
   * @returns {string} 时间跨度描述
   */
  calculateTimeSpan(dataPoints) {
    if (dataPoints.length < 2) return '0天'

    const firstDate = new Date(dataPoints[0].date)
    const lastDate = new Date(dataPoints[dataPoints.length - 1].date)
    const diffTime = Math.abs(lastDate - firstDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 30) {
      return `${diffDays}天`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return `${months}个月`
    } else {
      const years = Math.floor(diffDays / 365)
      const remainingMonths = Math.floor((diffDays % 365) / 30)
      return remainingMonths > 0 ? `${years}年${remainingMonths}个月` : `${years}年`
    }
  }

  /**
   * 计算两个指标间的相关性
   * @param {Array} data1 - 指标1的数据点
   * @param {Array} data2 - 指标2的数据点
   * @returns {number} 相关系数
   */
  calculateIndicatorCorrelation(data1, data2) {
    // 找到共同的时间点
    const commonDates = this.findCommonDates(data1, data2)
    if (commonDates.length < 2) return 0

    const values1 = commonDates.map(date => {
      const point = data1.find(p => p.date === date)
      return parseFloat(point.value) || 0
    })

    const values2 = commonDates.map(date => {
      const point = data2.find(p => p.date === date)
      return parseFloat(point.value) || 0
    })

    return this.calculatePearsonCorrelation(values1, values2)
  }

  /**
   * 找到两个数据集的共同日期
   * @param {Array} data1 - 数据集1
   * @param {Array} data2 - 数据集2
   * @returns {Array} 共同日期数组
   */
  findCommonDates(data1, data2) {
    const dates1 = new Set(data1.map(p => p.date))
    const dates2 = new Set(data2.map(p => p.date))
    return [...dates1].filter(date => dates2.has(date))
  }

  /**
   * 计算皮尔逊相关系数
   * @param {Array} x - 数值数组1
   * @param {Array} y - 数值数组2
   * @returns {number} 相关系数
   */
  calculatePearsonCorrelation(x, y) {
    const n = x.length
    if (n !== y.length || n < 2) return 0

    const meanX = x.reduce((sum, val) => sum + val, 0) / n
    const meanY = y.reduce((sum, val) => sum + val, 0) / n

    let numerator = 0
    let denominatorX = 0
    let denominatorY = 0

    for (let i = 0; i < n; i++) {
      const deltaX = x[i] - meanX
      const deltaY = y[i] - meanY
      numerator += deltaX * deltaY
      denominatorX += deltaX * deltaX
      denominatorY += deltaY * deltaY
    }

    if (denominatorX === 0 || denominatorY === 0) return 0

    return numerator / Math.sqrt(denominatorX * denominatorY)
  }

  /**
   * 生成多指标分析摘要
   * @param {Object} results - 各指标分析结果
   * @param {Object} correlations - 指标间相关性
   * @returns {string} 多指标分析摘要
   */
  generateMultiIndicatorSummary(results, correlations) {
    const indicators = Object.keys(results)
    const trendCounts = {
      increasing: 0,
      decreasing: 0,
      stable: 0,
      fluctuating: 0
    }

    // 统计各种趋势的数量
    indicators.forEach(indicator => {
      const trend = results[indicator].trend
      if (trendCounts.hasOwnProperty(trend)) {
        trendCounts[trend]++
      }
    })

    // 找出强相关的指标对
    const strongCorrelations = Object.entries(correlations)
      .filter(([key, value]) => Math.abs(value) > 0.7)
      .map(([key, value]) => ({ pair: key, correlation: value }))

    let summary = `分析了${indicators.length}个健康指标的趋势变化。`
    
    if (trendCounts.increasing > 0) {
      summary += `其中${trendCounts.increasing}个指标呈上升趋势，`
    }
    if (trendCounts.decreasing > 0) {
      summary += `${trendCounts.decreasing}个指标呈下降趋势，`
    }
    if (trendCounts.stable > 0) {
      summary += `${trendCounts.stable}个指标保持稳定。`
    }

    if (strongCorrelations.length > 0) {
      summary += `发现${strongCorrelations.length}对指标存在强相关性，建议重点关注。`
    }

    return summary
  }
}

// 创建单例实例
const trendAnalysisService = new TrendAnalysisService()

export default trendAnalysisService