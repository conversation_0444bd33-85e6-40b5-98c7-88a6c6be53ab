<template>
	<view class="page-container">
		<view class="login-container">
			<view class="logo-section">
				<text class="app-title">健康报告管理</text>
				<text class="app-subtitle">记录健康，关爱生活</text>
			</view>
			
			<view class="form-section">
				<!-- 登录方式切换 -->
				<view class="login-type-tabs">
					<view 
						class="tab-item" 
						:class="{ active: loginType === 'password' }"
						@click="switchLoginType('password')"
					>
						密码登录
					</view>
					<view 
						class="tab-item" 
						:class="{ active: loginType === 'code' }"
						@click="switchLoginType('code')"
					>
						验证码登录
					</view>
				</view>
				
				<!-- 手机号输入 -->
				<view class="form-item">
					<text class="form-label">手机号</text>
					<input 
						class="form-input" 
						type="number" 
						placeholder="请输入手机号" 
						v-model="formData.phone"
						maxlength="11"
						@blur="validatePhone"
					/>
					<text v-if="errors.phone" class="error-text">{{ errors.phone }}</text>
				</view>
				
				<!-- 密码登录 -->
				<view v-if="loginType === 'password'" class="form-item">
					<text class="form-label">密码</text>
					<view class="password-input-container">
						<input 
							class="form-input" 
							:type="showPassword ? 'text' : 'password'" 
							placeholder="请输入密码" 
							v-model="formData.password"
						/>
						<text class="password-toggle" @click="togglePassword">
							{{ showPassword ? '隐藏' : '显示' }}
						</text>
					</view>
					<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
				</view>
				
				<!-- 验证码登录 -->
				<view v-if="loginType === 'code'" class="form-item">
					<text class="form-label">验证码</text>
					<view class="code-input-container">
						<input 
							class="form-input code-input" 
							type="number" 
							placeholder="请输入验证码" 
							v-model="formData.code"
							maxlength="6"
						/>
						<button 
							class="code-btn" 
							:class="{ disabled: codeCountdown > 0 || !isPhoneValid }"
							:disabled="codeCountdown > 0 || !isPhoneValid"
							@click="sendCode"
						>
							{{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
						</button>
					</view>
					<text v-if="errors.code" class="error-text">{{ errors.code }}</text>
				</view>
				
				<!-- 忘记密码链接 -->
				<view v-if="loginType === 'password'" class="forgot-password">
					<text class="link-text" @click="goToResetPassword">忘记密码？</text>
				</view>
				
				<!-- 登录按钮 -->
				<button 
					class="login-btn" 
					:class="{ disabled: !canLogin }"
					:disabled="!canLogin"
					@click="handleLogin"
					:loading="loading"
				>
					{{ loading ? '登录中...' : '登录' }}
				</button>
				
				<!-- 生物识别登录 -->
				<view v-if="biometricSupported && biometricEnabled" class="biometric-section">
					<view class="divider">
						<text class="divider-text">或</text>
					</view>
					<button class="biometric-btn" @click="handleBiometricLogin">
						<text class="biometric-icon">👆</text>
						<text class="biometric-text">{{ biometricTypeName }}登录</text>
					</button>
				</view>
				
				<!-- 微信登录 -->
				<view v-if="isWechatMiniProgram" class="wechat-section">
					<view class="divider">
						<text class="divider-text">或</text>
					</view>
					<button class="wechat-btn" @click="handleWechatLogin" :loading="wechatLoading">
						<text class="wechat-icon">💬</text>
						<text class="wechat-text">{{ wechatLoading ? '授权中...' : '微信快速登录' }}</text>
					</button>
				</view>
				
				<!-- 注册链接 -->
				<view class="register-link">
					<text class="link-text" @click="goToRegister">还没有账户？立即注册</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { useUserStore } from '../../stores/user.js'
	import authService from '../../services/auth/authService.js'
	import biometricService from '../../services/auth/biometricService.js'
	import sessionService from '../../services/auth/sessionService.js'
	import { PlatformAdapter } from '../../utils/platform/platformAdapter.js'
	
	export default {
		name: 'Login',
		data() {
			return {
				loginType: 'password', // password | code
				formData: {
					phone: '',
					password: '',
					code: ''
				},
				errors: {},
				showPassword: false,
				loading: false,
				wechatLoading: false,
				codeCountdown: 0,
				countdownTimer: null,
				biometricSupported: false,
				biometricEnabled: false,
				biometricTypes: [],
				platformAdapter: null
			}
		},
		computed: {
			isPhoneValid() {
				return /^1[3-9]\d{9}$/.test(this.formData.phone)
			},
			canLogin() {
				if (this.loginType === 'password') {
					return this.isPhoneValid && this.formData.password && !this.loading
				} else {
					return this.isPhoneValid && this.formData.code && !this.loading
				}
			},
			isWechatMiniProgram() {
				return this.platformAdapter?.isWechatMiniProgram() || false
			},
			biometricTypeName() {
				if (this.biometricTypes.includes('fingerprint')) {
					return '指纹'
				} else if (this.biometricTypes.includes('facial')) {
					return '面部识别'
				}
				return '生物识别'
			}
		},
		async onLoad() {
			await this.initPage()
		},
		
		async onShow() {
			// 页面显示时检查是否已登录
			const userStore = useUserStore()
			if (userStore.isAuthenticated) {
				// 已登录，跳转到首页
				uni.reLaunch({
					url: '/pages/index/index'
				})
			}
		},
		methods: {
			// 初始化页面
			async initPage() {
				try {
					// 初始化平台适配器
					this.platformAdapter = new PlatformAdapter()
					
					// 初始化生物识别
					await this.initBiometric()
					
					// 检查是否有保存的手机号
					const savedPhone = uni.getStorageSync('last_login_phone')
					if (savedPhone) {
						this.formData.phone = savedPhone
					}
				} catch (error) {
					console.error('页面初始化失败:', error)
				}
			},
			
			// 初始化生物识别
			async initBiometric() {
				try {
					const initResult = await biometricService.init()
					if (initResult.success) {
						this.biometricSupported = initResult.isSupported
						this.biometricTypes = initResult.types
						this.biometricEnabled = biometricService.isEnabled()
					}
				} catch (error) {
					console.error('生物识别初始化失败:', error)
				}
			},
			
			// 切换登录方式
			switchLoginType(type) {
				this.loginType = type
				this.clearErrors()
				this.clearForm()
			},
			
			// 清除错误信息
			clearErrors() {
				this.errors = {}
			},
			
			// 清除表单
			clearForm() {
				if (this.loginType === 'password') {
					this.formData.code = ''
				} else {
					this.formData.password = ''
				}
			},
			
			// 验证手机号
			validatePhone() {
				if (!this.formData.phone) {
					this.errors.phone = '请输入手机号'
				} else if (!this.isPhoneValid) {
					this.errors.phone = '手机号格式不正确'
				} else {
					delete this.errors.phone
				}
				this.$forceUpdate()
			},
			
			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword
			},
			
			// 发送验证码
			async sendCode() {
				if (!this.isPhoneValid) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}
				
				try {
					const result = await authService.sendVerificationCode(this.formData.phone, 'login')
					
					if (result.success) {
						uni.showToast({
							title: '验证码已发送',
							icon: 'success'
						})
						
						// 开始倒计时
						this.startCountdown()
					} else {
						uni.showToast({
							title: result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('发送验证码失败:', error)
					uni.showToast({
						title: '发送验证码失败',
						icon: 'none'
					})
				}
			},
			
			// 开始倒计时
			startCountdown() {
				this.codeCountdown = 60
				this.countdownTimer = setInterval(() => {
					this.codeCountdown--
					if (this.codeCountdown <= 0) {
						clearInterval(this.countdownTimer)
						this.countdownTimer = null
					}
				}, 1000)
			},
			
			// 处理登录
			async handleLogin() {
				if (!this.canLogin) return
				
				this.loading = true
				
				try {
					const userStore = useUserStore()
					let result
					
					if (this.loginType === 'password') {
						result = await authService.loginWithPassword({
							phone: this.formData.phone,
							password: this.formData.password
						})
					} else {
						result = await authService.loginWithCode({
							phone: this.formData.phone,
							code: this.formData.code
						})
					}
					
					if (result.success) {
						// 确定登录方式
						const loginMethod = this.loginType === 'password' ? 'password' : 'code'
						
						// 保存用户信息到store
						await userStore.login(result.data, loginMethod)
						
						// 创建会话
						await sessionService.createSession(result.data)
						
						// 保存手机号以便下次登录
						uni.setStorageSync('last_login_phone', this.formData.phone)
						
						uni.showToast({
							title: '登录成功',
							icon: 'success'
						})
						
						// 延迟跳转到首页
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/index/index'
							})
						}, 1500)
					} else {
						uni.showToast({
							title: result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('登录失败:', error)
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			
			// 生物识别登录
			async handleBiometricLogin() {
				try {
					const result = await authService.loginWithBiometric()
					
					if (result.success) {
						const userStore = useUserStore()
						await userStore.login(result.data, 'biometric')
						
						// 创建会话
						await sessionService.createSession(result.data)
						
						uni.showToast({
							title: '登录成功',
							icon: 'success'
						})
						
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/index/index'
							})
						}, 1500)
					} else {
						uni.showToast({
							title: result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('生物识别登录失败:', error)
					uni.showToast({
						title: '生物识别登录失败',
						icon: 'none'
					})
				}
			},
			
			// 微信登录
			async handleWechatLogin() {
				this.wechatLoading = true
				
				try {
					const result = await authService.loginWithWechat()
					
					if (result.success) {
						const userStore = useUserStore()
						await userStore.login(result.data, 'wechat')
						
						// 创建会话
						await sessionService.createSession(result.data)
						
						uni.showToast({
							title: '登录成功',
							icon: 'success'
						})
						
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/index/index'
							})
						}, 1500)
					} else {
						uni.showToast({
							title: result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('微信登录失败:', error)
					uni.showToast({
						title: '微信登录失败',
						icon: 'none'
					})
				} finally {
					this.wechatLoading = false
				}
			},
			
			// 跳转到注册页
			goToRegister() {
				uni.navigateTo({
					url: '/pages/auth/register'
				})
			},
			
			// 跳转到密码重置页
			goToResetPassword() {
				uni.navigateTo({
					url: '/pages/auth/reset-password'
				})
			}
		},
		
		onUnload() {
			// 清理倒计时器
			if (this.countdownTimer) {
				clearInterval(this.countdownTimer)
			}
		}
	}
</script>

<style scoped>
	.login-container {
		padding: 40px 30px;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	
	.logo-section {
		text-align: center;
		margin-bottom: 60px;
	}
	
	.app-title {
		font-size: 28px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 10px;
	}
	
	.app-subtitle {
		font-size: 14px;
		color: #8E8E93;
		display: block;
	}
	
	.form-section {
		flex: 1;
	}
	
	.login-type-tabs {
		display: flex;
		background-color: #F2F2F7;
		border-radius: 10px;
		padding: 4px;
		margin-bottom: 30px;
	}
	
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 12px;
		border-radius: 8px;
		font-size: 16px;
		color: #8E8E93;
		transition: all 0.3s;
	}
	
	.tab-item.active {
		background-color: #FFFFFF;
		color: #007AFF;
		font-weight: 600;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}
	
	.form-item {
		margin-bottom: 20px;
	}
	
	.form-label {
		display: block;
		font-size: 16px;
		color: #333333;
		margin-bottom: 8px;
		font-weight: 500;
	}
	
	.form-input {
		width: 100%;
		height: 44px;
		padding: 0 15px;
		border: 1px solid #E5E5E5;
		border-radius: 8px;
		font-size: 16px;
		background-color: #FFFFFF;
		box-sizing: border-box;
	}
	
	.form-input:focus {
		border-color: #007AFF;
	}
	
	.password-input-container {
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.password-toggle {
		position: absolute;
		right: 15px;
		color: #007AFF;
		font-size: 14px;
		cursor: pointer;
	}
	
	.code-input-container {
		display: flex;
		align-items: center;
		gap: 10px;
	}
	
	.code-input {
		flex: 1;
	}
	
	.code-btn {
		height: 44px;
		padding: 0 16px;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 8px;
		font-size: 14px;
		white-space: nowrap;
	}
	
	.code-btn.disabled {
		background-color: #C7C7CC;
		color: #8E8E93;
	}
	
	.error-text {
		display: block;
		color: #FF3B30;
		font-size: 12px;
		margin-top: 5px;
	}
	
	.forgot-password {
		text-align: right;
		margin-bottom: 30px;
	}
	
	.link-text {
		color: #007AFF;
		font-size: 14px;
		text-decoration: underline;
	}
	
	.login-btn {
		width: 100%;
		height: 50px;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 25px;
		font-size: 18px;
		font-weight: 600;
		margin-bottom: 30px;
	}
	
	.login-btn.disabled {
		background-color: #C7C7CC;
		color: #8E8E93;
	}
	
	.biometric-section,
	.wechat-section {
		margin-bottom: 20px;
	}
	
	.divider {
		text-align: center;
		margin: 20px 0;
		position: relative;
	}
	
	.divider::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		height: 1px;
		background-color: #E5E5E5;
	}
	
	.divider-text {
		background-color: #FFFFFF;
		color: #8E8E93;
		padding: 0 15px;
		font-size: 14px;
	}
	
	.biometric-btn,
	.wechat-btn {
		width: 100%;
		height: 50px;
		background-color: #FFFFFF;
		border: 1px solid #E5E5E5;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
	}
	
	.biometric-icon,
	.wechat-icon {
		font-size: 20px;
	}
	
	.biometric-text,
	.wechat-text {
		font-size: 16px;
		color: #333333;
	}
	
	.wechat-btn {
		background-color: #07C160;
		border-color: #07C160;
	}
	
	.wechat-text {
		color: #FFFFFF;
	}
	
	.register-link {
		text-align: center;
		margin-top: 30px;
	}
	
	.register-link .link-text {
		font-size: 16px;
	}
</style>