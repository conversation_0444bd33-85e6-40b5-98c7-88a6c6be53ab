<template>
  <view class="interactive-chart">
    <canvas 
      :canvas-id="canvasId" 
      :id="canvasId"
      class="chart-canvas"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @tap="handleTap"
    ></canvas>
    
    <!-- 图表控制面板 -->
    <view class="chart-controls" v-if="showControls">
      <view class="control-group">
        <button 
          class="control-btn" 
          :class="{ active: enableZoom }"
          @click="toggleZoom"
        >
          <uni-icons type="search" size="16"></uni-icons>
          缩放
        </button>
        <button 
          class="control-btn" 
          :class="{ active: enablePan }"
          @click="togglePan"
        >
          <uni-icons type="hand" size="16"></uni-icons>
          拖拽
        </button>
        <button 
          class="control-btn"
          @click="resetView"
        >
          <uni-icons type="refresh" size="16"></uni-icons>
          重置
        </button>
      </view>
      
      <view class="zoom-controls" v-if="enableZoom">
        <button class="zoom-btn" @click="zoomIn">+</button>
        <text class="zoom-level">{{ Math.round(scale * 100) }}%</text>
        <button class="zoom-btn" @click="zoomOut">-</button>
      </view>
    </view>
    
    <!-- 数据点提示框 -->
    <view 
      v-if="tooltip.show" 
      class="tooltip"
      :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
    >
      <text class="tooltip-date">{{ tooltip.date }}</text>
      <text class="tooltip-value">{{ tooltip.value }}</text>
      <text class="tooltip-status" :class="tooltip.status">{{ tooltip.statusText }}</text>
    </view>
  </view>
</template>

<script>
import UChartsAdapter from '@/utils/charts/uCharts.js'

export default {
  name: 'InteractiveChart',
  props: {
    canvasId: {
      type: String,
      default: 'interactiveChart'
    },
    chartData: {
      type: Array,
      default: () => []
    },
    chartType: {
      type: String,
      default: 'line'
    },
    width: {
      type: Number,
      default: 350
    },
    height: {
      type: Number,
      default: 250
    },
    normalRange: {
      type: Object,
      default: () => ({ min: null, max: null })
    },
    showControls: {
      type: Boolean,
      default: true
    },
    enableInteraction: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      chart: null,
      
      // 交互状态
      enableZoom: false,
      enablePan: false,
      
      // 触摸状态
      touchStartX: 0,
      touchStartY: 0,
      lastDistance: null,
      
      // 视图状态
      scale: 1,
      offsetX: 0,
      offsetY: 0,
      
      // 提示框
      tooltip: {
        show: false,
        x: 0,
        y: 0,
        date: '',
        value: '',
        status: 'normal',
        statusText: ''
      },
      
      // 数据点位置缓存
      dataPoints: []
    }
  },
  
  mounted() {
    this.initChart()
  },
  
  watch: {
    chartData: {
      handler() {
        this.drawChart()
      },
      deep: true
    }
  },
  
  methods: {
    async initChart() {
      try {
        this.chart = new UChartsAdapter({
          canvasId: this.canvasId,
          width: this.width,
          height: this.height,
          type: this.chartType,
          padding: [40, 40, 40, 40]
        })
        
        await this.chart.init()
        
        this.$nextTick(() => {
          this.drawChart()
        })
      } catch (error) {
        console.error('交互式图表初始化失败:', error)
      }
    },
    
    drawChart() {
      if (!this.chart || !this.chartData.length) return
      
      const options = {
        normalRange: this.normalRange,
        showGrid: true,
        showLegend: true,
        colors: ['#007AFF', '#FF3B30', '#34C759', '#FF9500']
      }
      
      // 应用缩放和偏移
      this.applyTransform()
      
      // 绘制图表
      this.chart.updateData(this.chartData, options)
      
      // 缓存数据点位置用于交互
      this.cacheDataPoints()
    },
    
    applyTransform() {
      if (!this.chart.ctx) return
      
      // 保存当前变换状态
      this.chart.ctx.save()
      
      // 应用缩放和平移
      this.chart.ctx.translate(this.offsetX, this.offsetY)
      this.chart.ctx.scale(this.scale, this.scale)
    },
    
    cacheDataPoints() {
      if (!this.chartData.length) return
      
      const padding = [40, 40, 40, 40]
      const chartWidth = this.width - padding[1] - padding[3]
      const chartHeight = this.height - padding[0] - padding[2]
      
      const values = this.chartData.map(item => item.value)
      const minValue = Math.min(...values)
      const maxValue = Math.max(...values)
      const valueRange = maxValue - minValue || 1
      
      this.dataPoints = this.chartData.map((item, index) => {
        const x = padding[3] + (index / (this.chartData.length - 1)) * chartWidth
        const y = padding[0] + chartHeight - ((item.value - minValue) / valueRange) * chartHeight
        
        return {
          x: x * this.scale + this.offsetX,
          y: y * this.scale + this.offsetY,
          data: item,
          isAbnormal: this.isAbnormalValue(item.value)
        }
      })
    },
    
    // 交互控制方法
    toggleZoom() {
      this.enableZoom = !this.enableZoom
      if (this.enableZoom) {
        this.enablePan = false
      }
    },
    
    togglePan() {
      this.enablePan = !this.enablePan
      if (this.enablePan) {
        this.enableZoom = false
      }
    },
    
    resetView() {
      this.scale = 1
      this.offsetX = 0
      this.offsetY = 0
      this.drawChart()
    },
    
    zoomIn() {
      this.scale = Math.min(this.scale * 1.2, 3)
      this.drawChart()
    },
    
    zoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.5)
      this.drawChart()
    },
    
    // 触摸事件处理
    handleTouchStart(e) {
      if (!this.enableInteraction) return
      
      this.touchStartX = e.touches[0].clientX
      this.touchStartY = e.touches[0].clientY
      
      // 隐藏提示框
      this.hideTooltip()
    },
    
    handleTouchMove(e) {
      if (!this.enableInteraction) return
      
      e.preventDefault()
      
      if (e.touches.length === 1 && this.enablePan) {
        // 单指拖拽
        const deltaX = e.touches[0].clientX - this.touchStartX
        const deltaY = e.touches[0].clientY - this.touchStartY
        
        this.offsetX += deltaX
        this.offsetY += deltaY
        
        this.touchStartX = e.touches[0].clientX
        this.touchStartY = e.touches[0].clientY
        
        this.drawChart()
      } else if (e.touches.length === 2 && this.enableZoom) {
        // 双指缩放
        const touch1 = e.touches[0]
        const touch2 = e.touches[1]
        const distance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) + 
          Math.pow(touch2.clientY - touch1.clientY, 2)
        )
        
        if (this.lastDistance) {
          const scaleChange = distance / this.lastDistance
          this.scale *= scaleChange
          this.scale = Math.max(0.5, Math.min(3, this.scale))
          this.drawChart()
        }
        
        this.lastDistance = distance
      }
    },
    
    handleTouchEnd() {
      this.lastDistance = null
    },
    
    handleTap(e) {
      if (!this.enableInteraction) return
      
      const rect = e.currentTarget.getBoundingClientRect()
      const x = e.detail.x - rect.left
      const y = e.detail.y - rect.top
      
      // 检查是否点击了数据点
      const clickedPoint = this.findNearestDataPoint(x, y)
      if (clickedPoint) {
        this.showTooltip(clickedPoint, x, y)
      } else {
        this.hideTooltip()
      }
    },
    
    findNearestDataPoint(x, y) {
      const threshold = 20 // 点击阈值
      
      for (const point of this.dataPoints) {
        const distance = Math.sqrt(
          Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2)
        )
        
        if (distance <= threshold) {
          return point
        }
      }
      
      return null
    },
    
    showTooltip(point, x, y) {
      this.tooltip = {
        show: true,
        x: Math.min(x, this.width - 120), // 防止超出边界
        y: Math.max(y - 60, 10),
        date: this.formatDate(point.data.date),
        value: `${point.data.value}${this.getUnit()}`,
        status: point.isAbnormal ? 'abnormal' : 'normal',
        statusText: point.isAbnormal ? '异常' : '正常'
      }
    },
    
    hideTooltip() {
      this.tooltip.show = false
    },
    
    // 辅助方法
    isAbnormalValue(value) {
      if (this.normalRange.min === null || this.normalRange.max === null) {
        return false
      }
      return value < this.normalRange.min || value > this.normalRange.max
    },
    
    getUnit() {
      // 根据图表类型返回单位，这里简化处理
      return ''
    },
    
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
}
</script>

<style scoped>
.interactive-chart {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-canvas {
  width: 350px;
  height: 250px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.chart-controls {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 8px;
  width: 100%;
}

.control-group {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  color: #666666;
}

.control-btn.active {
  background-color: #007AFF;
  color: #ffffff;
  border-color: #007AFF;
}

.zoom-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.zoom-btn {
  width: 30px;
  height: 30px;
  background-color: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 15px;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-level {
  font-size: 14px;
  color: #333333;
  min-width: 50px;
  text-align: center;
}

.tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
}

.tooltip-date {
  display: block;
  font-weight: bold;
  margin-bottom: 2px;
}

.tooltip-value {
  display: block;
  font-size: 14px;
  margin-bottom: 2px;
}

.tooltip-status {
  display: block;
  font-size: 11px;
}

.tooltip-status.normal {
  color: #34C759;
}

.tooltip-status.abnormal {
  color: #FF3B30;
}
</style>