<template>
  <view class="report-filter">
    <!-- 快速筛选栏 -->
    <view class="quick-filters">
      <scroll-view class="filter-scroll" scroll-x="true">
        <view class="filter-chips">
          <view 
            v-for="chip in quickFilterChips"
            :key="chip.key"
            :class="['filter-chip', { active: isChipActive(chip) }]"
            @tap="toggleQuickFilter(chip)"
          >
            <text class="chip-text">{{ chip.label }}</text>
            <view v-if="chip.count" class="chip-count">{{ chip.count }}</view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 高级筛选 -->
    <view v-if="showAdvanced" class="advanced-filters">
      <!-- 时间范围筛选 -->
      <view class="filter-group">
        <text class="group-title">时间范围</text>
        <view class="time-range-options">
          <view 
            v-for="option in timeRangeOptions"
            :key="option.key"
            :class="['time-option', { active: selectedTimeRange === option.key }]"
            @tap="selectTimeRange(option)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        
        <!-- 自定义时间范围 -->
        <view v-if="selectedTimeRange === 'custom'" class="custom-time-range">
          <view class="date-inputs">
            <picker 
              mode="date" 
              :value="customTimeRange.start"
              @change="onCustomStartDateChange"
            >
              <view class="date-input">
                <text class="date-label">开始日期</text>
                <text class="date-value">
                  {{ customTimeRange.start || '选择日期' }}
                </text>
              </view>
            </picker>
            
            <view class="date-separator">
              <text>至</text>
            </view>
            
            <picker 
              mode="date" 
              :value="customTimeRange.end"
              @change="onCustomEndDateChange"
            >
              <view class="date-input">
                <text class="date-label">结束日期</text>
                <text class="date-value">
                  {{ customTimeRange.end || '选择日期' }}
                </text>
              </view>
            </picker>
          </view>
        </view>
      </view>
      
      <!-- 检查类别筛选 -->
      <view class="filter-group">
        <text class="group-title">检查类别</text>
        <view class="category-grid">
          <view 
            v-for="category in categoryOptions"
            :key="category.value"
            :class="['category-item', { active: selectedCategories.includes(category.value) }]"
            @tap="toggleCategory(category.value)"
          >
            <view class="category-icon">
              <text class="icon-text">{{ category.icon }}</text>
            </view>
            <text class="category-name">{{ category.label }}</text>
            <view v-if="category.count" class="category-count">{{ category.count }}</view>
          </view>
        </view>
      </view>
      
      <!-- 医院筛选 */
      <view class="filter-group">
        <text class="group-title">医院</text>
        <view class="hospital-filter">
          <input 
            class="hospital-search"
            v-model="hospitalKeyword"
            placeholder="搜索医院名称"
            @input="onHospitalSearch"
          />
          <view class="hospital-suggestions">
            <view 
              v-for="hospital in hospitalSuggestions"
              :key="hospital.name"
              :class="['hospital-item', { active: selectedHospitals.includes(hospital.name) }]"
              @tap="toggleHospital(hospital.name)"
            >
              <text class="hospital-name">{{ hospital.name }}</text>
              <text class="hospital-count">{{ hospital.count }}份报告</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 异常状态筛选 -->
      <view class="filter-group">
        <text class="group-title">异常状态</text>
        <view class="abnormal-options">
          <view 
            v-for="option in abnormalOptions"
            :key="option.key"
            :class="['abnormal-option', { active: selectedAbnormalStatus === option.key }]"
            @tap="selectAbnormalStatus(option.key)"
          >
            <view class="option-indicator" :style="{ backgroundColor: option.color }"></view>
            <text class="option-label">{{ option.label }}</text>
            <text class="option-count">{{ option.count }}</text>
          </view>
        </view>
      </view>
      
      <!-- 排序选项 */
      <view class="filter-group">
        <text class="group-title">排序方式</text>
        <view class="sort-options">
          <view 
            v-for="option in sortOptions"
            :key="option.key"
            :class="['sort-option', { active: selectedSort === option.key }]"
            @tap="selectSort(option.key)"
          >
            <uni-icons :type="option.icon" size="16" color="#666"></uni-icons>
            <text class="sort-label">{{ option.label }}</text>
            <uni-icons 
              v-if="selectedSort === option.key"
              :type="sortOrder === 'asc' ? 'arrow-up' : 'arrow-down'" 
              size="14" 
              color="#007AFF"
            ></uni-icons>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="filter-actions">
      <button class="action-btn toggle-btn" @tap="toggleAdvanced">
        <text class="btn-text">
          {{ showAdvanced ? '收起筛选' : '高级筛选' }}
        </text>
        <uni-icons 
          :type="showAdvanced ? 'arrow-up' : 'arrow-down'" 
          size="16" 
          color="#007AFF"
        ></uni-icons>
      </button>
      
      <view class="main-actions">
        <button class="action-btn reset-btn" @tap="resetFilters">
          <text class="btn-text">重置</text>
        </button>
        <button class="action-btn apply-btn" @tap="applyFilters">
          <text class="btn-text">应用筛选</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { Constants } from '@/types/index.js'

export default {
  name: 'ReportFilter',
  props: {
    // 报告数据
    reports: {
      type: Array,
      default: () => []
    },
    // 当前筛选条件
    currentFilter: {
      type: Object,
      default: () => ({
        timeRange: { start: null, end: null },
        categories: [],
        hospitals: [],
        abnormalStatus: 'all',
        sortBy: 'reportDate',
        sortOrder: 'desc'
      })
    }
  },
  
  data() {
    return {
      // 显示高级筛选
      showAdvanced: false,
      
      // 筛选条件
      selectedTimeRange: 'all',
      customTimeRange: { start: null, end: null },
      selectedCategories: [],
      selectedHospitals: [],
      selectedAbnormalStatus: 'all',
      selectedSort: 'reportDate',
      sortOrder: 'desc',
      
      // 医院搜索
      hospitalKeyword: '',
      
      // 时间范围选项
      timeRangeOptions: [
        { key: 'all', label: '全部时间' },
        { key: 'today', label: '今天' },
        { key: 'week', label: '最近一周' },
        { key: 'month', label: '最近一月' },
        { key: 'quarter', label: '最近三月' },
        { key: 'year', label: '最近一年' },
        { key: 'custom', label: '自定义' }
      ],
      
      // 异常状态选项
      abnormalOptions: [
        { key: 'all', label: '全部', color: '#999999', count: 0 },
        { key: 'normal', label: '正常', color: '#4CAF50', count: 0 },
        { key: 'abnormal', label: '异常', color: '#FF5252', count: 0 },
        { key: 'critical', label: '严重异常', color: '#D32F2F', count: 0 }
      ],
      
      // 排序选项
      sortOptions: [
        { key: 'reportDate', label: '报告日期', icon: 'calendar' },
        { key: 'checkDate', label: '检查日期', icon: 'calendar' },
        { key: 'hospital', label: '医院名称', icon: 'home' },
        { key: 'abnormalCount', label: '异常数量', icon: 'warning' },
        { key: 'createdAt', label: '创建时间', icon: 'clock' }
      ]
    }
  },
  
  computed: {
    // 快速筛选选项
    quickFilterChips() {
      return [
        {
          key: 'recent',
          label: '最近',
          count: this.getRecentReportsCount(),
          filter: { timeRange: 'week' }
        },
        {
          key: 'abnormal',
          label: '异常',
          count: this.getAbnormalReportsCount(),
          filter: { abnormalStatus: 'abnormal' }
        },
        {
          key: 'blood',
          label: '血液',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.BLOOD_ROUTINE),
          filter: { categories: [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE] }
        },
        {
          key: 'biochemistry',
          label: '生化',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.BIOCHEMISTRY),
          filter: { categories: [Constants.REPORT_CATEGORIES.BIOCHEMISTRY] }
        }
      ]
    },
    
    // 分类选项
    categoryOptions() {
      return [
        {
          value: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
          label: '血常规',
          icon: '🩸',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.BLOOD_ROUTINE)
        },
        {
          value: Constants.REPORT_CATEGORIES.BIOCHEMISTRY,
          label: '生化',
          icon: '🧪',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.BIOCHEMISTRY)
        },
        {
          value: Constants.REPORT_CATEGORIES.IMMUNOLOGY,
          label: '免疫',
          icon: '🛡️',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.IMMUNOLOGY)
        },
        {
          value: Constants.REPORT_CATEGORIES.URINE_ROUTINE,
          label: '尿常规',
          icon: '💧',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.URINE_ROUTINE)
        },
        {
          value: Constants.REPORT_CATEGORIES.IMAGING,
          label: '影像',
          icon: '📷',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.IMAGING)
        },
        {
          value: Constants.REPORT_CATEGORIES.OTHER,
          label: '其他',
          icon: '📋',
          count: this.getCategoryCount(Constants.REPORT_CATEGORIES.OTHER)
        }
      ]
    },
    
    // 医院建议列表
    hospitalSuggestions() {
      const hospitalMap = new Map()
      
      this.reports.forEach(report => {
        if (report.hospital) {
          const name = report.hospital
          if (!this.hospitalKeyword || name.toLowerCase().includes(this.hospitalKeyword.toLowerCase())) {
            hospitalMap.set(name, (hospitalMap.get(name) || 0) + 1)
          }
        }
      })
      
      return Array.from(hospitalMap.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
    }
  },
  
  watch: {
    currentFilter: {
      handler(newFilter) {
        this.syncFromCurrentFilter(newFilter)
      },
      immediate: true,
      deep: true
    }
  },
  
  methods: {
    // 同步当前筛选条件
    syncFromCurrentFilter(filter) {
      if (filter.timeRange?.start && filter.timeRange?.end) {
        this.selectedTimeRange = 'custom'
        this.customTimeRange = { ...filter.timeRange }
      }
      
      this.selectedCategories = [...(filter.categories || [])]
      this.selectedHospitals = [...(filter.hospitals || [])]
      this.selectedAbnormalStatus = filter.abnormalStatus || 'all'
      this.selectedSort = filter.sortBy || 'reportDate'
      this.sortOrder = filter.sortOrder || 'desc'
    },
    
    // 判断快速筛选是否激活
    isChipActive(chip) {
      // 这里可以根据当前筛选条件判断
      return false
    },
    
    // 切换快速筛选
    toggleQuickFilter(chip) {
      this.$emit('quickFilter', chip.filter)
    },
    
    // 切换高级筛选显示
    toggleAdvanced() {
      this.showAdvanced = !this.showAdvanced
    },
    
    // 选择时间范围
    selectTimeRange(option) {
      this.selectedTimeRange = option.key
      
      if (option.key !== 'custom') {
        this.customTimeRange = { start: null, end: null }
        this.calculateTimeRange(option.key)
      }
    },
    
    // 计算时间范围
    calculateTimeRange(rangeKey) {
      const now = new Date()
      let start = null
      let end = new Date(now)
      
      switch (rangeKey) {
        case 'today':
          start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          start = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
          break
        case 'quarter':
          start = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
          break
        case 'year':
          start = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
          break
      }
      
      if (start) {
        this.customTimeRange = {
          start: start.toISOString().split('T')[0],
          end: end.toISOString().split('T')[0]
        }
      }
    },
    
    // 自定义开始日期变化
    onCustomStartDateChange(e) {
      this.customTimeRange.start = e.detail.value
    },
    
    // 自定义结束日期变化
    onCustomEndDateChange(e) {
      this.customTimeRange.end = e.detail.value
    },
    
    // 切换分类
    toggleCategory(category) {
      const index = this.selectedCategories.indexOf(category)
      if (index > -1) {
        this.selectedCategories.splice(index, 1)
      } else {
        this.selectedCategories.push(category)
      }
    },
    
    // 医院搜索
    onHospitalSearch() {
      // 搜索逻辑在computed中处理
    },
    
    // 切换医院
    toggleHospital(hospital) {
      const index = this.selectedHospitals.indexOf(hospital)
      if (index > -1) {
        this.selectedHospitals.splice(index, 1)
      } else {
        this.selectedHospitals.push(hospital)
      }
    },
    
    // 选择异常状态
    selectAbnormalStatus(status) {
      this.selectedAbnormalStatus = status
    },
    
    // 选择排序
    selectSort(sortKey) {
      if (this.selectedSort === sortKey) {
        // 切换排序顺序
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc'
      } else {
        this.selectedSort = sortKey
        this.sortOrder = 'desc'
      }
    },
    
    // 重置筛选
    resetFilters() {
      this.selectedTimeRange = 'all'
      this.customTimeRange = { start: null, end: null }
      this.selectedCategories = []
      this.selectedHospitals = []
      this.selectedAbnormalStatus = 'all'
      this.selectedSort = 'reportDate'
      this.sortOrder = 'desc'
      this.hospitalKeyword = ''
      
      this.$emit('reset')
    },
    
    // 应用筛选
    applyFilters() {
      const filter = {
        timeRange: this.selectedTimeRange === 'custom' ? this.customTimeRange : null,
        categories: [...this.selectedCategories],
        hospitals: [...this.selectedHospitals],
        abnormalStatus: this.selectedAbnormalStatus,
        sortBy: this.selectedSort,
        sortOrder: this.sortOrder
      }
      
      this.$emit('apply', filter)
    },
    
    // 获取最近报告数量
    getRecentReportsCount() {
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      return this.reports.filter(report => 
        new Date(report.reportDate) >= weekAgo
      ).length
    },
    
    // 获取异常报告数量
    getAbnormalReportsCount() {
      return this.reports.filter(report => 
        report.items.some(item => item.isAbnormal)
      ).length
    },
    
    // 获取分类报告数量
    getCategoryCount(category) {
      return this.reports.filter(report => 
        report.category === category
      ).length
    }
  }
}
</script>

<style lang="scss" scoped>
.report-filter {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  overflow: hidden;
}

.quick-filters {
  padding: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-chips {
  display: flex;
  gap: 16rpx;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  flex-shrink: 0;
  transition: all 0.3s ease;
  
  &.active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.chip-text {
  font-size: 26rpx;
  color: #333333;
  
  .active & {
    color: #1976d2;
  }
}

.chip-count {
  background: #007AFF;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}

.advanced-filters {
  padding: 30rpx;
}

.filter-group {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.time-range-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.time-option {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  
  &.active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.option-text {
  font-size: 24rpx;
  color: #333333;
  
  .active & {
    color: #1976d2;
  }
}

.custom-time-range {
  margin-top: 20rpx;
}

.date-inputs {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.date-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #ffffff;
}

.date-label {
  font-size: 22rpx;
  color: #999999;
  display: block;
  margin-bottom: 8rpx;
}

.date-value {
  font-size: 26rpx;
  color: #333333;
}

.date-separator {
  font-size: 24rpx;
  color: #999999;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  position: relative;
  
  &.active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.category-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
  
  .active & {
    color: #1976d2;
  }
}

.category-count {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #007AFF;
  color: #ffffff;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 24rpx;
  text-align: center;
}

.hospital-filter {
  margin-bottom: 20rpx;
}

.hospital-search {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

.hospital-suggestions {
  max-height: 300rpx;
  overflow-y: auto;
}

.hospital-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  margin-bottom: 8rpx;
  
  &.active {
    background: #e3f2fd;
    border: 2rpx solid #1976d2;
  }
}

.hospital-name {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.hospital-count {
  font-size: 22rpx;
  color: #999999;
}

.abnormal-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.abnormal-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  
  &.active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.option-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
}

.option-label {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.option-count {
  font-size: 24rpx;
  color: #999999;
}

.sort-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.sort-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  
  &.active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.sort-label {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.filter-actions {
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.action-btn {
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.toggle-btn {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  color: #007AFF;
  margin-bottom: 20rpx;
}

.main-actions {
  display: flex;
  gap: 20rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666666;
}

.apply-btn {
  background: #007AFF;
  color: #ffffff;
}

.btn-text {
  font-size: 28rpx;
}
</style>