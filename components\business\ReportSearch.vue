<template>
  <view class="report-search">
    <!-- 搜索输入框 -->
    <view class="search-input-container">
      <view class="search-input-wrapper">
        <uni-icons type="search" size="20" color="#999"></uni-icons>
        <input 
          class="search-input"
          v-model="searchKeyword"
          :placeholder="placeholder"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          @confirm="handleConfirm"
        />
        <button 
          v-if="searchKeyword"
          class="clear-btn"
          @tap="clearSearch"
        >
          <uni-icons type="close" size="16" color="#999"></uni-icons>
        </button>
      </view>
      
      <!-- 搜索按钮 -->
      <button 
        v-if="showSearchButton"
        class="search-btn"
        @tap="handleSearch"
      >
        <text class="search-btn-text">搜索</text>
      </button>
    </view>
    
    <!-- 搜索建议 -->
    <view v-if="showSuggestions && suggestions.length > 0" class="search-suggestions">
      <view class="suggestions-header">
        <text class="suggestions-title">搜索建议</text>
        <button class="clear-history-btn" @tap="clearHistory">
          <text class="clear-text">清除历史</text>
        </button>
      </view>
      
      <view class="suggestions-list">
        <view 
          v-for="suggestion in suggestions"
          :key="suggestion.id"
          class="suggestion-item"
          @tap="selectSuggestion(suggestion)"
        >
          <view class="suggestion-icon">
            <uni-icons 
              :type="suggestion.type === 'history' ? 'clock' : 'search'" 
              size="16" 
              color="#999"
            ></uni-icons>
          </view>
          <text class="suggestion-text">{{ suggestion.text }}</text>
          <view v-if="suggestion.count" class="suggestion-count">
            {{ suggestion.count }}
          </view>
          <button 
            v-if="suggestion.type === 'history'"
            class="remove-suggestion-btn"
            @tap.stop="removeSuggestion(suggestion)"
          >
            <uni-icons type="close" size="14" color="#ccc"></uni-icons>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 热门搜索 -->
    <view v-if="showHotSearch && hotSearches.length > 0" class="hot-searches">
      <text class="hot-title">热门搜索</text>
      <view class="hot-tags">
        <view 
          v-for="hot in hotSearches"
          :key="hot.id"
          class="hot-tag"
          @tap="selectHotSearch(hot)"
        >
          <text class="hot-text">{{ hot.text }}</text>
        </view>
      </view>
    </view>
    
    <!-- 搜索结果统计 -->
    <view v-if="showResultStats && searchKeyword" class="search-stats">
      <text class="stats-text">
        找到 {{ resultCount }} 条相关结果
      </text>
      <text v-if="searchTime" class="search-time">
        (用时 {{ searchTime }}ms)
      </text>
    </view>
    
    <!-- 搜索历史 -->
    <view v-if="showHistory && searchHistory.length > 0" class="search-history">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <button class="clear-all-history-btn" @tap="clearAllHistory">
          <uni-icons type="trash" size="16" color="#999"></uni-icons>
          <text class="clear-text">清空</text>
        </button>
      </view>
      
      <view class="history-list">
        <view 
          v-for="history in searchHistory"
          :key="history.id"
          class="history-item"
          @tap="selectHistory(history)"
        >
          <uni-icons type="clock" size="16" color="#999"></uni-icons>
          <text class="history-text">{{ history.text }}</text>
          <text class="history-time">{{ formatTime(history.timestamp) }}</text>
          <button 
            class="remove-history-btn"
            @tap.stop="removeHistory(history)"
          >
            <uni-icons type="close" size="14" color="#ccc"></uni-icons>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ReportSearch',
  props: {
    // 占位符文本
    placeholder: {
      type: String,
      default: '搜索医院、医生、检查项目...'
    },
    // 是否显示搜索按钮
    showSearchButton: {
      type: Boolean,
      default: false
    },
    // 是否显示搜索建议
    showSuggestions: {
      type: Boolean,
      default: true
    },
    // 是否显示热门搜索
    showHotSearch: {
      type: Boolean,
      default: true
    },
    // 是否显示搜索历史
    showHistory: {
      type: Boolean,
      default: true
    },
    // 是否显示结果统计
    showResultStats: {
      type: Boolean,
      default: true
    },
    // 报告数据
    reports: {
      type: Array,
      default: () => []
    },
    // 搜索结果数量
    resultCount: {
      type: Number,
      default: 0
    },
    // 搜索耗时
    searchTime: {
      type: Number,
      default: 0
    },
    // 防抖延迟
    debounceDelay: {
      type: Number,
      default: 300
    }
  },
  
  data() {
    return {
      // 搜索关键词
      searchKeyword: '',
      // 是否聚焦
      isFocused: false,
      // 防抖定时器
      debounceTimer: null,
      // 搜索建议
      suggestions: [],
      // 搜索历史
      searchHistory: [],
      // 热门搜索
      hotSearches: [
        { id: 1, text: '血常规', count: 156 },
        { id: 2, text: '肝功能', count: 89 },
        { id: 3, text: '血糖', count: 67 },
        { id: 4, text: '血脂', count: 45 },
        { id: 5, text: '尿常规', count: 34 }
      ]
    }
  },
  
  mounted() {
    this.loadSearchHistory()
  },
  
  methods: {
    // 处理输入
    handleInput() {
      this.clearDebounceTimer()
      
      if (this.searchKeyword.trim()) {
        this.debounceTimer = setTimeout(() => {
          this.generateSuggestions()
          this.$emit('input', this.searchKeyword)
        }, this.debounceDelay)
      } else {
        this.suggestions = []
        this.$emit('clear')
      }
    },
    
    // 处理聚焦
    handleFocus() {
      this.isFocused = true
      if (this.searchKeyword.trim()) {
        this.generateSuggestions()
      }
      this.$emit('focus')
    },
    
    // 处理失焦
    handleBlur() {
      // 延迟隐藏建议，以便点击建议项
      setTimeout(() => {
        this.isFocused = false
      }, 200)
      this.$emit('blur')
    },
    
    // 处理确认搜索
    handleConfirm() {
      this.handleSearch()
    },
    
    // 执行搜索
    handleSearch() {
      if (!this.searchKeyword.trim()) return
      
      this.addToHistory(this.searchKeyword.trim())
      this.suggestions = []
      this.$emit('search', this.searchKeyword.trim())
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.suggestions = []
      this.clearDebounceTimer()
      this.$emit('clear')
    },
    
    // 生成搜索建议
    generateSuggestions() {
      if (!this.searchKeyword.trim()) {
        this.suggestions = []
        return
      }
      
      const keyword = this.searchKeyword.toLowerCase()
      const suggestions = []
      
      // 从搜索历史中匹配
      this.searchHistory.forEach(history => {
        if (history.text.toLowerCase().includes(keyword)) {
          suggestions.push({
            id: `history_${history.id}`,
            text: history.text,
            type: 'history'
          })
        }
      })
      
      // 从报告数据中提取建议
      const hospitalSet = new Set()
      const doctorSet = new Set()
      const itemSet = new Set()
      
      this.reports.forEach(report => {
        // 医院建议
        if (report.hospital && report.hospital.toLowerCase().includes(keyword)) {
          hospitalSet.add(report.hospital)
        }
        
        // 医生建议
        if (report.doctor && report.doctor.toLowerCase().includes(keyword)) {
          doctorSet.add(report.doctor)
        }
        
        // 检查项目建议
        report.items.forEach(item => {
          if (item.name && item.name.toLowerCase().includes(keyword)) {
            itemSet.add(item.name)
          }
        })
      })
      
      // 添加医院建议
      Array.from(hospitalSet).slice(0, 3).forEach((hospital, index) => {
        const count = this.reports.filter(r => r.hospital === hospital).length
        suggestions.push({
          id: `hospital_${index}`,
          text: hospital,
          type: 'hospital',
          count
        })
      })
      
      // 添加医生建议
      Array.from(doctorSet).slice(0, 3).forEach((doctor, index) => {
        const count = this.reports.filter(r => r.doctor === doctor).length
        suggestions.push({
          id: `doctor_${index}`,
          text: doctor,
          type: 'doctor',
          count
        })
      })
      
      // 添加检查项目建议
      Array.from(itemSet).slice(0, 3).forEach((item, index) => {
        const count = this.reports.reduce((total, report) => {
          return total + report.items.filter(i => i.name === item).length
        }, 0)
        suggestions.push({
          id: `item_${index}`,
          text: item,
          type: 'item',
          count
        })
      })
      
      // 去重并限制数量
      this.suggestions = suggestions
        .filter((item, index, self) => 
          index === self.findIndex(t => t.text === item.text)
        )
        .slice(0, 8)
    },
    
    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.searchKeyword = suggestion.text
      this.suggestions = []
      this.handleSearch()
    },
    
    // 移除搜索建议
    removeSuggestion(suggestion) {
      if (suggestion.type === 'history') {
        this.removeHistory(suggestion)
      }
    },
    
    // 选择热门搜索
    selectHotSearch(hot) {
      this.searchKeyword = hot.text
      this.handleSearch()
    },
    
    // 选择搜索历史
    selectHistory(history) {
      this.searchKeyword = history.text
      this.handleSearch()
    },
    
    // 添加到搜索历史
    addToHistory(keyword) {
      // 移除重复项
      this.searchHistory = this.searchHistory.filter(item => item.text !== keyword)
      
      // 添加到开头
      this.searchHistory.unshift({
        id: Date.now(),
        text: keyword,
        timestamp: Date.now()
      })
      
      // 限制历史记录数量
      if (this.searchHistory.length > 20) {
        this.searchHistory = this.searchHistory.slice(0, 20)
      }
      
      this.saveSearchHistory()
    },
    
    // 移除搜索历史
    removeHistory(history) {
      this.searchHistory = this.searchHistory.filter(item => item.id !== history.id)
      this.saveSearchHistory()
    },
    
    // 清除搜索历史
    clearHistory() {
      this.suggestions = this.suggestions.filter(item => item.type !== 'history')
    },
    
    // 清空所有历史
    clearAllHistory() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = []
            this.saveSearchHistory()
          }
        }
      })
    },
    
    // 加载搜索历史
    loadSearchHistory() {
      try {
        const history = uni.getStorageSync('search_history')
        if (history && Array.isArray(history)) {
          this.searchHistory = history
        }
      } catch (error) {
        console.error('加载搜索历史失败:', error)
      }
    },
    
    // 保存搜索历史
    saveSearchHistory() {
      try {
        uni.setStorageSync('search_history', this.searchHistory)
      } catch (error) {
        console.error('保存搜索历史失败:', error)
      }
    },
    
    // 清除防抖定时器
    clearDebounceTimer() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
        this.debounceTimer = null
      }
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        const date = new Date(timestamp)
        return `${date.getMonth() + 1}/${date.getDate()}`
      }
    }
  },
  
  beforeUnmount() {
    this.clearDebounceTimer()
  }
}
</script>

<style lang="scss" scoped>
.report-search {
  background: #ffffff;
}

.search-input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 0 20rpx;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 40rpx;
  border: 2rpx solid #e9ecef;
  
  &:focus-within {
    border-color: #007AFF;
    background: #ffffff;
  }
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
  
  &::placeholder {
    color: #999999;
  }
}

.clear-btn {
  padding: 8rpx;
  background: none;
  border: none;
  border-radius: 50%;
  
  &:active {
    background: #f0f0f0;
  }
}

.search-btn {
  padding: 0 30rpx;
  height: 80rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.search-btn-text {
  color: #ffffff;
}

.search-suggestions {
  border-top: 2rpx solid #f0f0f0;
  background: #ffffff;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f8f9fa;
}

.suggestions-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
}

.clear-history-btn {
  background: none;
  border: none;
  padding: 0;
}

.clear-text {
  font-size: 24rpx;
  color: #999999;
}

.suggestions-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f8f9fa;
  
  &:active {
    background: #f8f9fa;
  }
}

.suggestion-icon {
  flex-shrink: 0;
}

.suggestion-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.suggestion-count {
  font-size: 22rpx;
  color: #999999;
  background: #f0f0f0;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

.remove-suggestion-btn {
  background: none;
  border: none;
  padding: 8rpx;
  border-radius: 50%;
  
  &:active {
    background: #f0f0f0;
  }
}

.hot-searches {
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.hot-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-tag {
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
  
  &:active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.hot-text {
  font-size: 24rpx;
  color: #333333;
}

.search-stats {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 2rpx solid #f0f0f0;
}

.stats-text {
  font-size: 26rpx;
  color: #333333;
}

.search-time {
  font-size: 22rpx;
  color: #999999;
}

.search-history {
  border-top: 2rpx solid #f0f0f0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f8f9fa;
}

.history-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
}

.clear-all-history-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: none;
  border: none;
  padding: 0;
}

.history-list {
  max-height: 300rpx;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f8f9fa;
  
  &:active {
    background: #f8f9fa;
  }
}

.history-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.history-time {
  font-size: 22rpx;
  color: #999999;
}

.remove-history-btn {
  background: none;
  border: none;
  padding: 8rpx;
  border-radius: 50%;
  
  &:active {
    background: #f0f0f0;
  }
}
</style>