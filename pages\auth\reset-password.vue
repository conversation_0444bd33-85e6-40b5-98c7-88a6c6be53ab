<template>
	<view class="page-container">
		<view class="reset-container">
			<view class="header-section">
				<text class="page-title">重置密码</text>
				<text class="page-subtitle">请输入手机号获取验证码</text>
			</view>
			
			<view class="form-section">
				<view class="form-item">
					<text class="form-label">手机号</text>
					<input 
						class="form-input" 
						type="number" 
						placeholder="请输入手机号" 
						v-model="formData.phone"
						maxlength="11"
						@blur="validatePhone"
					/>
					<text v-if="errors.phone" class="error-text">{{ errors.phone }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">验证码</text>
					<view class="code-input-container">
						<input 
							class="form-input code-input" 
							type="number" 
							placeholder="请输入验证码" 
							v-model="formData.code"
							maxlength="6"
						/>
						<button 
							class="code-btn" 
							:class="{ disabled: codeCountdown > 0 || !isPhoneValid }"
							:disabled="codeCountdown > 0 || !isPhoneValid"
							@click="sendCode"
						>
							{{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
						</button>
					</view>
					<text v-if="errors.code" class="error-text">{{ errors.code }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">新密码</text>
					<view class="password-input-container">
						<input 
							class="form-input" 
							:type="showPassword ? 'text' : 'password'" 
							placeholder="请输入新密码（至少8位，包含字母和数字）" 
							v-model="formData.newPassword"
							@blur="validatePassword"
						/>
						<text class="password-toggle" @click="togglePassword">
							{{ showPassword ? '隐藏' : '显示' }}
						</text>
					</view>
					<text v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">确认新密码</text>
					<input 
						class="form-input" 
						:type="showConfirmPassword ? 'text' : 'password'" 
						placeholder="请再次输入新密码" 
						v-model="formData.confirmPassword"
						@blur="validateConfirmPassword"
					/>
					<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
				</view>
				
				<button 
					class="reset-btn" 
					:class="{ disabled: !canReset }"
					:disabled="!canReset"
					@click="handleReset"
					:loading="loading"
				>
					{{ loading ? '重置中...' : '重置密码' }}
				</button>
				
				<view class="back-link">
					<text class="link-text" @click="goBack">返回登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import authService from '../../services/auth/authService.js'
	
	export default {
		name: 'ResetPassword',
		data() {
			return {
				formData: {
					phone: '',
					code: '',
					newPassword: '',
					confirmPassword: ''
				},
				errors: {},
				showPassword: false,
				showConfirmPassword: false,
				loading: false,
				codeCountdown: 0,
				countdownTimer: null
			}
		},
		computed: {
			isPhoneValid() {
				return /^1[3-9]\d{9}$/.test(this.formData.phone)
			},
			canReset() {
				return this.isPhoneValid && 
					   this.formData.code && 
					   this.formData.newPassword && 
					   this.formData.confirmPassword && 
					   this.formData.newPassword === this.formData.confirmPassword &&
					   !this.loading
			}
		},
		methods: {
			// 验证手机号
			validatePhone() {
				if (!this.formData.phone) {
					this.errors.phone = '请输入手机号'
				} else if (!this.isPhoneValid) {
					this.errors.phone = '手机号格式不正确'
				} else {
					delete this.errors.phone
				}
				this.$forceUpdate()
			},
			
			// 验证密码
			validatePassword() {
				const password = this.formData.newPassword
				if (!password) {
					this.errors.newPassword = '请输入新密码'
				} else if (password.length < 8) {
					this.errors.newPassword = '密码至少8位'
				} else if (password.length > 20) {
					this.errors.newPassword = '密码不能超过20位'
				} else if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(password)) {
					this.errors.newPassword = '密码必须包含字母和数字'
				} else if (this.isWeakPassword(password)) {
					this.errors.newPassword = '密码强度过低，请使用更复杂的密码'
				} else {
					delete this.errors.newPassword
				}
				this.$forceUpdate()
			},
			
			// 检查密码强度
			isWeakPassword(password) {
				// 常见弱密码列表
				const weakPasswords = [
					'12345678', '87654321', 'password', 'password123',
					'123456789', '111111111', '000000000', 'qwerty123',
					'abc12345', '123abc456'
				]
				
				// 检查是否为常见弱密码
				if (weakPasswords.includes(password.toLowerCase())) {
					return true
				}
				
				// 检查是否为连续数字或字母
				if (/^(\d)\1{7,}$/.test(password) || /^(abc|123|qwe)/i.test(password)) {
					return true
				}
				
				// 检查是否包含手机号
				if (this.formData.phone && password.includes(this.formData.phone.slice(-4))) {
					return true
				}
				
				return false
			},
			
			// 验证确认密码
			validateConfirmPassword() {
				if (!this.formData.confirmPassword) {
					this.errors.confirmPassword = '请确认新密码'
				} else if (this.formData.newPassword !== this.formData.confirmPassword) {
					this.errors.confirmPassword = '两次输入的密码不一致'
				} else {
					delete this.errors.confirmPassword
				}
				this.$forceUpdate()
			},
			
			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword
			},
			
			// 切换确认密码显示
			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword
			},
			
			// 发送验证码
			async sendCode() {
				if (!this.isPhoneValid) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}
				
				try {
					const result = await authService.sendVerificationCode(this.formData.phone, 'reset')
					
					if (result.success) {
						uni.showToast({
							title: '验证码已发送',
							icon: 'success'
						})
						
						// 开始倒计时
						this.startCountdown()
					} else {
						uni.showToast({
							title: result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('发送验证码失败:', error)
					uni.showToast({
						title: '发送验证码失败',
						icon: 'none'
					})
				}
			},
			
			// 开始倒计时
			startCountdown() {
				this.codeCountdown = 60
				this.countdownTimer = setInterval(() => {
					this.codeCountdown--
					if (this.codeCountdown <= 0) {
						clearInterval(this.countdownTimer)
						this.countdownTimer = null
					}
				}, 1000)
			},
			
			// 处理密码重置
			async handleReset() {
				if (!this.canReset) return
				
				// 验证所有字段
				this.validatePhone()
				this.validatePassword()
				this.validateConfirmPassword()
				
				if (Object.keys(this.errors).length > 0) {
					uni.showToast({
						title: '请检查输入信息',
						icon: 'none'
					})
					return
				}
				
				// 防止重复提交
				if (this.loading) return
				this.loading = true
				
				try {
					const result = await authService.resetPassword({
						phone: this.formData.phone,
						code: this.formData.code,
						newPassword: this.formData.newPassword
					})
					
					if (result.success) {
						// 显示成功提示
						uni.showModal({
							title: '重置成功',
							content: '密码重置成功，请使用新密码登录',
							showCancel: false,
							confirmText: '去登录',
							success: (res) => {
								if (res.confirm) {
									// 清空表单数据
									this.clearFormData()
									// 返回登录页
									uni.navigateBack()
								}
							}
						})
					} else {
						// 显示错误信息
						uni.showModal({
							title: '重置失败',
							content: result.message || '密码重置失败，请重试',
							showCancel: false,
							confirmText: '确定'
						})
						
						// 如果是验证码错误，清空验证码输入
						if (result.message && result.message.includes('验证码')) {
							this.formData.code = ''
						}
					}
				} catch (error) {
					console.error('密码重置失败:', error)
					uni.showModal({
						title: '网络错误',
						content: '网络连接失败，请检查网络后重试',
						showCancel: false,
						confirmText: '确定'
					})
				} finally {
					this.loading = false
				}
			},
			
			// 清空表单数据
			clearFormData() {
				this.formData = {
					phone: '',
					code: '',
					newPassword: '',
					confirmPassword: ''
				}
				this.errors = {}
				this.showPassword = false
				this.showConfirmPassword = false
			},
			
			// 返回登录页
			goBack() {
				uni.navigateBack()
			}
		},
		
		onUnload() {
			// 清理倒计时器
			if (this.countdownTimer) {
				clearInterval(this.countdownTimer)
			}
		}
	}
</script>

<style scoped>
	.reset-container {
		padding: 40px 30px;
		min-height: 100vh;
	}
	
	.header-section {
		text-align: center;
		margin-bottom: 40px;
	}
	
	.page-title {
		font-size: 24px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 8px;
	}
	
	.page-subtitle {
		font-size: 14px;
		color: #8E8E93;
		display: block;
	}
	
	.form-section {
		flex: 1;
	}
	
	.form-item {
		margin-bottom: 20px;
	}
	
	.form-label {
		display: block;
		font-size: 16px;
		color: #333333;
		margin-bottom: 8px;
		font-weight: 500;
	}
	
	.form-input {
		width: 100%;
		height: 44px;
		padding: 0 15px;
		border: 1px solid #E5E5E5;
		border-radius: 8px;
		font-size: 16px;
		background-color: #FFFFFF;
		box-sizing: border-box;
	}
	
	.form-input:focus {
		border-color: #007AFF;
	}
	
	.code-input-container {
		display: flex;
		align-items: center;
		gap: 10px;
	}
	
	.code-input {
		flex: 1;
	}
	
	.code-btn {
		height: 44px;
		padding: 0 16px;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 8px;
		font-size: 14px;
		white-space: nowrap;
	}
	
	.code-btn.disabled {
		background-color: #C7C7CC;
		color: #8E8E93;
	}
	
	.password-input-container {
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.password-toggle {
		position: absolute;
		right: 15px;
		color: #007AFF;
		font-size: 14px;
		cursor: pointer;
	}
	
	.error-text {
		display: block;
		color: #FF3B30;
		font-size: 12px;
		margin-top: 5px;
	}
	
	.reset-btn {
		width: 100%;
		height: 50px;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 25px;
		font-size: 18px;
		font-weight: 600;
		margin-bottom: 20px;
	}
	
	.reset-btn.disabled {
		background-color: #C7C7CC;
		color: #8E8E93;
	}
	
	.back-link {
		text-align: center;
		margin-top: 20px;
	}
	
	.link-text {
		font-size: 16px;
		color: #007AFF;
		text-decoration: underline;
	}
</style>