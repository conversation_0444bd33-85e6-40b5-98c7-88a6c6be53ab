/**
 * 健康报告管理功能集成测试
 * 测试报告的增删改查、数据验证等完整流程
 */

import reportService from '@/services/report/index.js'
import { ReportValidator } from '@/utils/validation/reportValidator.js'
import { useReportStore } from '@/stores/report.js'
import { createPinia, setActivePinia } from 'pinia'

// 模拟数据
const mockUser = {
  id: 'test_user_001',
  username: 'testuser'
}

const mockReportData = {
  userId: 'test_user_001',
  hospitalName: '测试医院',
  reportDate: '2024-01-15',
  doctorName: '张医生',
  department: '内科',
  reportType: '血液检查',
  originalImagePath: '/test/image.jpg',
  ocrText: '血糖: 5.6 mmol/L\n血压: 120/80 mmHg',
  notes: '患者状态良好',
  indicators: [
    {
      name: '血糖',
      value: '5.6',
      unit: 'mmol/L',
      referenceRange: '3.9-6.1',
      isAbnormal: false,
      category: '血液指标',
      description: '空腹血糖正常'
    },
    {
      name: '血压收缩压',
      value: '120',
      unit: 'mmHg',
      referenceRange: '90-140',
      isAbnormal: false,
      category: '血液指标',
      description: '血压正常'
    },
    {
      name: '胆固醇',
      value: '6.8',
      unit: 'mmol/L',
      referenceRange: '3.1-5.2',
      isAbnormal: true,
      category: '血液指标',
      description: '胆固醇偏高，需要注意饮食'
    }
  ]
}

const mockInvalidReportData = {
  userId: 'test_user_001',
  hospitalName: '', // 无效：空医院名称
  reportDate: '2025-12-31', // 无效：未来日期
  indicators: [
    {
      name: '', // 无效：空指标名称
      value: 'invalid', // 无效：非数值
      unit: 'mmol/L',
      category: '血液指标'
    }
  ]
}

describe('健康报告管理集成测试', () => {
  let reportStore
  let createdReportId
  
  beforeEach(() => {
    // 设置 Pinia
    setActivePinia(createPinia())
    reportStore = useReportStore()
    
    // 清理测试数据
    reportStore.setReports([])
  })
  
  afterEach(async () => {
    // 清理创建的测试报告
    if (createdReportId) {
      try {
        await reportService.permanentDeleteReport(createdReportId)
      } catch (error) {
        console.warn('清理测试数据失败:', error)
      }
      createdReportId = null
    }
  })
  
  describe('报告创建功能', () => {
    test('应该能够创建有效的健康报告', async () => {
      const report = await reportService.createReport(mockReportData)
      createdReportId = report.id
      
      expect(report).toBeDefined()
      expect(report.id).toBeDefined()
      expect(report.hospitalName).toBe(mockReportData.hospitalName)
      expect(report.indicators).toHaveLength(3)
      expect(report.createdAt).toBeDefined()
      expect(report.updatedAt).toBeDefined()
    })
    
    test('应该拒绝创建无效的健康报告', async () => {
      await expect(reportService.createReport(mockInvalidReportData))
        .rejects.toThrow('创建报告失败')
    })
    
    test('创建报告后应该更新store状态', async () => {
      const report = await reportService.createReport(mockReportData)
      createdReportId = report.id
      
      reportStore.addReport(report)
      
      expect(reportStore.reports).toHaveLength(1)
      expect(reportStore.reports[0].id).toBe(report.id)
      expect(reportStore.statistics.totalReports).toBe(1)
      expect(reportStore.statistics.abnormalCount).toBe(1) // 有一个异常指标
    })
  })
  
  describe('报告查询功能', () => {
    beforeEach(async () => {
      const report = await reportService.createReport(mockReportData)
      createdReportId = report.id
      reportStore.addReport(report)
    })
    
    test('应该能够根据ID获取报告详情', async () => {
      const report = await reportService.getReportById(createdReportId)
      
      expect(report).toBeDefined()
      expect(report.id).toBe(createdReportId)
      expect(report.hospitalName).toBe(mockReportData.hospitalName)
      expect(report.indicators).toHaveLength(3)
    })
    
    test('应该能够获取用户的报告列表', async () => {
      const reports = await reportService.getReports({
        userId: mockUser.id,
        page: 1,
        pageSize: 10
      })
      
      expect(reports).toHaveLength(1)
      expect(reports[0].id).toBe(createdReportId)
    })
    
    test('应该能够搜索报告', async () => {
      const searchResults = await reportService.searchReports({
        keyword: '测试医院',
        userId: mockUser.id
      })
      
      expect(searchResults).toHaveLength(1)
      expect(searchResults[0].hospitalName).toContain('测试医院')
    })
    
    test('应该能够获取报告统计信息', async () => {
      const stats = await reportService.getReportStatistics(mockUser.id)
      
      expect(stats.totalReports).toBe(1)
      expect(stats.abnormalReports).toBe(1)
      expect(stats.latestReport).toBeDefined()
      expect(stats.categories).toHaveProperty('血液指标')
      expect(stats.hospitals).toHaveProperty('测试医院')
    })
  })
  
  describe('报告更新功能', () => {
    beforeEach(async () => {
      const report = await reportService.createReport(mockReportData)
      createdReportId = report.id
      reportStore.addReport(report)
    })
    
    test('应该能够更新报告基本信息', async () => {
      const updateData = {
        hospitalName: '更新后的医院',
        doctorName: '李医生',
        notes: '更新后的备注'
      }
      
      const updatedReport = await reportService.updateReport(createdReportId, updateData)
      
      expect(updatedReport.hospitalName).toBe('更新后的医院')
      expect(updatedReport.doctorName).toBe('李医生')
      expect(updatedReport.notes).toBe('更新后的备注')
      expect(updatedReport.updatedAt).not.toBe(updatedReport.createdAt)
    })
    
    test('应该能够更新健康指标', async () => {
      const newIndicators = [
        {
          name: '新指标',
          value: '10.5',
          unit: 'mg/dL',
          referenceRange: '8.0-12.0',
          isAbnormal: false,
          category: '生化指标',
          description: '新增的指标'
        }
      ]
      
      const updatedReport = await reportService.updateReport(createdReportId, {
        indicators: newIndicators
      })
      
      expect(updatedReport.indicators).toHaveLength(1)
      expect(updatedReport.indicators[0].name).toBe('新指标')
    })
    
    test('应该拒绝无效的更新数据', async () => {
      const invalidUpdateData = {
        hospitalName: '', // 无效：空医院名称
        reportDate: '2025-12-31' // 无效：未来日期
      }
      
      await expect(reportService.updateReport(createdReportId, invalidUpdateData))
        .rejects.toThrow('更新报告失败')
    })
    
    test('更新报告后应该同步store状态', async () => {
      const updateData = { hospitalName: '更新后的医院' }
      const updatedReport = await reportService.updateReport(createdReportId, updateData)
      
      reportStore.updateReport(createdReportId, updatedReport)
      
      const storeReport = reportStore.reports.find(r => r.id === createdReportId)
      expect(storeReport.hospitalName).toBe('更新后的医院')
    })
  })
  
  describe('报告删除功能', () => {
    beforeEach(async () => {
      const report = await reportService.createReport(mockReportData)
      createdReportId = report.id
      reportStore.addReport(report)
    })
    
    test('应该能够软删除报告', async () => {
      const result = await reportService.deleteReport(createdReportId)
      
      expect(result).toBe(true)
      
      // 验证报告被标记为已删除
      await expect(reportService.getReportById(createdReportId))
        .rejects.toThrow('报告不存在')
    })
    
    test('应该能够永久删除报告', async () => {
      const result = await reportService.permanentDeleteReport(createdReportId)
      
      expect(result).toBe(true)
      createdReportId = null // 已被永久删除，无需清理
    })
    
    test('删除报告后应该更新store状态', async () => {
      await reportService.deleteReport(createdReportId)
      reportStore.deleteReport(createdReportId)
      
      expect(reportStore.reports).toHaveLength(0)
      expect(reportStore.statistics.totalReports).toBe(0)
    })
  })
  
  describe('数据验证功能', () => {
    test('应该能够验证完整的报告数据', () => {
      const validation = ReportValidator.validateCompleteReport(mockReportData)
      
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toEqual({})
    })
    
    test('应该能够检测无效的报告数据', () => {
      const validation = ReportValidator.validateCompleteReport(mockInvalidReportData)
      
      expect(validation.isValid).toBe(false)
      expect(validation.errors).toHaveProperty('hospitalName')
      expect(validation.errors).toHaveProperty('reportDate')
      expect(validation.errors).toHaveProperty('indicator_0_name')
      expect(validation.errors).toHaveProperty('indicator_0_value')
    })
    
    test('应该能够检查数据完整性', () => {
      const integrity = ReportValidator.checkDataIntegrity(mockReportData)
      
      expect(integrity.isComplete).toBe(true)
      expect(integrity.completeness).toBeGreaterThan(80)
      expect(integrity.issues).toHaveLength(0)
    })
    
    test('应该能够生成验证报告', () => {
      const validationReport = ReportValidator.generateValidationReport(mockReportData)
      
      expect(validationReport.validation.isValid).toBe(true)
      expect(validationReport.integrity.isComplete).toBe(true)
      expect(validationReport.summary.status).toBe('PASS')
      expect(validationReport.summary.score).toBeGreaterThan(0)
    })
  })
  
  describe('Store状态管理', () => {
    test('应该能够正确管理报告列表状态', () => {
      const mockReports = [
        { ...mockReportData, id: 'report1', reportDate: '2024-01-15' },
        { ...mockReportData, id: 'report2', reportDate: '2024-01-10' }
      ]
      
      reportStore.setReports(mockReports)
      
      expect(reportStore.reports).toHaveLength(2)
      expect(reportStore.filteredReports).toHaveLength(2)
      // 应该按时间倒序排列
      expect(reportStore.filteredReports[0].id).toBe('report1')
    })
    
    test('应该能够正确应用筛选条件', () => {
      const mockReports = [
        { 
          ...mockReportData, 
          id: 'report1', 
          hospitalName: '第一医院',
          indicators: [{ ...mockReportData.indicators[0], isAbnormal: true }]
        },
        { 
          ...mockReportData, 
          id: 'report2', 
          hospitalName: '第二医院',
          indicators: [{ ...mockReportData.indicators[0], isAbnormal: false }]
        }
      ]
      
      reportStore.setReports(mockReports)
      
      // 测试医院筛选
      reportStore.updateFilter({ hospital: '第一' })
      expect(reportStore.filteredReports).toHaveLength(1)
      expect(reportStore.filteredReports[0].hospitalName).toContain('第一')
      
      // 测试异常筛选
      reportStore.updateFilter({ hospital: '', abnormalOnly: true })
      expect(reportStore.filteredReports).toHaveLength(1)
      expect(reportStore.filteredReports[0].id).toBe('report1')
    })
    
    test('应该能够正确计算统计信息', () => {
      const mockReports = [
        { 
          ...mockReportData, 
          id: 'report1',
          indicators: [
            { ...mockReportData.indicators[0], isAbnormal: true, category: '血液指标' }
          ]
        },
        { 
          ...mockReportData, 
          id: 'report2',
          indicators: [
            { ...mockReportData.indicators[0], isAbnormal: false, category: '尿液指标' }
          ]
        }
      ]
      
      reportStore.setReports(mockReports)
      
      expect(reportStore.statistics.totalReports).toBe(2)
      expect(reportStore.statistics.abnormalCount).toBe(1)
      expect(reportStore.categoryStats).toHaveLength(2)
      expect(reportStore.hasAbnormalIndicators).toBe(true)
    })
  })
  
  describe('错误处理', () => {
    test('应该正确处理不存在的报告ID', async () => {
      await expect(reportService.getReportById('nonexistent_id'))
        .rejects.toThrow('报告不存在')
    })
    
    test('应该正确处理无效的用户ID', async () => {
      const reports = await reportService.getReports({ userId: 'invalid_user' })
      expect(reports).toHaveLength(0)
    })
    
    test('应该正确处理网络错误', async () => {
      // 模拟网络错误
      const originalCreate = reportService.reportRepo.create
      reportService.reportRepo.create = jest.fn().mockRejectedValue(new Error('Network error'))
      
      await expect(reportService.createReport(mockReportData))
        .rejects.toThrow('创建报告失败')
      
      // 恢复原始方法
      reportService.reportRepo.create = originalCreate
    })
  })
  
  describe('性能测试', () => {
    test('批量创建报告应该在合理时间内完成', async () => {
      const startTime = Date.now()
      const promises = []
      
      for (let i = 0; i < 10; i++) {
        const reportData = {
          ...mockReportData,
          hospitalName: `测试医院${i}`,
          userId: `test_user_${i}`
        }
        promises.push(reportService.createReport(reportData))
      }
      
      const reports = await Promise.all(promises)
      const endTime = Date.now()
      
      expect(reports).toHaveLength(10)
      expect(endTime - startTime).toBeLessThan(5000) // 应该在5秒内完成
      
      // 清理测试数据
      for (const report of reports) {
        await reportService.permanentDeleteReport(report.id)
      }
    })
    
    test('大量数据筛选应该保持响应性', () => {
      const mockReports = Array.from({ length: 1000 }, (_, i) => ({
        ...mockReportData,
        id: `report_${i}`,
        hospitalName: i % 2 === 0 ? '第一医院' : '第二医院',
        reportDate: new Date(2024, 0, i % 30 + 1).toISOString()
      }))
      
      const startTime = Date.now()
      reportStore.setReports(mockReports)
      reportStore.updateFilter({ hospital: '第一' })
      const filteredResults = reportStore.filteredReports
      const endTime = Date.now()
      
      expect(filteredResults).toHaveLength(500)
      expect(endTime - startTime).toBeLessThan(1000) // 应该在1秒内完成
    })
  })
})

// 辅助函数
function createMockReport(overrides = {}) {
  return {
    ...mockReportData,
    id: `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }
}

function createMockIndicator(overrides = {}) {
  return {
    id: `indicator_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: '测试指标',
    value: '10.0',
    unit: 'mg/dL',
    referenceRange: '8.0-12.0',
    isAbnormal: false,
    category: '血液指标',
    description: '测试指标描述',
    createdAt: new Date().toISOString(),
    ...overrides
  }
}