/**
 * 平台常量定义单元测试
 */

import {
  PLATFORM_TYPES,
  SYSTEM_TYPES,
  CAMERA_CONFIG,
  STORAGE_CONFIG,
  SHARE_CONFIG,
  NETWORK_CONFIG,
  FILE_CONFIG,
  PERMISSION_CONFIG,
  ERROR_CODES
} from '../../utils/platform/constants.js'

describe('平台常量定义测试', () => {
  
  describe('PLATFORM_TYPES', () => {
    test('应该包含所有平台类型', () => {
      expect(PLATFORM_TYPES).toHaveProperty('APP_PLUS')
      expect(PLATFORM_TYPES).toHaveProperty('MP_WEIXIN')
      expect(PLATFORM_TYPES).toHaveProperty('MP_ALIPAY')
      expect(PLATFORM_TYPES).toHaveProperty('MP_BAIDU')
      expect(PLATFORM_TYPES).toHaveProperty('H5')
      expect(PLATFORM_TYPES).toHaveProperty('MP')
    })

    test('平台类型值应该是字符串', () => {
      Object.values(PLATFORM_TYPES).forEach(value => {
        expect(typeof value).toBe('string')
        expect(value.length).toBeGreaterThan(0)
      })
    })
  })

  describe('SYSTEM_TYPES', () => {
    test('应该包含所有系统类型', () => {
      expect(SYSTEM_TYPES).toHaveProperty('IOS')
      expect(SYSTEM_TYPES).toHaveProperty('ANDROID')
      expect(SYSTEM_TYPES).toHaveProperty('WINDOWS')
      expect(SYSTEM_TYPES).toHaveProperty('MAC')
    })
  })

  describe('CAMERA_CONFIG', () => {
    test('应该包含所有平台的相机配置', () => {
      expect(CAMERA_CONFIG).toHaveProperty('APP_PLUS')
      expect(CAMERA_CONFIG).toHaveProperty('MP_WEIXIN')
      expect(CAMERA_CONFIG).toHaveProperty('H5')
    })

    test('每个平台配置应该包含必要字段', () => {
      Object.values(CAMERA_CONFIG).forEach(config => {
        expect(config).toHaveProperty('sourceType')
        expect(config).toHaveProperty('sizeType')
        expect(config).toHaveProperty('maxCount')
        expect(config).toHaveProperty('quality')
        
        expect(Array.isArray(config.sourceType)).toBe(true)
        expect(Array.isArray(config.sizeType)).toBe(true)
        expect(typeof config.maxCount).toBe('number')
        expect(typeof config.quality).toBe('number')
      })
    })
  })

  describe('STORAGE_CONFIG', () => {
    test('应该包含存储配置', () => {
      expect(STORAGE_CONFIG).toHaveProperty('KEY_PREFIX')
      expect(STORAGE_CONFIG).toHaveProperty('STORAGE_TYPES')
      expect(STORAGE_CONFIG).toHaveProperty('LIMITS')
    })

    test('存储类型应该正确定义', () => {
      const { STORAGE_TYPES } = STORAGE_CONFIG
      expect(STORAGE_TYPES).toHaveProperty('LOCAL')
      expect(STORAGE_TYPES).toHaveProperty('SESSION')
      expect(STORAGE_TYPES).toHaveProperty('SECURE')
    })

    test('存储限制应该包含所有平台', () => {
      const { LIMITS } = STORAGE_CONFIG
      expect(LIMITS).toHaveProperty('APP_PLUS')
      expect(LIMITS).toHaveProperty('MP_WEIXIN')
      expect(LIMITS).toHaveProperty('H5')
      
      Object.values(LIMITS).forEach(limit => {
        expect(limit).toHaveProperty('maxSize')
        expect(limit).toHaveProperty('supportSecure')
        expect(typeof limit.maxSize).toBe('number')
        expect(typeof limit.supportSecure).toBe('boolean')
      })
    })
  })

  describe('SHARE_CONFIG', () => {
    test('应该包含分享配置', () => {
      expect(SHARE_CONFIG).toHaveProperty('SHARE_TYPES')
      expect(SHARE_CONFIG).toHaveProperty('PLATFORM_SUPPORT')
    })

    test('分享类型应该正确定义', () => {
      const { SHARE_TYPES } = SHARE_CONFIG
      expect(SHARE_TYPES).toHaveProperty('TEXT')
      expect(SHARE_TYPES).toHaveProperty('IMAGE')
      expect(SHARE_TYPES).toHaveProperty('FILE')
      expect(SHARE_TYPES).toHaveProperty('LINK')
    })
  })

  describe('NETWORK_CONFIG', () => {
    test('应该包含网络配置', () => {
      expect(NETWORK_CONFIG).toHaveProperty('TIMEOUT')
      expect(NETWORK_CONFIG).toHaveProperty('RETRY_COUNT')
      expect(NETWORK_CONFIG).toHaveProperty('CONCURRENT_LIMIT')
    })

    test('超时时间应该是合理的数值', () => {
      Object.values(NETWORK_CONFIG.TIMEOUT).forEach(timeout => {
        expect(typeof timeout).toBe('number')
        expect(timeout).toBeGreaterThan(0)
        expect(timeout).toBeLessThan(60000) // 小于60秒
      })
    })
  })

  describe('FILE_CONFIG', () => {
    test('应该包含文件配置', () => {
      expect(FILE_CONFIG).toHaveProperty('IMAGE_FORMATS')
      expect(FILE_CONFIG).toHaveProperty('SIZE_LIMITS')
    })

    test('图片格式应该是数组', () => {
      Object.values(FILE_CONFIG.IMAGE_FORMATS).forEach(formats => {
        expect(Array.isArray(formats)).toBe(true)
        expect(formats.length).toBeGreaterThan(0)
      })
    })

    test('文件大小限制应该是合理数值', () => {
      const { SIZE_LIMITS } = FILE_CONFIG
      Object.values(SIZE_LIMITS).forEach(category => {
        Object.values(category).forEach(size => {
          expect(typeof size).toBe('number')
          expect(size).toBeGreaterThan(0)
        })
      })
    })
  })

  describe('PERMISSION_CONFIG', () => {
    test('应该包含权限配置', () => {
      expect(PERMISSION_CONFIG).toHaveProperty('REQUIRED_PERMISSIONS')
      expect(PERMISSION_CONFIG).toHaveProperty('PERMISSION_DESC')
    })

    test('权限列表应该是数组', () => {
      Object.values(PERMISSION_CONFIG.REQUIRED_PERMISSIONS).forEach(permissions => {
        expect(Array.isArray(permissions)).toBe(true)
      })
    })
  })

  describe('ERROR_CODES', () => {
    test('应该包含所有错误码', () => {
      expect(ERROR_CODES).toHaveProperty('PLATFORM_NOT_SUPPORTED')
      expect(ERROR_CODES).toHaveProperty('FEATURE_NOT_AVAILABLE')
      expect(ERROR_CODES).toHaveProperty('PERMISSION_DENIED')
      expect(ERROR_CODES).toHaveProperty('FILE_TOO_LARGE')
      expect(ERROR_CODES).toHaveProperty('NETWORK_ERROR')
      expect(ERROR_CODES).toHaveProperty('STORAGE_FULL')
    })

    test('错误码应该是字符串', () => {
      Object.values(ERROR_CODES).forEach(code => {
        expect(typeof code).toBe('string')
        expect(code.length).toBeGreaterThan(0)
      })
    })
  })
})