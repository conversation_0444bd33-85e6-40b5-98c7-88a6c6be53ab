/**
 * 实时数据验证工具
 */

// 验证规则定义
const validationRules = {
  // 用户信息验证
  user: {
    phone: {
      required: true,
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码'
    },
    password: {
      required: true,
      minLength: 6,
      maxLength: 20,
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,
      message: '密码需包含字母和数字，长度6-20位'
    },
    name: {
      required: true,
      minLength: 2,
      maxLength: 20,
      pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/,
      message: '姓名只能包含中文、英文和空格'
    },
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入正确的邮箱地址'
    },
    idCard: {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号码'
    }
  },
  
  // 健康报告验证
  report: {
    title: {
      required: true,
      minLength: 2,
      maxLength: 50,
      message: '报告标题长度应在2-50字符之间'
    },
    hospitalName: {
      required: true,
      minLength: 2,
      maxLength: 50,
      message: '医院名称长度应在2-50字符之间'
    },
    doctorName: {
      minLength: 2,
      maxLength: 20,
      pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/,
      message: '医生姓名只能包含中文、英文和空格'
    },
    reportDate: {
      required: true,
      validator: (value) => {
        const date = new Date(value)
        const now = new Date()
        return date <= now && date >= new Date('1900-01-01')
      },
      message: '请选择正确的检查日期'
    }
  },
  
  // 健康指标验证
  indicator: {
    bloodPressureHigh: {
      type: 'number',
      min: 60,
      max: 300,
      message: '收缩压应在60-300mmHg之间'
    },
    bloodPressureLow: {
      type: 'number',
      min: 30,
      max: 200,
      message: '舒张压应在30-200mmHg之间'
    },
    heartRate: {
      type: 'number',
      min: 30,
      max: 220,
      message: '心率应在30-220次/分之间'
    },
    bloodSugar: {
      type: 'number',
      min: 1,
      max: 50,
      message: '血糖应在1-50mmol/L之间'
    },
    cholesterol: {
      type: 'number',
      min: 1,
      max: 20,
      message: '胆固醇应在1-20mmol/L之间'
    },
    weight: {
      type: 'number',
      min: 20,
      max: 300,
      message: '体重应在20-300kg之间'
    },
    height: {
      type: 'number',
      min: 50,
      max: 250,
      message: '身高应在50-250cm之间'
    }
  }
}

// 验证器类
class Validator {
  constructor() {
    this.errors = {}
    this.isValidating = false
  }

  /**
   * 验证单个字段
   */
  validateField(category, field, value, customRules = {}) {
    const rules = { ...validationRules[category]?.[field], ...customRules }
    if (!rules) return { isValid: true, message: '' }

    const errors = []

    // 必填验证
    if (rules.required && (!value || value.toString().trim() === '')) {
      errors.push('此字段为必填项')
    }

    // 如果值为空且非必填，跳过其他验证
    if (!value && !rules.required) {
      return { isValid: true, message: '' }
    }

    // 类型验证
    if (rules.type === 'number') {
      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        errors.push('请输入有效的数字')
      } else {
        // 数值范围验证
        if (rules.min !== undefined && numValue < rules.min) {
          errors.push(`数值不能小于${rules.min}`)
        }
        if (rules.max !== undefined && numValue > rules.max) {
          errors.push(`数值不能大于${rules.max}`)
        }
      }
    }

    // 长度验证
    if (rules.minLength && value.length < rules.minLength) {
      errors.push(`长度不能少于${rules.minLength}个字符`)
    }
    if (rules.maxLength && value.length > rules.maxLength) {
      errors.push(`长度不能超过${rules.maxLength}个字符`)
    }

    // 正则验证
    if (rules.pattern && !rules.pattern.test(value)) {
      errors.push(rules.message || '格式不正确')
    }

    // 自定义验证器
    if (rules.validator && !rules.validator(value)) {
      errors.push(rules.message || '验证失败')
    }

    return {
      isValid: errors.length === 0,
      message: errors[0] || '',
      errors
    }
  }

  /**
   * 验证整个表单
   */
  validateForm(category, data, customRules = {}) {
    const results = {}
    let isFormValid = true

    for (const [field, value] of Object.entries(data)) {
      const result = this.validateField(category, field, value, customRules[field])
      results[field] = result
      if (!result.isValid) {
        isFormValid = false
      }
    }

    return {
      isValid: isFormValid,
      results,
      errors: Object.fromEntries(
        Object.entries(results)
          .filter(([_, result]) => !result.isValid)
          .map(([field, result]) => [field, result.message])
      )
    }
  }

  /**
   * 实时验证（防抖）
   */
  validateRealtime(category, field, value, callback, delay = 300) {
    // 清除之前的定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }

    this.debounceTimer = setTimeout(() => {
      const result = this.validateField(category, field, value)
      callback(result)
    }, delay)
  }

  /**
   * 健康指标异常检测
   */
  checkHealthIndicatorAbnormal(indicator, value, userInfo = {}) {
    const abnormalRules = {
      bloodPressureHigh: {
        normal: { min: 90, max: 140 },
        getStatus: (val) => {
          if (val < 90) return { level: 'low', message: '血压偏低' }
          if (val > 140) return { level: 'high', message: '血压偏高' }
          return { level: 'normal', message: '血压正常' }
        }
      },
      bloodPressureLow: {
        normal: { min: 60, max: 90 },
        getStatus: (val) => {
          if (val < 60) return { level: 'low', message: '血压偏低' }
          if (val > 90) return { level: 'high', message: '血压偏高' }
          return { level: 'normal', message: '血压正常' }
        }
      },
      heartRate: {
        normal: { min: 60, max: 100 },
        getStatus: (val) => {
          if (val < 60) return { level: 'low', message: '心率偏慢' }
          if (val > 100) return { level: 'high', message: '心率偏快' }
          return { level: 'normal', message: '心率正常' }
        }
      },
      bloodSugar: {
        normal: { min: 3.9, max: 6.1 },
        getStatus: (val) => {
          if (val < 3.9) return { level: 'low', message: '血糖偏低' }
          if (val > 6.1) return { level: 'high', message: '血糖偏高' }
          return { level: 'normal', message: '血糖正常' }
        }
      },
      cholesterol: {
        normal: { min: 3.1, max: 5.7 },
        getStatus: (val) => {
          if (val < 3.1) return { level: 'low', message: '胆固醇偏低' }
          if (val > 5.7) return { level: 'high', message: '胆固醇偏高' }
          return { level: 'normal', message: '胆固醇正常' }
        }
      }
    }

    const rule = abnormalRules[indicator]
    if (!rule) return { level: 'unknown', message: '未知指标' }

    const numValue = parseFloat(value)
    if (isNaN(numValue)) return { level: 'invalid', message: '无效数值' }

    return rule.getStatus(numValue)
  }

  /**
   * BMI计算和评估
   */
  calculateBMI(weight, height) {
    const w = parseFloat(weight)
    const h = parseFloat(height) / 100 // 转换为米

    if (isNaN(w) || isNaN(h) || h <= 0) {
      return { bmi: null, status: 'invalid', message: '请输入有效的身高体重' }
    }

    const bmi = (w / (h * h)).toFixed(1)
    let status, message

    if (bmi < 18.5) {
      status = 'underweight'
      message = '体重偏轻'
    } else if (bmi < 24) {
      status = 'normal'
      message = '体重正常'
    } else if (bmi < 28) {
      status = 'overweight'
      message = '体重超重'
    } else {
      status = 'obese'
      message = '肥胖'
    }

    return { bmi: parseFloat(bmi), status, message }
  }
}

// 创建全局验证器实例
const validator = new Validator()

// 导出验证相关功能
export {
  validator,
  validationRules,
  Validator
}

export default validator