<template>
  <view class="page-container">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-left" @click="goBack">
        <text class="icon-back">‹</text>
      </view>
      <view class="navbar-title">隐私政策</view>
      <view class="navbar-right"></view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content" scroll-y>
      <!-- 更新时间 -->
      <view class="update-info">
        <text class="update-text">最后更新时间：{{ updateTime }}</text>
      </view>

      <!-- 隐私政策内容 -->
      <view class="privacy-content">
        <!-- 引言 -->
        <view class="section">
          <view class="section-title">引言</view>
          <view class="section-content">
            <text class="content-text">
              健康报告应用（以下简称"我们"）非常重视用户的隐私保护。本隐私政策详细说明了我们如何收集、使用、存储和保护您的个人信息。请您仔细阅读本政策，如果您不同意本政策的任何内容，请不要使用我们的服务。
            </text>
          </view>
        </view>

        <!-- 信息收集 -->
        <view class="section">
          <view class="section-title">我们收集的信息</view>
          <view class="section-content">
            <view class="subsection">
              <view class="subsection-title">1. 账户信息</view>
              <text class="content-text">
                • 手机号码：用于账户注册、登录验证和安全通知\n
                • 昵称和头像：用于个性化显示和用户识别\n
                • 密码：经过加密存储，用于账户安全验证
              </text>
            </view>

            <view class="subsection">
              <view class="subsection-title">2. 健康数据</view>
              <text class="content-text">
                • 健康检查报告：包括检查项目、数值、图片等\n
                • 健康指标趋势：用于生成分析报告和健康建议\n
                • 医疗机构信息：检查报告的来源机构信息
              </text>
            </view>

            <view class="subsection">
              <view class="subsection-title">3. 设备信息</view>
              <text class="content-text">
                • 设备型号、操作系统版本\n
                • 应用版本、网络状态\n
                • 设备唯一标识符（用于安全验证）
              </text>
            </view>

            <view class="subsection">
              <view class="subsection-title">4. 使用信息</view>
              <text class="content-text">
                • 应用使用记录、功能使用频率\n
                • 错误日志和崩溃报告\n
                • 性能数据（用于优化应用体验）
              </text>
            </view>
          </view>
        </view>

        <!-- 信息使用 -->
        <view class="section">
          <view class="section-title">信息使用目的</view>
          <view class="section-content">
            <view class="purpose-item">
              <view class="purpose-title">✓ 提供核心服务</view>
              <text class="purpose-desc">存储和管理您的健康检查报告，提供数据分析和趋势展示</text>
            </view>

            <view class="purpose-item">
              <view class="purpose-title">✓ 账户安全</view>
              <text class="purpose-desc">验证用户身份，检测异常登录，保护账户安全</text>
            </view>

            <view class="purpose-item">
              <view class="purpose-title">✓ 服务优化</view>
              <text class="purpose-desc">分析使用数据，改进产品功能和用户体验</text>
            </view>

            <view class="purpose-item">
              <view class="purpose-title">✓ 技术支持</view>
              <text class="purpose-desc">诊断和修复技术问题，提供客户服务</text>
            </view>
          </view>
        </view>

        <!-- 信息保护 -->
        <view class="section">
          <view class="section-title">信息保护措施</view>
          <view class="section-content">
            <view class="protection-item">
              <view class="protection-icon">🔒</view>
              <view class="protection-content">
                <view class="protection-title">数据加密</view>
                <text class="protection-desc">使用AES-256加密算法保护敏感数据，传输过程使用HTTPS协议</text>
              </view>
            </view>

            <view class="protection-item">
              <view class="protection-icon">🛡️</view>
              <view class="protection-content">
                <view class="protection-title">访问控制</view>
                <text class="protection-desc">严格限制数据访问权限，只有授权人员才能访问必要数据</text>
              </view>
            </view>

            <view class="protection-item">
              <view class="protection-icon">📱</view>
              <view class="protection-content">
                <view class="protection-title">本地存储</view>
                <text class="protection-desc">健康数据优先存储在本地设备，减少网络传输风险</text>
              </view>
            </view>

            <view class="protection-item">
              <view class="protection-icon">🔍</view>
              <view class="protection-content">
                <view class="protection-title">安全监控</view>
                <text class="protection-desc">实时监控异常访问和安全威胁，及时响应安全事件</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 信息共享 -->
        <view class="section">
          <view class="section-title">信息共享原则</view>
          <view class="section-content">
            <view class="share-principle">
              <view class="principle-title">❌ 我们不会</view>
              <text class="principle-content">
                • 出售、出租或交易您的个人信息\n
                • 向第三方提供您的健康数据\n
                • 将您的信息用于营销推广\n
                • 在未经授权的情况下公开您的信息
              </text>
            </view>

            <view class="share-principle">
              <view class="principle-title">⚖️ 法律要求</view>
              <text class="principle-content">
                仅在法律法规要求或司法机关依法要求的情况下，我们可能会提供必要的信息配合调查。
              </text>
            </view>
          </view>
        </view>

        <!-- 用户权利 -->
        <view class="section">
          <view class="section-title">您的权利</view>
          <view class="section-content">
            <view class="right-item">
              <view class="right-title">📋 查看权</view>
              <text class="right-desc">您可以随时查看我们收集的关于您的个人信息</text>
            </view>

            <view class="right-item">
              <view class="right-title">✏️ 修改权</view>
              <text class="right-desc">您可以更新、修改或补充您的个人信息</text>
            </view>

            <view class="right-item">
              <view class="right-title">🗑️ 删除权</view>
              <text class="right-desc">您可以要求删除您的个人信息和账户数据</text>
            </view>

            <view class="right-item">
              <view class="right-title">📤 导出权</view>
              <text class="right-desc">您可以导出您的健康数据，便于迁移到其他平台</text>
            </view>
          </view>
        </view>

        <!-- 数据保留 -->
        <view class="section">
          <view class="section-title">数据保留期限</view>
          <view class="section-content">
            <text class="content-text">
              • 账户信息：在您使用服务期间持续保留，账户注销后30天内删除\n
              • 健康数据：根据您的设置保留，最长不超过7年\n
              • 日志数据：保留6个月后自动删除\n
              • 备份数据：保留90天后自动删除
            </text>
          </view>
        </view>

        <!-- 第三方服务 -->
        <view class="section">
          <view class="section-title">第三方服务</view>
          <view class="section-content">
            <text class="content-text">
              我们的应用可能使用以下第三方服务，这些服务有各自的隐私政策：
            </text>
            
            <view class="third-party-item">
              <view class="third-party-name">微信小程序平台</view>
              <text class="third-party-desc">用于微信小程序版本的运行，遵循微信隐私政策</text>
            </view>

            <view class="third-party-item">
              <view class="third-party-name">OCR识别服务</view>
              <text class="third-party-desc">用于识别检查报告图片中的文字信息</text>
            </view>

            <view class="third-party-item">
              <view class="third-party-name">云存储服务</view>
              <text class="third-party-desc">用于数据备份和同步，所有数据均经过加密处理</text>
            </view>
          </view>
        </view>

        <!-- 政策更新 -->
        <view class="section">
          <view class="section-title">政策更新</view>
          <view class="section-content">
            <text class="content-text">
              我们可能会不定期更新本隐私政策。重大变更时，我们会通过应用内通知、邮件或其他方式告知您。继续使用我们的服务即表示您同意更新后的隐私政策。
            </text>
          </view>
        </view>

        <!-- 联系我们 -->
        <view class="section">
          <view class="section-title">联系我们</view>
          <view class="section-content">
            <text class="content-text">
              如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：
            </text>
            
            <view class="contact-item">
              <text class="contact-label">邮箱：</text>
              <text class="contact-value"><EMAIL></text>
            </view>

            <view class="contact-item">
              <text class="contact-label">客服电话：</text>
              <text class="contact-value">400-123-4567</text>
            </view>

            <view class="contact-item">
              <text class="contact-label">工作时间：</text>
              <text class="contact-value">周一至周五 9:00-18:00</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="btn btn-secondary" @click="exportData">导出我的数据</button>
        <button class="btn btn-danger" @click="deleteAccount">删除账户</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      updateTime: '2024年1月1日'
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    // 导出用户数据
    async exportData() {
      try {
        uni.showLoading({ title: '准备导出数据...' })
        
        // 这里实现数据导出逻辑
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        uni.hideLoading()
        uni.showToast({
          title: '数据导出完成',
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '导出失败',
          icon: 'none'
        })
      }
    },

    // 删除账户
    deleteAccount() {
      uni.showModal({
        title: '删除账户',
        content: '删除账户将永久清除您的所有数据，此操作不可恢复。确定要继续吗？',
        confirmText: '确定删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            this.confirmDeleteAccount()
          }
        }
      })
    },

    // 确认删除账户
    async confirmDeleteAccount() {
      try {
        uni.showLoading({ title: '删除账户中...' })
        
        // 这里实现账户删除逻辑
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        uni.hideLoading()
        uni.showToast({
          title: '账户已删除',
          icon: 'success'
        })

        // 跳转到登录页面
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/auth/login'
          })
        }, 1500)
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped>
.page-container {
  height: 100vh;
  background-color: #F5F5F5;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #E5E5EA;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-back {
  font-size: 24px;
  color: #007AFF;
  font-weight: 600;
}

.navbar-title {
  font-size: 17px;
  font-weight: 600;
  color: #000000;
}

.navbar-right {
  width: 44px;
}

.content {
  height: calc(100vh - 44px);
  padding: 0 15px;
}

.update-info {
  padding: 15px 0;
  text-align: center;
}

.update-text {
  font-size: 12px;
  color: #8E8E93;
}

.privacy-content {
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
}

.section {
  padding: 20px;
  border-bottom: 1px solid #F2F2F7;
}

.section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 15px;
}

.section-content {
  line-height: 1.6;
}

.content-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
}

.subsection {
  margin-bottom: 15px;
}

.subsection:last-child {
  margin-bottom: 0;
}

.subsection-title {
  font-size: 15px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8px;
}

.purpose-item {
  margin-bottom: 15px;
}

.purpose-item:last-child {
  margin-bottom: 0;
}

.purpose-title {
  font-size: 15px;
  font-weight: 600;
  color: #34C759;
  margin-bottom: 5px;
}

.purpose-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.protection-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.protection-item:last-child {
  margin-bottom: 0;
}

.protection-icon {
  font-size: 20px;
  margin-right: 10px;
  margin-top: 2px;
}

.protection-content {
  flex: 1;
}

.protection-title {
  font-size: 15px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 5px;
}

.protection-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.share-principle {
  margin-bottom: 15px;
}

.share-principle:last-child {
  margin-bottom: 0;
}

.principle-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 8px;
}

.principle-content {
  font-size: 14px;
  color: #666666;
  line-height: 1.6;
}

.right-item {
  margin-bottom: 15px;
}

.right-item:last-child {
  margin-bottom: 0;
}

.right-title {
  font-size: 15px;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 5px;
}

.right-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.third-party-item {
  margin-bottom: 15px;
  padding: 12px;
  background-color: #F8F9FA;
  border-radius: 6px;
}

.third-party-item:last-child {
  margin-bottom: 0;
}

.third-party-name {
  font-size: 15px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 5px;
}

.third-party-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.contact-item {
  display: flex;
  margin-bottom: 10px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  font-size: 14px;
  color: #666666;
  width: 80px;
}

.contact-value {
  font-size: 14px;
  color: #007AFF;
  flex: 1;
}

.action-buttons {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.btn {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
}

.btn-secondary {
  background-color: #F2F2F7;
  color: #007AFF;
}

.btn-danger {
  background-color: #FF3B30;
  color: #FFFFFF;
}
</style>