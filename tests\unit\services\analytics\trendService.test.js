/**
 * 趋势服务综合测试
 * 测试趋势分析和图表生成的整合功能
 */

import trendService from '@/services/analytics/trendService.js'

// Mock依赖
jest.mock('@/services/analytics/trendAnalysis.js')
jest.mock('@/services/analytics/chartService.js')

describe('TrendService', () => {
  // 模拟报告数据
  const mockReportData = [
    {
      id: 'report1',
      checkDate: '2024-01-01',
      items: [
        {
          name: '血糖',
          value: '5.2',
          unit: 'mmol/L',
          referenceRange: '3.9-6.1',
          isAbnormal: false
        },
        {
          name: '血压',
          value: '120/80',
          unit: 'mmHg',
          referenceRange: '<140/90',
          isAbnormal: false
        }
      ]
    },
    {
      id: 'report2',
      checkDate: '2024-02-01',
      items: [
        {
          name: '血糖',
          value: '5.8',
          unit: 'mmol/L',
          referenceRange: '3.9-6.1',
          isAbnormal: false
        },
        {
          name: '血压',
          value: '125/82',
          unit: 'mmHg',
          referenceRange: '<140/90',
          isAbnormal: false
        }
      ]
    },
    {
      id: 'report3',
      checkDate: '2024-03-01',
      items: [
        {
          name: '血糖',
          value: '6.4',
          unit: 'mmol/L',
          referenceRange: '3.9-6.1',
          isAbnormal: true
        },
        {
          name: '血压',
          value: '130/85',
          unit: 'mmHg',
          referenceRange: '<140/90',
          isAbnormal: false
        }
      ]
    }
  ]

  describe('extractIndicatorData', () => {
    test('应该正确提取指标数据', () => {
      const dataPoints = trendService.extractIndicatorData('血糖', mockReportData)
      
      expect(dataPoints).toHaveLength(3)
      expect(dataPoints[0].value).toBe(5.2)
      expect(dataPoints[0].date).toBe('2024-01-01')
      expect(dataPoints[0].unit).toBe('mmol/L')
      expect(dataPoints[0].referenceRange).toBe('3.9-6.1')
      expect(dataPoints[0].isAbnormal).toBe(false)
      expect(dataPoints[2].isAbnormal).toBe(true)
    })

    test('应该按日期排序数据点', () => {
      const unsortedReports = [mockReportData[2], mockReportData[0], mockReportData[1]]
      const dataPoints = trendService.extractIndicatorData('血糖', unsortedReports)
      
      expect(dataPoints[0].date).toBe('2024-01-01')
      expect(dataPoints[1].date).toBe('2024-02-01')
      expect(dataPoints[2].date).toBe('2024-03-01')
    })

    test('应该处理不存在的指标', () => {
      const dataPoints = trendService.extractIndicatorData('不存在的指标', mockReportData)
      
      expect(dataPoints).toHaveLength(0)
    })

    test('应该支持模糊匹配指标名称', () => {
      const dataPoints = trendService.extractIndicatorData('血', mockReportData)
      
      expect(dataPoints.length).toBeGreaterThan(0)
    })
  })

  describe('parseNumericValue', () => {
    test('应该正确解析数值', () => {
      expect(trendService.parseNumericValue('5.2')).toBe(5.2)
      expect(trendService.parseNumericValue(5.2)).toBe(5.2)
      expect(trendService.parseNumericValue('5.2 mmol/L')).toBe(5.2)
      expect(trendService.parseNumericValue('120/80')).toBe(120) // 取第一个数值
      expect(trendService.parseNumericValue('阴性')).toBeNull()
      expect(trendService.parseNumericValue('')).toBeNull()
    })

    test('应该处理负数', () => {
      expect(trendService.parseNumericValue('-2.5')).toBe(-2.5)
      expect(trendService.parseNumericValue('负2.5')).toBe(-2.5)
    })
  })

  describe('getIndicatorUnit', () => {
    test('应该返回正确的指标单位', () => {
      expect(trendService.getIndicatorUnit('血糖')).toBe('mmol/L')
      expect(trendService.getIndicatorUnit('血压')).toBe('mmHg')
      expect(trendService.getIndicatorUnit('胆固醇')).toBe('mmol/L')
      expect(trendService.getIndicatorUnit('白细胞')).toBe('×10⁹/L')
      expect(trendService.getIndicatorUnit('未知指标')).toBe('')
    })
  })

  describe('parseReferenceRange', () => {
    test('应该正确解析参考范围', () => {
      expect(trendService.parseReferenceRange('3.9-6.1')).toEqual({ min: 3.9, max: 6.1 })
      expect(trendService.parseReferenceRange('3.9~6.1')).toEqual({ min: 3.9, max: 6.1 })
      expect(trendService.parseReferenceRange('<6.1')).toEqual({ max: 6.1 })
      expect(trendService.parseReferenceRange('>3.9')).toEqual({ min: 3.9 })
      expect(trendService.parseReferenceRange('≤6.1')).toEqual({ max: 6.1 })
      expect(trendService.parseReferenceRange('≥3.9')).toEqual({ min: 3.9 })
    })

    test('应该处理无效的参考范围', () => {
      expect(trendService.parseReferenceRange('')).toBeNull()
      expect(trendService.parseReferenceRange('阴性')).toBeNull()
      expect(trendService.parseReferenceRange(null)).toBeNull()
    })
  })

  describe('getIndicatorTrend', () => {
    test('应该返回完整的指标趋势分析', async () => {
      const result = await trendService.getIndicatorTrend('血糖', mockReportData)
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('indicator')
      expect(result.data).toHaveProperty('analysis')
      expect(result.data).toHaveProperty('chart')
      expect(result.data).toHaveProperty('dataPoints')
      expect(result.data).toHaveProperty('recommendations')
      expect(result.data.indicator).toBe('血糖')
    })

    test('应该处理不存在的指标', async () => {
      const result = await trendService.getIndicatorTrend('不存在的指标', mockReportData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('未找到该指标的数据')
      expect(result.data).toBeNull()
    })

    test('应该处理错误情况', async () => {
      // 模拟错误
      const invalidData = null
      const result = await trendService.getIndicatorTrend('血糖', invalidData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('趋势分析失败')
      expect(result).toHaveProperty('error')
    })
  })

  describe('getMultiIndicatorTrends', () => {
    test('应该返回多指标趋势分析', async () => {
      const indicatorNames = ['血糖', '血压']
      const result = await trendService.getMultiIndicatorTrends(indicatorNames, mockReportData)
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('indicators')
      expect(result.data).toHaveProperty('analysis')
      expect(result.data).toHaveProperty('chart')
      expect(result.data).toHaveProperty('correlations')
      expect(result.data).toHaveProperty('recommendations')
      expect(result.data.indicators).toContain('血糖')
    })

    test('应该处理没有数据的情况', async () => {
      const indicatorNames = ['不存在的指标1', '不存在的指标2']
      const result = await trendService.getMultiIndicatorTrends(indicatorNames, mockReportData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('未找到任何指标数据')
    })
  })

  describe('getIndicatorDistribution', () => {
    test('应该返回指标分布统计', async () => {
      const result = await trendService.getIndicatorDistribution('血糖', mockReportData)
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('indicator')
      expect(result.data).toHaveProperty('distribution')
      expect(result.data).toHaveProperty('chart')
      expect(result.data).toHaveProperty('statistics')
    })
  })

  describe('calculateDistribution', () => {
    test('应该正确计算分布统计', () => {
      const dataPoints = [
        { value: 5.2, isAbnormal: false },
        { value: 5.8, isAbnormal: false },
        { value: 6.4, isAbnormal: true }
      ]
      
      const distribution = trendService.calculateDistribution(dataPoints, '血糖')
      
      expect(distribution.categories['正常']).toBe(2)
      expect(distribution.categories['异常']).toBe(1)
      expect(distribution.total).toBe(3)
      expect(distribution.abnormalRate).toBe('33.3')
    })
  })

  describe('calculateStatistics', () => {
    test('应该正确计算统计信息', () => {
      const dataPoints = [
        { value: '5.0' },
        { value: '6.0' },
        { value: '7.0' }
      ]
      
      const stats = trendService.calculateStatistics(dataPoints)
      
      expect(stats.count).toBe(3)
      expect(stats.mean).toBe(6)
      expect(stats.median).toBe(6)
      expect(stats.min).toBe(5)
      expect(stats.max).toBe(7)
      expect(stats.std).toBeCloseTo(0.82, 1)
    })

    test('应该处理空数据', () => {
      const stats = trendService.calculateStatistics([])
      
      expect(stats.count).toBe(0)
      expect(stats.mean).toBe(0)
      expect(stats.median).toBe(0)
      expect(stats.min).toBe(0)
      expect(stats.max).toBe(0)
      expect(stats.std).toBe(0)
    })

    test('应该处理非数值数据', () => {
      const dataPoints = [
        { value: '阴性' },
        { value: '5.0' },
        { value: '正常' }
      ]
      
      const stats = trendService.calculateStatistics(dataPoints)
      
      expect(stats.count).toBe(1) // 只有一个有效数值
      expect(stats.mean).toBe(5)
    })
  })

  describe('generateRecommendations', () => {
    test('应该为上升趋势生成建议', () => {
      const trendAnalysis = {
        trend: 'increasing',
        changeRate: 25
      }
      
      const recommendations = trendService.generateRecommendations(trendAnalysis, '血糖')
      
      expect(recommendations).toHaveLength(1)
      expect(recommendations[0]).toContain('血糖呈明显上升趋势')
      expect(recommendations[0]).toContain('建议咨询医生')
    })

    test('应该为下降趋势生成建议', () => {
      const trendAnalysis = {
        trend: 'decreasing',
        changeRate: -15
      }
      
      const recommendations = trendService.generateRecommendations(trendAnalysis, '血糖')
      
      expect(recommendations).toHaveLength(1)
      expect(recommendations[0]).toContain('血糖轻微下降')
      expect(recommendations[0]).toContain('建议持续关注')
    })

    test('应该为稳定趋势生成建议', () => {
      const trendAnalysis = {
        trend: 'stable',
        changeRate: 2
      }
      
      const recommendations = trendService.generateRecommendations(trendAnalysis, '血糖')
      
      expect(recommendations).toHaveLength(1)
      expect(recommendations[0]).toContain('血糖保持稳定')
      expect(recommendations[0]).toContain('继续保持良好的生活习惯')
    })

    test('应该为波动趋势生成建议', () => {
      const trendAnalysis = {
        trend: 'fluctuating',
        changeRate: 10
      }
      
      const recommendations = trendService.generateRecommendations(trendAnalysis, '血糖')
      
      expect(recommendations).toHaveLength(1)
      expect(recommendations[0]).toContain('血糖波动较大')
      expect(recommendations[0]).toContain('建议规律检查并咨询医生')
    })
  })

  describe('generateMultiIndicatorRecommendations', () => {
    test('应该为多指标分析生成建议', () => {
      const multiTrendAnalysis = {
        indicators: {
          '血糖': { trend: 'increasing' },
          '血压': { trend: 'increasing' },
          '胆固醇': { trend: 'stable' }
        },
        correlations: {
          '血糖_血压': 0.8
        }
      }
      
      const recommendations = trendService.generateMultiIndicatorRecommendations(multiTrendAnalysis)
      
      expect(recommendations.length).toBeGreaterThan(0)
      expect(recommendations.some(r => r.includes('多项指标呈上升趋势'))).toBe(true)
      expect(recommendations.some(r => r.includes('强相关性'))).toBe(true)
    })

    test('应该处理波动较大的情况', () => {
      const multiTrendAnalysis = {
        indicators: {
          '指标1': { trend: 'fluctuating' },
          '指标2': { trend: 'fluctuating' },
          '指标3': { trend: 'fluctuating' }
        },
        correlations: {}
      }
      
      const recommendations = trendService.generateMultiIndicatorRecommendations(multiTrendAnalysis)
      
      expect(recommendations.some(r => r.includes('多项指标波动较大'))).toBe(true)
    })
  })
})