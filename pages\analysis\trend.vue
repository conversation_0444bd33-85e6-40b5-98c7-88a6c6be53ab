<template>
  <view class="trend-analysis">
    <uni-nav-bar 
      title="趋势分析" 
      left-icon="back" 
      @clickLeft="goBack"
      background-color="#007AFF"
      color="#ffffff"
    />
    
    <!-- 指标选择器 -->
    <view class="indicator-selector">
      <scroll-view scroll-x="true" class="indicator-scroll">
        <view class="indicator-list">
          <view 
            v-for="indicator in availableIndicators" 
            :key="indicator.key"
            class="indicator-item"
            :class="{ active: selectedIndicator === indicator.key }"
            @click="selectIndicator(indicator.key)"
          >
            {{ indicator.name }}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 时间范围选择 -->
    <view class="time-range-selector">
      <view class="range-buttons">
        <button 
          v-for="range in timeRanges" 
          :key="range.key"
          class="range-btn"
          :class="{ active: selectedTimeRange === range.key }"
          @click="selectTimeRange(range.key)"
        >
          {{ range.label }}
        </button>
      </view>
    </view>
    
    <!-- 图表容器 -->
    <view class="chart-container">
      <view class="chart-header">
        <text class="chart-title">{{ getCurrentIndicatorName() }}</text>
        <text class="chart-subtitle">{{ getTimeRangeLabel() }}</text>
      </view>
      
      <HealthChart
        :canvas-id="'trendChart'"
        :chart-data="chartData"
        :chart-type="'line'"
        :width="350"
        :height="250"
        :show-grid="true"
        :show-legend="true"
        :normal-range="currentNormalRange"
      />
    </view>
    
    <!-- 统计信息 -->
    <view class="statistics-panel">
      <view class="stat-item">
        <text class="stat-label">平均值</text>
        <text class="stat-value">{{ statistics.average }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">最高值</text>
        <text class="stat-value" :class="{ abnormal: statistics.maxAbnormal }">
          {{ statistics.max }}
        </text>
      </view>
      <view class="stat-item">
        <text class="stat-label">最低值</text>
        <text class="stat-value" :class="{ abnormal: statistics.minAbnormal }">
          {{ statistics.min }}
        </text>
      </view>
      <view class="stat-item">
        <text class="stat-label">变化幅度</text>
        <text class="stat-value">{{ statistics.range }}</text>
      </view>
    </view>
    
    <!-- 异常值提醒 -->
    <view v-if="abnormalValues.length > 0" class="abnormal-alert">
      <view class="alert-header">
        <uni-icons type="info-filled" color="#FF3B30" size="16"></uni-icons>
        <text class="alert-title">发现异常值</text>
      </view>
      <view class="abnormal-list">
        <view 
          v-for="abnormal in abnormalValues" 
          :key="abnormal.date"
          class="abnormal-item"
        >
          <text class="abnormal-date">{{ abnormal.date }}</text>
          <text class="abnormal-value">{{ abnormal.value }}</text>
          <text class="abnormal-status">{{ abnormal.status }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import HealthChart from '@/components/common/HealthChart.vue'

export default {
  name: 'TrendAnalysis',
  components: {
    HealthChart
  },
  data() {
    return {
      selectedIndicator: 'blood_pressure_systolic',
      selectedTimeRange: '3months',
      availableIndicators: [
        { key: 'blood_pressure_systolic', name: '收缩压', unit: 'mmHg', normalRange: { min: 90, max: 140 } },
        { key: 'blood_pressure_diastolic', name: '舒张压', unit: 'mmHg', normalRange: { min: 60, max: 90 } },
        { key: 'heart_rate', name: '心率', unit: 'bpm', normalRange: { min: 60, max: 100 } },
        { key: 'blood_glucose', name: '血糖', unit: 'mmol/L', normalRange: { min: 3.9, max: 6.1 } },
        { key: 'cholesterol', name: '胆固醇', unit: 'mmol/L', normalRange: { min: 0, max: 5.2 } },
        { key: 'triglycerides', name: '甘油三酯', unit: 'mmol/L', normalRange: { min: 0, max: 1.7 } }
      ],
      timeRanges: [
        { key: '1month', label: '近1个月', days: 30 },
        { key: '3months', label: '近3个月', days: 90 },
        { key: '6months', label: '近6个月', days: 180 },
        { key: '1year', label: '近1年', days: 365 }
      ],
      healthReports: [],
      chartData: [],
      statistics: {
        average: 0,
        max: 0,
        min: 0,
        range: 0,
        maxAbnormal: false,
        minAbnormal: false
      },
      abnormalValues: []
    }
  },
  computed: {
    currentNormalRange() {
      const indicator = this.availableIndicators.find(item => item.key === this.selectedIndicator)
      return indicator ? indicator.normalRange : { min: null, max: null }
    }
  },
  onLoad() {
    this.loadHealthReports()
  },
  methods: {
    async loadHealthReports() {
      try {
        // 模拟从数据库加载健康报告数据
        // 实际实现中应该调用数据库服务
        this.healthReports = await this.getHealthReportsFromDB()
        this.updateChartData()
      } catch (error) {
        console.error('加载健康报告数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'error'
        })
      }
    },
    
    async getHealthReportsFromDB() {
      // 模拟数据 - 实际实现中应该从SQLite数据库读取
      return [
        {
          id: 1,
          date: '2024-01-15',
          indicators: {
            blood_pressure_systolic: 120,
            blood_pressure_diastolic: 80,
            heart_rate: 72,
            blood_glucose: 5.2,
            cholesterol: 4.8,
            triglycerides: 1.2
          }
        },
        {
          id: 2,
          date: '2024-02-15',
          indicators: {
            blood_pressure_systolic: 135,
            blood_pressure_diastolic: 85,
            heart_rate: 78,
            blood_glucose: 5.8,
            cholesterol: 5.1,
            triglycerides: 1.5
          }
        },
        {
          id: 3,
          date: '2024-03-15',
          indicators: {
            blood_pressure_systolic: 145,
            blood_pressure_diastolic: 92,
            heart_rate: 82,
            blood_glucose: 6.5,
            cholesterol: 5.5,
            triglycerides: 1.8
          }
        },
        {
          id: 4,
          date: '2024-04-15',
          indicators: {
            blood_pressure_systolic: 125,
            blood_pressure_diastolic: 78,
            heart_rate: 70,
            blood_glucose: 5.0,
            cholesterol: 4.9,
            triglycerides: 1.3
          }
        }
      ]
    },
    
    selectIndicator(indicatorKey) {
      this.selectedIndicator = indicatorKey
      this.updateChartData()
    },
    
    selectTimeRange(rangeKey) {
      this.selectedTimeRange = rangeKey
      this.updateChartData()
    },
    
    updateChartData() {
      const timeRange = this.timeRanges.find(range => range.key === this.selectedTimeRange)
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - timeRange.days)
      
      // 过滤时间范围内的数据
      const filteredReports = this.healthReports.filter(report => {
        const reportDate = new Date(report.date)
        return reportDate >= cutoffDate
      })
      
      // 提取选中指标的数据
      this.chartData = filteredReports.map(report => ({
        date: report.date,
        value: report.indicators[this.selectedIndicator] || 0
      })).sort((a, b) => new Date(a.date) - new Date(b.date))
      
      // 计算统计信息
      this.calculateStatistics()
      
      // 检测异常值
      this.detectAbnormalValues()
    },
    
    calculateStatistics() {
      if (this.chartData.length === 0) {
        this.statistics = {
          average: 0,
          max: 0,
          min: 0,
          range: 0,
          maxAbnormal: false,
          minAbnormal: false
        }
        return
      }
      
      const values = this.chartData.map(item => item.value)
      const sum = values.reduce((acc, val) => acc + val, 0)
      const max = Math.max(...values)
      const min = Math.min(...values)
      
      this.statistics = {
        average: (sum / values.length).toFixed(1),
        max: max.toFixed(1),
        min: min.toFixed(1),
        range: (max - min).toFixed(1),
        maxAbnormal: this.isAbnormalValue(max),
        minAbnormal: this.isAbnormalValue(min)
      }
    },
    
    detectAbnormalValues() {
      this.abnormalValues = this.chartData
        .filter(item => this.isAbnormalValue(item.value))
        .map(item => ({
          date: this.formatDate(item.date),
          value: item.value.toFixed(1),
          status: this.getAbnormalStatus(item.value)
        }))
    },
    
    isAbnormalValue(value) {
      const { min, max } = this.currentNormalRange
      if (min === null || max === null) return false
      return value < min || value > max
    },
    
    getAbnormalStatus(value) {
      const { min, max } = this.currentNormalRange
      if (value < min) return '偏低'
      if (value > max) return '偏高'
      return '正常'
    },
    
    getCurrentIndicatorName() {
      const indicator = this.availableIndicators.find(item => item.key === this.selectedIndicator)
      return indicator ? indicator.name : ''
    },
    
    getTimeRangeLabel() {
      const range = this.timeRanges.find(item => item.key === this.selectedTimeRange)
      return range ? range.label : ''
    },
    
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    },
    
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.trend-analysis {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.indicator-selector {
  background-color: #ffffff;
  padding: 15px 0;
  margin-bottom: 10px;
}

.indicator-scroll {
  white-space: nowrap;
}

.indicator-list {
  display: flex;
  padding: 0 15px;
}

.indicator-item {
  flex-shrink: 0;
  padding: 8px 16px;
  margin-right: 10px;
  background-color: #f0f0f0;
  border-radius: 20px;
  font-size: 14px;
  color: #666666;
}

.indicator-item.active {
  background-color: #007AFF;
  color: #ffffff;
}

.time-range-selector {
  background-color: #ffffff;
  padding: 15px;
  margin-bottom: 10px;
}

.range-buttons {
  display: flex;
  justify-content: space-between;
}

.range-btn {
  flex: 1;
  margin: 0 5px;
  padding: 8px 12px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  color: #666666;
}

.range-btn.active {
  background-color: #007AFF;
  color: #ffffff;
}

.chart-container {
  background-color: #ffffff;
  margin-bottom: 10px;
  padding: 20px;
}

.chart-header {
  margin-bottom: 20px;
  text-align: center;
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 5px;
}

.chart-subtitle {
  font-size: 14px;
  color: #666666;
}

.statistics-panel {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 16px;
  color: #333333;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #007AFF;
}

.stat-value.abnormal {
  color: #FF3B30;
}

.abnormal-alert {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 10px;
}

.alert-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.alert-title {
  font-size: 16px;
  font-weight: bold;
  color: #FF3B30;
  margin-left: 8px;
}

.abnormal-list {
  background-color: #fff5f5;
  border-radius: 8px;
  padding: 15px;
}

.abnormal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #ffe0e0;
}

.abnormal-item:last-child {
  border-bottom: none;
}

.abnormal-date {
  font-size: 14px;
  color: #666666;
}

.abnormal-value {
  font-size: 14px;
  font-weight: bold;
  color: #FF3B30;
}

.abnormal-status {
  font-size: 12px;
  color: #FF3B30;
  background-color: #ffe0e0;
  padding: 2px 8px;
  border-radius: 10px;
}
</style>