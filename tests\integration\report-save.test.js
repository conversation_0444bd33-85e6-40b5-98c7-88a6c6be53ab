const ReportSaveService = require('@/services/report/saveService.js');
const ReportValidator = require('@/utils/validation/reportValidator.js');

// Mock存储API
const mockStorage = {
  data: {},
  getItem: jest.fn((key) => mockStorage.data[key] || null),
  setItem: jest.fn((key, value) => {
    mockStorage.data[key] = value;
  }),
  clear: jest.fn(() => {
    mockStorage.data = {};
  })
};

// Mock uni API
global.uni = {
  getStorage: jest.fn(),
  setStorage: jest.fn()
};

// Mock localStorage for H5
global.localStorage = mockStorage;

describe('Report Save Integration Tests', () => {
  let saveService;
  let validator;

  beforeEach(() => {
    jest.clearAllMocks();
    mockStorage.clear();
    
    saveService = new ReportSaveService();
    validator = new ReportValidator();
  });

  describe('完整的报告保存流程', () => {
    it('应该成功保存完整的报告数据', async () => {
      const reportData = {
        hospital: '北京协和医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        title: '常规体检报告',
        notes: '患者身体状况良好',
        items: [
          {
            name: '白细胞计数',
            value: '6.2',
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            category: '血常规',
            isAbnormal: false
          },
          {
            name: '血红蛋白',
            value: '145',
            unit: 'g/L',
            referenceRange: '130-175',
            category: '血常规',
            isAbnormal: false
          }
        ]
      };

      const result = await saveService.saveReport(reportData);

      expect(result.success).toBe(true);
      expect(result.data.id).toBeDefined();
      expect(result.data.hospital).toBe('北京协和医院');
      expect(result.data.items).toHaveLength(2);
      expect(result.data.summary).toBeDefined();
      expect(result.data.summary.totalItems).toBe(2);
      expect(result.data.summary.abnormalItems).toBe(0);
      expect(result.data.createdAt).toBeDefined();
      expect(result.data.updatedAt).toBeDefined();
    });

    it('应该正确处理包含异常值的报告', async () => {
      const reportData = {
        hospital: '北京医院',
        doctor: '李医生',
        checkDate: '2024-01-16',
        items: [
          {
            name: '白细胞计数',
            value: '12.5', // 异常值
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            category: '血常规',
            isAbnormal: false // 故意设置错误，测试自动修正
          },
          {
            name: '血糖',
            value: '3.2', // 异常值
            unit: 'mmol/L',
            referenceRange: '3.9-6.1',
            category: '血糖',
            isAbnormal: false // 故意设置错误
          }
        ]
      };

      const result = await saveService.saveReport(reportData);

      expect(result.success).toBe(true);
      expect(result.data.items[0].isAbnormal).toBe(true); // 应该被自动修正
      expect(result.data.items[1].isAbnormal).toBe(true); // 应该被自动修正
      expect(result.data.summary.abnormalItems).toBe(2);
      expect(result.data.summary.hasAbnormal).toBe(true);
      expect(result.data.summary.abnormalItemNames).toEqual(['白细胞计数', '血糖']);
    });

    it('应该拒绝保存无效数据', async () => {
      const invalidReportData = {
        hospital: '', // 必填项为空
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [] // 没有检查项目
      };

      const result = await saveService.saveReport(invalidReportData);

      expect(result.success).toBe(false);
      expect(result.error.code).toBe('VALIDATION_FAILED');
      expect(result.validation.isValid).toBe(false);
      expect(result.validation.errors.hospital).toBeDefined();
      expect(result.validation.errors.items).toBeDefined();
    });

    it('应该能够更新已存在的报告', async () => {
      // 先保存一个报告
      const originalData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [
          {
            name: '白细胞计数',
            value: '6.0',
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            category: '血常规'
          }
        ]
      };

      const saveResult = await saveService.saveReport(originalData);
      expect(saveResult.success).toBe(true);

      const reportId = saveResult.data.id;
      const createdAt = saveResult.data.createdAt;

      // 更新报告
      const updatedData = {
        ...originalData,
        id: reportId,
        title: '更新后的标题',
        items: [
          ...originalData.items,
          {
            name: '血红蛋白',
            value: '140',
            unit: 'g/L',
            referenceRange: '130-175',
            category: '血常规'
          }
        ]
      };

      const updateResult = await saveService.saveReport(updatedData);

      expect(updateResult.success).toBe(true);
      expect(updateResult.data.id).toBe(reportId);
      expect(updateResult.data.title).toBe('更新后的标题');
      expect(updateResult.data.items).toHaveLength(2);
      // 创建时间应该保持不变或者非常接近
      const createdTime = new Date(createdAt).getTime();
      const updatedCreatedTime = new Date(updateResult.data.createdAt).getTime();
      expect(Math.abs(updatedCreatedTime - createdTime)).toBeLessThan(1000); // 允许1秒内的差异
      expect(new Date(updateResult.data.updatedAt).getTime()).toBeGreaterThanOrEqual(new Date(createdAt).getTime()); // 更新时间应该不早于创建时间
      expect(updateResult.message).toMatch(/(更新成功|报告.*成功)/);
    });
  });

  describe('数据验证集成', () => {
    it('应该进行完整的数据验证', async () => {
      const reportData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2025-12-31', // 未来日期，应该被拒绝
        items: [
          {
            name: '白细胞计数',
            value: 'abc', // 无效数值
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            category: '血常规'
          }
        ]
      };

      const result = await saveService.saveReport(reportData);

      expect(result.success).toBe(false);
      expect(result.validation.errors.checkDate).toContain('不能晚于今天');
      expect(result.validation.details.items.errors[0].value).toContain('必须是有效数字');
    });

    it('应该计算正确的完整性得分', async () => {
      const completeReportData = {
        hospital: '北京协和医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        title: '完整的体检报告',
        notes: '详细备注信息',
        items: [
          {
            name: '白细胞计数',
            value: '6.2',
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            category: '血常规'
          }
        ]
      };

      const result = await saveService.saveReport(completeReportData);

      expect(result.success).toBe(true);
      expect(result.validation.score).toBeGreaterThan(80); // 完整数据应该有高分
    });

    it('应该检测数据一致性问题', async () => {
      const inconsistentData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [
          {
            name: '白细胞计数',
            value: '12.0', // 超出参考范围
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            category: '血常规',
            isAbnormal: false // 标记不一致
          }
        ]
      };

      const result = await saveService.saveReport(inconsistentData);

      expect(result.success).toBe(true);
      expect(result.validation.warnings.length).toBeGreaterThan(0);
      expect(result.validation.warnings.some(w => w.includes('异常标记'))).toBe(true);
    });
  });

  describe('存储和索引管理', () => {
    it('应该正确管理存储和索引', async () => {
      const reportData1 = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [
          {
            name: '白细胞计数',
            value: '6.0',
            category: '血常规'
          }
        ]
      };

      const reportData2 = {
        hospital: '上海医院',
        doctor: '李医生',
        checkDate: '2024-01-16',
        items: [
          {
            name: '血糖',
            value: '5.5',
            category: '血糖'
          }
        ]
      };

      // 保存两个报告
      const result1 = await saveService.saveReport(reportData1);
      const result2 = await saveService.saveReport(reportData2);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);

      // 检查存储统计
      const stats = await saveService.getStorageStats();
      expect(stats.totalReports).toBe(2);
      expect(stats.hospitals).toContain('北京医院');
      expect(stats.hospitals).toContain('上海医院');
      expect(stats.categories).toContain('血常规');
      expect(stats.categories).toContain('血糖');
    });

    it('应该能够删除报告并更新索引', async () => {
      // 先保存一个报告
      const reportData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [
          {
            name: '白细胞计数',
            value: '6.0',
            category: '血常规'
          }
        ]
      };

      const saveResult = await saveService.saveReport(reportData);
      expect(saveResult.success).toBe(true);

      const reportId = saveResult.data.id;

      // 删除报告
      const deleteResult = await saveService.deleteReport(reportId);
      expect(deleteResult.success).toBe(true);
      expect(deleteResult.deletedReport.id).toBe(reportId);

      // 验证报告已被删除
      const stats = await saveService.getStorageStats();
      expect(stats.totalReports).toBe(0);
    });

    it('应该处理删除不存在的报告', async () => {
      const deleteResult = await saveService.deleteReport('nonexistent_id');
      
      expect(deleteResult.success).toBe(false);
      expect(deleteResult.error.code).toBe('REPORT_NOT_FOUND');
    });
  });

  describe('批量操作', () => {
    it('应该能够批量保存报告', async () => {
      const reports = [
        {
          hospital: '北京医院',
          doctor: '张医生',
          checkDate: '2024-01-15',
          items: [{ name: '白细胞计数', value: '6.0' }]
        },
        {
          hospital: '上海医院',
          doctor: '李医生',
          checkDate: '2024-01-16',
          items: [{ name: '血糖', value: '5.5' }]
        },
        {
          hospital: '', // 无效数据
          doctor: '王医生',
          checkDate: '2024-01-17',
          items: []
        }
      ];

      const result = await saveService.batchSaveReports(reports);

      expect(result.total).toBe(3);
      expect(result.saved).toBe(2);
      expect(result.failed).toBe(1);
      expect(result.success).toBe(false); // 因为有失败的
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].index).toBe(2);
    });

    it('应该能够跳过验证批量保存', async () => {
      const reports = [
        {
          hospital: '', // 无效数据
          doctor: '张医生',
          checkDate: '2024-01-15',
          items: []
        }
      ];

      const result = await saveService.batchSaveReports(reports, { skipValidation: true });

      expect(result.success).toBe(true);
      expect(result.saved).toBe(1);
      expect(result.failed).toBe(0);
    });
  });

  describe('错误处理', () => {
    it('应该处理存储错误', async () => {
      // Mock存储失败
      mockStorage.setItem.mockImplementationOnce(() => {
        throw new Error('存储空间不足');
      });

      const reportData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [{ name: '白细胞计数', value: '6.0' }]
      };

      const result = await saveService.saveReport(reportData);

      expect(result.success).toBe(false);
      expect(result.error.code).toBe('STORAGE_FAILED');
    });

    it('应该处理数据预处理错误', async () => {
      const reportData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: null // 无效的items
      };

      const result = await saveService.saveReport(reportData);

      expect(result.success).toBe(false);
      expect(result.error.code).toBe('VALIDATION_FAILED');
    });
  });

  describe('数据清理和标准化', () => {
    it('应该清理和标准化输入数据', async () => {
      const reportData = {
        hospital: '  北京医院  ', // 包含空白字符
        doctor: '  张医生  ',
        checkDate: '2024-01-15',
        title: '  体检报告  ',
        notes: '  备注信息  ',
        items: [
          {
            name: '  白细胞计数  ',
            value: '6.20', // 数值标准化
            unit: '  10^9/L  ',
            referenceRange: '  3.5-9.5  ',
            category: '  血常规  '
          }
        ]
      };

      const result = await saveService.saveReport(reportData);

      expect(result.success).toBe(true);
      expect(result.data.hospital).toBe('北京医院');
      expect(result.data.doctor).toBe('张医生');
      expect(result.data.title).toBe('体检报告');
      expect(result.data.notes).toBe('备注信息');
      expect(result.data.items[0].name).toBe('白细胞计数');
      expect(result.data.items[0].value).toBe('6.2'); // 数值标准化
      expect(result.data.items[0].unit).toBe('10^9/L');
      expect(result.data.items[0].referenceRange).toBe('3.5-9.5');
      expect(result.data.items[0].category).toBe('血常规');
    });

    it('应该生成正确的摘要信息', async () => {
      const reportData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [
          {
            name: '白细胞计数',
            value: '6.0',
            category: '血常规',
            isAbnormal: false
          },
          {
            name: '血糖',
            value: '8.5',
            referenceRange: '3.9-6.1',
            category: '血糖',
            isAbnormal: true
          },
          {
            name: '血红蛋白',
            value: '140',
            category: '血常规',
            isAbnormal: false
          }
        ]
      };

      const result = await saveService.saveReport(reportData);

      expect(result.success).toBe(true);
      expect(result.data.summary.totalItems).toBe(3);
      expect(result.data.summary.abnormalItems).toBe(1);
      expect(result.data.summary.normalItems).toBe(2);
      expect(result.data.summary.categories).toContain('血常规');
      expect(result.data.summary.categories).toContain('血糖');
      expect(result.data.summary.hasAbnormal).toBe(true);
      expect(result.data.summary.abnormalItemNames).toContain('血糖');
    });
  });
});