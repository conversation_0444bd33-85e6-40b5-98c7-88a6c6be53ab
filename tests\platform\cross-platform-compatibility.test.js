/**
 * 跨平台兼容性测试套件
 * 测试iOS、Android和微信小程序平台的功能兼容性
 */

import { PlatformAdapterClass } from '../../utils/platform/PlatformAdapter.js'
import { getCurrentPlatform, isApp, isWeixinMp, isH5 } from '../../utils/platform/detector.js'
import { PLATFORM_TYPES, ERROR_CODES } from '../../utils/platform/constants.js'
import { syncService } from '../../services/sync/syncService.js'

// Mock uni对象 - 模拟不同平台的行为
const createPlatformMockUni = (platform) => {
  const baseMock = {
    chooseImage: jest.fn(),
    setStorage: jest.fn(),
    getStorage: jest.fn(),
    removeStorage: jest.fn(),
    clearStorage: jest.fn(),
    saveImageToPhotosAlbum: jest.fn(),
    getFileInfo: jest.fn(),
    share: jest.fn(),
    showToast: jest.fn(),
    getNetworkType: jest.fn(),
    getSystemInfo: jest.fn()
  }

  // 根据平台调整mock行为
  switch (platform) {
    case PLATFORM_TYPES.APP_PLUS:
      return {
        ...baseMock,
        chooseImage: jest.fn().mockImplementation(({ success, sourceType, count }) => {
          // APP平台支持相机和相册，支持多选
          success({
            tempFilePaths: Array(Math.min(count || 1, 9)).fill('/temp/image.jpg'),
            tempFiles: Array(Math.min(count || 1, 9)).fill({ path: '/temp/image.jpg', size: 1024 })
          })
        }),
        saveImageToPhotosAlbum: jest.fn().mockImplementation(({ success }) => success()),
        getSystemInfo: jest.fn().mockImplementation(({ success }) => {
          success({
            platform: 'android',
            system: 'Android 10',
            version: '10',
            model: 'Test Device',
            pixelRatio: 2,
            screenWidth: 375,
            screenHeight: 667
          })
        })
      }

    case PLATFORM_TYPES.MP_WEIXIN:
      return {
        ...baseMock,
        chooseImage: jest.fn().mockImplementation(({ success, count }) => {
          // 微信小程序限制单选
          success({
            tempFilePaths: ['/temp/image.jpg'],
            tempFiles: [{ path: '/temp/image.jpg', size: 1024 }]
          })
        }),
        saveImageToPhotosAlbum: jest.fn().mockImplementation(({ success }) => success()),
        getSystemInfo: jest.fn().mockImplementation(({ success }) => {
          success({
            platform: 'devtools',
            system: 'iOS 14.0',
            version: '8.0.5',
            model: 'iPhone',
            pixelRatio: 2,
            screenWidth: 375,
            screenHeight: 667
          })
        })
      }

    case PLATFORM_TYPES.H5:
      return {
        ...baseMock,
        chooseImage: jest.fn().mockImplementation(({ success, sourceType }) => {
          // H5平台只支持相册选择
          if (sourceType.includes('camera')) {
            throw new Error('H5平台不支持相机')
          }
          success({
            tempFilePaths: ['/temp/image.jpg'],
            tempFiles: [{ path: '/temp/image.jpg', size: 1024 }]
          })
        }),
        saveImageToPhotosAlbum: jest.fn().mockImplementation(({ fail }) => {
          fail(new Error('H5平台不支持保存到相册'))
        }),
        getSystemInfo: jest.fn().mockImplementation(({ success }) => {
          success({
            platform: 'mac',
            system: 'Mac OS X',
            version: '10.15.7',
            model: 'MacBook Pro',
            pixelRatio: 2,
            screenWidth: 1440,
            screenHeight: 900
          })
        })
      }

    default:
      return baseMock
  }
}

describe('跨平台兼容性测试', () => {
  let originalUni
  let adapter

  beforeAll(() => {
    originalUni = global.uni
  })

  afterAll(() => {
    global.uni = originalUni
  })

  beforeEach(() => {
    adapter = new PlatformAdapterClass()
  })

  describe('平台检测兼容性', () => {
    test('应该正确识别当前平台', () => {
      const platform = getCurrentPlatform()
      expect(typeof platform).toBe('string')
      expect(Object.values(PLATFORM_TYPES)).toContain(platform)
    })

    test('平台判断函数应该互斥', () => {
      const platformChecks = [isApp(), isWeixinMp(), isH5()]
      const trueCount = platformChecks.filter(Boolean).length
      
      // 只能有一个平台判断为true（或者都为false在未知平台）
      expect(trueCount).toBeLessThanOrEqual(1)
    })
  })

  describe('APP平台兼容性测试', () => {
    beforeEach(() => {
      global.uni = createPlatformMockUni(PLATFORM_TYPES.APP_PLUS)
      adapter.platform = PLATFORM_TYPES.APP_PLUS
      adapter.config = {
        supportCamera: true,
        supportShare: true,
        supportBiometric: true,
        maxImageSize: 10 * 1024 * 1024,
        supportedImageFormats: ['jpg', 'jpeg', 'png', 'webp']
      }
    })

    test('相机功能应该完全支持', async () => {
      // 测试拍照
      const photoResult = await adapter.takePhoto()
      expect(photoResult.success).toBe(true)
      expect(photoResult.tempFilePaths).toHaveLength(1)

      // 测试多选图片
      const multiSelectResult = await adapter.chooseImage({ count: 5 })
      expect(multiSelectResult.success).toBe(true)
      expect(multiSelectResult.tempFilePaths.length).toBeGreaterThan(1)
    })

    test('存储功能应该支持大容量', async () => {
      const largeData = 'x'.repeat(1024 * 1024) // 1MB数据
      
      const setResult = await adapter.setStorage('large_data', largeData)
      expect(setResult).toBe(true)

      const getData = await adapter.getStorage('large_data')
      expect(getData).toBe(largeData)
    })

    test('分享功能应该支持多种方式', async () => {
      const shareResult = await adapter.share({
        type: 'text',
        title: '测试分享',
        content: '这是一个测试分享内容'
      })
      expect(shareResult.success).toBe(true)
    })

    test('文件操作应该完全支持', async () => {
      // 测试保存图片到相册
      const saveResult = await adapter.saveImageToPhotosAlbum('/temp/test.jpg')
      expect(saveResult).toBe(true)

      // 测试获取文件信息
      const fileInfo = await adapter.getFileInfo('/temp/test.jpg')
      expect(fileInfo).toBeDefined()
    })
  })

  describe('微信小程序平台兼容性测试', () => {
    beforeEach(() => {
      global.uni = createPlatformMockUni(PLATFORM_TYPES.MP_WEIXIN)
      adapter.platform = PLATFORM_TYPES.MP_WEIXIN
      adapter.config = {
        supportCamera: true,
        supportShare: true,
        supportBiometric: false,
        maxImageSize: 2 * 1024 * 1024,
        supportedImageFormats: ['jpg', 'jpeg', 'png']
      }
    })

    test('相机功能应该有限制', async () => {
      // 测试单选限制
      const result = await adapter.chooseImage({ count: 5 })
      expect(result.success).toBe(true)
      expect(result.tempFilePaths).toHaveLength(1) // 微信小程序限制单选
    })

    test('存储功能应该有容量限制', async () => {
      const mediumData = 'x'.repeat(512 * 1024) // 512KB数据
      
      const setResult = await adapter.setStorage('medium_data', mediumData)
      expect(setResult).toBe(true)

      const getData = await adapter.getStorage('medium_data')
      expect(getData).toBe(mediumData)
    })

    test('分享功能应该使用小程序特定方式', async () => {
      global.getApp = jest.fn(() => ({
        globalData: {}
      }))

      const shareResult = await adapter.share({
        title: '小程序分享测试',
        content: '测试内容'
      })
      expect(shareResult.success).toBe(true)
      expect(global.getApp().globalData.shareInfo).toBeDefined()
    })

    test('不应该支持生物识别', () => {
      expect(adapter.config.supportBiometric).toBe(false)
    })
  })

  describe('H5平台兼容性测试', () => {
    beforeEach(() => {
      global.uni = createPlatformMockUni(PLATFORM_TYPES.H5)
      adapter.platform = PLATFORM_TYPES.H5
      adapter.config = {
        supportCamera: false,
        supportShare: false,
        supportBiometric: false,
        maxImageSize: 5 * 1024 * 1024,
        supportedImageFormats: ['jpg', 'jpeg', 'png', 'webp']
      }

      // Mock navigator
      global.navigator = {
        share: jest.fn().mockResolvedValue(),
        clipboard: {
          writeText: jest.fn().mockResolvedValue()
        }
      }
    })

    test('相机功能应该有严格限制', async () => {
      // H5不支持直接拍照
      await expect(adapter.takePhoto()).rejects.toThrow('H5平台不支持直接拍照功能')

      // 只支持从相册选择
      const albumResult = await adapter.chooseFromAlbum()
      expect(albumResult.success).toBe(true)
    })

    test('分享功能应该降级处理', async () => {
      // 测试Web Share API
      const shareResult = await adapter.share({
        title: 'H5分享测试',
        content: '测试内容',
        url: 'https://example.com'
      })
      expect(shareResult.success).toBe(true)
      expect(navigator.share).toHaveBeenCalled()
    })

    test('分享降级到剪贴板', async () => {
      // 模拟不支持Web Share API
      global.navigator.share = undefined

      const shareResult = await adapter.share({
        content: '测试内容'
      })
      expect(shareResult.success).toBe(true)
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('测试内容')
    })

    test('不应该支持保存到相册', async () => {
      await expect(adapter.saveImageToPhotosAlbum('/temp/test.jpg'))
        .rejects.toThrow('H5平台不支持保存图片到相册')
    })
  })

  describe('数据同步跨平台兼容性', () => {
    beforeEach(() => {
      // Mock同步服务依赖
      global.uni = createPlatformMockUni(PLATFORM_TYPES.APP_PLUS)
    })

    test('不同平台应该使用相同的数据格式', async () => {
      const testData = {
        id: 1,
        user_id: 1,
        report_title: '测试报告',
        report_date: '2024-01-01',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      // 模拟不同平台的数据处理
      const platforms = [PLATFORM_TYPES.APP_PLUS, PLATFORM_TYPES.MP_WEIXIN, PLATFORM_TYPES.H5]
      
      for (const platform of platforms) {
        global.uni = createPlatformMockUni(platform)
        adapter.platform = platform
        
        // 测试数据序列化和反序列化
        await adapter.setStorage('test_report', testData)
        const retrievedData = await adapter.getStorage('test_report')
        
        expect(retrievedData).toEqual(testData)
      }
    })

    test('网络请求应该适配不同平台的限制', async () => {
      const platforms = [
        { type: PLATFORM_TYPES.APP_PLUS, timeout: 30000 },
        { type: PLATFORM_TYPES.MP_WEIXIN, timeout: 20000 },
        { type: PLATFORM_TYPES.H5, timeout: 15000 }
      ]

      for (const platform of platforms) {
        global.uni = createPlatformMockUni(platform.type)
        global.uni.getNetworkType.mockImplementation(({ success }) => {
          success({ networkType: 'wifi' })
        })

        // 验证平台特定的网络配置
        const networkType = await adapter.getNetworkType()
        expect(networkType).toBe('wifi')
      }
    })
  })

  describe('性能兼容性测试', () => {
    test('图片处理性能应该适配平台限制', async () => {
      const platforms = [
        { type: PLATFORM_TYPES.APP_PLUS, maxSize: 10 * 1024 * 1024 },
        { type: PLATFORM_TYPES.MP_WEIXIN, maxSize: 2 * 1024 * 1024 },
        { type: PLATFORM_TYPES.H5, maxSize: 5 * 1024 * 1024 }
      ]

      for (const platform of platforms) {
        global.uni = createPlatformMockUni(platform.type)
        adapter.platform = platform.type
        adapter.config.maxImageSize = platform.maxSize

        // 测试图片大小限制
        expect(adapter.config.maxImageSize).toBe(platform.maxSize)
      }
    })

    test('存储性能应该适配平台限制', async () => {
      const testSizes = [
        { platform: PLATFORM_TYPES.APP_PLUS, size: 1024 * 1024 }, // 1MB
        { platform: PLATFORM_TYPES.MP_WEIXIN, size: 512 * 1024 }, // 512KB
        { platform: PLATFORM_TYPES.H5, size: 256 * 1024 } // 256KB
      ]

      for (const test of testSizes) {
        global.uni = createPlatformMockUni(test.platform)
        adapter.platform = test.platform

        const testData = 'x'.repeat(test.size)
        const startTime = Date.now()
        
        await adapter.setStorage('perf_test', testData)
        const endTime = Date.now()
        
        // 验证存储操作在合理时间内完成（5秒内）
        expect(endTime - startTime).toBeLessThan(5000)
      }
    })
  })

  describe('错误处理兼容性', () => {
    test('不同平台应该返回一致的错误格式', async () => {
      const platforms = [PLATFORM_TYPES.APP_PLUS, PLATFORM_TYPES.MP_WEIXIN, PLATFORM_TYPES.H5]

      for (const platform of platforms) {
        global.uni = createPlatformMockUni(platform)
        global.uni.chooseImage.mockImplementation(({ fail }) => {
          fail(new Error('用户取消选择'))
        })

        adapter.platform = platform

        try {
          await adapter.chooseImage()
        } catch (error) {
          expect(error).toHaveProperty('success', false)
          expect(error).toHaveProperty('error')
          expect(error).toHaveProperty('code')
        }
      }
    })

    test('平台特定功能应该优雅降级', async () => {
      // H5平台测试拍照降级
      global.uni = createPlatformMockUni(PLATFORM_TYPES.H5)
      adapter.platform = PLATFORM_TYPES.H5

      await expect(adapter.takePhoto()).rejects.toThrow()

      // 但是选择相册应该正常工作
      const albumResult = await adapter.chooseFromAlbum()
      expect(albumResult.success).toBe(true)
    })
  })

  describe('用户体验一致性', () => {
    test('相同功能在不同平台应该有一致的接口', async () => {
      const platforms = [PLATFORM_TYPES.APP_PLUS, PLATFORM_TYPES.MP_WEIXIN, PLATFORM_TYPES.H5]
      const testOptions = { count: 1, sizeType: ['compressed'] }

      for (const platform of platforms) {
        global.uni = createPlatformMockUni(platform)
        adapter.platform = platform

        // 所有平台都应该支持基本的图片选择（即使有限制）
        if (platform === PLATFORM_TYPES.H5) {
          // H5平台只支持相册选择
          const result = await adapter.chooseFromAlbum(testOptions)
          expect(result).toHaveProperty('success')
          expect(result).toHaveProperty('tempFilePaths')
        } else {
          const result = await adapter.chooseImage(testOptions)
          expect(result).toHaveProperty('success')
          expect(result).toHaveProperty('tempFilePaths')
        }
      }
    })

    test('存储接口应该在所有平台保持一致', async () => {
      const platforms = [PLATFORM_TYPES.APP_PLUS, PLATFORM_TYPES.MP_WEIXIN, PLATFORM_TYPES.H5]
      const testData = { name: '测试', value: 123 }

      for (const platform of platforms) {
        global.uni = createPlatformMockUni(platform)
        adapter.platform = platform

        // 设置存储
        const setResult = await adapter.setStorage('test_key', testData)
        expect(setResult).toBe(true)

        // 获取存储
        const getData = await adapter.getStorage('test_key')
        expect(getData).toEqual(testData)

        // 删除存储
        const removeResult = await adapter.removeStorage('test_key')
        expect(removeResult).toBe(true)
      }
    })
  })
})