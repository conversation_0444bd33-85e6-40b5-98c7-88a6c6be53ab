/**
 * 离线数据缓存和网络恢复后自动同步管理器
 * 处理离线状态下的数据缓存和网络恢复后的自动同步
 */

import { syncService } from './syncService.js';
import { syncStatusManager } from './syncStatusManager.js';
import { useSyncStore } from '../../stores/sync.js';
import { storage } from '../../utils/storage/index.js';

class OfflineManager {
  constructor() {
    this.isOnline = true;
    this.networkListeners = new Set();
    this.offlineQueue = [];
    this.syncStore = null;
    this.autoSyncTimer = null;
    this.networkCheckInterval = null;
    this.retryAttempts = 0;
    this.maxRetryAttempts = 5;
    this.retryDelay = 5000; // 5秒
  }

  /**
   * 初始化离线管理器
   */
  async initialize() {
    this.syncStore = useSyncStore();
    
    // 监听网络状态变化
    this.setupNetworkListeners();
    
    // 检查初始网络状态
    await this.checkNetworkStatus();
    
    // 加载离线队列
    await this.loadOfflineQueue();
    
    // 设置定期网络检查
    this.startNetworkCheck();
    
    console.log('离线管理器初始化完成');
  }

  /**
   * 设置网络状态监听器
   */
  setupNetworkListeners() {
    // uni-app网络状态监听
    uni.onNetworkStatusChange((res) => {
      const wasOnline = this.isOnline;
      this.isOnline = res.isConnected;
      
      console.log(`网络状态变化: ${wasOnline ? '在线' : '离线'} -> ${this.isOnline ? '在线' : '离线'}`);
      
      if (!wasOnline && this.isOnline) {
        // 从离线恢复到在线
        this.handleNetworkReconnection();
      } else if (wasOnline && !this.isOnline) {
        // 从在线变为离线
        this.handleNetworkDisconnection();
      }
      
      this.notifyNetworkStatusChange();
    });
  }

  /**
   * 检查网络状态
   * @returns {Promise<boolean>}
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          const wasOnline = this.isOnline;
          this.isOnline = res.networkType !== 'none';
          
          if (wasOnline !== this.isOnline) {
            this.notifyNetworkStatusChange();
          }
          
          resolve(this.isOnline);
        },
        fail: () => {
          this.isOnline = false;
          resolve(false);
        }
      });
    });
  }

  /**
   * 开始定期网络检查
   */
  startNetworkCheck() {
    if (this.networkCheckInterval) {
      clearInterval(this.networkCheckInterval);
    }
    
    this.networkCheckInterval = setInterval(async () => {
      await this.checkNetworkStatus();
    }, 30000); // 每30秒检查一次
  }

  /**
   * 停止网络检查
   */
  stopNetworkCheck() {
    if (this.networkCheckInterval) {
      clearInterval(this.networkCheckInterval);
      this.networkCheckInterval = null;
    }
  }

  /**
   * 处理网络重连
   */
  async handleNetworkReconnection() {
    console.log('网络已恢复，开始自动同步');
    
    this.retryAttempts = 0;
    
    // 延迟一段时间再开始同步，确保网络稳定
    setTimeout(async () => {
      try {
        await this.processOfflineQueue();
        await this.triggerAutoSync();
      } catch (error) {
        console.error('网络恢复后同步失败:', error);
        this.scheduleRetrySync();
      }
    }, 2000);
  }

  /**
   * 处理网络断开
   */
  handleNetworkDisconnection() {
    console.log('网络已断开，进入离线模式');
    
    // 停止自动同步
    this.stopAutoSync();
    
    // 通知用户进入离线模式
    syncStatusManager.updateSyncStatus({
      isOffline: true,
      lastOfflineTime: Date.now()
    });
  }

  /**
   * 添加数据到离线队列
   * @param {Object} operation 操作信息
   */
  async addToOfflineQueue(operation) {
    const queueItem = {
      id: Date.now().toString(),
      timestamp: Date.now(),
      ...operation
    };
    
    this.offlineQueue.push(queueItem);
    await this.saveOfflineQueue();
    
    console.log('数据已添加到离线队列:', queueItem);
  }

  /**
   * 处理离线队列
   */
  async processOfflineQueue() {
    if (this.offlineQueue.length === 0) {
      return;
    }
    
    console.log(`开始处理离线队列，共${this.offlineQueue.length}项`);
    
    const processedItems = [];
    const failedItems = [];
    
    for (const item of this.offlineQueue) {
      try {
        await this.processOfflineItem(item);
        processedItems.push(item);
      } catch (error) {
        console.error('处理离线项目失败:', error);
        failedItems.push({ ...item, error: error.message });
      }
    }
    
    // 移除已处理的项目
    this.offlineQueue = this.offlineQueue.filter(item => 
      !processedItems.some(processed => processed.id === item.id)
    );
    
    // 保存更新后的队列
    await this.saveOfflineQueue();
    
    console.log(`离线队列处理完成: 成功${processedItems.length}项，失败${failedItems.length}项`);
    
    return {
      processed: processedItems.length,
      failed: failedItems.length,
      failedItems
    };
  }

  /**
   * 处理单个离线项目
   * @param {Object} item 离线项目
   */
  async processOfflineItem(item) {
    const { type, data, userId } = item;
    
    switch (type) {
      case 'create_report':
        await this.syncCreateReport(data, userId);
        break;
      case 'update_report':
        await this.syncUpdateReport(data, userId);
        break;
      case 'delete_report':
        await this.syncDeleteReport(data, userId);
        break;
      case 'create_indicator':
        await this.syncCreateIndicator(data, userId);
        break;
      case 'update_user':
        await this.syncUpdateUser(data, userId);
        break;
      default:
        throw new Error(`未知的离线操作类型: ${type}`);
    }
  }

  /**
   * 同步创建报告
   * @param {Object} reportData 
   * @param {number} userId 
   */
  async syncCreateReport(reportData, userId) {
    // 添加到同步队列
    await storage.sync.createSyncRecord(userId, 'health_reports', reportData.id, 'INSERT');
  }

  /**
   * 同步更新报告
   * @param {Object} reportData 
   * @param {number} userId 
   */
  async syncUpdateReport(reportData, userId) {
    // 添加到同步队列
    await storage.sync.createSyncRecord(userId, 'health_reports', reportData.id, 'UPDATE');
  }

  /**
   * 同步删除报告
   * @param {Object} reportData 
   * @param {number} userId 
   */
  async syncDeleteReport(reportData, userId) {
    // 添加到同步队列
    await storage.sync.createSyncRecord(userId, 'health_reports', reportData.id, 'DELETE');
  }

  /**
   * 同步创建指标
   * @param {Object} indicatorData 
   * @param {number} userId 
   */
  async syncCreateIndicator(indicatorData, userId) {
    // 添加到同步队列
    await storage.sync.createSyncRecord(userId, 'health_indicators', indicatorData.id, 'INSERT');
  }

  /**
   * 同步更新用户
   * @param {Object} userData 
   * @param {number} userId 
   */
  async syncUpdateUser(userData, userId) {
    // 添加到同步队列
    await storage.sync.createSyncRecord(userId, 'users', userId, 'UPDATE');
  }

  /**
   * 触发自动同步
   */
  async triggerAutoSync() {
    if (!this.isOnline) {
      console.log('网络不可用，跳过自动同步');
      return;
    }
    
    if (!this.syncStore.syncConfig.autoSync) {
      console.log('自动同步已禁用');
      return;
    }
    
    try {
      const userId = this.getCurrentUserId();
      if (!userId) {
        console.log('用户未登录，跳过自动同步');
        return;
      }
      
      const result = await syncService.startSync({
        userId,
        background: true,
        syncType: 'full'
      });
      
      console.log('自动同步完成:', result);
      
    } catch (error) {
      console.error('自动同步失败:', error);
      this.scheduleRetrySync();
    }
  }

  /**
   * 安排重试同步
   */
  scheduleRetrySync() {
    if (this.retryAttempts >= this.maxRetryAttempts) {
      console.log('达到最大重试次数，停止重试');
      return;
    }
    
    this.retryAttempts++;
    const delay = this.retryDelay * Math.pow(2, this.retryAttempts - 1); // 指数退避
    
    console.log(`安排第${this.retryAttempts}次重试同步，延迟${delay}ms`);
    
    setTimeout(async () => {
      if (this.isOnline) {
        await this.triggerAutoSync();
      }
    }, delay);
  }

  /**
   * 开始自动同步定时器
   */
  startAutoSync() {
    if (!this.syncStore.syncConfig.autoSync) {
      return;
    }
    
    const interval = this.syncStore.syncConfig.syncInterval * 60 * 1000;
    
    if (this.autoSyncTimer) {
      clearInterval(this.autoSyncTimer);
    }
    
    this.autoSyncTimer = setInterval(async () => {
      if (this.isOnline) {
        await this.triggerAutoSync();
      }
    }, interval);
    
    console.log(`自动同步定时器已启动，间隔${interval}ms`);
  }

  /**
   * 停止自动同步定时器
   */
  stopAutoSync() {
    if (this.autoSyncTimer) {
      clearInterval(this.autoSyncTimer);
      this.autoSyncTimer = null;
      console.log('自动同步定时器已停止');
    }
  }

  /**
   * 保存离线队列到本地存储
   */
  async saveOfflineQueue() {
    try {
      uni.setStorageSync('offline_queue', this.offlineQueue);
    } catch (error) {
      console.error('保存离线队列失败:', error);
    }
  }

  /**
   * 从本地存储加载离线队列
   */
  async loadOfflineQueue() {
    try {
      const savedQueue = uni.getStorageSync('offline_queue');
      if (savedQueue && Array.isArray(savedQueue)) {
        this.offlineQueue = savedQueue;
        console.log(`已加载离线队列，共${this.offlineQueue.length}项`);
      }
    } catch (error) {
      console.error('加载离线队列失败:', error);
      this.offlineQueue = [];
    }
  }

  /**
   * 清空离线队列
   */
  async clearOfflineQueue() {
    this.offlineQueue = [];
    await this.saveOfflineQueue();
    console.log('离线队列已清空');
  }

  /**
   * 获取当前用户ID
   * @returns {number|null}
   */
  getCurrentUserId() {
    try {
      const userInfo = uni.getStorageSync('user_info');
      return userInfo ? userInfo.id : null;
    } catch (error) {
      console.error('获取用户ID失败:', error);
      return null;
    }
  }

  /**
   * 获取网络状态
   * @returns {Object}
   */
  getNetworkStatus() {
    return {
      isOnline: this.isOnline,
      offlineQueueSize: this.offlineQueue.length,
      retryAttempts: this.retryAttempts,
      maxRetryAttempts: this.maxRetryAttempts,
      autoSyncEnabled: this.syncStore ? this.syncStore.syncConfig.autoSync : false
    };
  }

  /**
   * 获取离线统计信息
   * @returns {Object}
   */
  getOfflineStatistics() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    const oneDayAgo = now - 24 * 60 * 60 * 1000;
    
    const recentItems = this.offlineQueue.filter(item => item.timestamp > oneHourAgo);
    const todayItems = this.offlineQueue.filter(item => item.timestamp > oneDayAgo);
    
    return {
      total: this.offlineQueue.length,
      recentHour: recentItems.length,
      today: todayItems.length,
      oldestItem: this.offlineQueue.length > 0 
        ? Math.min(...this.offlineQueue.map(item => item.timestamp))
        : null
    };
  }

  /**
   * 注册网络状态变化监听器
   * @param {Function} callback 回调函数
   */
  onNetworkStatusChange(callback) {
    this.networkListeners.add(callback);
    
    // 返回取消注册的函数
    return () => {
      this.networkListeners.delete(callback);
    };
  }

  /**
   * 通知网络状态变化
   */
  notifyNetworkStatusChange() {
    const status = this.getNetworkStatus();
    this.networkListeners.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('网络状态回调执行失败:', error);
      }
    });
  }

  /**
   * 强制检查并处理离线队列
   */
  async forceProcessOfflineQueue() {
    if (!this.isOnline) {
      throw new Error('网络不可用，无法处理离线队列');
    }
    
    return await this.processOfflineQueue();
  }

  /**
   * 清理离线管理器
   */
  cleanup() {
    this.stopAutoSync();
    this.stopNetworkCheck();
    this.networkListeners.clear();
    this.offlineQueue = [];
  }
}

// 导出单例实例
export const offlineManager = new OfflineManager();
export default offlineManager;