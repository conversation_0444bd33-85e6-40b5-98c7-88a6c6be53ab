const { mount } = require('@vue/test-utils')
const CameraUpload = require('@/components/business/CameraUpload/index.vue')

// Mock ImageProcessor
const mockImageProcessor = {
  validateImageQuality: jest.fn(),
  preprocessForOCR: jest.fn(),
  generateThumbnail: jest.fn()
}

jest.doMock('@/utils/image/processor.js', () => mockImageProcessor)

// Mock uni API
global.uni = {
  chooseImage: jest.fn(),
  previewImage: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn()
}

// Mock plus API for APP-PLUS
global.plus = {
  android: {
    requestPermissions: jest.fn()
  }
}

describe('CameraUpload', () => {
  let wrapper

  beforeEach(() => {
    jest.clearAllMocks()
    wrapper = mount(CameraUpload, {
      props: {
        maxCount: 3,
        showTips: true,
        autoCompress: true
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  describe('组件渲染', () => {
    it('应该正确渲染上传按钮', () => {
      const cameraBtn = wrapper.find('.camera-btn')
      const albumBtn = wrapper.find('.album-btn')
      
      expect(cameraBtn.exists()).toBe(true)
      expect(albumBtn.exists()).toBe(true)
      expect(cameraBtn.text()).toContain('拍照')
      expect(albumBtn.text()).toContain('从相册选择')
    })

    it('应该显示拍照提示', () => {
      const tips = wrapper.find('.quality-tips')
      expect(tips.exists()).toBe(true)
      expect(tips.text()).toContain('拍照建议')
    })

    it('showTips为false时不应该显示提示', async () => {
      await wrapper.setProps({ showTips: false })
      const tips = wrapper.find('.quality-tips')
      expect(tips.exists()).toBe(false)
    })
  })

  describe('拍照功能', () => {
    it('应该调用相机拍照', async () => {
      const mockImagePath = '/test/photo.jpg'
      
      uni.chooseImage.mockImplementation(({ success }) => {
        success({ tempFilePaths: [mockImagePath] })
      })
      
      ImageProcessor.validateImageQuality.mockResolvedValue({
        passed: true,
        issues: [],
        width: 1920,
        height: 1080
      })
      
      ImageProcessor.preprocessForOCR.mockResolvedValue(mockImagePath)
      ImageProcessor.generateThumbnail.mockResolvedValue('/test/thumbnail.jpg')

      const cameraBtn = wrapper.find('.camera-btn')
      await cameraBtn.trigger('click')

      expect(uni.chooseImage).toHaveBeenCalledWith({
        count: 1,
        sourceType: ['camera'],
        sizeType: ['original'],
        success: expect.any(Function),
        fail: expect.any(Function)
      })
    })

    it('达到最大数量时应该显示提示', async () => {
      // 设置已有图片达到最大数量
      wrapper.vm.imageList = [
        { path: '/test/1.jpg' },
        { path: '/test/2.jpg' },
        { path: '/test/3.jpg' }
      ]

      const cameraBtn = wrapper.find('.camera-btn')
      await cameraBtn.trigger('click')

      expect(uni.showToast).toHaveBeenCalledWith({
        title: '最多只能上传3张图片',
        icon: 'none'
      })
      expect(uni.chooseImage).not.toHaveBeenCalled()
    })

    it('拍照失败时应该显示错误提示', async () => {
      uni.chooseImage.mockImplementation(({ fail }) => {
        fail(new Error('相机调用失败'))
      })

      const cameraBtn = wrapper.find('.camera-btn')
      await cameraBtn.trigger('click')

      expect(uni.showToast).toHaveBeenCalledWith({
        title: '拍照失败，请重试',
        icon: 'none'
      })
    })
  })

  describe('相册选择功能', () => {
    it('应该从相册选择图片', async () => {
      const mockImagePaths = ['/test/album1.jpg', '/test/album2.jpg']
      
      uni.chooseImage.mockImplementation(({ success }) => {
        success({ tempFilePaths: mockImagePaths })
      })
      
      ImageProcessor.validateImageQuality.mockResolvedValue({
        passed: true,
        issues: []
      })
      ImageProcessor.preprocessForOCR.mockImplementation(path => Promise.resolve(path))
      ImageProcessor.generateThumbnail.mockImplementation(path => Promise.resolve(path + '_thumb'))

      const albumBtn = wrapper.find('.album-btn')
      await albumBtn.trigger('click')

      expect(uni.chooseImage).toHaveBeenCalledWith({
        count: 3, // maxCount - current count
        sourceType: ['album'],
        sizeType: ['original'],
        success: expect.any(Function),
        fail: expect.any(Function)
      })
    })

    it('应该正确计算剩余可选择数量', async () => {
      // 已有1张图片
      wrapper.vm.imageList = [{ path: '/test/existing.jpg' }]
      
      uni.chooseImage.mockImplementation(({ success }) => {
        success({ tempFilePaths: ['/test/new.jpg'] })
      })
      
      ImageProcessor.validateImageQuality.mockResolvedValue({ passed: true, issues: [] })
      ImageProcessor.preprocessForOCR.mockResolvedValue('/test/new.jpg')
      ImageProcessor.generateThumbnail.mockResolvedValue('/test/new_thumb.jpg')

      const albumBtn = wrapper.find('.album-btn')
      await albumBtn.trigger('click')

      expect(uni.chooseImage).toHaveBeenCalledWith(
        expect.objectContaining({
          count: 2 // maxCount(3) - existing(1)
        })
      )
    })
  })

  describe('图片处理', () => {
    it('应该处理上传的图片', async () => {
      const mockImagePath = '/test/image.jpg'
      const mockProcessedPath = '/test/processed.jpg'
      const mockThumbnail = '/test/thumbnail.jpg'
      
      ImageProcessor.validateImageQuality.mockResolvedValue({
        passed: true,
        issues: [],
        width: 1920,
        height: 1080
      })
      ImageProcessor.preprocessForOCR.mockResolvedValue(mockProcessedPath)
      ImageProcessor.generateThumbnail.mockResolvedValue(mockThumbnail)

      await wrapper.vm.processImage(mockImagePath)

      expect(ImageProcessor.validateImageQuality).toHaveBeenCalledWith(mockImagePath)
      expect(ImageProcessor.preprocessForOCR).toHaveBeenCalledWith(mockImagePath)
      expect(ImageProcessor.generateThumbnail).toHaveBeenCalledWith(mockProcessedPath)
      
      expect(wrapper.vm.imageList).toHaveLength(1)
      expect(wrapper.vm.imageList[0]).toMatchObject({
        path: mockProcessedPath,
        originalPath: mockImagePath,
        thumbnail: mockThumbnail
      })
    })

    it('质量检查不通过时应该显示警告', async () => {
      const mockImagePath = '/test/poor-quality.jpg'
      
      ImageProcessor.validateImageQuality.mockResolvedValue({
        passed: false,
        issues: ['图片分辨率过低'],
        width: 200,
        height: 150
      })
      ImageProcessor.preprocessForOCR.mockResolvedValue(mockImagePath)
      ImageProcessor.generateThumbnail.mockResolvedValue('/test/thumbnail.jpg')

      // Mock popup ref
      wrapper.vm.$refs.qualityPopup = { open: jest.fn() }

      await wrapper.vm.processImage(mockImagePath)

      expect(wrapper.vm.$refs.qualityPopup.open).toHaveBeenCalled()
      expect(wrapper.vm.qualityResult.title).toBe('图片质量提醒')
    })

    it('图片处理失败时应该显示错误提示', async () => {
      const mockImagePath = '/test/invalid.jpg'
      
      ImageProcessor.validateImageQuality.mockRejectedValue(new Error('处理失败'))

      await wrapper.vm.processImage(mockImagePath)

      expect(uni.showToast).toHaveBeenCalledWith({
        title: '图片处理失败',
        icon: 'none'
      })
    })
  })

  describe('图片管理', () => {
    beforeEach(() => {
      wrapper.vm.imageList = [
        { path: '/test/1.jpg', thumbnail: '/test/1_thumb.jpg' },
        { path: '/test/2.jpg', thumbnail: '/test/2_thumb.jpg' }
      ]
    })

    it('应该预览图片', async () => {
      const imagePath = '/test/1.jpg'
      
      await wrapper.vm.previewImage(imagePath)

      expect(uni.previewImage).toHaveBeenCalledWith({
        urls: [imagePath],
        current: imagePath
      })
    })

    it('应该删除图片', async () => {
      uni.showModal.mockImplementation(({ success }) => {
        success({ confirm: true })
      })

      await wrapper.vm.removeImage(0)

      expect(uni.showModal).toHaveBeenCalledWith({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        success: expect.any(Function)
      })
      
      expect(wrapper.vm.imageList).toHaveLength(1)
      expect(uni.showToast).toHaveBeenCalledWith({
        title: '删除成功',
        icon: 'success'
      })
    })

    it('取消删除时不应该删除图片', async () => {
      uni.showModal.mockImplementation(({ success }) => {
        success({ confirm: false })
      })

      await wrapper.vm.removeImage(0)

      expect(wrapper.vm.imageList).toHaveLength(2)
    })

    it('应该重新拍摄图片', async () => {
      const newImagePath = '/test/new.jpg'
      
      uni.chooseImage.mockImplementation(({ success }) => {
        success({ tempFilePaths: [newImagePath] })
      })
      
      ImageProcessor.validateImageQuality.mockResolvedValue({
        passed: true,
        issues: []
      })
      ImageProcessor.preprocessForOCR.mockResolvedValue(newImagePath)
      ImageProcessor.generateThumbnail.mockResolvedValue('/test/new_thumb.jpg')

      await wrapper.vm.retakeImage(0)

      expect(wrapper.vm.imageList[0].path).toBe(newImagePath)
      expect(uni.showToast).toHaveBeenCalledWith({
        title: '重拍成功',
        icon: 'success'
      })
    })
  })

  describe('事件触发', () => {
    it('上传图片时应该触发upload事件', async () => {
      const mockImagePath = '/test/image.jpg'
      
      ImageProcessor.validateImageQuality.mockResolvedValue({
        passed: true,
        issues: []
      })
      ImageProcessor.preprocessForOCR.mockResolvedValue(mockImagePath)
      ImageProcessor.generateThumbnail.mockResolvedValue('/test/thumbnail.jpg')

      await wrapper.vm.processImage(mockImagePath)

      expect(wrapper.emitted('upload')).toBeTruthy()
      expect(wrapper.emitted('upload')[0][0]).toMatchObject({
        path: mockImagePath,
        originalPath: mockImagePath
      })
    })

    it('删除图片时应该触发remove事件', async () => {
      wrapper.vm.imageList = [{ path: '/test/1.jpg' }]
      
      uni.showModal.mockImplementation(({ success }) => {
        success({ confirm: true })
      })

      await wrapper.vm.removeImage(0)

      expect(wrapper.emitted('remove')).toBeTruthy()
    })

    it('重拍图片时应该触发replace事件', async () => {
      wrapper.vm.imageList = [{ path: '/test/old.jpg' }]
      
      const newImagePath = '/test/new.jpg'
      uni.chooseImage.mockImplementation(({ success }) => {
        success({ tempFilePaths: [newImagePath] })
      })
      
      ImageProcessor.validateImageQuality.mockResolvedValue({ passed: true, issues: [] })
      ImageProcessor.preprocessForOCR.mockResolvedValue(newImagePath)
      ImageProcessor.generateThumbnail.mockResolvedValue('/test/new_thumb.jpg')

      await wrapper.vm.retakeImage(0)

      expect(wrapper.emitted('replace')).toBeTruthy()
    })

    it('清空图片时应该触发clear事件', () => {
      wrapper.vm.imageList = [{ path: '/test/1.jpg' }]
      
      wrapper.vm.clearImages()

      expect(wrapper.vm.imageList).toHaveLength(0)
      expect(wrapper.emitted('clear')).toBeTruthy()
    })
  })

  describe('工具方法', () => {
    it('getImages应该返回所有图片', () => {
      const mockImages = [
        { path: '/test/1.jpg' },
        { path: '/test/2.jpg' }
      ]
      wrapper.vm.imageList = mockImages

      const result = wrapper.vm.getImages()

      expect(result).toEqual(mockImages)
    })

    it('clearImages应该清空所有图片', () => {
      wrapper.vm.imageList = [{ path: '/test/1.jpg' }]

      wrapper.vm.clearImages()

      expect(wrapper.vm.imageList).toHaveLength(0)
    })
  })

  describe('加载状态', () => {
    it('处理图片时应该显示加载状态', async () => {
      wrapper.vm.loading = true
      await wrapper.vm.$nextTick()

      const loadingOverlay = wrapper.find('.loading-overlay')
      expect(loadingOverlay.exists()).toBe(true)
      expect(loadingOverlay.text()).toContain('处理中')
    })

    it('加载时按钮应该被禁用', async () => {
      wrapper.vm.loading = true
      await wrapper.vm.$nextTick()

      const cameraBtn = wrapper.find('.camera-btn')
      const albumBtn = wrapper.find('.album-btn')
      
      expect(cameraBtn.attributes('disabled')).toBeDefined()
      expect(albumBtn.attributes('disabled')).toBeDefined()
    })
  })
})