/**
 * Service接口定义
 * 定义业务逻辑层的标准接口
 */

/**
 * 基础Service接口
 */
export class IService {
  /**
   * 初始化服务
   * @returns {Promise<void>}
   */
  async initialize() {
    // 默认实现为空，子类可以重写
  }
  
  /**
   * 销毁服务
   * @returns {Promise<void>}
   */
  async destroy() {
    // 默认实现为空，子类可以重写
  }
}

/**
 * 认证服务接口
 */
export class IAuthService extends IService {
  /**
   * 用户注册
   * @param {Object} userInfo - 用户信息
   * @returns {Promise<Object>} 注册结果
   */
  async register(userInfo) {
    throw new Error('Method register must be implemented')
  }
  
  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @returns {Promise<Object>} 登录结果
   */
  async login(credentials) {
    throw new Error('Method login must be implemented')
  }
  
  /**
   * 用户登出
   * @returns {Promise<void>}
   */
  async logout() {
    throw new Error('Method logout must be implemented')
  }
  
  /**
   * 发送验证码
   * @param {String} phone - 手机号
   * @param {String} type - 验证码类型
   * @returns {Promise<Boolean>} 发送是否成功
   */
  async sendVerificationCode(phone, type) {
    throw new Error('Method sendVerificationCode must be implemented')
  }
  
  /**
   * 验证验证码
   * @param {String} phone - 手机号
   * @param {String} code - 验证码
   * @returns {Promise<Boolean>} 验证是否成功
   */
  async verifyCode(phone, code) {
    throw new Error('Method verifyCode must be implemented')
  }
  
  /**
   * 重置密码
   * @param {String} phone - 手机号
   * @param {String} newPassword - 新密码
   * @param {String} verificationCode - 验证码
   * @returns {Promise<Boolean>} 重置是否成功
   */
  async resetPassword(phone, newPassword, verificationCode) {
    throw new Error('Method resetPassword must be implemented')
  }
  
  /**
   * 刷新Token
   * @returns {Promise<String>} 新的Token
   */
  async refreshToken() {
    throw new Error('Method refreshToken must be implemented')
  }
}

/**
 * OCR服务接口
 */
export class IOCRService extends IService {
  /**
   * 识别图片中的文字
   * @param {String} imagePath - 图片路径
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeText(imagePath, options = {}) {
    throw new Error('Method recognizeText must be implemented')
  }
  
  /**
   * 解析医疗报告
   * @param {String} imagePath - 图片路径
   * @returns {Promise<Object>} 解析结果
   */
  async parseMedicalReport(imagePath) {
    throw new Error('Method parseMedicalReport must be implemented')
  }
  
  /**
   * 验证图片质量
   * @param {String} imagePath - 图片路径
   * @returns {Promise<Object>} 质量检查结果
   */
  async validateImageQuality(imagePath) {
    throw new Error('Method validateImageQuality must be implemented')
  }
}

/**
 * 报告服务接口
 */
export class IReportService extends IService {
  /**
   * 创建报告
   * @param {Object} reportData - 报告数据
   * @returns {Promise<Object>} 创建的报告
   */
  async createReport(reportData) {
    throw new Error('Method createReport must be implemented')
  }
  
  /**
   * 获取用户报告列表
   * @param {String} userId - 用户ID
   * @param {Object} filter - 筛选条件
   * @returns {Promise<Array>} 报告列表
   */
  async getUserReports(userId, filter = {}) {
    throw new Error('Method getUserReports must be implemented')
  }
  
  /**
   * 获取报告详情
   * @param {String} reportId - 报告ID
   * @returns {Promise<Object>} 报告详情
   */
  async getReportDetail(reportId) {
    throw new Error('Method getReportDetail must be implemented')
  }
  
  /**
   * 更新报告
   * @param {String} reportId - 报告ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新后的报告
   */
  async updateReport(reportId, updateData) {
    throw new Error('Method updateReport must be implemented')
  }
  
  /**
   * 删除报告
   * @param {String} reportId - 报告ID
   * @returns {Promise<Boolean>} 删除是否成功
   */
  async deleteReport(reportId) {
    throw new Error('Method deleteReport must be implemented')
  }
  
  /**
   * 导出报告数据
   * @param {String} userId - 用户ID
   * @param {String} format - 导出格式
   * @returns {Promise<String>} 导出文件路径
   */
  async exportReports(userId, format = 'json') {
    throw new Error('Method exportReports must be implemented')
  }
}

/**
 * 分析服务接口
 */
export class IAnalysisService extends IService {
  /**
   * 生成趋势分析
   * @param {String} userId - 用户ID
   * @param {Array} items - 分析项目
   * @param {Object} timeRange - 时间范围
   * @returns {Promise<Object>} 趋势分析结果
   */
  async generateTrendAnalysis(userId, items, timeRange) {
    throw new Error('Method generateTrendAnalysis must be implemented')
  }
  
  /**
   * 检测异常指标
   * @param {String} userId - 用户ID
   * @returns {Promise<Array>} 异常指标列表
   */
  async detectAbnormalIndicators(userId) {
    throw new Error('Method detectAbnormalIndicators must be implemented')
  }
  
  /**
   * 生成健康摘要
   * @param {String} userId - 用户ID
   * @returns {Promise<Object>} 健康摘要
   */
  async generateHealthSummary(userId) {
    throw new Error('Method generateHealthSummary must be implemented')
  }
  
  /**
   * 生成PDF报告
   * @param {String} userId - 用户ID
   * @param {Object} options - 生成选项
   * @returns {Promise<String>} PDF文件路径
   */
  async generatePDFReport(userId, options = {}) {
    throw new Error('Method generatePDFReport must be implemented')
  }
}

/**
 * 同步服务接口
 */
export class ISyncService extends IService {
  /**
   * 上传数据到云端
   * @param {String} userId - 用户ID
   * @param {Array} data - 要上传的数据
   * @returns {Promise<Object>} 上传结果
   */
  async uploadData(userId, data) {
    throw new Error('Method uploadData must be implemented')
  }
  
  /**
   * 从云端下载数据
   * @param {String} userId - 用户ID
   * @param {Date} lastSyncTime - 上次同步时间
   * @returns {Promise<Array>} 下载的数据
   */
  async downloadData(userId, lastSyncTime) {
    throw new Error('Method downloadData must be implemented')
  }
  
  /**
   * 解决数据冲突
   * @param {Array} conflicts - 冲突数据
   * @returns {Promise<Array>} 解决后的数据
   */
  async resolveConflicts(conflicts) {
    throw new Error('Method resolveConflicts must be implemented')
  }
  
  /**
   * 获取同步状态
   * @param {String} userId - 用户ID
   * @returns {Promise<Object>} 同步状态
   */
  async getSyncStatus(userId) {
    throw new Error('Method getSyncStatus must be implemented')
  }
}