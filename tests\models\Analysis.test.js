/**
 * 分析模型单元测试
 */

import { Analysis, TrendData, DataPoint, ComparisonData } from '../../models/Analysis.js'

describe('Analysis Model', () => {
  describe('创建分析', () => {
    test('应该能够创建有效的分析', () => {
      const analysisData = {
        userId: 'user123',
        type: 'trend',
        title: '血常规趋势分析',
        timeRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-03-01')
        }
      }
      
      const analysis = Analysis.create(analysisData)
      
      expect(analysis.userId).toBe('user123')
      expect(analysis.type).toBe('trend')
      expect(analysis.title).toBe('血常规趋势分析')
      expect(analysis.timeRange.start).toEqual(new Date('2024-01-01'))
      expect(analysis.timeRange.end).toEqual(new Date('2024-03-01'))
      expect(analysis.id).toBeDefined()
      expect(analysis.createdAt).toBeInstanceOf(Date)
    })
    
    test('创建分析时缺少用户ID应该抛出错误', () => {
      const analysisData = {
        type: 'trend'
      }
      
      expect(() => {
        Analysis.create(analysisData)
      }).toThrow('用户ID是必填项')
    })
    
    test('创建分析时类型无效应该抛出错误', () => {
      const analysisData = {
        userId: 'user123',
        type: 'invalid_type'
      }
      
      expect(() => {
        Analysis.create(analysisData)
      }).toThrow('分析数据验证失败')
    })
  })
  
  describe('趋势数据管理', () => {
    let analysis
    
    beforeEach(() => {
      analysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
    })
    
    test('应该能够添加趋势数据', () => {
      const trendData = {
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.5 },
          { date: new Date('2024-02-01'), value: 7.0 }
        ],
        trend: 'up'
      }
      
      analysis.addTrend(trendData)
      
      expect(analysis.results.trends).toHaveLength(1)
      expect(analysis.results.trends[0]).toBeInstanceOf(TrendData)
      expect(analysis.results.trends[0].item).toBe('白细胞计数')
    })
    
    test('应该能够根据项目名称获取趋势数据', () => {
      const trendData = new TrendData({
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.5 },
          { date: new Date('2024-02-01'), value: 7.0 }
        ]
      })
      
      analysis.addTrend(trendData)
      
      const foundTrend = analysis.getTrendByItem('白细胞计数')
      expect(foundTrend).toBe(analysis.results.trends[0])
      
      const notFoundTrend = analysis.getTrendByItem('不存在的项目')
      expect(notFoundTrend).toBeNull()
    })
    
    test('应该能够获取异常趋势', () => {
      const normalTrend = new TrendData({
        item: '正常项目',
        data: [
          { date: new Date('2024-01-01'), value: 6.5, isAbnormal: false },
          { date: new Date('2024-02-01'), value: 7.0, isAbnormal: false }
        ]
      })
      
      const abnormalTrend = new TrendData({
        item: '异常项目',
        data: [
          { date: new Date('2024-01-01'), value: 15.0, isAbnormal: true },
          { date: new Date('2024-02-01'), value: 16.0, isAbnormal: true }
        ]
      })
      
      analysis.addTrend(normalTrend)
      analysis.addTrend(abnormalTrend)
      
      const abnormalTrends = analysis.getAbnormalTrends()
      expect(abnormalTrends).toHaveLength(1)
      expect(abnormalTrends[0].item).toBe('异常项目')
    })
  })
  
  describe('比较数据管理', () => {
    let analysis
    
    beforeEach(() => {
      analysis = new Analysis({
        userId: 'user123',
        type: 'comparison'
      })
    })
    
    test('应该能够添加比较数据', () => {
      const comparisonData = {
        item: '血糖',
        baselineValue: 5.5,
        currentValue: 6.0,
        baselineDate: new Date('2024-01-01'),
        currentDate: new Date('2024-02-01')
      }
      
      analysis.addComparison(comparisonData)
      
      expect(analysis.results.comparisons).toHaveLength(1)
      expect(analysis.results.comparisons[0]).toBeInstanceOf(ComparisonData)
      expect(analysis.results.comparisons[0].item).toBe('血糖')
    })
  })
  
  describe('分析结果管理', () => {
    let analysis
    
    beforeEach(() => {
      analysis = new Analysis({
        userId: 'user123',
        type: 'summary'
      })
    })
    
    test('应该能够设置分析摘要', () => {
      const summary = '整体健康状况良好'
      analysis.setSummary(summary)
      
      expect(analysis.results.summary).toBe(summary)
    })
    
    test('应该能够添加建议', () => {
      analysis.addRecommendation('建议定期复查')
      analysis.addRecommendation('注意饮食健康')
      
      expect(analysis.results.recommendations).toContain('建议定期复查')
      expect(analysis.results.recommendations).toContain('注意饮食健康')
      expect(analysis.results.recommendations).toHaveLength(2)
    })
    
    test('不应该添加重复建议', () => {
      analysis.addRecommendation('建议定期复查')
      analysis.addRecommendation('建议定期复查')
      
      expect(analysis.results.recommendations).toHaveLength(1)
    })
    
    test('应该能够设置统计数据', () => {
      const statistics = {
        totalReports: 10,
        abnormalCount: 2,
        averageScore: 85
      }
      
      analysis.setStatistics(statistics)
      
      expect(analysis.results.statistics.totalReports).toBe(10)
      expect(analysis.results.statistics.abnormalCount).toBe(2)
      expect(analysis.results.statistics.averageScore).toBe(85)
    })
  })
  
  describe('健康评分计算', () => {
    test('应该能够计算健康评分', () => {
      const analysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
      
      // 添加正常趋势
      const normalTrend = new TrendData({
        item: '正常项目',
        data: [
          { date: new Date('2024-01-01'), value: 6.5, isAbnormal: false },
          { date: new Date('2024-02-01'), value: 7.0, isAbnormal: false }
        ],
        trend: 'improving',
        importance: 1
      })
      
      // 添加异常趋势
      const abnormalTrend = new TrendData({
        item: '异常项目',
        data: [
          { date: new Date('2024-01-01'), value: 15.0, isAbnormal: true },
          { date: new Date('2024-02-01'), value: 16.0, isAbnormal: true }
        ],
        trend: 'worsening',
        importance: 2
      })
      
      analysis.addTrend(normalTrend)
      analysis.addTrend(abnormalTrend)
      
      const healthScore = analysis.calculateHealthScore()
      expect(healthScore).toBeGreaterThanOrEqual(0)
      expect(healthScore).toBeLessThanOrEqual(100)
    })
    
    test('应该能够获取健康等级', () => {
      const analysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
      
      // 模拟高分情况
      analysis.calculateHealthScore = jest.fn().mockReturnValue(95)
      expect(analysis.getHealthGrade()).toBe('excellent')
      expect(analysis.getHealthGradeDisplay()).toBe('优秀')
      
      // 模拟低分情况
      analysis.calculateHealthScore = jest.fn().mockReturnValue(45)
      expect(analysis.getHealthGrade()).toBe('critical')
      expect(analysis.getHealthGradeDisplay()).toBe('危险')
    })
  })
  
  describe('报告生成', () => {
    test('应该能够生成分析报告', () => {
      const analysis = new Analysis({
        userId: 'user123',
        type: 'trend',
        title: '健康趋势分析'
      })
      
      analysis.setSummary('整体健康状况良好')
      analysis.addRecommendation('建议定期复查')
      
      const normalTrend = new TrendData({
        item: '正常项目',
        data: [
          { date: new Date('2024-01-01'), value: 6.5, isAbnormal: false },
          { date: new Date('2024-02-01'), value: 7.0, isAbnormal: false }
        ],
        trend: 'stable'
      })
      
      analysis.addTrend(normalTrend)
      
      const report = analysis.generateReport()
      
      expect(report.title).toBe('健康趋势分析')
      expect(report.summary).toBe('整体健康状况良好')
      expect(report.healthScore).toBeDefined()
      expect(report.healthGrade).toBeDefined()
      expect(report.keyFindings).toBeInstanceOf(Array)
      expect(report.trends.total).toBe(1)
      expect(report.recommendations).toContain('建议定期复查')
      expect(report.generatedAt).toBeInstanceOf(Date)
    })
    
    test('应该能够生成标题', () => {
      const analysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
      
      const title = analysis.generateTitle()
      expect(title).toContain('趋势分析')
      expect(title).toContain('报告')
    })
    
    test('应该能够生成关键发现', () => {
      const analysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
      
      // 添加异常趋势
      const abnormalTrend = new TrendData({
        item: '异常项目',
        data: [
          { date: new Date('2024-01-01'), value: 15.0, isAbnormal: true },
          { date: new Date('2024-02-01'), value: 16.0, isAbnormal: true }
        ],
        trend: 'worsening'
      })
      
      analysis.addTrend(abnormalTrend)
      
      const findings = analysis.generateKeyFindings()
      expect(findings.length).toBeGreaterThan(0)
      expect(findings.some(f => f.includes('异常'))).toBe(true)
    })
  })
  
  describe('状态管理', () => {
    let analysis
    
    beforeEach(() => {
      analysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
    })
    
    test('应该能够设置分析状态', () => {
      analysis.setStatus('processing')
      expect(analysis.status).toBe('processing')
      
      analysis.setStatus('completed')
      expect(analysis.status).toBe('completed')
    })
    
    test('应该能够更新元数据', () => {
      const metadata = {
        reportCount: 10,
        dataPoints: 50,
        confidence: 0.95
      }
      
      analysis.updateMetadata(metadata)
      
      expect(analysis.metadata.reportCount).toBe(10)
      expect(analysis.metadata.dataPoints).toBe(50)
      expect(analysis.metadata.confidence).toBe(0.95)
    })
  })
  
  describe('标签管理', () => {
    let analysis
    
    beforeEach(() => {
      analysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
    })
    
    test('应该能够添加标签', () => {
      analysis.addTag('重要')
      analysis.addTag('复查')
      
      expect(analysis.tags).toContain('重要')
      expect(analysis.tags).toContain('复查')
      expect(analysis.tags).toHaveLength(2)
    })
    
    test('应该能够移除标签', () => {
      analysis.addTag('重要')
      analysis.addTag('复查')
      
      analysis.removeTag('重要')
      
      expect(analysis.tags).not.toContain('重要')
      expect(analysis.tags).toContain('复查')
      expect(analysis.tags).toHaveLength(1)
    })
  })
  
  describe('过期检查', () => {
    test('应该能够检查是否过期', () => {
      const oldAnalysis = new Analysis({
        userId: 'user123',
        type: 'trend',
        createdAt: new Date('2023-01-01')
      })
      
      const newAnalysis = new Analysis({
        userId: 'user123',
        type: 'trend'
      })
      
      expect(oldAnalysis.isExpired(30)).toBe(true)
      expect(newAnalysis.isExpired(30)).toBe(false)
    })
  })
  
  describe('数据序列化', () => {
    test('应该正确序列化为JSON', () => {
      const analysis = new Analysis({
        userId: 'user123',
        type: 'trend',
        title: '测试分析'
      })
      
      const trendData = new TrendData({
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.5 },
          { date: new Date('2024-02-01'), value: 7.0 }
        ]
      })
      
      analysis.addTrend(trendData)
      
      const json = analysis.toJSON()
      
      expect(json.userId).toBe('user123')
      expect(json.type).toBe('trend')
      expect(json.title).toBe('测试分析')
      expect(json.results.trends).toHaveLength(1)
      expect(json.results.trends[0].item).toBe('白细胞计数')
    })
    
    test('应该能够从JSON创建分析实例', () => {
      const json = {
        id: 'test-id',
        userId: 'user123',
        type: 'trend',
        title: '测试分析',
        results: {
          trends: [
            {
              id: 'trend-id',
              item: '白细胞计数',
              data: [
                { id: 'point-id', date: new Date('2024-01-01'), value: 6.5 }
              ]
            }
          ]
        }
      }
      
      const analysis = Analysis.fromJSON(json)
      
      expect(analysis).toBeInstanceOf(Analysis)
      expect(analysis.userId).toBe('user123')
      expect(analysis.type).toBe('trend')
      expect(analysis.title).toBe('测试分析')
      expect(analysis.results.trends).toHaveLength(1)
      expect(analysis.results.trends[0]).toBeInstanceOf(TrendData)
    })
  })
})

describe('TrendData Model', () => {
  describe('创建趋势数据', () => {
    test('应该能够创建有效的趋势数据', () => {
      const trendData = {
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.5 },
          { date: new Date('2024-02-01'), value: 7.0 }
        ],
        trend: 'up'
      }
      
      const trend = new TrendData(trendData)
      
      expect(trend.item).toBe('白细胞计数')
      expect(trend.data).toHaveLength(2)
      expect(trend.data[0]).toBeInstanceOf(DataPoint)
      expect(trend.trend).toBe('up')
    })
    
    test('数据点少于2个应该验证失败', () => {
      const trendData = {
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.5 }
        ]
      }
      
      const trend = new TrendData(trendData)
      const validation = trend.validate()
      
      expect(validation.isValid).toBe(false)
      expect(validation.errors.some(e => e.message.includes('至少需要2个数据点'))).toBe(true)
    })
  })
  
  describe('数据点管理', () => {
    let trend
    
    beforeEach(() => {
      trend = new TrendData({
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.5 },
          { date: new Date('2024-02-01'), value: 7.0 }
        ]
      })
    })
    
    test('应该能够添加数据点', () => {
      const newPoint = {
        date: new Date('2024-03-01'),
        value: 7.5
      }
      
      trend.addDataPoint(newPoint)
      
      expect(trend.data).toHaveLength(3)
      expect(trend.data[2].value).toBe(7.5)
    })
    
    test('应该能够获取最新和最早数据点', () => {
      const latest = trend.getLatestDataPoint()
      const earliest = trend.getEarliestDataPoint()
      
      expect(latest.date).toEqual(new Date('2024-02-01'))
      expect(earliest.date).toEqual(new Date('2024-01-01'))
    })
    
    test('应该能够获取异常数据点', () => {
      trend.data[0].isAbnormal = true
      
      const abnormalPoints = trend.getAbnormalDataPoints()
      expect(abnormalPoints).toHaveLength(1)
      expect(trend.hasAbnormalData()).toBe(true)
    })
  })
  
  describe('统计计算', () => {
    let trend
    
    beforeEach(() => {
      trend = new TrendData({
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.0 },
          { date: new Date('2024-02-01'), value: 8.0 },
          { date: new Date('2024-03-01'), value: 7.0 }
        ]
      })
    })
    
    test('应该能够计算平均值', () => {
      const average = trend.getAverageValue()
      expect(average).toBeCloseTo(7.0, 1)
    })
    
    test('应该能够计算最大值和最小值', () => {
      expect(trend.getMaxValue()).toBe(8.0)
      expect(trend.getMinValue()).toBe(6.0)
    })
    
    test('应该能够计算变化幅度', () => {
      const changePercentage = trend.getChangePercentage()
      expect(changePercentage).toBeCloseTo(16.67, 1) // (7-6)/6 * 100
    })
  })
  
  describe('趋势评分', () => {
    test('应该能够计算趋势评分', () => {
      const improvingTrend = new TrendData({
        item: '测试项目',
        data: [
          { date: new Date('2024-01-01'), value: 6.0, isAbnormal: false },
          { date: new Date('2024-02-01'), value: 7.0, isAbnormal: false }
        ],
        trend: 'improving',
        trendStrength: 0.5
      })
      
      const score = improvingTrend.calculateTrendScore()
      expect(score).toBeGreaterThan(50) // 改善趋势应该有较高分数
    })
  })
  
  describe('趋势描述', () => {
    test('应该能够生成趋势描述', () => {
      const trend = new TrendData({
        item: '白细胞计数',
        data: [
          { date: new Date('2024-01-01'), value: 6.0, isAbnormal: false },
          { date: new Date('2024-02-01'), value: 8.0, isAbnormal: true }
        ]
      })
      
      const description = trend.generateDescription()
      
      expect(description).toContain('白细胞计数')
      expect(description).toContain('上升')
      expect(description).toContain('异常')
    })
  })
})

describe('DataPoint Model', () => {
  describe('创建数据点', () => {
    test('应该能够创建有效的数据点', () => {
      const pointData = {
        date: new Date('2024-01-01'),
        value: 6.5,
        isAbnormal: false
      }
      
      const point = new DataPoint(pointData)
      
      expect(point.date).toEqual(new Date('2024-01-01'))
      expect(point.value).toBe(6.5)
      expect(point.isAbnormal).toBe(false)
    })
    
    test('未来日期应该验证失败', () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 1)
      
      const point = new DataPoint({
        date: futureDate,
        value: 6.5
      })
      
      const validation = point.validate()
      expect(validation.isValid).toBe(false)
    })
  })
})

describe('ComparisonData Model', () => {
  describe('创建比较数据', () => {
    test('应该能够创建有效的比较数据', () => {
      const comparisonData = {
        item: '血糖',
        baselineValue: 5.5,
        currentValue: 6.0,
        baselineDate: new Date('2024-01-01'),
        currentDate: new Date('2024-02-01')
      }
      
      const comparison = new ComparisonData(comparisonData)
      
      expect(comparison.item).toBe('血糖')
      expect(comparison.baselineValue).toBe(5.5)
      expect(comparison.currentValue).toBe(6.0)
    })
  })
  
  describe('变化计算', () => {
    test('应该能够计算变化', () => {
      const comparison = new ComparisonData({
        item: '血糖',
        baselineValue: 5.0,
        currentValue: 6.0
      })
      
      comparison.calculateChange()
      
      expect(comparison.change).toBe(1.0)
      expect(comparison.changePercentage).toBe(20.0)
      expect(comparison.direction).toBe('up')
    })
    
    test('应该能够识别稳定状态', () => {
      const comparison = new ComparisonData({
        item: '血糖',
        baselineValue: 5.0,
        currentValue: 5.1 // 变化小于5%
      })
      
      comparison.calculateChange()
      
      expect(comparison.direction).toBe('stable')
    })
  })
})