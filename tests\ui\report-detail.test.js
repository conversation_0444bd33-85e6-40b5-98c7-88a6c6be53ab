/**
 * 报告详情页面UI测试
 * 测试页面渲染、异常指标标记和用户交互
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ReportDetail from '@/pages/report/detail.vue'
import { useReportStore } from '@/stores/report.js'
import { Report, ReportItem } from '@/models/Report.js'
import { Constants } from '@/types/index.js'

// 模拟uni-app API
global.uni = {
  navigateBack: jest.fn(),
  navigateTo: jest.fn(),
  showToast: jest.fn(),
  previewImage: jest.fn(),
  downloadFile: jest.fn().mockResolvedValue({ tempFilePath: 'temp/image.jpg' }),
  saveImageToPhotosAlbum: jest.fn().mockResolvedValue({}),
  share: jest.fn(),
  setClipboardData: jest.fn()
}

describe('报告详情页面UI测试', () => {
  let wrapper
  let pinia
  let reportStore

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    reportStore = useReportStore()
    
    // 重置模拟函数
    jest.clearAllMocks()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  /**
   * 创建测试报告数据
   */
  function createTestReport(hasAbnormal = true) {
    const items = [
      new ReportItem({
        id: 'item_1',
        name: '白细胞计数',
        value: '12.5',
        unit: '×10^9/L',
        referenceRange: '3.5-9.5',
        isAbnormal: hasAbnormal,
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
        numericValue: 12.5,
        minReference: 3.5,
        maxReference: 9.5
      }),
      new ReportItem({
        id: 'item_2',
        name: '红细胞计数',
        value: '4.5',
        unit: '×10^12/L',
        referenceRange: '4.3-5.8',
        isAbnormal: false,
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
        numericValue: 4.5,
        minReference: 4.3,
        maxReference: 5.8
      }),
      new ReportItem({
        id: 'item_3',
        name: '血红蛋白',
        value: '85',
        unit: 'g/L',
        referenceRange: '130-175',
        isAbnormal: hasAbnormal,
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
        numericValue: 85,
        minReference: 130,
        maxReference: 175
      })
    ]

    return new Report({
      id: 'test_report_1',
      userId: 'test_user',
      title: '血常规检查报告',
      hospital: '北京协和医院',
      doctor: '张医生',
      department: '内科',
      checkDate: new Date('2024-01-15'),
      reportDate: new Date('2024-01-15'),
      category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
      items,
      originalImage: 'https://example.com/report.jpg',
      notes: '患者需要注意休息，定期复查。'
    })
  }

  describe('页面基本渲染测试', () => {
    it('应该正确渲染加载状态', () => {
      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      expect(wrapper.find('.loading-container').exists()).toBe(true)
      expect(wrapper.vm.loading).toBe(true)
    })

    it('应该正确渲染报告详情', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      // 模拟页面加载
      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      expect(wrapper.find('.detail-content').exists()).toBe(true)
      expect(wrapper.find('.report-header').exists()).toBe(true)
      expect(wrapper.find('.hospital-name').text()).toBe('北京协和医院')
    })

    it('应该正确渲染错误状态', async () => {
      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      // 设置报告不存在的状态
      wrapper.vm.reportId = 'non_existent_report'
      await wrapper.vm.loadReportDetail()

      expect(wrapper.find('.error-state').exists()).toBe(true)
      expect(wrapper.find('.error-title').text()).toBe('报告不存在')
    })
  })

  describe('异常指标标记测试', () => {
    it('应该正确显示整体状态', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const statusBadge = wrapper.find('.status-badge')
      expect(statusBadge.exists()).toBe(true)
      
      // 验证异常状态
      const overallStatus = wrapper.vm.overallStatus
      expect(overallStatus.type).toBe('abnormal')
      expect(overallStatus.text).toBe('异常')
    })

    it('应该正确显示严重异常警告', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 检查是否有严重异常项目
      const criticalItems = wrapper.vm.criticalAbnormalItems
      if (criticalItems.length > 0) {
        expect(wrapper.find('.critical-alert').exists()).toBe(true)
        expect(wrapper.find('.alert-title').text()).toBe('严重异常指标')
      }
    })

    it('应该正确标记异常项目', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const itemCards = wrapper.findAll('.item-card')
      expect(itemCards.length).toBeGreaterThan(0)

      // 检查异常项目的样式类
      const abnormalItems = itemCards.filter(card => 
        card.classes().some(cls => ['mild', 'moderate', 'severe'].includes(cls))
      )
      expect(abnormalItems.length).toBeGreaterThan(0)
    })

    it('应该正确显示异常提示', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const abnormalHints = wrapper.findAll('.abnormal-hint')
      expect(abnormalHints.length).toBeGreaterThan(0)

      // 验证提示内容
      const hintText = abnormalHints[0].find('.hint-text')
      expect(hintText.exists()).toBe(true)
      expect(hintText.text()).toContain('异常')
    })
  })

  describe('筛选标签功能测试', () => {
    it('应该正确显示筛选标签', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const filterTabs = wrapper.findAll('.filter-tab')
      expect(filterTabs.length).toBe(4) // 全部、异常、正常、严重

      // 验证标签文本
      const tabTexts = filterTabs.map(tab => tab.find('.tab-text').text())
      expect(tabTexts).toContain('全部')
      expect(tabTexts).toContain('异常')
      expect(tabTexts).toContain('正常')
      expect(tabTexts).toContain('严重')
    })

    it('应该能够切换筛选标签', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 点击异常标签
      const abnormalTab = wrapper.findAll('.filter-tab').find(tab => 
        tab.find('.tab-text').text() === '异常'
      )

      if (abnormalTab) {
        await abnormalTab.trigger('tap')
        expect(wrapper.vm.activeTab).toBe('abnormal')
        
        // 验证筛选结果
        const filteredItems = wrapper.vm.filteredItems
        expect(filteredItems.every(item => item.isAbnormal)).toBe(true)
      }
    })

    it('应该正确更新标签计数', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const filterTabs = wrapper.vm.filterTabs
      const allTab = filterTabs.find(tab => tab.key === 'all')
      const abnormalTab = filterTabs.find(tab => tab.key === 'abnormal')
      const normalTab = filterTabs.find(tab => tab.key === 'normal')

      expect(allTab.count).toBe(testReport.items.length)
      expect(abnormalTab.count).toBe(wrapper.vm.abnormalCount)
      expect(normalTab.count).toBe(wrapper.vm.normalCount)
    })
  })

  describe('用户交互测试', () => {
    it('应该能够预览图片', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 点击图片预览
      const reportImage = wrapper.find('.report-image')
      if (reportImage.exists()) {
        await reportImage.trigger('tap')
        expect(global.uni.previewImage).toHaveBeenCalledWith({
          urls: [testReport.originalImage],
          current: testReport.originalImage
        })
      }
    })

    it('应该能够保存图片', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 点击保存图片按钮
      await wrapper.vm.saveImage()

      expect(global.uni.downloadFile).toHaveBeenCalledWith({
        url: testReport.originalImage
      })
      expect(global.uni.saveImageToPhotosAlbum).toHaveBeenCalled()
    })

    it('应该能够显示项目详情弹窗', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': {
              template: '<div class="popup-mock"><slot /></div>',
              methods: {
                open: jest.fn(),
                close: jest.fn()
              }
            }
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 点击项目卡片
      const itemCard = wrapper.find('.item-card')
      if (itemCard.exists()) {
        await itemCard.trigger('tap')
        
        expect(wrapper.vm.selectedItem).toBeTruthy()
        expect(wrapper.vm.$refs.itemDetailPopup.open).toHaveBeenCalled()
      }
    })

    it('应该能够分享报告', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 调用分享功能
      await wrapper.vm.shareReport()

      expect(global.uni.share).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'weixin',
          title: '健康检查报告'
        })
      )
    })

    it('应该能够编辑报告', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 点击编辑按钮
      await wrapper.vm.editReport()

      expect(global.uni.navigateTo).toHaveBeenCalledWith({
        url: `/pages/report/add?reportId=${testReport.id}`
      })
    })
  })

  describe('健康建议测试', () => {
    it('应该根据异常情况生成健康建议', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const healthSuggestions = wrapper.vm.healthSuggestions
      expect(healthSuggestions.length).toBeGreaterThan(0)

      // 验证建议内容
      const hasFollowUpSuggestion = healthSuggestions.some(s => 
        s.title.includes('复查')
      )
      expect(hasFollowUpSuggestion).toBe(true)
    })

    it('应该显示健康建议卡片', async () => {
      const testReport = createTestReport(true)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      if (wrapper.vm.healthSuggestions.length > 0) {
        expect(wrapper.find('.suggestions-card').exists()).toBe(true)
        expect(wrapper.findAll('.suggestion-item').length).toBeGreaterThan(0)
      }
    })
  })

  describe('异常严重程度判断测试', () => {
    it('应该正确判断轻度异常', () => {
      const item = new ReportItem({
        id: 'test_item',
        name: '测试项目',
        value: '10.5',
        isAbnormal: true,
        numericValue: 10.5,
        minReference: 9.0,
        maxReference: 12.0
      })

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      const severity = wrapper.vm.getAbnormalSeverity(item)
      expect(severity).toBe('mild')
    })

    it('应该正确判断中度异常', () => {
      const item = new ReportItem({
        id: 'test_item',
        name: '测试项目',
        value: '15.0',
        isAbnormal: true,
        numericValue: 15.0,
        minReference: 9.0,
        maxReference: 12.0
      })

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      const severity = wrapper.vm.getAbnormalSeverity(item)
      expect(severity).toBe('moderate')
    })

    it('应该正确判断严重异常', () => {
      const item = new ReportItem({
        id: 'test_item',
        name: '测试项目',
        value: '20.0',
        isAbnormal: true,
        numericValue: 20.0,
        minReference: 9.0,
        maxReference: 12.0
      })

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      const severity = wrapper.vm.getAbnormalSeverity(item)
      expect(severity).toBe('severe')
    })
  })

  describe('响应式设计测试', () => {
    it('应该在不同屏幕尺寸下正确显示', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      // 验证关键元素存在
      expect(wrapper.find('.report-header').exists()).toBe(true)
      expect(wrapper.find('.items-card').exists()).toBe(true)
      expect(wrapper.find('.bottom-actions').exists()).toBe(true)
    })

    it('应该正确处理长文本内容', async () => {
      const testReport = createTestReport()
      testReport.notes = '这是一个非常长的备注信息，用于测试页面在处理长文本时的表现。'.repeat(10)
      reportStore.setReports([testReport])

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const notesCard = wrapper.find('.notes-card')
      expect(notesCard.exists()).toBe(true)
      
      const notesContent = wrapper.find('.notes-content')
      expect(notesContent.text()).toContain('这是一个非常长的备注信息')
    })
  })

  describe('性能优化测试', () => {
    it('应该能够处理大量检查项目', async () => {
      const testReport = createTestReport()
      
      // 添加大量检查项目
      for (let i = 0; i < 50; i++) {
        testReport.items.push(new ReportItem({
          id: `item_${i + 10}`,
          name: `检查项目 ${i + 1}`,
          value: (Math.random() * 100).toFixed(2),
          unit: 'mg/dL',
          referenceRange: '0-100',
          isAbnormal: Math.random() > 0.7,
          category: Constants.REPORT_CATEGORIES.BIOCHEMISTRY
        }))
      }
      
      reportStore.setReports([testReport])

      const startTime = performance.now()

      wrapper = mount(ReportDetail, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-load-more': true,
            'uni-icons': true,
            'uni-popup': true
          }
        }
      })

      wrapper.vm.reportId = 'test_report_1'
      await wrapper.vm.loadReportDetail()

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // 渲染时间应该在合理范围内
      expect(renderTime).toBeLessThan(200)
      expect(wrapper.findAll('.item-card').length).toBeGreaterThan(0)
    })
  })
})