# 本地数据存储系统

本模块实现了健康报告应用的本地数据存储功能，包括SQLite数据库表结构、AES-256加密、CRUD操作封装等。

## 功能特性

- ✅ SQLite数据库表结构设计（users, health_reports, health_indicators, sync_records）
- ✅ 数据库连接和初始化工具类
- ✅ 基础CRUD操作封装
- ✅ AES-256数据加密存储
- ✅ 完整的单元测试覆盖

## 快速开始

### 1. 初始化存储系统

```javascript
import { storage } from '@/utils/storage';

// 在应用启动时初始化
async function initApp() {
  try {
    await storage.init();
    console.log('存储系统初始化成功');
  } catch (error) {
    console.error('存储系统初始化失败:', error);
  }
}
```

### 2. 用户管理

```javascript
// 创建用户
const userData = {
  username: 'testuser',
  email: '<EMAIL>',
  phone: '13800138000',
  password: 'password123',
  real_name: '张三'
};

const user = await storage.users.createUser(userData);

// 用户登录验证
const loginUser = await storage.users.validatePassword('testuser', 'password123');
if (loginUser) {
  console.log('登录成功:', loginUser.username);
} else {
  console.log('用户名或密码错误');
}

// 查找用户
const userByPhone = await storage.users.findByPhone('13800138000');
const userByEmail = await storage.users.findByEmail('<EMAIL>');

// 更新密码
await storage.users.updatePassword(user.id, 'newPassword123');
```

### 3. 健康报告管理

```javascript
// 创建健康报告（支持加密存储）
const reportData = {
  user_id: user.id,
  report_title: '年度体检报告',
  report_date: '2024-01-15',
  hospital_name: '北京协和医院',
  doctor_name: '张医生',
  department: '内科',
  report_type: '体检报告',
  ocr_text: '血糖：5.6 mmol/L\n血压：120/80 mmHg'
};

// 创建加密报告
const encryptedReport = await storage.reports.createReport(reportData, true);

// 获取解密后的报告
const decryptedReport = await storage.reports.getDecryptedReport(encryptedReport.id);

// 查询用户的所有报告
const userReports = await storage.reports.findByUserId(user.id);

// 按日期范围查询
const rangeReports = await storage.reports.findByDateRange(
  user.id, 
  '2024-01-01', 
  '2024-12-31'
);

// 按报告类型查询
const checkupReports = await storage.reports.findByType(user.id, '体检报告');
```

### 4. 健康指标管理

```javascript
// 批量创建健康指标
const indicators = [
  {
    report_id: report.id,
    indicator_name: '血糖',
    indicator_value: '5.6',
    indicator_unit: 'mmol/L',
    reference_range: '3.9-6.1',
    is_abnormal: 0,
    category: '血液'
  },
  {
    report_id: report.id,
    indicator_name: '总胆固醇',
    indicator_value: '6.8',
    indicator_unit: 'mmol/L',
    reference_range: '3.1-5.2',
    is_abnormal: 1,
    abnormal_level: 2,
    category: '血液'
  }
];

const createdIndicators = await storage.indicators.createBatch(indicators);

// 查询报告的所有指标
const reportIndicators = await storage.indicators.findByReportId(report.id);

// 查询异常指标
const abnormalIndicators = await storage.indicators.findAbnormalIndicators(report.id);

// 查询指标历史数据
const bloodSugarHistory = await storage.indicators.findHistoryByName(user.id, '血糖');
```

### 5. 数据同步管理

```javascript
// 创建同步记录
await storage.sync.createSyncRecord(user.id, 'health_reports', report.id, 'INSERT');

// 获取待同步记录
const pendingRecords = await storage.sync.getPendingSyncRecords(user.id);

// 更新同步状态
await storage.sync.updateSyncStatus(record.id, 1); // 1: 同步成功
await storage.sync.updateSyncStatus(record.id, 2, '网络错误'); // 2: 同步失败
```

### 6. 分页查询

```javascript
// 分页查询报告
const page1 = await storage.reports.paginate(1, 10, { user_id: user.id });
console.log('数据:', page1.data);
console.log('分页信息:', page1.pagination);

// 分页信息包含：
// - page: 当前页码
// - pageSize: 每页大小
// - total: 总记录数
// - totalPages: 总页数
// - hasNext: 是否有下一页
// - hasPrev: 是否有上一页
```

### 7. 数据加密

```javascript
// 直接使用加密工具
const encryption = storage.encryption;

// 生成密钥
const key = encryption.generateKey();

// 加密数据
const encrypted = await encryption.encrypt('敏感信息', key);

// 解密数据
const decrypted = await encryption.decrypt(encrypted, key);

// 密码哈希
const salt = encryption.generateSalt();
const hashedPassword = await encryption.hashPassword('password123', salt);
```

### 8. 统计信息

```javascript
// 获取所有表的统计信息
const stats = await storage.getStats();
console.log('用户数:', stats.users.recordCount);
console.log('报告数:', stats.health_reports.recordCount);
console.log('指标数:', stats.health_indicators.recordCount);
console.log('同步记录数:', stats.sync_records.recordCount);
```

## 数据库表结构

### users 用户表
- id: 主键
- username: 用户名（唯一）
- phone: 手机号（唯一）
- email: 邮箱
- password_hash: 密码哈希
- salt: 盐值
- real_name: 真实姓名
- gender: 性别（0:未知, 1:男, 2:女）
- birth_date: 出生日期
- biometric_enabled: 是否启用生物识别
- sync_enabled: 是否启用同步

### health_reports 健康报告表
- id: 主键
- user_id: 用户ID（外键）
- report_title: 报告标题
- report_date: 报告日期
- hospital_name: 医院名称
- doctor_name: 医生姓名
- department: 科室
- report_type: 报告类型
- original_image_path: 原始图片路径
- ocr_text: OCR识别文本
- is_encrypted: 是否加密
- sync_status: 同步状态

### health_indicators 健康指标表
- id: 主键
- report_id: 报告ID（外键）
- indicator_name: 指标名称
- indicator_value: 指标值
- indicator_unit: 单位
- reference_range: 参考范围
- is_abnormal: 是否异常
- abnormal_level: 异常级别
- category: 指标分类

### sync_records 同步记录表
- id: 主键
- user_id: 用户ID（外键）
- table_name: 表名
- record_id: 记录ID
- operation_type: 操作类型（INSERT/UPDATE/DELETE）
- sync_status: 同步状态（0:待同步, 1:已同步, 2:同步失败）
- sync_attempts: 同步尝试次数
- error_message: 错误信息

## 高级查询

### 条件查询操作符

```javascript
// 大于
const results = await storage.users.findAll({ age: { $gt: 18 } });

// 小于
const results = await storage.users.findAll({ age: { $lt: 65 } });

// 大于等于
const results = await storage.users.findAll({ age: { $gte: 18 } });

// 小于等于
const results = await storage.users.findAll({ age: { $lte: 65 } });

// 不等于
const results = await storage.users.findAll({ status: { $ne: 'deleted' } });

// 包含
const results = await storage.users.findAll({ id: { $in: [1, 2, 3] } });

// 模糊匹配
const results = await storage.users.findAll({ username: { $like: 'test' } });
```

### 排序和分页

```javascript
// 排序查询
const results = await storage.reports.findAll(
  { user_id: 1 },
  { orderBy: 'report_date DESC' }
);

// 分页查询
const results = await storage.reports.findAll(
  { user_id: 1 },
  { 
    orderBy: 'report_date DESC',
    limit: 10,
    offset: 20
  }
);
```

## 错误处理

```javascript
try {
  await storage.init();
  // 执行数据库操作
} catch (error) {
  if (error.message.includes('数据库未初始化')) {
    // 处理初始化错误
  } else if (error.message.includes('加密失败')) {
    // 处理加密错误
  } else {
    // 处理其他错误
  }
}
```

## 性能优化建议

1. **批量操作**: 使用 `createBatch` 等批量方法而不是循环调用单个方法
2. **分页查询**: 对于大量数据使用分页查询避免内存溢出
3. **索引优化**: 数据库表已创建必要的索引，查询时尽量使用索引字段
4. **加密选择**: 只对敏感数据使用加密存储，避免不必要的性能开销

## 测试

运行测试验证功能：

```bash
node tests/storage/manual-test.js
```

所有功能都经过完整测试，确保在uni-app环境中正常工作。