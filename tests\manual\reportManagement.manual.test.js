/**
 * 健康报告管理功能手动测试
 * 用于验证报告管理功能的基本实现
 */

// 模拟uni-app环境
global.uni = {
  setStorageSync: (key, data) => {
    console.log(`存储数据 ${key}:`, data)
  },
  getStorageSync: (key) => {
    console.log(`获取数据 ${key}`)
    return null
  },
  removeStorageSync: (key) => {
    console.log(`删除数据 ${key}`)
  },
  showToast: (options) => {
    console.log('显示提示:', options.title)
  },
  showModal: (options) => {
    console.log('显示弹窗:', options.title, options.content)
    if (options.success) {
      options.success({ confirm: true })
    }
  }
}

// 导入要测试的模块
import { ReportValidator } from '../../utils/validation/reportValidator.js'

// 测试数据
const validReportData = {
  userId: 'test_user_001',
  hospitalName: '测试医院',
  reportDate: '2024-01-15',
  doctorName: '张医生',
  department: '内科',
  reportType: '血液检查',
  notes: '患者状态良好',
  indicators: [
    {
      name: '血糖',
      value: '5.6',
      unit: 'mmol/L',
      referenceRange: '3.9-6.1',
      isAbnormal: false,
      category: '血液指标',
      description: '空腹血糖正常'
    },
    {
      name: '胆固醇',
      value: '6.8',
      unit: 'mmol/L',
      referenceRange: '3.1-5.2',
      isAbnormal: true,
      category: '血液指标',
      description: '胆固醇偏高'
    }
  ]
}

const invalidReportData = {
  userId: 'test_user_001',
  hospitalName: '', // 无效：空医院名称
  reportDate: '2025-12-31', // 无效：未来日期
  indicators: [
    {
      name: '', // 无效：空指标名称
      value: 'invalid', // 无效：非数值
      category: '血液指标'
    }
  ]
}

console.log('=== 健康报告管理功能测试 ===\n')

// 测试1: 数据验证功能
console.log('1. 测试数据验证功能')
console.log('-------------------')

try {
  // 测试有效数据验证
  const validValidation = ReportValidator.validateCompleteReport(validReportData)
  console.log('✅ 有效数据验证结果:', {
    isValid: validValidation.isValid,
    errorCount: Object.keys(validValidation.errors).length
  })
  
  // 测试无效数据验证
  const invalidValidation = ReportValidator.validateCompleteReport(invalidReportData)
  console.log('❌ 无效数据验证结果:', {
    isValid: invalidValidation.isValid,
    errorCount: Object.keys(invalidValidation.errors).length,
    errors: Object.keys(invalidValidation.errors)
  })
  
  console.log('✅ 数据验证功能测试通过\n')
} catch (error) {
  console.error('❌ 数据验证功能测试失败:', error.message)
}

// 测试2: 数据完整性检查
console.log('2. 测试数据完整性检查')
console.log('---------------------')

try {
  const integrity = ReportValidator.checkDataIntegrity(validReportData)
  console.log('✅ 数据完整性检查结果:', {
    isComplete: integrity.isComplete,
    completeness: integrity.completeness + '%',
    issueCount: integrity.issues.length,
    warningCount: integrity.warnings.length
  })
  
  if (integrity.issues.length > 0) {
    console.log('问题:', integrity.issues)
  }
  
  if (integrity.warnings.length > 0) {
    console.log('警告:', integrity.warnings)
  }
  
  console.log('✅ 数据完整性检查测试通过\n')
} catch (error) {
  console.error('❌ 数据完整性检查测试失败:', error.message)
}

// 测试3: 数据清理和标准化
console.log('3. 测试数据清理和标准化')
console.log('-----------------------')

try {
  const dirtyData = {
    ...validReportData,
    hospitalName: '  测试医院  ', // 有空格
    doctorName: '  张医生  ',
    indicators: [
      {
        name: '  血糖  ',
        value: '  5.6  ',
        unit: '  mmol/L  ',
        category: '血液指标',
        isAbnormal: 'false' // 字符串类型
      }
    ]
  }
  
  const cleanedData = ReportValidator.sanitizeReportData(dirtyData)
  console.log('✅ 数据清理结果:', {
    hospitalName: `"${cleanedData.hospitalName}"`,
    doctorName: `"${cleanedData.doctorName}"`,
    indicatorName: `"${cleanedData.indicators[0].name}"`,
    indicatorValue: `"${cleanedData.indicators[0].value}"`,
    isAbnormal: cleanedData.indicators[0].isAbnormal,
    isAbnormalType: typeof cleanedData.indicators[0].isAbnormal
  })
  
  console.log('✅ 数据清理和标准化测试通过\n')
} catch (error) {
  console.error('❌ 数据清理和标准化测试失败:', error.message)
}

// 测试4: 验证报告生成
console.log('4. 测试验证报告生成')
console.log('-------------------')

try {
  const validationReport = ReportValidator.generateValidationReport(validReportData)
  console.log('✅ 验证报告生成结果:', {
    status: validationReport.summary.status,
    score: validationReport.summary.score,
    isValid: validationReport.validation.isValid,
    isComplete: validationReport.integrity.isComplete,
    recommendationCount: validationReport.summary.recommendations.length
  })
  
  if (validationReport.summary.recommendations.length > 0) {
    console.log('建议:', validationReport.summary.recommendations)
  }
  
  console.log('✅ 验证报告生成测试通过\n')
} catch (error) {
  console.error('❌ 验证报告生成测试失败:', error.message)
}

// 测试5: 边界情况测试
console.log('5. 测试边界情况')
console.log('---------------')

try {
  // 测试空数据
  const emptyValidation = ReportValidator.validateCompleteReport({})
  console.log('✅ 空数据验证:', {
    isValid: emptyValidation.isValid,
    errorCount: Object.keys(emptyValidation.errors).length
  })
  
  // 测试null/undefined
  const nullValidation = ReportValidator.validateCompleteReport(null)
  console.log('✅ null数据验证:', {
    isValid: nullValidation.isValid,
    errorCount: Object.keys(nullValidation.errors).length
  })
  
  // 测试超长字符串
  const longStringData = {
    ...validReportData,
    hospitalName: 'a'.repeat(200), // 超过100字符限制
    notes: 'b'.repeat(2000) // 超过1000字符限制
  }
  
  const longStringValidation = ReportValidator.validateCompleteReport(longStringData)
  console.log('✅ 超长字符串验证:', {
    isValid: longStringValidation.isValid,
    hasHospitalNameError: 'hospitalName' in longStringValidation.errors,
    hasNotesError: 'notes' in longStringValidation.errors
  })
  
  console.log('✅ 边界情况测试通过\n')
} catch (error) {
  console.error('❌ 边界情况测试失败:', error.message)
}

console.log('=== 测试完成 ===')
console.log('所有核心功能测试通过！报告管理功能实现正确。')