<template>
	<view class="page-container">
		<view class="add-report-container">
			<!-- 图片上传区域 -->
			<view class="image-section">
				<view class="section-title">检查报告图片</view>
				<view class="image-upload-area" @click="selectImage">
					<image v-if="reportForm.originalImagePath" 
						   :src="reportForm.originalImagePath" 
						   class="uploaded-image" 
						   mode="aspectFit" />
					<view v-else class="upload-placeholder">
						<text class="upload-icon">📷</text>
						<text class="upload-text">点击上传检查报告图片</text>
						<text class="upload-hint">支持拍照或从相册选择</text>
					</view>
				</view>
				
				<!-- OCR识别结果 -->
				<view v-if="ocrResult.text" class="ocr-result">
					<view class="ocr-title">OCR识别结果</view>
					<textarea class="ocr-text" 
							  :value="ocrResult.text" 
							  readonly 
							  placeholder="OCR识别的文本内容将显示在这里" />
					<button class="btn-secondary" @click="autoFillFromOCR">自动填充表单</button>
				</view>
			</view>

			<!-- 基本信息表单 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				
				<view class="form-item">
					<text class="form-label">医院名称 *</text>
					<input class="form-input" 
						   v-model="reportForm.hospitalName" 
						   placeholder="请输入医院名称" 
						   :class="{ 'error': errors.hospitalName }" />
					<text v-if="errors.hospitalName" class="error-text">{{ errors.hospitalName }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">检查日期 *</text>
					<picker mode="date" 
							:value="reportForm.reportDate" 
							@change="onDateChange"
							:class="{ 'error': errors.reportDate }">
						<view class="picker-input">
							{{ reportForm.reportDate || '请选择检查日期' }}
						</view>
					</picker>
					<text v-if="errors.reportDate" class="error-text">{{ errors.reportDate }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">医生姓名</text>
					<input class="form-input" 
						   v-model="reportForm.doctorName" 
						   placeholder="请输入医生姓名" />
				</view>
				
				<view class="form-item">
					<text class="form-label">科室</text>
					<input class="form-input" 
						   v-model="reportForm.department" 
						   placeholder="请输入科室名称" />
				</view>
				
				<view class="form-item">
					<text class="form-label">检查类型</text>
					<picker :range="reportTypes" 
							:value="reportTypeIndex" 
							@change="onReportTypeChange">
						<view class="picker-input">
							{{ reportForm.reportType || '请选择检查类型' }}
						</view>
					</picker>
				</view>
			</view>

			<!-- 健康指标 -->
			<view class="indicators-section">
				<view class="section-header">
					<text class="section-title">健康指标</text>
					<button class="btn-add" @click="addIndicator">+ 添加指标</button>
				</view>
				
				<view v-for="(indicator, index) in reportForm.indicators" 
					  :key="index" 
					  class="indicator-item">
					<view class="indicator-header">
						<text class="indicator-index">指标 {{ index + 1 }}</text>
						<button class="btn-remove" @click="removeIndicator(index)">删除</button>
					</view>
					
					<view class="indicator-form">
						<view class="form-row">
							<view class="form-item half">
								<text class="form-label">指标名称 *</text>
								<input class="form-input" 
									   v-model="indicator.name" 
									   placeholder="如：血糖" 
									   :class="{ 'error': errors[`indicator_${index}_name`] }" />
								<text v-if="errors[`indicator_${index}_name`]" class="error-text">
									{{ errors[`indicator_${index}_name`] }}
								</text>
							</view>
							<view class="form-item half">
								<text class="form-label">数值 *</text>
								<input class="form-input" 
									   v-model="indicator.value" 
									   placeholder="如：5.6" 
									   :class="{ 'error': errors[`indicator_${index}_value`] }" />
								<text v-if="errors[`indicator_${index}_value`]" class="error-text">
									{{ errors[`indicator_${index}_value`] }}
								</text>
							</view>
						</view>
						
						<view class="form-row">
							<view class="form-item half">
								<text class="form-label">单位</text>
								<input class="form-input" 
									   v-model="indicator.unit" 
									   placeholder="如：mmol/L" />
							</view>
							<view class="form-item half">
								<text class="form-label">类别</text>
								<picker :range="indicatorCategories" 
										:value="getCategoryIndex(indicator.category)" 
										@change="onCategoryChange(index, $event)">
									<view class="picker-input">
										{{ indicator.category || '请选择类别' }}
									</view>
								</picker>
							</view>
						</view>
						
						<view class="form-item">
							<text class="form-label">参考范围</text>
							<input class="form-input" 
								   v-model="indicator.referenceRange" 
								   placeholder="如：3.9-6.1" />
						</view>
						
						<view class="form-item">
							<text class="form-label">备注</text>
							<textarea class="form-textarea" 
									  v-model="indicator.description" 
									  placeholder="指标相关说明" />
						</view>
						
						<view class="form-item">
							<label class="checkbox-label">
								<checkbox :checked="indicator.isAbnormal" 
										  @change="onAbnormalChange(index, $event)" />
								<text>标记为异常指标</text>
							</label>
						</view>
					</view>
				</view>
				
				<view v-if="reportForm.indicators.length === 0" class="empty-indicators">
					<text class="empty-text">暂无健康指标，点击"添加指标"开始录入</text>
				</view>
			</view>

			<!-- 备注信息 -->
			<view class="notes-section">
				<view class="section-title">备注信息</view>
				<textarea class="form-textarea large" 
						  v-model="reportForm.notes" 
						  placeholder="请输入其他备注信息" />
			</view>

			<!-- 操作按钮 -->
			<view class="action-buttons">
				<button class="btn-secondary" @click="saveDraft">保存草稿</button>
				<button class="btn-primary" 
						@click="submitReport" 
						:disabled="submitting">
					{{ submitting ? '提交中...' : '提交报告' }}
				</button>
			</view>
		</view>
		
		<!-- 加载遮罩 -->
		<view v-if="loading" class="loading-overlay">
			<view class="loading-content">
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { useReportStore } from '@/stores/report.js'
	import { useUserStore } from '@/stores/user.js'
	import reportService from '@/services/report/index.js'
	import ocrService from '@/services/ocr/index.js'
	
	export default {
		name: 'AddReport',
		data() {
			return {
				// 表单数据
				reportForm: {
					hospitalName: '',
					reportDate: '',
					doctorName: '',
					department: '',
					reportType: '常规检查',
					originalImagePath: '',
					ocrText: '',
					notes: '',
					indicators: []
				},
				
				// OCR识别结果
				ocrResult: {
					text: '',
					structured: null
				},
				
				// 表单验证错误
				errors: {},
				
				// 状态
				loading: false,
				loadingText: '',
				submitting: false,
				
				// 选项数据
				reportTypes: [
					'常规检查', '血液检查', '尿液检查', '生化检查', 
					'免疫检查', '内分泌检查', '肿瘤标志物', '其他检查'
				],
				reportTypeIndex: 0,
				
				indicatorCategories: [
					'血液指标', '尿液指标', '生化指标', '免疫指标',
					'内分泌指标', '肿瘤标志物', '其他指标'
				]
			}
		},
		
		onLoad(options) {
			// 如果是编辑模式
			if (options.reportId) {
				this.loadReportForEdit(options.reportId)
			}
			
			// 设置默认日期为今天
			if (!this.reportForm.reportDate) {
				const today = new Date()
				this.reportForm.reportDate = today.toISOString().split('T')[0]
			}
		},
		
		methods: {
			/**
			 * 选择图片
			 */
			selectImage() {
				uni.showActionSheet({
					itemList: ['拍照', '从相册选择'],
					success: (res) => {
						if (res.tapIndex === 0) {
							this.takePhoto()
						} else {
							this.chooseFromAlbum()
						}
					}
				})
			},
			
			/**
			 * 拍照
			 */
			takePhoto() {
				uni.chooseImage({
					count: 1,
					sourceType: ['camera'],
					success: (res) => {
						this.handleImageSelected(res.tempFilePaths[0])
					},
					fail: (error) => {
						console.error('拍照失败:', error)
						uni.showToast({
							title: '拍照失败',
							icon: 'none'
						})
					}
				})
			},
			
			/**
			 * 从相册选择
			 */
			chooseFromAlbum() {
				uni.chooseImage({
					count: 1,
					sourceType: ['album'],
					success: (res) => {
						this.handleImageSelected(res.tempFilePaths[0])
					},
					fail: (error) => {
						console.error('选择图片失败:', error)
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						})
					}
				})
			},
			
			/**
			 * 处理选中的图片
			 */
			async handleImageSelected(imagePath) {
				try {
					this.reportForm.originalImagePath = imagePath
					
					// 开始OCR识别
					this.loading = true
					this.loadingText = 'OCR识别中...'
					
					const ocrResult = await ocrService.recognizeHealthReport(imagePath)
					this.ocrResult = ocrResult
					this.reportForm.ocrText = ocrResult.text
					
					uni.showToast({
						title: 'OCR识别完成',
						icon: 'success'
					})
				} catch (error) {
					console.error('OCR识别失败:', error)
					uni.showToast({
						title: 'OCR识别失败',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			
			/**
			 * 从OCR结果自动填充表单
			 */
			autoFillFromOCR() {
				if (!this.ocrResult.structured) {
					uni.showToast({
						title: '暂无结构化数据可填充',
						icon: 'none'
					})
					return
				}
				
				const structured = this.ocrResult.structured
				
				// 填充基本信息
				if (structured.hospitalName) {
					this.reportForm.hospitalName = structured.hospitalName
				}
				if (structured.reportDate) {
					this.reportForm.reportDate = structured.reportDate
				}
				if (structured.doctorName) {
					this.reportForm.doctorName = structured.doctorName
				}
				if (structured.department) {
					this.reportForm.department = structured.department
				}
				
				// 填充健康指标
				if (structured.indicators && structured.indicators.length > 0) {
					this.reportForm.indicators = structured.indicators.map(indicator => ({
						name: indicator.name || '',
						value: indicator.value || '',
						unit: indicator.unit || '',
						referenceRange: indicator.referenceRange || '',
						isAbnormal: indicator.isAbnormal || false,
						category: indicator.category || '其他指标',
						description: indicator.description || ''
					}))
				}
				
				uni.showToast({
					title: '自动填充完成',
					icon: 'success'
				})
			},
			
			/**
			 * 日期变化
			 */
			onDateChange(e) {
				this.reportForm.reportDate = e.detail.value
				this.clearError('reportDate')
			},
			
			/**
			 * 检查类型变化
			 */
			onReportTypeChange(e) {
				this.reportTypeIndex = e.detail.value
				this.reportForm.reportType = this.reportTypes[e.detail.value]
			},
			
			/**
			 * 添加健康指标
			 */
			addIndicator() {
				this.reportForm.indicators.push({
					name: '',
					value: '',
					unit: '',
					referenceRange: '',
					isAbnormal: false,
					category: '其他指标',
					description: ''
				})
			},
			
			/**
			 * 删除健康指标
			 */
			removeIndicator(index) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这个健康指标吗？',
					success: (res) => {
						if (res.confirm) {
							this.reportForm.indicators.splice(index, 1)
							// 清除相关错误信息
							this.clearIndicatorErrors(index)
						}
					}
				})
			},
			
			/**
			 * 指标类别变化
			 */
			onCategoryChange(index, e) {
				this.reportForm.indicators[index].category = this.indicatorCategories[e.detail.value]
			},
			
			/**
			 * 异常状态变化
			 */
			onAbnormalChange(index, e) {
				this.reportForm.indicators[index].isAbnormal = e.detail.value.length > 0
			},
			
			/**
			 * 获取类别索引
			 */
			getCategoryIndex(category) {
				const index = this.indicatorCategories.indexOf(category)
				return index >= 0 ? index : 0
			},
			
			/**
			 * 表单验证
			 */
			validateForm() {
				this.errors = {}
				let isValid = true
				
				// 验证基本信息
				if (!this.reportForm.hospitalName.trim()) {
					this.errors.hospitalName = '医院名称不能为空'
					isValid = false
				}
				
				if (!this.reportForm.reportDate) {
					this.errors.reportDate = '检查日期不能为空'
					isValid = false
				}
				
				// 验证健康指标
				this.reportForm.indicators.forEach((indicator, index) => {
					if (!indicator.name.trim()) {
						this.errors[`indicator_${index}_name`] = '指标名称不能为空'
						isValid = false
					}
					
					if (!indicator.value.trim()) {
						this.errors[`indicator_${index}_value`] = '指标数值不能为空'
						isValid = false
					}
				})
				
				return isValid
			},
			
			/**
			 * 清除错误信息
			 */
			clearError(field) {
				if (this.errors[field]) {
					delete this.errors[field]
				}
			},
			
			/**
			 * 清除指标错误信息
			 */
			clearIndicatorErrors(index) {
				const keysToRemove = Object.keys(this.errors).filter(key => 
					key.startsWith(`indicator_${index}_`)
				)
				keysToRemove.forEach(key => {
					delete this.errors[key]
				})
			},
			
			/**
			 * 保存草稿
			 */
			async saveDraft() {
				try {
					// 保存到本地存储
					uni.setStorageSync('report_draft', this.reportForm)
					uni.showToast({
						title: '草稿已保存',
						icon: 'success'
					})
				} catch (error) {
					console.error('保存草稿失败:', error)
					uni.showToast({
						title: '保存草稿失败',
						icon: 'none'
					})
				}
			},
			
			/**
			 * 提交报告
			 */
			async submitReport() {
				if (!this.validateForm()) {
					uni.showToast({
						title: '请检查表单信息',
						icon: 'none'
					})
					return
				}
				
				try {
					this.submitting = true
					
					const userStore = useUserStore()
					const reportStore = useReportStore()
					
					// 准备报告数据
					const reportData = {
						...this.reportForm,
						userId: userStore.currentUser.id
					}
					
					// 创建报告
					const newReport = await reportService.createReport(reportData)
					
					// 更新store
					reportStore.addReport(newReport)
					
					// 清除草稿
					uni.removeStorageSync('report_draft')
					
					uni.showToast({
						title: '报告提交成功',
						icon: 'success'
					})
					
					// 返回报告列表
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
					
				} catch (error) {
					console.error('提交报告失败:', error)
					uni.showToast({
						title: error.message || '提交失败',
						icon: 'none'
					})
				} finally {
					this.submitting = false
				}
			},
			
			/**
			 * 加载报告用于编辑
			 */
			async loadReportForEdit(reportId) {
				try {
					this.loading = true
					this.loadingText = '加载报告数据...'
					
					const report = await reportService.getReportById(reportId)
					
					// 填充表单数据
					this.reportForm = {
						hospitalName: report.hospitalName,
						reportDate: report.reportDate.split('T')[0],
						doctorName: report.doctorName,
						department: report.department,
						reportType: report.reportType,
						originalImagePath: report.originalImagePath,
						ocrText: report.ocrText,
						notes: report.notes,
						indicators: report.indicators || []
					}
					
					// 设置检查类型索引
					this.reportTypeIndex = this.reportTypes.indexOf(report.reportType)
					
				} catch (error) {
					console.error('加载报告失败:', error)
					uni.showToast({
						title: '加载报告失败',
						icon: 'none'
					})
					uni.navigateBack()
				} finally {
					this.loading = false
				}
			}
		},
		
		onShow() {
			// 尝试加载草稿
			try {
				const draft = uni.getStorageSync('report_draft')
				if (draft && !this.reportForm.hospitalName) {
					this.reportForm = { ...this.reportForm, ...draft }
				}
			} catch (error) {
				console.error('加载草稿失败:', error)
			}
		}
	}
</script>

<style scoped>
	.add-report-container {
		padding: 15px;
		padding-bottom: 100px;
	}
	
	.section-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
	}
	
	/* 图片上传区域 */
	.image-section {
		margin-bottom: 20px;
	}
	
	.image-upload-area {
		border: 2px dashed #ddd;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		background-color: #fafafa;
	}
	
	.uploaded-image {
		width: 100%;
		max-height: 300px;
		border-radius: 8px;
	}
	
	.upload-placeholder {
		padding: 40px 20px;
	}
	
	.upload-icon {
		font-size: 48px;
		display: block;
		margin-bottom: 10px;
	}
	
	.upload-text {
		font-size: 16px;
		color: #333;
		display: block;
		margin-bottom: 5px;
	}
	
	.upload-hint {
		font-size: 12px;
		color: #999;
		display: block;
	}
	
	/* OCR结果 */
	.ocr-result {
		margin-top: 15px;
		padding: 15px;
		background-color: #f8f9fa;
		border-radius: 8px;
	}
	
	.ocr-title {
		font-size: 14px;
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
	}
	
	.ocr-text {
		width: 100%;
		height: 100px;
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
		background-color: #fff;
		font-size: 12px;
		margin-bottom: 10px;
	}
	
	/* 表单样式 */
	.form-section, .indicators-section, .notes-section {
		margin-bottom: 20px;
		background-color: #fff;
		border-radius: 8px;
		padding: 15px;
	}
	
	.form-item {
		margin-bottom: 15px;
	}
	
	.form-item.half {
		width: 48%;
		display: inline-block;
		margin-right: 4%;
	}
	
	.form-item.half:nth-child(2n) {
		margin-right: 0;
	}
	
	.form-row {
		display: flex;
		justify-content: space-between;
	}
	
	.form-label {
		display: block;
		font-size: 14px;
		color: #333;
		margin-bottom: 5px;
	}
	
	.form-input, .form-textarea {
		width: 100%;
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 14px;
		box-sizing: border-box;
	}
	
	.form-input.error, .form-textarea.error {
		border-color: #ff4757;
	}
	
	.form-textarea {
		height: 80px;
		resize: vertical;
	}
	
	.form-textarea.large {
		height: 120px;
	}
	
	.picker-input {
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 14px;
		color: #333;
		background-color: #fff;
	}
	
	.error-text {
		color: #ff4757;
		font-size: 12px;
		margin-top: 5px;
		display: block;
	}
	
	/* 健康指标 */
	.indicator-item {
		border: 1px solid #eee;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 15px;
		background-color: #fafafa;
	}
	
	.indicator-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
	}
	
	.indicator-index {
		font-size: 14px;
		font-weight: bold;
		color: #333;
	}
	
	.checkbox-label {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #333;
	}
	
	.checkbox-label checkbox {
		margin-right: 8px;
	}
	
	.empty-indicators {
		text-align: center;
		padding: 40px 20px;
		color: #999;
	}
	
	.empty-text {
		font-size: 14px;
	}
	
	/* 按钮样式 */
	.btn-primary, .btn-secondary, .btn-add, .btn-remove {
		padding: 10px 20px;
		border-radius: 4px;
		font-size: 14px;
		text-align: center;
		border: none;
	}
	
	.btn-primary {
		background-color: #007AFF;
		color: #fff;
	}
	
	.btn-primary:disabled {
		background-color: #ccc;
		color: #999;
	}
	
	.btn-secondary {
		background-color: #f8f9fa;
		color: #333;
		border: 1px solid #ddd;
	}
	
	.btn-add {
		background-color: #28a745;
		color: #fff;
		padding: 8px 15px;
		font-size: 12px;
	}
	
	.btn-remove {
		background-color: #dc3545;
		color: #fff;
		padding: 5px 10px;
		font-size: 12px;
	}
	
	.action-buttons {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 15px;
		background-color: #fff;
		border-top: 1px solid #eee;
		display: flex;
		gap: 10px;
	}
	
	.action-buttons button {
		flex: 1;
	}
	
	/* 加载遮罩 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}
	
	.loading-content {
		background-color: #fff;
		padding: 20px;
		border-radius: 8px;
		text-align: center;
	}
	
	.loading-text {
		font-size: 14px;
		color: #333;
	}
</style>