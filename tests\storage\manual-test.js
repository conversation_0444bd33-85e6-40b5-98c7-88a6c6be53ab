/**
 * 手动测试脚本 - 验证存储系统功能
 */

// Mock uni-app API
global.uni = {
  getStorageSync: function(key) {
    console.log(`[<PERSON><PERSON>] getStorageSync: ${key}`);
    return this._storage[key] || null;
  },
  setStorageSync: function(key, value) {
    console.log(`[<PERSON><PERSON>] setStorageSync: ${key}`);
    if (!this._storage) this._storage = {};
    this._storage[key] = value;
  },
  _storage: {}
};

// Mock crypto API
global.crypto = {
  getRandomValues: function(array) {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  }
};

global.btoa = function(str) {
  return Buffer.from(str, 'binary').toString('base64');
};

global.atob = function(str) {
  return Buffer.from(str, 'base64').toString('binary');
};

// 导入我们的存储系统
const { storage } = require('../../utils/storage/index.js');

async function runTests() {
  console.log('=== 开始测试存储系统 ===\n');

  try {
    // 1. 初始化存储系统
    console.log('1. 初始化存储系统...');
    await storage.init();
    console.log('✓ 存储系统初始化成功\n');

    // 2. 测试用户创建和认证
    console.log('2. 测试用户功能...');
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      phone: '13800138000',
      password: 'password123',
      real_name: '测试用户'
    };

    const user = await storage.users.createUser(userData);
    console.log('✓ 用户创建成功:', user.username);

    const loginUser = await storage.users.validatePassword('testuser', 'password123');
    console.log('✓ 用户登录验证成功:', loginUser ? loginUser.username : 'null');

    const wrongPasswordUser = await storage.users.validatePassword('testuser', 'wrongpassword');
    console.log('✓ 错误密码验证失败:', wrongPasswordUser ? 'ERROR' : 'null');

    // 3. 测试健康报告
    console.log('\n3. 测试健康报告功能...');
    const reportData = {
      user_id: user.id,
      report_title: '年度体检报告',
      report_date: '2024-01-15',
      hospital_name: '测试医院',
      doctor_name: '张医生',
      department: '内科',
      report_type: '体检报告',
      ocr_text: '血糖：5.6 mmol/L\n血压：120/80 mmHg\n总胆固醇：4.5 mmol/L'
    };

    const report = await storage.reports.createReport(reportData, true); // 加密存储
    console.log('✓ 健康报告创建成功 (加密):', report.id);

    const decryptedReport = await storage.reports.getDecryptedReport(report.id);
    console.log('✓ 报告解密成功:', decryptedReport.ocr_text.substring(0, 20) + '...');

    // 4. 测试健康指标
    console.log('\n4. 测试健康指标功能...');
    const indicators = [
      {
        report_id: report.id,
        indicator_name: '血糖',
        indicator_value: '5.6',
        indicator_unit: 'mmol/L',
        reference_range: '3.9-6.1',
        is_abnormal: 0,
        category: '血液'
      },
      {
        report_id: report.id,
        indicator_name: '总胆固醇',
        indicator_value: '6.8',
        indicator_unit: 'mmol/L',
        reference_range: '3.1-5.2',
        is_abnormal: 1,
        abnormal_level: 2,
        category: '血液'
      }
    ];

    const createdIndicators = await storage.indicators.createBatch(indicators);
    console.log('✓ 健康指标批量创建成功:', createdIndicators.length, '条');

    const abnormalIndicators = await storage.indicators.findAbnormalIndicators(report.id);
    console.log('✓ 异常指标查询成功:', abnormalIndicators.length, '条异常');

    // 5. 测试同步记录
    console.log('\n5. 测试同步记录功能...');
    await storage.sync.createSyncRecord(user.id, 'health_reports', report.id, 'INSERT');
    await storage.sync.createSyncRecord(user.id, 'health_indicators', createdIndicators[0].id, 'INSERT');

    const pendingSync = await storage.sync.getPendingSyncRecords(user.id);
    console.log('✓ 待同步记录:', pendingSync.length, '条');

    await storage.sync.updateSyncStatus(pendingSync[0].id, 1); // 标记为已同步
    const remainingSync = await storage.sync.getPendingSyncRecords(user.id);
    console.log('✓ 同步状态更新成功，剩余:', remainingSync.length, '条');

    // 6. 测试数据查询
    console.log('\n6. 测试数据查询功能...');
    const userReports = await storage.reports.findByUserId(user.id);
    console.log('✓ 用户报告查询:', userReports.length, '条');

    const reportIndicators = await storage.indicators.findByReportId(report.id);
    console.log('✓ 报告指标查询:', reportIndicators.length, '条');

    // 7. 测试分页查询
    console.log('\n7. 测试分页查询...');
    const page1 = await storage.reports.paginate(1, 10, { user_id: user.id });
    console.log('✓ 分页查询成功:', page1.data.length, '条数据，总计', page1.pagination.total, '条');

    // 8. 测试统计信息
    console.log('\n8. 测试统计信息...');
    const stats = await storage.getStats();
    console.log('✓ 统计信息:');
    console.log('  - 用户:', stats.users.recordCount, '条');
    console.log('  - 报告:', stats.health_reports.recordCount, '条');
    console.log('  - 指标:', stats.health_indicators.recordCount, '条');
    console.log('  - 同步记录:', stats.sync_records.recordCount, '条');

    // 9. 测试加密功能
    console.log('\n9. 测试加密功能...');
    const encryption = storage.encryption;
    const key = encryption.generateKey();
    const originalText = '这是一段需要加密的敏感信息';
    const encrypted = await encryption.encrypt(originalText, key);
    const decrypted = await encryption.decrypt(encrypted, key);
    console.log('✓ 加密测试成功:', decrypted === originalText ? '解密正确' : '解密失败');

    // 10. 测试密码哈希
    console.log('\n10. 测试密码哈希...');
    const salt = encryption.generateSalt();
    const hash1 = await encryption.hashPassword('testpassword', salt);
    const hash2 = await encryption.hashPassword('testpassword', salt);
    console.log('✓ 密码哈希测试:', hash1 === hash2 ? '哈希一致' : '哈希不一致');

    console.log('\n=== 所有测试通过！ ===');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

// 运行测试
runTests();