/**
 * 平台特定的常量定义
 */

// 平台类型常量
export const PLATFORM_TYPES = {
  APP_PLUS: 'app-plus',
  MP_WEIXIN: 'mp-weixin',
  MP_ALIPAY: 'mp-alipay',
  MP_BAIDU: 'mp-baidu',
  H5: 'h5',
  MP: 'mp'
}

// 系统类型常量
export const SYSTEM_TYPES = {
  IOS: 'ios',
  ANDROID: 'android',
  WINDOWS: 'windows',
  MAC: 'mac'
}

// 相机相关常量
export const CAMERA_CONFIG = {
  // APP平台相机配置
  APP_PLUS: {
    sourceType: ['camera', 'album'],
    sizeType: ['original', 'compressed'],
    maxCount: 9,
    quality: 80,
    crop: {
      width: 800,
      height: 600
    }
  },
  // 微信小程序相机配置
  MP_WEIXIN: {
    sourceType: ['camera', 'album'],
    sizeType: ['original', 'compressed'],
    maxCount: 1,
    quality: 80
  },
  // H5平台相机配置（受限）
  H5: {
    sourceType: ['album'],
    sizeType: ['compressed'],
    maxCount: 1,
    quality: 70
  }
}

// 存储相关常量
export const STORAGE_CONFIG = {
  // 本地存储键名前缀
  KEY_PREFIX: 'heath_report_',
  
  // 存储类型
  STORAGE_TYPES: {
    LOCAL: 'local',        // 本地存储
    SESSION: 'session',    // 会话存储
    SECURE: 'secure'       // 安全存储
  },
  
  // 存储限制
  LIMITS: {
    APP_PLUS: {
      maxSize: 50 * 1024 * 1024,  // 50MB
      supportSecure: true
    },
    MP_WEIXIN: {
      maxSize: 10 * 1024 * 1024,  // 10MB
      supportSecure: false
    },
    H5: {
      maxSize: 5 * 1024 * 1024,   // 5MB
      supportSecure: false
    }
  }
}

// 分享相关常量
export const SHARE_CONFIG = {
  // 分享类型
  SHARE_TYPES: {
    TEXT: 'text',
    IMAGE: 'image',
    FILE: 'file',
    LINK: 'link'
  },
  
  // 平台支持的分享方式
  PLATFORM_SUPPORT: {
    APP_PLUS: {
      wechat: true,
      qq: true,
      weibo: true,
      system: true
    },
    MP_WEIXIN: {
      wechat: true,
      qq: false,
      weibo: false,
      system: false
    },
    H5: {
      wechat: false,
      qq: false,
      weibo: false,
      system: true
    }
  }
}

// 网络相关常量
export const NETWORK_CONFIG = {
  // 请求超时时间（毫秒）
  TIMEOUT: {
    APP_PLUS: 30000,
    MP_WEIXIN: 20000,
    H5: 15000
  },
  
  // 重试次数
  RETRY_COUNT: {
    APP_PLUS: 3,
    MP_WEIXIN: 2,
    H5: 2
  },
  
  // 并发请求限制
  CONCURRENT_LIMIT: {
    APP_PLUS: 10,
    MP_WEIXIN: 5,
    H5: 5
  }
}

// 文件相关常量
export const FILE_CONFIG = {
  // 支持的图片格式
  IMAGE_FORMATS: {
    APP_PLUS: ['jpg', 'jpeg', 'png', 'webp', 'gif'],
    MP_WEIXIN: ['jpg', 'jpeg', 'png'],
    H5: ['jpg', 'jpeg', 'png', 'webp']
  },
  
  // 文件大小限制（字节）
  SIZE_LIMITS: {
    IMAGE: {
      APP_PLUS: 10 * 1024 * 1024,  // 10MB
      MP_WEIXIN: 2 * 1024 * 1024,  // 2MB
      H5: 5 * 1024 * 1024          // 5MB
    },
    DOCUMENT: {
      APP_PLUS: 50 * 1024 * 1024,  // 50MB
      MP_WEIXIN: 10 * 1024 * 1024, // 10MB
      H5: 20 * 1024 * 1024         // 20MB
    }
  }
}

// 权限相关常量
export const PERMISSION_CONFIG = {
  // 需要的权限列表
  REQUIRED_PERMISSIONS: {
    APP_PLUS: [
      'android.permission.CAMERA',
      'android.permission.WRITE_EXTERNAL_STORAGE',
      'android.permission.READ_EXTERNAL_STORAGE'
    ],
    MP_WEIXIN: [
      'scope.camera',
      'scope.writePhotosAlbum'
    ]
  },
  
  // 权限描述
  PERMISSION_DESC: {
    CAMERA: '需要相机权限来拍摄健康报告照片',
    STORAGE: '需要存储权限来保存和读取健康数据',
    ALBUM: '需要相册权限来选择健康报告图片'
  }
}

// 错误码常量
export const ERROR_CODES = {
  // 平台相关错误
  PLATFORM_NOT_SUPPORTED: 'PLATFORM_NOT_SUPPORTED',
  FEATURE_NOT_AVAILABLE: 'FEATURE_NOT_AVAILABLE',
  
  // 权限相关错误
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  PERMISSION_NOT_GRANTED: 'PERMISSION_NOT_GRANTED',
  
  // 文件相关错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  FILE_FORMAT_NOT_SUPPORTED: 'FILE_FORMAT_NOT_SUPPORTED',
  
  // 网络相关错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  REQUEST_TIMEOUT: 'REQUEST_TIMEOUT',
  
  // 存储相关错误
  STORAGE_FULL: 'STORAGE_FULL',
  STORAGE_ACCESS_DENIED: 'STORAGE_ACCESS_DENIED',
  
  // 图片处理相关错误
  IMAGE_SELECT_FAILED: 'IMAGE_SELECT_FAILED',
  CAMERA_FAILED: 'CAMERA_FAILED',
  ALBUM_SELECT_FAILED: 'ALBUM_SELECT_FAILED',
  IMAGE_COMPRESS_FAILED: 'IMAGE_COMPRESS_FAILED',
  IMAGE_QUALITY_CHECK_FAILED: 'IMAGE_QUALITY_CHECK_FAILED',
  
  // OCR相关错误
  OCR_API_FAILED: 'OCR_API_FAILED',
  OCR_RECOGNITION_FAILED: 'OCR_RECOGNITION_FAILED',
  OCR_BATCH_FAILED: 'OCR_BATCH_FAILED',
  OCR_TOKEN_EXPIRED: 'OCR_TOKEN_EXPIRED',
  OCR_QUOTA_EXCEEDED: 'OCR_QUOTA_EXCEEDED',
  OCR_IMAGE_FORMAT_ERROR: 'OCR_IMAGE_FORMAT_ERROR',
  OCR_IMAGE_SIZE_ERROR: 'OCR_IMAGE_SIZE_ERROR'
}