<template>
	<view class="page-container">
		<view class="home-container">
			<!-- 头部欢迎区域 -->
			<view class="welcome-section">
				<view class="welcome-text">
					<text class="greeting">{{ greeting }}</text>
					<text class="user-name">{{ displayName }}</text>
				</view>
				<view class="app-status">
					<view class="status-item" :class="{ 'status-online': isOnline }">
						<text class="status-dot"></text>
						<text class="status-text">{{ networkStatusText }}</text>
					</view>
				</view>
			</view>
			
			<!-- 快速操作区域 -->
			<view class="quick-actions">
				<view class="section-title">
					<text>快速操作</text>
				</view>
				<view class="action-grid">
					<view class="action-item" @tap="navigateToAddReport">
						<view class="action-icon">📋</view>
						<text class="action-text">添加报告</text>
					</view>
					<view class="action-item" @tap="navigateToReportList">
						<view class="action-icon">📊</view>
						<text class="action-text">查看报告</text>
					</view>
					<view class="action-item" @tap="navigateToAnalysis">
						<view class="action-icon">📈</view>
						<text class="action-text">数据分析</text>
					</view>
					<view class="action-item" @tap="navigateToProfile">
						<view class="action-icon">👤</view>
						<text class="action-text">个人中心</text>
					</view>
				</view>
			</view>
			
			<!-- 统计概览 -->
			<view class="stats-section">
				<view class="section-title">
					<text>健康概览</text>
				</view>
				<view class="stats-grid">
					<view class="stat-item">
						<text class="stat-number">{{ totalReports }}</text>
						<text class="stat-label">总报告数</text>
					</view>
					<view class="stat-item">
						<text class="stat-number">{{ abnormalCount }}</text>
						<text class="stat-label">异常指标</text>
					</view>
					<view class="stat-item">
						<text class="stat-number">{{ daysSinceLastReport }}</text>
						<text class="stat-label">距上次检查</text>
					</view>
				</view>
			</view>
			
			<!-- 最近报告 -->
			<view class="recent-reports" v-if="latestReport">
				<view class="section-title">
					<text>最近报告</text>
					<text class="more-link" @tap="navigateToReportList">查看更多</text>
				</view>
				<view class="report-card">
					<view class="report-header">
						<text class="report-title">{{ latestReport.title }}</text>
						<text class="report-date">{{ formatDate(latestReport.reportDate) }}</text>
					</view>
					<view class="report-info">
						<text class="hospital-name">{{ latestReport.hospitalName }}</text>
						<text class="doctor-name">{{ latestReport.doctorName }}</text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="totalReports === 0">
				<view class="empty-state-icon">📋</view>
				<text class="empty-state-text">还没有健康报告\n点击上方"添加报告"开始记录您的健康数据</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { useAppStore } from '../../stores/app.js'
	import { useUserStore } from '../../stores/user.js'
	import { useReportStore } from '../../stores/report.js'
	
	export default {
		name: 'Home',
		data() {
			return {
				
			}
		},
		computed: {
			// 问候语
			greeting() {
				const hour = new Date().getHours()
				if (hour < 6) return '夜深了'
				if (hour < 12) return '早上好'
				if (hour < 18) return '下午好'
				return '晚上好'
			},
			
			// 用户显示名称
			displayName() {
				const userStore = useUserStore()
				return userStore.displayName
			},
			
			// 网络状态
			isOnline() {
				const appStore = useAppStore()
				return appStore.isNetworkAvailable
			},
			
			// 网络状态文本
			networkStatusText() {
				const appStore = useAppStore()
				return appStore.isNetworkAvailable ? '在线' : '离线'
			},
			
			// 报告统计
			totalReports() {
				const reportStore = useReportStore()
				return reportStore.statistics.totalReports
			},
			
			abnormalCount() {
				const reportStore = useReportStore()
				return reportStore.statistics.abnormalCount
			},
			
			// 距离上次报告天数
			daysSinceLastReport() {
				const reportStore = useReportStore()
				if (!reportStore.statistics.lastReportDate) return '-'
				
				const lastDate = new Date(reportStore.statistics.lastReportDate)
				const today = new Date()
				const diffTime = Math.abs(today - lastDate)
				const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
				
				return diffDays + '天'
			},
			
			// 最新报告
			latestReport() {
				const reportStore = useReportStore()
				return reportStore.latestReport
			}
		},
		onLoad() {
			console.log('首页加载')
		},
		onShow() {
			console.log('首页显示')
			// 刷新数据
			this.refreshData()
		},
		onPullDownRefresh() {
			this.refreshData()
			setTimeout(() => {
				uni.stopPullDownRefresh()
			}, 1000)
		},
		methods: {
			// 刷新数据
			async refreshData() {
				try {
					const reportStore = useReportStore()
					await reportStore.initReports()
				} catch (error) {
					console.error('刷新数据失败:', error)
				}
			},
			
			// 导航到添加报告页面
			navigateToAddReport() {
				uni.navigateTo({
					url: '/pages/report/add'
				})
			},
			
			// 导航到报告列表页面
			navigateToReportList() {
				uni.switchTab({
					url: '/pages/report/list'
				})
			},
			
			// 导航到数据分析页面
			navigateToAnalysis() {
				uni.switchTab({
					url: '/pages/analysis/index'
				})
			},
			
			// 导航到个人中心页面
			navigateToProfile() {
				uni.switchTab({
					url: '/pages/profile/index'
				})
			},
			
			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return ''
				const date = new Date(dateString)
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
			}
		}
	}
</script>

<style scoped>
	.home-container {
		padding: 15px;
	}
	
	/* 欢迎区域 */
	.welcome-section {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px 0;
		border-bottom: 1px solid #F0F0F0;
		margin-bottom: 20px;
	}
	
	.welcome-text {
		display: flex;
		flex-direction: column;
	}
	
	.greeting {
		font-size: 16px;
		color: #8E8E93;
		margin-bottom: 5px;
	}
	
	.user-name {
		font-size: 20px;
		font-weight: 600;
		color: #333333;
	}
	
	.app-status {
		display: flex;
		align-items: center;
	}
	
	.status-item {
		display: flex;
		align-items: center;
	}
	
	.status-dot {
		width: 8px;
		height: 8px;
		border-radius: 50%;
		background-color: #FF3B30;
		margin-right: 6px;
	}
	
	.status-online .status-dot {
		background-color: #34C759;
	}
	
	.status-text {
		font-size: 12px;
		color: #8E8E93;
	}
	
	/* 区域标题 */
	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		font-size: 16px;
		font-weight: 600;
		color: #333333;
	}
	
	.more-link {
		font-size: 14px;
		color: #007AFF;
		font-weight: normal;
	}
	
	/* 快速操作 */
	.quick-actions {
		margin-bottom: 25px;
	}
	
	.action-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 15px;
	}
	
	.action-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 15px 10px;
		background-color: #FFFFFF;
		border-radius: 12px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s ease;
	}
	
	.action-item:active {
		transform: scale(0.95);
	}
	
	.action-icon {
		font-size: 24px;
		margin-bottom: 8px;
	}
	
	.action-text {
		font-size: 12px;
		color: #333333;
		text-align: center;
	}
	
	/* 统计概览 */
	.stats-section {
		margin-bottom: 25px;
	}
	
	.stats-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 15px;
	}
	
	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20px 15px;
		background-color: #FFFFFF;
		border-radius: 12px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	}
	
	.stat-number {
		font-size: 24px;
		font-weight: 600;
		color: #007AFF;
		margin-bottom: 5px;
	}
	
	.stat-label {
		font-size: 12px;
		color: #8E8E93;
		text-align: center;
	}
	
	/* 最近报告 */
	.recent-reports {
		margin-bottom: 25px;
	}
	
	.report-card {
		background-color: #FFFFFF;
		border-radius: 12px;
		padding: 15px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	}
	
	.report-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	
	.report-title {
		font-size: 16px;
		font-weight: 600;
		color: #333333;
	}
	
	.report-date {
		font-size: 12px;
		color: #8E8E93;
	}
	
	.report-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.hospital-name,
	.doctor-name {
		font-size: 14px;
		color: #666666;
	}
	
	/* 空状态 */
	.empty-state {
		text-align: center;
		padding: 60px 20px;
	}
	
	.empty-state-icon {
		font-size: 48px;
		margin-bottom: 15px;
		opacity: 0.5;
	}
	
	.empty-state-text {
		font-size: 14px;
		color: #8E8E93;
		line-height: 1.5;
	}
</style>
