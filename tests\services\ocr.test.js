const OCRService = require('@/services/ocr/index.js');
const BaiduOCRService = require('@/services/ocr/baidu.js');
const OCRParser = require('@/services/ocr/parser.js');

// Mock uni API
global.uni = {
  request: jest.fn(),
  getFileSystemManager: jest.fn(() => ({
    readFile: jest.fn()
  }))
};

describe('OCR Service', () => {
  let ocrService;
  let mockBaiduService;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock BaiduOCRService
    mockBaiduService = {
      validateConfig: jest.fn(() => true),
      recognize: jest.fn(),
      getAccessToken: jest.fn(),
      config: { apiKey: 'test', secretKey: 'test' }
    };

    ocrService = new OCRService({
      baidu: { apiKey: 'test', secretKey: 'test' }
    });
    
    // 替换为mock实例
    ocrService.providers.baidu = mockBaiduService;
  });

  describe('recognizeAndParse', () => {
    it('应该成功识别并解析OCR结果', async () => {
      const mockOCRResult = {
        success: true,
        data: {
          fullText: '白细胞计数 5.2 10^9/L 参考值: 3.5-9.5',
          lines: [
            { text: '白细胞计数 5.2 10^9/L 参考值: 3.5-9.5', confidence: 0.95 }
          ],
          wordsCount: 1
        }
      };

      mockBaiduService.recognize.mockResolvedValue(mockOCRResult);

      const result = await ocrService.recognizeAndParse('/test/image.jpg');

      expect(result.success).toBe(true);
      expect(result.provider).toBe('baidu');
      expect(result.data).toBeDefined();
      expect(result.data.items).toBeDefined();
      expect(result.validation).toBeDefined();
    });

    it('应该处理OCR识别失败的情况', async () => {
      mockBaiduService.recognize.mockRejectedValue(new Error('API调用失败'));

      const result = await ocrService.recognizeAndParse('/test/image.jpg');

      expect(result.success).toBe(false);
      expect(result.provider).toBe('fallback');
      expect(result.error).toBeDefined();
      expect(result.manualInputRequired).toBe(true);
    });

    it('应该在质量较差时尝试降级处理', async () => {
      const mockPoorResult = {
        success: true,
        data: {
          fullText: '模糊文字',
          lines: [{ text: '模糊文字', confidence: 0.3 }],
          wordsCount: 1
        }
      };

      mockBaiduService.recognize.mockResolvedValue(mockPoorResult);

      const result = await ocrService.recognizeAndParse('/test/image.jpg');

      expect(result.fallback).toBe(true);
      expect(result.warning).toContain('识别质量较差');
    });

    it('应该正确处理重试机制', async () => {
      mockBaiduService.recognize
        .mockRejectedValueOnce(new Error('临时失败'))
        .mockResolvedValueOnce({
          success: true,
          data: {
            fullText: '血糖 6.1 mmol/L',
            lines: [{ text: '血糖 6.1 mmol/L', confidence: 0.9 }],
            wordsCount: 1
          }
        });

      const result = await ocrService.recognizeAndParse('/test/image.jpg');

      expect(mockBaiduService.recognize).toHaveBeenCalledTimes(2);
      expect(result.success).toBe(true);
    });
  });

  describe('executeWithRetry', () => {
    it('应该在成功时不重试', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');

      const result = await ocrService.executeWithRetry(mockFn, 3);

      expect(mockFn).toHaveBeenCalledTimes(1);
      expect(result).toBe('success');
    });

    it('应该在失败时进行重试', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('失败1'))
        .mockRejectedValueOnce(new Error('失败2'))
        .mockResolvedValueOnce('成功');

      const result = await ocrService.executeWithRetry(mockFn, 3);

      expect(mockFn).toHaveBeenCalledTimes(3);
      expect(result).toBe('成功');
    });

    it('应该在达到最大重试次数后抛出错误', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('持续失败'));

      await expect(ocrService.executeWithRetry(mockFn, 2))
        .rejects.toThrow('持续失败');

      expect(mockFn).toHaveBeenCalledTimes(3); // 初始调用 + 2次重试
    });
  });

  describe('handleFallback', () => {
    it('应该返回部分识别结果作为降级方案', async () => {
      const partialResult = {
        success: true,
        data: { items: [] },
        validation: { isValid: false, issues: ['缺少数据'] }
      };

      const result = await ocrService.handleFallback('/test/image.jpg', partialResult, Date.now());

      expect(result.success).toBe(true);
      expect(result.fallback).toBe(true);
      expect(result.warning).toContain('识别质量较差');
    });

    it('应该返回手动输入提示', async () => {
      const error = new Error('所有服务都失败');

      const result = await ocrService.handleFallback('/test/image.jpg', error, Date.now());

      expect(result.success).toBe(false);
      expect(result.manualInputRequired).toBe(true);
      expect(result.error.code).toBe('OCR_FAILED');
      expect(result.error.suggestions).toBeDefined();
    });
  });

  describe('getStatus', () => {
    it('应该返回服务状态信息', () => {
      const status = ocrService.getStatus();

      expect(status.providers).toBeDefined();
      expect(status.providers.baidu).toBeDefined();
      expect(status.providers.baidu.available).toBe(true);
      expect(status.config).toBeDefined();
    });
  });

  describe('addProvider', () => {
    it('应该能添加新的OCR提供商', () => {
      const mockProvider = {
        validateConfig: jest.fn(() => true),
        recognize: jest.fn()
      };

      ocrService.addProvider('tencent', mockProvider);

      expect(ocrService.providers.tencent).toBe(mockProvider);
      expect(ocrService.providerOrder).toContain('tencent');
    });
  });

  describe('setProviderOrder', () => {
    it('应该能设置提供商优先级', () => {
      const mockProvider = {
        validateConfig: jest.fn(() => true),
        recognize: jest.fn()
      };

      ocrService.addProvider('tencent', mockProvider);
      ocrService.setProviderOrder(['tencent', 'baidu']);

      expect(ocrService.providerOrder).toEqual(['tencent', 'baidu']);
    });

    it('应该过滤不存在的提供商', () => {
      ocrService.setProviderOrder(['nonexistent', 'baidu']);

      expect(ocrService.providerOrder).toEqual(['baidu']);
    });
  });

  describe('testService', () => {
    it('应该测试所有配置的提供商', async () => {
      mockBaiduService.recognize.mockResolvedValue({
        success: true,
        data: { wordsCount: 5 }
      });

      const results = await ocrService.testService('/test/image.jpg');

      expect(results.baidu).toBeDefined();
      expect(results.baidu.success).toBe(true);
      expect(results.baidu.duration).toBeDefined();
      expect(results.baidu.wordsCount).toBe(5);
    });

    it('应该处理测试失败的情况', async () => {
      mockBaiduService.recognize.mockRejectedValue(new Error('测试失败'));

      const results = await ocrService.testService('/test/image.jpg');

      expect(results.baidu.success).toBe(false);
      expect(results.baidu.error).toBe('测试失败');
    });

    it('应该处理配置无效的提供商', async () => {
      mockBaiduService.validateConfig.mockReturnValue(false);

      const results = await ocrService.testService('/test/image.jpg');

      expect(results.baidu.success).toBe(false);
      expect(results.baidu.error).toBe('配置无效');
    });
  });
});

describe('OCRParser', () => {
  let parser;

  beforeEach(() => {
    parser = new OCRParser();
  });

  describe('parse', () => {
    it('应该解析有效的OCR结果', () => {
      const mockOCRResult = {
        success: true,
        data: {
          fullText: '北京医院\n白细胞计数 5.2 10^9/L 参考值: 3.5-9.5\n2024-01-15',
          lines: [
            { text: '北京医院', confidence: 0.95 },
            { text: '白细胞计数 5.2 10^9/L 参考值: 3.5-9.5', confidence: 0.9 },
            { text: '2024-01-15', confidence: 0.85 }
          ]
        }
      };

      const result = parser.parse(mockOCRResult);

      expect(result.success).toBe(true);
      expect(result.data.hospital).toBe('北京医院');
      expect(result.data.checkDate).toBe('2024-01-15');
      expect(result.data.items).toHaveLength(1);
      expect(result.data.items[0].name).toContain('白细胞');
      expect(result.data.items[0].value).toBe('5.2');
      expect(result.data.items[0].unit).toBe('10^9/L');
    });

    it('应该处理无效的OCR结果', () => {
      const invalidResult = {
        success: false,
        data: null
      };

      const result = parser.parse(invalidResult);

      expect(result.success).toBe(false);
      expect(result.error).toBe('OCR识别结果无效');
    });
  });

  describe('extractHospital', () => {
    it('应该提取医院名称', () => {
      const text = '北京协和医院\n检验报告';
      const hospital = parser.extractHospital(text);
      expect(hospital).toBe('北京协和医院');
    });

    it('应该从包含医院关键词的行提取', () => {
      const text = '检验报告\n医院：上海第一人民医院\n患者姓名';
      const hospital = parser.extractHospital(text);
      expect(hospital).toBe('上海第一人民医院');
    });
  });

  describe('extractDate', () => {
    it('应该提取日期信息', () => {
      const text = '检查日期：2024年1月15日\n报告日期：2024-01-16';
      const checkDate = parser.extractDate(text, 'check');
      const reportDate = parser.extractDate(text, 'report');
      
      expect(checkDate).toBe('2024-01-15');
      expect(reportDate).toBe('2024-01-16');
    });

    it('应该处理不同的日期格式', () => {
      const text1 = '2024/01/15';
      const text2 = '2024-1-5';
      const text3 = '2024年12月25日';
      
      expect(parser.extractDate(text1)).toBe('2024-01-15');
      expect(parser.extractDate(text2)).toBe('2024-01-05');
      expect(parser.extractDate(text3)).toBe('2024-12-25');
    });
  });

  describe('isAbnormalValue', () => {
    it('应该正确判断异常值', () => {
      expect(parser.isAbnormalValue('10.5', '3.5-9.5')).toBe(true);
      expect(parser.isAbnormalValue('2.0', '3.5-9.5')).toBe(true);
      expect(parser.isAbnormalValue('6.0', '3.5-9.5')).toBe(false);
    });

    it('应该处理无效输入', () => {
      expect(parser.isAbnormalValue('', '3.5-9.5')).toBe(false);
      expect(parser.isAbnormalValue('6.0', '')).toBe(false);
      expect(parser.isAbnormalValue('abc', '3.5-9.5')).toBe(false);
    });
  });

  describe('validateResult', () => {
    it('应该验证完整的解析结果', () => {
      const goodData = {
        hospital: '北京医院',
        checkDate: '2024-01-15',
        items: [
          { name: '白细胞', value: '5.2', referenceRange: '3.5-9.5' },
          { name: '红细胞', value: '4.5', referenceRange: '4.0-5.5' }
        ]
      };

      const validation = parser.validateResult(goodData);
      expect(validation.isValid).toBe(true);
      expect(validation.score).toBeGreaterThan(60);
    });

    it('应该识别不完整的解析结果', () => {
      const poorData = {
        hospital: '',
        checkDate: '',
        items: []
      };

      const validation = parser.validateResult(poorData);
      expect(validation.isValid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);
    });
  });
});