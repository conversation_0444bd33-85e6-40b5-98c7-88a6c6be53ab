/**
 * 图表生成服务
 * 实现需求 4.1: 生成各项健康指标随时间变化的折线图
 * 实现需求 4.2: 显示特定检查项目在不同时间点的数值对比
 */

import { formatDate } from '@/utils/dateUtils'

/**
 * 图表服务类
 */
class ChartService {
  /**
   * 生成单指标趋势折线图配置
   * @param {Object} trendData - 趋势分析数据
   * @param {Object} options - 图表选项
   * @returns {Object} 图表配置对象
   */
  generateTrendLineChart(trendData, options = {}) {
    const {
      title = '健康指标趋势',
      unit = '',
      referenceRange = null,
      showAbnormal = true,
      theme = 'light'
    } = options

    const dataPoints = trendData.dataPoints || []
    
    // 准备数据
    const categories = dataPoints.map(point => formatDate(point.date, 'MM-DD'))
    const values = dataPoints.map(point => parseFloat(point.value) || 0)
    const abnormalPoints = showAbnormal 
      ? dataPoints.map((point, index) => point.isAbnormal ? index : null).filter(i => i !== null)
      : []

    // 基础配置
    const config = {
      type: 'line',
      title: {
        text: title,
        fontSize: 16,
        color: theme === 'dark' ? '#ffffff' : '#333333'
      },
      subtitle: {
        text: trendData.summary,
        fontSize: 12,
        color: theme === 'dark' ? '#cccccc' : '#666666'
      },
      canvas: true,
      animation: true,
      categories: categories,
      series: [
        {
          name: title,
          data: values,
          color: this.getTrendColor(trendData.trend),
          lineWidth: 2,
          pointSize: 4,
          smooth: true
        }
      ],
      xAxis: {
        disableGrid: false,
        gridColor: theme === 'dark' ? '#444444' : '#e0e0e0',
        fontColor: theme === 'dark' ? '#cccccc' : '#666666'
      },
      yAxis: {
        gridType: 'dash',
        gridColor: theme === 'dark' ? '#444444' : '#e0e0e0',
        fontColor: theme === 'dark' ? '#cccccc' : '#666666',
        unit: unit
      },
      legend: {
        show: false
      },
      extra: {
        line: {
          type: 'straight',
          width: 2,
          activeType: 'hollow'
        }
      }
    }

    // 添加参考范围线
    if (referenceRange) {
      this.addReferenceLines(config, referenceRange, theme)
    }

    // 标记异常点
    if (abnormalPoints.length > 0) {
      this.markAbnormalPoints(config, abnormalPoints)
    }

    // 添加趋势线
    if (trendData.regression && trendData.regression.points) {
      this.addTrendLine(config, trendData.regression, theme)
    }

    return config
  }

  /**
   * 生成多指标对比图表配置
   * @param {Object} multiTrendData - 多指标趋势数据
   * @param {Object} options - 图表选项
   * @returns {Object} 图表配置对象
   */
  generateMultiIndicatorChart(multiTrendData, options = {}) {
    const {
      title = '多指标趋势对比',
      theme = 'light',
      showLegend = true
    } = options

    const indicators = Object.keys(multiTrendData.indicators)
    const colors = this.generateColorPalette(indicators.length)
    
    // 找到所有日期的并集
    const allDates = new Set()
    indicators.forEach(indicator => {
      const dataPoints = multiTrendData.indicators[indicator].dataPoints || []
      dataPoints.forEach(point => allDates.add(point.date))
    })
    
    const sortedDates = Array.from(allDates).sort()
    const categories = sortedDates.map(date => formatDate(date, 'MM-DD'))

    // 准备系列数据
    const series = indicators.map((indicator, index) => {
      const indicatorData = multiTrendData.indicators[indicator]
      const dataPoints = indicatorData.dataPoints || []
      
      // 为每个日期填充数据
      const values = sortedDates.map(date => {
        const point = dataPoints.find(p => p.date === date)
        return point ? parseFloat(point.value) || 0 : null
      })

      return {
        name: indicator,
        data: values,
        color: colors[index],
        lineWidth: 2,
        pointSize: 3,
        smooth: true
      }
    })

    return {
      type: 'line',
      title: {
        text: title,
        fontSize: 16,
        color: theme === 'dark' ? '#ffffff' : '#333333'
      },
      subtitle: {
        text: multiTrendData.summary,
        fontSize: 12,
        color: theme === 'dark' ? '#cccccc' : '#666666'
      },
      canvas: true,
      animation: true,
      categories: categories,
      series: series,
      xAxis: {
        disableGrid: false,
        gridColor: theme === 'dark' ? '#444444' : '#e0e0e0',
        fontColor: theme === 'dark' ? '#cccccc' : '#666666'
      },
      yAxis: {
        gridType: 'dash',
        gridColor: theme === 'dark' ? '#444444' : '#e0e0e0',
        fontColor: theme === 'dark' ? '#cccccc' : '#666666'
      },
      legend: {
        show: showLegend,
        position: 'bottom',
        fontSize: 12,
        fontColor: theme === 'dark' ? '#cccccc' : '#666666'
      },
      extra: {
        line: {
          type: 'straight',
          width: 2,
          activeType: 'hollow'
        }
      }
    }
  }

  /**
   * 生成指标分布饼图配置
   * @param {Object} distributionData - 分布数据
   * @param {Object} options - 图表选项
   * @returns {Object} 饼图配置对象
   */
  generateDistributionPieChart(distributionData, options = {}) {
    const {
      title = '指标分布',
      theme = 'light'
    } = options

    const series = Object.entries(distributionData).map(([key, value]) => ({
      name: key,
      data: value,
      color: this.getDistributionColor(key)
    }))

    return {
      type: 'pie',
      title: {
        text: title,
        fontSize: 16,
        color: theme === 'dark' ? '#ffffff' : '#333333'
      },
      canvas: true,
      animation: true,
      series: series,
      legend: {
        show: true,
        position: 'right',
        fontSize: 12,
        fontColor: theme === 'dark' ? '#cccccc' : '#666666'
      },
      extra: {
        pie: {
          activeOpacity: 0.5,
          activeRadius: 10,
          offsetAngle: 0,
          labelWidth: 15,
          border: true,
          borderWidth: 2,
          borderColor: '#FFFFFF'
        }
      }
    }
  }

  /**
   * 根据趋势获取颜色
   * @param {string} trend - 趋势类型
   * @returns {string} 颜色值
   */
  getTrendColor(trend) {
    const colorMap = {
      'increasing': '#ff6b6b',    // 红色 - 上升
      'decreasing': '#4ecdc4',    // 青色 - 下降
      'stable': '#45b7d1',        // 蓝色 - 稳定
      'fluctuating': '#f9ca24',   // 黄色 - 波动
      'insufficient_data': '#95a5a6' // 灰色 - 数据不足
    }
    return colorMap[trend] || '#45b7d1'
  }

  /**
   * 生成颜色调色板
   * @param {number} count - 颜色数量
   * @returns {Array} 颜色数组
   */
  generateColorPalette(count) {
    const baseColors = [
      '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3',
      '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
      '#10ac84', '#ee5a24', '#0abde3', '#feca57'
    ]
    
    if (count <= baseColors.length) {
      return baseColors.slice(0, count)
    }
    
    // 如果需要更多颜色，生成渐变色
    const colors = [...baseColors]
    while (colors.length < count) {
      const hue = (colors.length * 137.508) % 360 // 黄金角度
      colors.push(`hsl(${hue}, 70%, 60%)`)
    }
    
    return colors.slice(0, count)
  }

  /**
   * 获取分布图颜色
   * @param {string} category - 分类名称
   * @returns {string} 颜色值
   */
  getDistributionColor(category) {
    const colorMap = {
      '正常': '#4ecdc4',
      '偏高': '#ff6b6b',
      '偏低': '#feca57',
      '异常': '#ff4757'
    }
    return colorMap[category] || '#45b7d1'
  }

  /**
   * 添加参考范围线
   * @param {Object} config - 图表配置
   * @param {Object} referenceRange - 参考范围 {min, max}
   * @param {string} theme - 主题
   */
  addReferenceLines(config, referenceRange, theme) {
    if (!config.extra) config.extra = {}
    if (!config.extra.markLine) config.extra.markLine = []

    const lineColor = theme === 'dark' ? '#666666' : '#cccccc'

    if (referenceRange.min !== undefined) {
      config.extra.markLine.push({
        type: 'horizontal',
        value: referenceRange.min,
        lineStyle: {
          color: lineColor,
          type: 'dash',
          width: 1
        },
        label: {
          text: `下限: ${referenceRange.min}`,
          fontSize: 10,
          color: lineColor
        }
      })
    }

    if (referenceRange.max !== undefined) {
      config.extra.markLine.push({
        type: 'horizontal',
        value: referenceRange.max,
        lineStyle: {
          color: lineColor,
          type: 'dash',
          width: 1
        },
        label: {
          text: `上限: ${referenceRange.max}`,
          fontSize: 10,
          color: lineColor
        }
      })
    }
  }

  /**
   * 标记异常点
   * @param {Object} config - 图表配置
   * @param {Array} abnormalPoints - 异常点索引数组
   */
  markAbnormalPoints(config, abnormalPoints) {
    if (!config.extra) config.extra = {}
    if (!config.extra.markPoint) config.extra.markPoint = []

    abnormalPoints.forEach(index => {
      config.extra.markPoint.push({
        index: index,
        style: {
          color: '#ff4757',
          borderColor: '#ffffff',
          borderWidth: 2,
          size: 6
        },
        label: {
          text: '异常',
          fontSize: 10,
          color: '#ff4757'
        }
      })
    })
  }

  /**
   * 添加趋势线
   * @param {Object} config - 图表配置
   * @param {Object} regression - 回归数据
   * @param {string} theme - 主题
   */
  addTrendLine(config, regression, theme) {
    const { points, slope, intercept } = regression
    if (!points || points.length < 2) return

    const trendLineData = points.map(point => slope * point.x + intercept)
    
    config.series.push({
      name: '趋势线',
      data: trendLineData,
      color: theme === 'dark' ? '#666666' : '#999999',
      lineWidth: 1,
      pointSize: 0,
      smooth: false,
      lineStyle: 'dash'
    })
  }

  /**
   * 生成图表导出配置
   * @param {Object} chartConfig - 图表配置
   * @param {Object} exportOptions - 导出选项
   * @returns {Object} 导出配置
   */
  generateExportConfig(chartConfig, exportOptions = {}) {
    const {
      format = 'png',
      width = 800,
      height = 600,
      quality = 0.9
    } = exportOptions

    return {
      ...chartConfig,
      width,
      height,
      pixelRatio: 2, // 高清显示
      export: {
        format,
        quality,
        backgroundColor: chartConfig.backgroundColor || '#ffffff'
      }
    }
  }
}

// 创建单例实例
const chartService = new ChartService()

export default chartService