/**
 * 异常指标检测和提醒服务
 * 负责检测异常指标并发送相应的提醒通知
 */

class AlertService {
  constructor() {
    // 提醒配置
    this.alertConfig = {
      // 连续异常次数阈值
      consecutiveThreshold: 2,
      // 提醒间隔（天）
      reminderInterval: 7,
      // 严重程度配置
      severityLevels: {
        low: { color: '#FFA500', priority: 1 },
        medium: { color: '#FF6B35', priority: 2 },
        high: { color: '#FF0000', priority: 3 }
      }
    }
  }

  /**
   * 检测异常指标
   * @param {Array} reports 历史报告列表
   * @returns {Array} 异常检测结果
   */
  detectAbnormalIndicators(reports) {
    const alerts = []
    
    // 按指标名称分组
    const indicatorGroups = this.groupIndicatorsByName(reports)
    
    Object.keys(indicatorGroups).forEach(indicatorName => {
      const indicators = indicatorGroups[indicatorName]
      const alert = this.analyzeIndicatorPattern(indicatorName, indicators)
      
      if (alert) {
        alerts.push(alert)
      }
    })

    // 按优先级排序
    return alerts.sort((a, b) => b.priority - a.priority)
  }

  /**
   * 按指标名称分组
   * @param {Array} reports 报告列表
   * @returns {Object} 分组后的指标
   */
  groupIndicatorsByName(reports) {
    const groups = {}
    
    reports.forEach(report => {
      report.indicators.forEach(indicator => {
        if (!groups[indicator.name]) {
          groups[indicator.name] = []
        }
        groups[indicator.name].push({
          ...indicator,
          reportDate: report.reportDate,
          reportId: report.id
        })
      })
    })

    // 按日期排序
    Object.keys(groups).forEach(name => {
      groups[name].sort((a, b) => new Date(b.reportDate) - new Date(a.reportDate))
    })

    return groups
  }

  /**
   * 分析指标模式
   * @param {string} indicatorName 指标名称
   * @param {Array} indicators 指标历史数据
   * @returns {Object|null} 异常提醒信息
   */
  analyzeIndicatorPattern(indicatorName, indicators) {
    if (indicators.length < 2) return null

    // 检查连续异常
    const consecutiveAbnormal = this.checkConsecutiveAbnormal(indicators)
    if (consecutiveAbnormal.count >= this.alertConfig.consecutiveThreshold) {
      return this.createAlert(indicatorName, 'consecutive_abnormal', consecutiveAbnormal)
    }

    // 检查急剧变化
    const rapidChange = this.checkRapidChange(indicators)
    if (rapidChange.isSignificant) {
      return this.createAlert(indicatorName, 'rapid_change', rapidChange)
    }

    // 检查持续恶化
    const deterioration = this.checkDeterioration(indicators)
    if (deterioration.isDeterioration) {
      return this.createAlert(indicatorName, 'deterioration', deterioration)
    }

    return null
  }

  /**
   * 检查连续异常
   * @param {Array} indicators 指标数据
   * @returns {Object} 连续异常信息
   */
  checkConsecutiveAbnormal(indicators) {
    let count = 0
    const abnormalDates = []

    for (const indicator of indicators) {
      if (indicator.isAbnormal) {
        count++
        abnormalDates.push(indicator.reportDate)
      } else {
        break
      }
    }

    return {
      count,
      dates: abnormalDates,
      latestValue: indicators[0].value,
      latestUnit: indicators[0].unit
    }
  }

  /**
   * 检查急剧变化
   * @param {Array} indicators 指标数据
   * @returns {Object} 急剧变化信息
   */
  checkRapidChange(indicators) {
    if (indicators.length < 2) return { isSignificant: false }

    const latest = parseFloat(indicators[0].value)
    const previous = parseFloat(indicators[1].value)
    const changeRate = Math.abs((latest - previous) / previous * 100)

    // 变化超过30%认为是急剧变化
    const isSignificant = changeRate > 30

    return {
      isSignificant,
      changeRate: changeRate.toFixed(1),
      direction: latest > previous ? 'increase' : 'decrease',
      latestValue: indicators[0].value,
      previousValue: indicators[1].value,
      unit: indicators[0].unit
    }
  }

  /**
   * 检查持续恶化
   * @param {Array} indicators 指标数据
   * @returns {Object} 恶化趋势信息
   */
  checkDeterioration(indicators) {
    if (indicators.length < 3) return { isDeterioration: false }

    // 取最近3次数据
    const recent = indicators.slice(0, 3).reverse()
    const values = recent.map(i => parseFloat(i.value))

    // 检查是否持续恶化（连续上升或下降且偏离正常范围）
    let isIncreasing = true
    let isDecreasing = true

    for (let i = 1; i < values.length; i++) {
      if (values[i] <= values[i - 1]) isIncreasing = false
      if (values[i] >= values[i - 1]) isDecreasing = false
    }

    const isDeterioration = (isIncreasing || isDecreasing) && 
                           recent.some(i => i.isAbnormal)

    return {
      isDeterioration,
      trend: isIncreasing ? 'increasing' : (isDecreasing ? 'decreasing' : 'stable'),
      values: values,
      dates: recent.map(i => i.reportDate)
    }
  }

  /**
   * 创建提醒信息
   * @param {string} indicatorName 指标名称
   * @param {string} type 提醒类型
   * @param {Object} data 相关数据
   * @returns {Object} 提醒信息
   */
  createAlert(indicatorName, type, data) {
    const severity = this.calculateSeverity(type, data)
    const config = this.alertConfig.severityLevels[severity]

    let title, message, suggestion

    switch (type) {
      case 'consecutive_abnormal':
        title = `${indicatorName}连续异常`
        message = `${indicatorName}已连续${data.count}次检查异常，最新值：${data.latestValue}${data.latestUnit}`
        suggestion = '建议尽快咨询医生，制定治疗方案'
        break

      case 'rapid_change':
        title = `${indicatorName}急剧变化`
        message = `${indicatorName}较上次检查${data.direction === 'increase' ? '上升' : '下降'}${data.changeRate}%`
        suggestion = '建议关注指标变化，必要时咨询医生'
        break

      case 'deterioration':
        title = `${indicatorName}持续恶化`
        message = `${indicatorName}呈${data.trend === 'increasing' ? '持续上升' : '持续下降'}趋势`
        suggestion = '建议及时就医，调整治疗方案'
        break

      default:
        title = `${indicatorName}异常提醒`
        message = '检测到异常情况'
        suggestion = '建议咨询医生'
    }

    return {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      indicatorName,
      type,
      severity,
      title,
      message,
      suggestion,
      color: config.color,
      priority: config.priority,
      data,
      createdAt: new Date().toISOString(),
      isRead: false
    }
  }

  /**
   * 计算严重程度
   * @param {string} type 提醒类型
   * @param {Object} data 相关数据
   * @returns {string} 严重程度
   */
  calculateSeverity(type, data) {
    switch (type) {
      case 'consecutive_abnormal':
        return data.count >= 3 ? 'high' : 'medium'
      
      case 'rapid_change':
        return data.changeRate > 50 ? 'high' : 'medium'
      
      case 'deterioration':
        return 'high'
      
      default:
        return 'low'
    }
  }

  /**
   * 发送通知
   * @param {Object} alert 提醒信息
   */
  async sendNotification(alert) {
    try {
      // 检查是否需要发送通知（避免重复提醒）
      if (!this.shouldSendNotification(alert)) {
        return
      }

      // 发送系统通知
      await this.sendSystemNotification(alert)
      
      // 记录通知历史
      this.recordNotification(alert)
      
    } catch (error) {
      console.error('发送通知失败:', error)
    }
  }

  /**
   * 检查是否应该发送通知
   * @param {Object} alert 提醒信息
   * @returns {boolean} 是否发送
   */
  shouldSendNotification(alert) {
    try {
      const notificationHistory = uni.getStorageSync('notification_history') || []
      const lastNotification = notificationHistory
        .filter(n => n.indicatorName === alert.indicatorName && n.type === alert.type)
        .sort((a, b) => new Date(b.sentAt) - new Date(a.sentAt))[0]

      if (!lastNotification) return true

      const daysSinceLastNotification = 
        (Date.now() - new Date(lastNotification.sentAt).getTime()) / (1000 * 60 * 60 * 24)

      return daysSinceLastNotification >= this.alertConfig.reminderInterval
    } catch (error) {
      console.error('检查通知状态失败:', error)
      return true
    }
  }

  /**
   * 发送系统通知
   * @param {Object} alert 提醒信息
   */
  async sendSystemNotification(alert) {
    // #ifdef APP-PLUS
    const notification = {
      title: alert.title,
      content: alert.message,
      payload: {
        type: 'health_alert',
        alertId: alert.id
      }
    }
    
    plus.push.createMessage(notification.content, notification.payload, {
      title: notification.title,
      when: new Date()
    })
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序使用订阅消息
    try {
      await wx.requestSubscribeMessage({
        tmplIds: ['health_alert_template_id'], // 需要在微信公众平台配置
        success: (res) => {
          console.log('订阅消息发送成功', res)
        }
      })
    } catch (error) {
      console.log('订阅消息发送失败', error)
    }
    // #endif
  }

  /**
   * 记录通知历史
   * @param {Object} alert 提醒信息
   */
  recordNotification(alert) {
    try {
      const history = uni.getStorageSync('notification_history') || []
      history.push({
        ...alert,
        sentAt: new Date().toISOString()
      })

      // 只保留最近100条记录
      if (history.length > 100) {
        history.splice(0, history.length - 100)
      }

      uni.setStorageSync('notification_history', history)
    } catch (error) {
      console.error('记录通知历史失败:', error)
    }
  }

  /**
   * 获取所有未读提醒
   * @returns {Array} 未读提醒列表
   */
  getUnreadAlerts() {
    try {
      const alerts = uni.getStorageSync('health_alerts') || []
      return alerts.filter(alert => !alert.isRead)
    } catch (error) {
      console.error('获取未读提醒失败:', error)
      return []
    }
  }

  /**
   * 标记提醒为已读
   * @param {string} alertId 提醒ID
   */
  markAlertAsRead(alertId) {
    try {
      const alerts = uni.getStorageSync('health_alerts') || []
      const alertIndex = alerts.findIndex(alert => alert.id === alertId)
      
      if (alertIndex !== -1) {
        alerts[alertIndex].isRead = true
        uni.setStorageSync('health_alerts', alerts)
      }
    } catch (error) {
      console.error('标记提醒已读失败:', error)
    }
  }

  /**
   * 保存提醒到本地
   * @param {Array} alerts 提醒列表
   */
  saveAlerts(alerts) {
    try {
      const existingAlerts = uni.getStorageSync('health_alerts') || []
      const mergedAlerts = [...existingAlerts, ...alerts]
      
      // 去重
      const uniqueAlerts = mergedAlerts.filter((alert, index, self) => 
        index === self.findIndex(a => a.id === alert.id)
      )

      uni.setStorageSync('health_alerts', uniqueAlerts)
    } catch (error) {
      console.error('保存提醒失败:', error)
    }
  }
}

export default new AlertService()