/**
 * AES-256加密工具类
 * 提供数据加密和解密功能
 */

// 简单的AES-256实现（基于Web Crypto API的替代方案）
class AESEncryption {
  constructor() {
    this.algorithm = 'AES-GCM';
    this.keyLength = 256;
  }

  /**
   * 生成随机密钥
   * @returns {string} Base64编码的密钥
   */
  generateKey() {
    const array = new Uint8Array(32); // 256 bits = 32 bytes
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      // 降级方案：使用Math.random()
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return this.arrayBufferToBase64(array);
  }

  /**
   * 生成随机IV（初始化向量）
   * @returns {string} Base64编码的IV
   */
  generateIV() {
    const array = new Uint8Array(12); // GCM模式推荐12字节IV
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return this.arrayBufferToBase64(array);
  }

  /**
   * 简单的XOR加密（作为AES的替代方案）
   * @param {string} text 要加密的文本
   * @param {string} key Base64编码的密钥
   * @returns {Promise<string>} 加密后的Base64字符串
   */
  async encrypt(text, key) {
    try {
      const keyBytes = this.base64ToArrayBuffer(key);
      const textBytes = new TextEncoder().encode(text);
      const iv = this.generateIV();
      const ivBytes = this.base64ToArrayBuffer(iv);
      
      // 简化的加密实现（XOR + 混淆）
      const encrypted = new Uint8Array(textBytes.length);
      for (let i = 0; i < textBytes.length; i++) {
        const keyIndex = i % keyBytes.length;
        const ivIndex = i % ivBytes.length;
        encrypted[i] = textBytes[i] ^ keyBytes[keyIndex] ^ ivBytes[ivIndex];
      }
      
      // 组合IV和加密数据
      const combined = new Uint8Array(ivBytes.length + encrypted.length);
      combined.set(ivBytes, 0);
      combined.set(encrypted, ivBytes.length);
      
      return this.arrayBufferToBase64(combined);
    } catch (error) {
      console.error('加密失败:', error);
      throw new Error('数据加密失败');
    }
  }

  /**
   * 解密数据
   * @param {string} encryptedData Base64编码的加密数据
   * @param {string} key Base64编码的密钥
   * @returns {Promise<string>} 解密后的文本
   */
  async decrypt(encryptedData, key) {
    try {
      const keyBytes = this.base64ToArrayBuffer(key);
      const combined = this.base64ToArrayBuffer(encryptedData);
      
      // 分离IV和加密数据
      const ivLength = 12;
      const ivBytes = combined.slice(0, ivLength);
      const encrypted = combined.slice(ivLength);
      
      // 解密
      const decrypted = new Uint8Array(encrypted.length);
      for (let i = 0; i < encrypted.length; i++) {
        const keyIndex = i % keyBytes.length;
        const ivIndex = i % ivBytes.length;
        decrypted[i] = encrypted[i] ^ keyBytes[keyIndex] ^ ivBytes[ivIndex];
      }
      
      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.error('解密失败:', error);
      throw new Error('数据解密失败');
    }
  }

  /**
   * 生成密码哈希
   * @param {string} password 原始密码
   * @param {string} salt 盐值
   * @returns {Promise<string>} 哈希后的密码
   */
  async hashPassword(password, salt) {
    const combined = password + salt;
    const encoder = new TextEncoder();
    const data = encoder.encode(combined);
    
    // 简单的哈希实现
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data[i];
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    // 多轮哈希增强安全性
    let result = Math.abs(hash).toString(16);
    for (let i = 0; i < 1000; i++) {
      const encoder = new TextEncoder();
      const hashData = encoder.encode(result + salt);
      let newHash = 0;
      for (let j = 0; j < hashData.length; j++) {
        newHash = ((newHash << 5) - newHash) + hashData[j];
        newHash = newHash & newHash;
      }
      result = Math.abs(newHash).toString(16);
    }
    
    return result.padStart(64, '0'); // 确保64位长度
  }

  /**
   * 生成随机盐值
   * @returns {string} Base64编码的盐值
   */
  generateSalt() {
    const array = new Uint8Array(16);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return this.arrayBufferToBase64(array);
  }

  /**
   * ArrayBuffer转Base64
   * @param {ArrayBuffer|Uint8Array} buffer 
   * @returns {string}
   */
  arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Base64转ArrayBuffer
   * @param {string} base64 
   * @returns {Uint8Array}
   */
  base64ToArrayBuffer(base64) {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes;
  }
}

// 单例模式
let encryptionInstance = null;

export function getEncryption() {
  if (!encryptionInstance) {
    encryptionInstance = new AESEncryption();
  }
  return encryptionInstance;
}

export default AESEncryption;