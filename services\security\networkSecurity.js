/**
 * 网络安全服务
 * 确保HTTPS传输和请求安全
 */

import encryptionService from './encryption.js'

class NetworkSecurityService {
  constructor() {
    this.baseURL = this.getSecureBaseURL()
    this.requestInterceptors = []
    this.responseInterceptors = []
    this.setupInterceptors()
  }

  /**
   * 获取安全的基础URL
   */
  getSecureBaseURL() {
    // 确保使用HTTPS协议
    const isDev = process.env.NODE_ENV === 'development'
    const baseURL = isDev 
      ? 'https://dev-api.heath-report.com' 
      : 'https://api.heath-report.com'
    
    // 验证URL是否使用HTTPS
    if (!baseURL.startsWith('https://')) {
      throw new Error('API必须使用HTTPS协议')
    }
    
    return baseURL
  }

  /**
   * 设置请求拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.addRequestInterceptor((config) => {
      // 确保使用HTTPS
      if (config.url && !config.url.startsWith('https://')) {
        config.url = config.url.replace('http://', 'https://')
      }

      // 添加安全头
      config.header = {
        ...config.header,
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-App-Version': this.getAppVersion(),
        'X-Platform': this.getPlatform(),
        'X-Timestamp': Date.now().toString()
      }

      // 添加认证token
      const token = this.getAuthToken()
      if (token) {
        config.header['Authorization'] = `Bearer ${token}`
      }

      // 对敏感数据进行加密
      if (config.data && this.needsEncryption(config)) {
        config.data = this.encryptRequestData(config.data)
        config.header['X-Encrypted'] = 'true'
      }

      // 添加请求签名
      const signature = this.generateRequestSignature(config)
      config.header['X-Signature'] = signature

      return config
    })

    // 响应拦截器
    this.addResponseInterceptor(
      (response) => {
        // 验证响应签名
        if (!this.verifyResponseSignature(response)) {
          throw new Error('响应签名验证失败')
        }

        // 解密响应数据
        if (response.header['X-Encrypted'] === 'true') {
          response.data = this.decryptResponseData(response.data)
        }

        return response
      },
      (error) => {
        this.handleNetworkError(error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(successInterceptor, errorInterceptor) {
    this.responseInterceptors.push({
      success: successInterceptor,
      error: errorInterceptor
    })
  }

  /**
   * 安全的HTTP请求
   */
  async request(config) {
    try {
      // 应用请求拦截器
      let processedConfig = config
      for (const interceptor of this.requestInterceptors) {
        processedConfig = interceptor(processedConfig)
      }

      // 发送请求
      const response = await this.makeRequest(processedConfig)

      // 应用响应拦截器
      let processedResponse = response
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.success) {
          processedResponse = interceptor.success(processedResponse)
        }
      }

      return processedResponse
    } catch (error) {
      // 应用错误拦截器
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.error) {
          interceptor.error(error)
        }
      }
      throw error
    }
  }

  /**
   * 实际发送请求
   */
  makeRequest(config) {
    return new Promise((resolve, reject) => {
      uni.request({
        ...config,
        success: (response) => {
          if (response.statusCode >= 200 && response.statusCode < 300) {
            resolve(response)
          } else {
            reject(new Error(`HTTP ${response.statusCode}: ${response.data?.message || '请求失败'}`))
          }
        },
        fail: (error) => {
          reject(new Error(`网络请求失败: ${error.errMsg}`))
        }
      })
    })
  }

  /**
   * 判断是否需要加密
   */
  needsEncryption(config) {
    const encryptionPaths = [
      '/auth/login',
      '/auth/register',
      '/user/profile',
      '/reports',
      '/sync'
    ]
    
    return encryptionPaths.some(path => config.url.includes(path))
  }

  /**
   * 加密请求数据
   */
  encryptRequestData(data) {
    try {
      return encryptionService.encrypt(data)
    } catch (error) {
      console.error('请求数据加密失败:', error)
      throw error
    }
  }

  /**
   * 解密响应数据
   */
  decryptResponseData(data) {
    try {
      if (data.data && data.iv) {
        return encryptionService.decrypt(data.data, data.iv)
      }
      return data
    } catch (error) {
      console.error('响应数据解密失败:', error)
      throw error
    }
  }

  /**
   * 生成请求签名
   */
  generateRequestSignature(config) {
    const timestamp = config.header['X-Timestamp']
    const data = config.data || ''
    return encryptionService.generateSignature(data, timestamp)
  }

  /**
   * 验证响应签名
   */
  verifyResponseSignature(response) {
    const signature = response.header['X-Signature']
    const timestamp = response.header['X-Timestamp']
    
    if (!signature || !timestamp) {
      return true // 如果没有签名，跳过验证
    }

    return encryptionService.verifySignature(response.data, timestamp, signature)
  }

  /**
   * 获取应用版本
   */
  getAppVersion() {
    try {
      const systemInfo = uni.getSystemInfoSync()
      return systemInfo.version || '1.0.0'
    } catch {
      return '1.0.0'
    }
  }

  /**
   * 获取平台信息
   */
  getPlatform() {
    try {
      const systemInfo = uni.getSystemInfoSync()
      return systemInfo.platform || 'unknown'
    } catch {
      return 'unknown'
    }
  }

  /**
   * 获取认证token
   */
  getAuthToken() {
    try {
      return uni.getStorageSync('auth_token')
    } catch {
      return null
    }
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error) {
    console.error('网络请求错误:', error)
    
    // 记录安全相关错误
    if (error.message.includes('证书') || error.message.includes('SSL')) {
      this.logSecurityEvent('SSL_ERROR', {
        error: error.message,
        timestamp: Date.now()
      })
    }
  }

  /**
   * 记录安全事件
   */
  logSecurityEvent(eventType, data) {
    try {
      const securityLogs = uni.getStorageSync('security_logs') || []
      securityLogs.unshift({
        type: eventType,
        data,
        timestamp: Date.now()
      })
      
      // 只保留最近50条安全日志
      if (securityLogs.length > 50) {
        securityLogs.splice(50)
      }
      
      uni.setStorageSync('security_logs', securityLogs)
    } catch (error) {
      console.error('记录安全事件失败:', error)
    }
  }

  /**
   * GET请求
   */
  get(url, params = {}) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    return this.request({
      url: `${this.baseURL}${fullUrl}`,
      method: 'GET'
    })
  }

  /**
   * POST请求
   */
  post(url, data = {}) {
    return this.request({
      url: `${this.baseURL}${url}`,
      method: 'POST',
      data
    })
  }

  /**
   * PUT请求
   */
  put(url, data = {}) {
    return this.request({
      url: `${this.baseURL}${url}`,
      method: 'PUT',
      data
    })
  }

  /**
   * DELETE请求
   */
  delete(url) {
    return this.request({
      url: `${this.baseURL}${url}`,
      method: 'DELETE'
    })
  }
}

export default new NetworkSecurityService()