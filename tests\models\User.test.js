/**
 * 用户模型单元测试
 */

import { User } from '../../models/User.js'
import { Constants } from '../../types/index.js'

describe('User Model', () => {
  describe('创建用户', () => {
    test('应该能够创建有效的用户', () => {
      const userData = {
        phone: '13800138000',
        nickname: '测试用户',
        gender: Constants.GENDERS.MALE
      }
      
      const user = User.create(userData)
      
      expect(user.phone).toBe('13800138000')
      expect(user.nickname).toBe('测试用户')
      expect(user.gender).toBe(Constants.GENDERS.MALE)
      expect(user.id).toBeDefined()
      expect(user.createdAt).toBeInstanceOf(Date)
      expect(user.updatedAt).toBeInstanceOf(Date)
    })
    
    test('创建用户时缺少手机号应该抛出错误', () => {
      const userData = {
        nickname: '测试用户'
      }
      
      expect(() => {
        User.create(userData)
      }).toThrow('手机号是必填项')
    })
    
    test('创建用户时手机号格式无效应该抛出错误', () => {
      const userData = {
        phone: '1234567890',
        nickname: '测试用户'
      }
      
      expect(() => {
        User.create(userData)
      }).toThrow('用户数据验证失败')
    })
  })
  
  describe('手机号验证', () => {
    test('应该验证有效的手机号', () => {
      const validPhones = [
        '13800138000',
        '15912345678',
        '18612345678',
        '19912345678'
      ]
      
      validPhones.forEach(phone => {
        expect(User.validatePhone(phone)).toBe(true)
      })
    })
    
    test('应该拒绝无效的手机号', () => {
      const invalidPhones = [
        '1234567890',
        '12345678901',
        '1080138000',
        '138001380001',
        'abcdefghijk',
        ''
      ]
      
      invalidPhones.forEach(phone => {
        expect(User.validatePhone(phone)).toBe(false)
      })
    })
  })
  
  describe('密码验证', () => {
    test('应该验证有效的密码', () => {
      const validPasswords = [
        'abc123',
        'password123',
        'MyPassword1',
        'Test@123'
      ]
      
      validPasswords.forEach(password => {
        const result = User.validatePassword(password)
        expect(result.isValid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })
    })
    
    test('应该拒绝无效的密码', () => {
      const invalidPasswords = [
        '',
        '123',
        'abc',
        '123456',
        'abcdef',
        'a'.repeat(25)
      ]
      
      invalidPasswords.forEach(password => {
        const result = User.validatePassword(password)
        expect(result.isValid).toBe(false)
        expect(result.errors.length).toBeGreaterThan(0)
      })
    })
    
    test('应该正确计算密码强度', () => {
      expect(User.calculatePasswordStrength('123456')).toBe('weak')
      expect(User.calculatePasswordStrength('abc123')).toBe('weak')
      expect(User.calculatePasswordStrength('Password123')).toBe('medium')
      expect(User.calculatePasswordStrength('MyP@ssw0rd123')).toBe('strong')
    })
  })
  
  describe('密码设置和验证', () => {
    test('应该能够设置和验证密码', () => {
      const user = new User({
        phone: '13800138000'
      })
      
      user.setPassword('test123')
      
      expect(user.passwordHash).toBeDefined()
      expect(user.salt).toBeDefined()
      expect(user.verifyPassword('test123')).toBe(true)
      expect(user.verifyPassword('wrong')).toBe(false)
    })
    
    test('设置无效密码应该抛出错误', () => {
      const user = new User({
        phone: '13800138000'
      })
      
      expect(() => {
        user.setPassword('123')
      }).toThrow('密码验证失败')
    })
  })
  
  describe('用户信息计算', () => {
    test('应该正确计算年龄', () => {
      const user = new User({
        phone: '13800138000',
        birthday: new Date('1990-01-01')
      })
      
      const age = user.getAge()
      const expectedAge = new Date().getFullYear() - 1990
      
      expect(age).toBeGreaterThanOrEqual(expectedAge - 1)
      expect(age).toBeLessThanOrEqual(expectedAge)
    })
    
    test('没有生日时年龄应该为null', () => {
      const user = new User({
        phone: '13800138000'
      })
      
      expect(user.getAge()).toBeNull()
    })
    
    test('应该正确计算BMI', () => {
      const user = new User({
        phone: '13800138000',
        height: 170,
        weight: 70
      })
      
      const bmi = user.getBMI()
      expect(bmi).toBeCloseTo(24.2, 1)
    })
    
    test('没有身高体重时BMI应该为null', () => {
      const user = new User({
        phone: '13800138000'
      })
      
      expect(user.getBMI()).toBeNull()
    })
    
    test('应该正确分类BMI', () => {
      const testCases = [
        { height: 170, weight: 50, expected: 'underweight' },
        { height: 170, weight: 65, expected: 'normal' },
        { height: 170, weight: 75, expected: 'overweight' },
        { height: 170, weight: 90, expected: 'obese' }
      ]
      
      testCases.forEach(({ height, weight, expected }) => {
        const user = new User({
          phone: '13800138000',
          height,
          weight
        })
        
        expect(user.getBMICategory()).toBe(expected)
      })
    })
  })
  
  describe('用户设置', () => {
    test('应该能够更新用户设置', () => {
      const user = new User({
        phone: '13800138000'
      })
      
      const newSettings = {
        theme: Constants.THEMES.DARK,
        notificationEnabled: false
      }
      
      user.updateSettings(newSettings)
      
      expect(user.settings.theme).toBe(Constants.THEMES.DARK)
      expect(user.settings.notificationEnabled).toBe(false)
      expect(user.settings.autoSync).toBe(true) // 保持原有设置
    })
  })
  
  describe('登录记录', () => {
    test('应该能够记录登录', () => {
      const user = new User({
        phone: '13800138000'
      })
      
      const initialLoginCount = user.loginCount
      const beforeLogin = new Date()
      
      user.recordLogin()
      
      expect(user.loginCount).toBe(initialLoginCount + 1)
      expect(user.lastLoginAt).toBeInstanceOf(Date)
      expect(user.lastLoginAt.getTime()).toBeGreaterThanOrEqual(beforeLogin.getTime())
    })
  })
  
  describe('显示信息', () => {
    test('应该返回昵称作为显示名称', () => {
      const user = new User({
        phone: '13800138000',
        nickname: '测试用户'
      })
      
      expect(user.getDisplayName()).toBe('测试用户')
    })
    
    test('没有昵称时应该返回脱敏手机号', () => {
      const user = new User({
        phone: '13800138000'
      })
      
      expect(user.getDisplayName()).toBe('138****8000')
    })
    
    test('应该检查基本信息是否完善', () => {
      const incompleteUser = new User({
        phone: '13800138000'
      })
      
      const completeUser = new User({
        phone: '13800138000',
        nickname: '测试用户',
        gender: Constants.GENDERS.MALE,
        birthday: new Date('1990-01-01'),
        height: 170,
        weight: 70
      })
      
      expect(incompleteUser.isProfileComplete()).toBe(false)
      expect(completeUser.isProfileComplete()).toBe(true)
    })
  })
  
  describe('数据序列化', () => {
    test('应该正确序列化为JSON', () => {
      const user = new User({
        phone: '13800138000',
        nickname: '测试用户'
      })
      
      user.setPassword('test123')
      
      const json = user.toJSON()
      
      expect(json.phone).toBe('13800138000')
      expect(json.nickname).toBe('测试用户')
      expect(json.passwordHash).toBeDefined()
      expect(json.salt).toBeDefined()
    })
    
    test('应该正确序列化为安全JSON', () => {
      const user = new User({
        phone: '13800138000',
        nickname: '测试用户'
      })
      
      user.setPassword('test123')
      
      const safeJson = user.toSafeJSON()
      
      expect(safeJson.phone).toBe('13800138000')
      expect(safeJson.nickname).toBe('测试用户')
      expect(safeJson.passwordHash).toBeUndefined()
      expect(safeJson.salt).toBeUndefined()
      expect(safeJson.verificationToken).toBeUndefined()
    })
    
    test('应该能够从JSON创建用户实例', () => {
      const json = {
        id: 'test-id',
        phone: '13800138000',
        nickname: '测试用户',
        gender: Constants.GENDERS.MALE,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      const user = User.fromJSON(json)
      
      expect(user).toBeInstanceOf(User)
      expect(user.phone).toBe('13800138000')
      expect(user.nickname).toBe('测试用户')
      expect(user.gender).toBe(Constants.GENDERS.MALE)
    })
  })
  
  describe('数据验证', () => {
    test('应该验证用户数据', () => {
      const validUser = new User({
        phone: '13800138000',
        nickname: '测试用户',
        height: 170,
        weight: 70
      })
      
      const validation = validUser.validate()
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toHaveLength(0)
    })
    
    test('应该检测无效的用户数据', () => {
      const invalidUser = new User({
        phone: '1234567890', // 无效手机号
        nickname: 'a'.repeat(25), // 昵称过长
        height: 500, // 身高超出范围
        weight: -10 // 体重无效
      })
      
      const validation = invalidUser.validate()
      expect(validation.isValid).toBe(false)
      expect(validation.errors.length).toBeGreaterThan(0)
    })
  })
})