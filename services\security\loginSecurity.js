/**
 * 登录安全服务
 * 提供异常登录检测和安全提醒功能
 */

class LoginSecurityService {
  constructor() {
    this.maxFailedAttempts = 5 // 最大失败尝试次数
    this.lockoutDuration = 30 * 60 * 1000 // 锁定时长30分钟
    this.suspiciousThreshold = 3 // 可疑活动阈值
  }

  /**
   * 记录登录尝试
   */
  recordLoginAttempt(phone, success, deviceInfo = {}) {
    try {
      const loginHistory = this.getLoginHistory()
      const attempt = {
        id: Date.now().toString(),
        phone,
        success,
        timestamp: Date.now(),
        deviceInfo: {
          platform: deviceInfo.platform || uni.getSystemInfoSync().platform,
          system: deviceInfo.system || uni.getSystemInfoSync().system,
          model: deviceInfo.model || uni.getSystemInfoSync().model,
          ip: deviceInfo.ip || 'unknown',
          location: deviceInfo.location || 'unknown'
        },
        userAgent: deviceInfo.userAgent || navigator.userAgent || 'unknown'
      }

      loginHistory.unshift(attempt)
      
      // 只保留最近100条记录
      if (loginHistory.length > 100) {
        loginHistory.splice(100)
      }

      uni.setStorageSync('login_history', loginHistory)
      
      // 检测异常登录
      if (success) {
        this.detectAbnormalLogin(attempt, loginHistory)
        this.clearFailedAttempts(phone)
      } else {
        this.handleFailedLogin(phone, attempt)
      }

      return attempt
    } catch (error) {
      console.error('记录登录尝试失败:', error)
      return null
    }
  }

  /**
   * 获取登录历史
   */
  getLoginHistory() {
    try {
      return uni.getStorageSync('login_history') || []
    } catch {
      return []
    }
  }

  /**
   * 处理登录失败
   */
  handleFailedLogin(phone, attempt) {
    const failedAttempts = this.getFailedAttempts(phone)
    failedAttempts.push(attempt)

    // 检查是否需要锁定账户
    if (failedAttempts.length >= this.maxFailedAttempts) {
      this.lockAccount(phone)
      this.sendSecurityAlert(phone, 'ACCOUNT_LOCKED', {
        attempts: failedAttempts.length,
        lastAttempt: attempt
      })
    } else if (failedAttempts.length >= this.suspiciousThreshold) {
      this.sendSecurityAlert(phone, 'SUSPICIOUS_LOGIN', {
        attempts: failedAttempts.length,
        lastAttempt: attempt
      })
    }

    this.saveFailedAttempts(phone, failedAttempts)
  }

  /**
   * 检测异常登录
   */
  detectAbnormalLogin(currentAttempt, loginHistory) {
    const userHistory = loginHistory.filter(h => 
      h.phone === currentAttempt.phone && h.success
    ).slice(0, 10) // 最近10次成功登录

    if (userHistory.length < 2) {
      return // 历史记录不足，无法判断
    }

    const anomalies = []

    // 检测设备异常
    if (this.isNewDevice(currentAttempt, userHistory)) {
      anomalies.push('NEW_DEVICE')
    }

    // 检测地理位置异常
    if (this.isUnusualLocation(currentAttempt, userHistory)) {
      anomalies.push('UNUSUAL_LOCATION')
    }

    // 检测时间异常
    if (this.isUnusualTime(currentAttempt, userHistory)) {
      anomalies.push('UNUSUAL_TIME')
    }

    // 检测频繁登录
    if (this.isFrequentLogin(currentAttempt, userHistory)) {
      anomalies.push('FREQUENT_LOGIN')
    }

    // 如果检测到异常，发送安全提醒
    if (anomalies.length > 0) {
      this.sendSecurityAlert(currentAttempt.phone, 'ABNORMAL_LOGIN', {
        anomalies,
        currentAttempt,
        userHistory: userHistory.slice(0, 3)
      })
    }
  }

  /**
   * 检测是否为新设备
   */
  isNewDevice(currentAttempt, userHistory) {
    const currentDevice = currentAttempt.deviceInfo
    return !userHistory.some(h => 
      h.deviceInfo.platform === currentDevice.platform &&
      h.deviceInfo.model === currentDevice.model
    )
  }

  /**
   * 检测是否为异常地理位置
   */
  isUnusualLocation(currentAttempt, userHistory) {
    const currentLocation = currentAttempt.deviceInfo.location
    if (currentLocation === 'unknown') return false

    const commonLocations = userHistory
      .map(h => h.deviceInfo.location)
      .filter(loc => loc !== 'unknown')

    return commonLocations.length > 0 && !commonLocations.includes(currentLocation)
  }

  /**
   * 检测是否为异常时间
   */
  isUnusualTime(currentAttempt, userHistory) {
    const currentHour = new Date(currentAttempt.timestamp).getHours()
    const historicalHours = userHistory.map(h => new Date(h.timestamp).getHours())
    
    // 如果历史记录中没有在当前时间段±2小时内登录的记录，认为是异常时间
    return !historicalHours.some(hour => Math.abs(hour - currentHour) <= 2)
  }

  /**
   * 检测是否为频繁登录
   */
  isFrequentLogin(currentAttempt, userHistory) {
    const recentLogins = userHistory.filter(h => 
      currentAttempt.timestamp - h.timestamp < 5 * 60 * 1000 // 5分钟内
    )
    
    return recentLogins.length > 3
  }

  /**
   * 发送安全提醒
   */
  async sendSecurityAlert(phone, alertType, data) {
    try {
      const alert = {
        id: Date.now().toString(),
        phone,
        type: alertType,
        data,
        timestamp: Date.now(),
        read: false
      }

      // 保存到本地
      const alerts = this.getSecurityAlerts()
      alerts.unshift(alert)
      
      if (alerts.length > 50) {
        alerts.splice(50)
      }
      
      uni.setStorageSync('security_alerts', alerts)

      // 显示用户提醒
      this.showSecurityNotification(alert)

      // 发送到服务器（如果需要）
      // await this.sendAlertToServer(alert)

    } catch (error) {
      console.error('发送安全提醒失败:', error)
    }
  }

  /**
   * 显示安全通知
   */
  showSecurityNotification(alert) {
    const messages = {
      'ACCOUNT_LOCKED': '账户已被锁定，请30分钟后重试或联系客服',
      'SUSPICIOUS_LOGIN': '检测到多次登录失败，请注意账户安全',
      'ABNORMAL_LOGIN': '检测到异常登录，如非本人操作请及时修改密码',
      'NEW_DEVICE': '检测到新设备登录，如非本人操作请及时修改密码'
    }

    const message = messages[alert.type] || '检测到安全异常，请注意账户安全'

    // 显示系统通知
    uni.showModal({
      title: '安全提醒',
      content: message,
      showCancel: true,
      cancelText: '忽略',
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm) {
          // 跳转到安全中心
          uni.navigateTo({
            url: '/pages/settings/security'
          })
        }
      }
    })

    // 发送本地通知（如果支持）
    // #ifdef APP-PLUS
    if (plus && plus.push) {
      plus.push.createMessage(message, '', {
        title: '健康报告 - 安全提醒'
      })
    }
    // #endif
  }

  /**
   * 获取安全提醒列表
   */
  getSecurityAlerts() {
    try {
      return uni.getStorageSync('security_alerts') || []
    } catch {
      return []
    }
  }

  /**
   * 标记提醒为已读
   */
  markAlertAsRead(alertId) {
    try {
      const alerts = this.getSecurityAlerts()
      const alert = alerts.find(a => a.id === alertId)
      if (alert) {
        alert.read = true
        uni.setStorageSync('security_alerts', alerts)
      }
    } catch (error) {
      console.error('标记提醒已读失败:', error)
    }
  }

  /**
   * 获取失败尝试记录
   */
  getFailedAttempts(phone) {
    try {
      const allFailedAttempts = uni.getStorageSync('failed_attempts') || {}
      return allFailedAttempts[phone] || []
    } catch {
      return []
    }
  }

  /**
   * 保存失败尝试记录
   */
  saveFailedAttempts(phone, attempts) {
    try {
      const allFailedAttempts = uni.getStorageSync('failed_attempts') || {}
      allFailedAttempts[phone] = attempts
      uni.setStorageSync('failed_attempts', allFailedAttempts)
    } catch (error) {
      console.error('保存失败尝试记录失败:', error)
    }
  }

  /**
   * 清除失败尝试记录
   */
  clearFailedAttempts(phone) {
    try {
      const allFailedAttempts = uni.getStorageSync('failed_attempts') || {}
      delete allFailedAttempts[phone]
      uni.setStorageSync('failed_attempts', allFailedAttempts)
    } catch (error) {
      console.error('清除失败尝试记录失败:', error)
    }
  }

  /**
   * 锁定账户
   */
  lockAccount(phone) {
    try {
      const lockedAccounts = uni.getStorageSync('locked_accounts') || {}
      lockedAccounts[phone] = {
        lockedAt: Date.now(),
        unlockAt: Date.now() + this.lockoutDuration
      }
      uni.setStorageSync('locked_accounts', lockedAccounts)
    } catch (error) {
      console.error('锁定账户失败:', error)
    }
  }

  /**
   * 检查账户是否被锁定
   */
  isAccountLocked(phone) {
    try {
      const lockedAccounts = uni.getStorageSync('locked_accounts') || {}
      const lockInfo = lockedAccounts[phone]
      
      if (!lockInfo) return false
      
      if (Date.now() > lockInfo.unlockAt) {
        // 锁定时间已过，解锁账户
        delete lockedAccounts[phone]
        uni.setStorageSync('locked_accounts', lockedAccounts)
        return false
      }
      
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取账户解锁时间
   */
  getUnlockTime(phone) {
    try {
      const lockedAccounts = uni.getStorageSync('locked_accounts') || {}
      const lockInfo = lockedAccounts[phone]
      return lockInfo ? lockInfo.unlockAt : null
    } catch {
      return null
    }
  }

  /**
   * 手动解锁账户（管理员功能）
   */
  unlockAccount(phone) {
    try {
      const lockedAccounts = uni.getStorageSync('locked_accounts') || {}
      delete lockedAccounts[phone]
      uni.setStorageSync('locked_accounts', lockedAccounts)
      this.clearFailedAttempts(phone)
      return true
    } catch (error) {
      console.error('解锁账户失败:', error)
      return false
    }
  }

  /**
   * 获取登录安全统计
   */
  getSecurityStats(phone) {
    try {
      const loginHistory = this.getLoginHistory()
      const userHistory = loginHistory.filter(h => h.phone === phone)
      const failedAttempts = this.getFailedAttempts(phone)
      const alerts = this.getSecurityAlerts().filter(a => a.phone === phone)
      
      return {
        totalLogins: userHistory.length,
        successfulLogins: userHistory.filter(h => h.success).length,
        failedLogins: userHistory.filter(h => !h.success).length,
        currentFailedAttempts: failedAttempts.length,
        securityAlerts: alerts.length,
        unreadAlerts: alerts.filter(a => !a.read).length,
        isLocked: this.isAccountLocked(phone),
        lastLogin: userHistory.find(h => h.success)?.timestamp || null
      }
    } catch (error) {
      console.error('获取安全统计失败:', error)
      return null
    }
  }
}

export default new LoginSecurityService()