/**
 * 智能提醒系统
 * 对持续异常指标发送通知和提醒
 */

class ReminderService {
  constructor() {
    this.reminderConfig = {
      // 提醒类型
      types: {
        checkup: 'checkup',           // 定期检查提醒
        abnormal: 'abnormal',         // 异常指标提醒
        followup: 'followup',         // 复查提醒
        medication: 'medication',     // 用药提醒
        lifestyle: 'lifestyle'        // 生活方式提醒
      },
      // 提醒频率
      frequencies: {
        once: 'once',                 // 一次性
        daily: 'daily',               // 每日
        weekly: 'weekly',             // 每周
        monthly: 'monthly',           // 每月
        custom: 'custom'              // 自定义
      },
      // 默认提醒时间
      defaultTimes: {
        checkup: { hour: 9, minute: 0 },
        abnormal: { hour: 10, minute: 0 },
        followup: { hour: 14, minute: 0 },
        medication: { hour: 8, minute: 0 },
        lifestyle: { hour: 20, minute: 0 }
      }
    }
  }

  /**
   * 创建智能提醒
   * @param {Object} reminderData 提醒数据
   * @returns {Promise<string>} 提醒ID
   */
  async createReminder(reminderData) {
    try {
      const reminder = {
        id: `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...reminderData,
        createdAt: new Date().toISOString(),
        isActive: true,
        lastTriggered: null,
        triggerCount: 0
      }

      // 验证提醒数据
      this.validateReminderData(reminder)

      // 保存提醒
      await this.saveReminder(reminder)

      // 设置系统通知
      await this.scheduleNotification(reminder)

      return reminder.id
    } catch (error) {
      console.error('创建提醒失败:', error)
      throw new Error('提醒创建失败')
    }
  }

  /**
   * 验证提醒数据
   * @param {Object} reminder 提醒对象
   */
  validateReminderData(reminder) {
    const required = ['type', 'title', 'message', 'frequency']
    
    required.forEach(field => {
      if (!reminder[field]) {
        throw new Error(`缺少必需字段: ${field}`)
      }
    })

    if (!this.reminderConfig.types[reminder.type]) {
      throw new Error(`无效的提醒类型: ${reminder.type}`)
    }

    if (!this.reminderConfig.frequencies[reminder.frequency]) {
      throw new Error(`无效的提醒频率: ${reminder.frequency}`)
    }
  }

  /**
   * 保存提醒
   * @param {Object} reminder 提醒对象
   */
  async saveReminder(reminder) {
    try {
      const reminders = uni.getStorageSync('health_reminders') || []
      reminders.push(reminder)
      uni.setStorageSync('health_reminders', reminders)
    } catch (error) {
      console.error('保存提醒失败:', error)
      throw error
    }
  }

  /**
   * 设置系统通知
   * @param {Object} reminder 提醒对象
   */
  async scheduleNotification(reminder) {
    try {
      // #ifdef APP-PLUS
      await this.scheduleNotificationApp(reminder)
      // #endif

      // #ifdef MP-WEIXIN
      await this.scheduleNotificationMP(reminder)
      // #endif

      // #ifdef H5
      await this.scheduleNotificationH5(reminder)
      // #endif
    } catch (error) {
      console.error('设置系统通知失败:', error)
    }
  }

  /**
   * APP平台设置通知
   * @param {Object} reminder 提醒对象
   */
  async scheduleNotificationApp(reminder) {
    const notificationTime = this.calculateNextNotificationTime(reminder)
    
    plus.push.createMessage(reminder.message, {
      title: reminder.title,
      when: notificationTime,
      cover: false
    }, {
      title: reminder.title,
      content: reminder.message,
      payload: {
        type: 'health_reminder',
        reminderId: reminder.id
      }
    })
  }

  /**
   * 小程序平台设置通知
   * @param {Object} reminder 提醒对象
   */
  async scheduleNotificationMP(reminder) {
    // 小程序使用订阅消息
    try {
      await wx.requestSubscribeMessage({
        tmplIds: ['reminder_template_id'], // 需要在微信公众平台配置
        success: (res) => {
          console.log('订阅消息设置成功', res)
        }
      })
    } catch (error) {
      console.log('订阅消息设置失败', error)
    }
  }

  /**
   * H5平台设置通知
   * @param {Object} reminder 提醒对象
   */
  async scheduleNotificationH5(reminder) {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      if (permission === 'granted') {
        const notificationTime = this.calculateNextNotificationTime(reminder)
        const delay = notificationTime.getTime() - Date.now()
        
        if (delay > 0) {
          setTimeout(() => {
            new Notification(reminder.title, {
              body: reminder.message,
              icon: '/static/logo.png',
              tag: reminder.id
            })
          }, delay)
        }
      }
    }
  }

  /**
   * 计算下次通知时间
   * @param {Object} reminder 提醒对象
   * @returns {Date} 通知时间
   */
  calculateNextNotificationTime(reminder) {
    const now = new Date()
    const nextTime = new Date()

    // 设置提醒时间
    const defaultTime = this.reminderConfig.defaultTimes[reminder.type]
    const reminderTime = reminder.time || defaultTime
    
    nextTime.setHours(reminderTime.hour, reminderTime.minute, 0, 0)

    // 根据频率计算下次时间
    switch (reminder.frequency) {
      case 'daily':
        if (nextTime <= now) {
          nextTime.setDate(nextTime.getDate() + 1)
        }
        break

      case 'weekly':
        const targetDay = reminder.dayOfWeek || 1 // 默认周一
        const currentDay = nextTime.getDay()
        let daysToAdd = targetDay - currentDay
        
        if (daysToAdd <= 0 || (daysToAdd === 0 && nextTime <= now)) {
          daysToAdd += 7
        }
        
        nextTime.setDate(nextTime.getDate() + daysToAdd)
        break

      case 'monthly':
        const targetDate = reminder.dayOfMonth || 1 // 默认每月1号
        nextTime.setDate(targetDate)
        
        if (nextTime <= now) {
          nextTime.setMonth(nextTime.getMonth() + 1)
        }
        break

      case 'custom':
        if (reminder.customInterval) {
          const intervalMs = reminder.customInterval * 24 * 60 * 60 * 1000
          nextTime.setTime(now.getTime() + intervalMs)
        }
        break

      case 'once':
        if (reminder.scheduledTime) {
          return new Date(reminder.scheduledTime)
        }
        break
    }

    return nextTime
  }

  /**
   * 基于健康数据创建智能提醒
   * @param {Array} reports 健康报告列表
   * @returns {Promise<Array>} 创建的提醒列表
   */
  async createIntelligentReminders(reports) {
    const reminders = []

    try {
      // 分析健康数据
      const analysisResults = await this.analyzeHealthData(reports)

      // 根据分析结果创建提醒
      for (const analysis of analysisResults) {
        const reminder = await this.createReminderFromAnalysis(analysis)
        if (reminder) {
          reminders.push(reminder)
        }
      }

      return reminders
    } catch (error) {
      console.error('创建智能提醒失败:', error)
      return []
    }
  }

  /**
   * 分析健康数据
   * @param {Array} reports 健康报告列表
   * @returns {Promise<Array>} 分析结果
   */
  async analyzeHealthData(reports) {
    const analyses = []

    // 检查是否需要定期检查提醒
    const lastReportDate = this.getLastReportDate(reports)
    if (this.shouldRemindCheckup(lastReportDate)) {
      analyses.push({
        type: 'checkup',
        priority: 'medium',
        data: { lastReportDate }
      })
    }

    // 检查异常指标
    const abnormalIndicators = this.getAbnormalIndicators(reports)
    if (abnormalIndicators.length > 0) {
      analyses.push({
        type: 'abnormal',
        priority: 'high',
        data: { indicators: abnormalIndicators }
      })
    }

    // 检查是否需要复查
    const followupNeeded = this.checkFollowupNeeded(reports)
    if (followupNeeded.length > 0) {
      analyses.push({
        type: 'followup',
        priority: 'high',
        data: { indicators: followupNeeded }
      })
    }

    // 生活方式建议
    const lifestyleAdvice = this.generateLifestyleAdvice(reports)
    if (lifestyleAdvice.length > 0) {
      analyses.push({
        type: 'lifestyle',
        priority: 'low',
        data: { advice: lifestyleAdvice }
      })
    }

    return analyses
  }

  /**
   * 获取最后检查日期
   * @param {Array} reports 报告列表
   * @returns {Date|null} 最后检查日期
   */
  getLastReportDate(reports) {
    if (reports.length === 0) return null
    
    return reports.reduce((latest, current) => {
      const currentDate = new Date(current.reportDate)
      const latestDate = new Date(latest.reportDate)
      return currentDate > latestDate ? current : latest
    }).reportDate
  }

  /**
   * 检查是否需要定期检查提醒
   * @param {Date|null} lastReportDate 最后检查日期
   * @returns {boolean} 是否需要提醒
   */
  shouldRemindCheckup(lastReportDate) {
    if (!lastReportDate) return true

    const daysSinceLastReport = (Date.now() - new Date(lastReportDate).getTime()) / (1000 * 60 * 60 * 24)
    return daysSinceLastReport > 90 // 超过3个月提醒检查
  }

  /**
   * 获取异常指标
   * @param {Array} reports 报告列表
   * @returns {Array} 异常指标列表
   */
  getAbnormalIndicators(reports) {
    const abnormalIndicators = []
    
    reports.forEach(report => {
      report.indicators.forEach(indicator => {
        if (indicator.isAbnormal) {
          abnormalIndicators.push({
            ...indicator,
            reportDate: report.reportDate,
            reportId: report.id
          })
        }
      })
    })

    return abnormalIndicators
  }

  /**
   * 检查是否需要复查
   * @param {Array} reports 报告列表
   * @returns {Array} 需要复查的指标
   */
  checkFollowupNeeded(reports) {
    const followupNeeded = []
    
    // 按指标名称分组
    const indicatorGroups = {}
    reports.forEach(report => {
      report.indicators.forEach(indicator => {
        if (!indicatorGroups[indicator.name]) {
          indicatorGroups[indicator.name] = []
        }
        indicatorGroups[indicator.name].push({
          ...indicator,
          reportDate: report.reportDate
        })
      })
    })

    // 检查连续异常的指标
    Object.keys(indicatorGroups).forEach(indicatorName => {
      const indicators = indicatorGroups[indicatorName]
        .sort((a, b) => new Date(b.reportDate) - new Date(a.reportDate))

      // 检查最近两次是否都异常
      if (indicators.length >= 2 && 
          indicators[0].isAbnormal && 
          indicators[1].isAbnormal) {
        followupNeeded.push({
          name: indicatorName,
          category: indicators[0].category,
          consecutiveAbnormal: 2,
          lastValue: indicators[0].value,
          lastDate: indicators[0].reportDate
        })
      }
    })

    return followupNeeded
  }

  /**
   * 生成生活方式建议
   * @param {Array} reports 报告列表
   * @returns {Array} 建议列表
   */
  generateLifestyleAdvice(reports) {
    const advice = []
    
    // 基于异常指标生成建议
    const abnormalCategories = new Set()
    reports.forEach(report => {
      report.indicators.forEach(indicator => {
        if (indicator.isAbnormal) {
          abnormalCategories.add(indicator.category)
        }
      })
    })

    abnormalCategories.forEach(category => {
      switch (category) {
        case '血压':
          advice.push({
            category: '血压管理',
            message: '建议减少盐分摄入，适量运动，保持心情愉悦',
            frequency: 'weekly'
          })
          break
        case '血糖':
          advice.push({
            category: '血糖控制',
            message: '建议控制饮食，定时定量进餐，适量运动',
            frequency: 'daily'
          })
          break
        case '血脂':
          advice.push({
            category: '血脂管理',
            message: '建议减少高脂食物，增加有氧运动，戒烟限酒',
            frequency: 'weekly'
          })
          break
      }
    })

    return advice
  }

  /**
   * 根据分析结果创建提醒
   * @param {Object} analysis 分析结果
   * @returns {Promise<Object|null>} 提醒对象
   */
  async createReminderFromAnalysis(analysis) {
    try {
      let reminderData = null

      switch (analysis.type) {
        case 'checkup':
          reminderData = {
            type: 'checkup',
            title: '定期健康检查提醒',
            message: '距离上次检查已超过3个月，建议进行健康检查',
            frequency: 'monthly',
            priority: analysis.priority
          }
          break

        case 'abnormal':
          const indicatorNames = analysis.data.indicators.map(i => i.name).join('、')
          reminderData = {
            type: 'abnormal',
            title: '异常指标关注提醒',
            message: `检测到${indicatorNames}等指标异常，请及时关注`,
            frequency: 'weekly',
            priority: analysis.priority,
            relatedIndicators: analysis.data.indicators
          }
          break

        case 'followup':
          const followupNames = analysis.data.indicators.map(i => i.name).join('、')
          reminderData = {
            type: 'followup',
            title: '复查提醒',
            message: `${followupNames}连续异常，建议尽快复查`,
            frequency: 'once',
            scheduledTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 一周后
            priority: analysis.priority
          }
          break

        case 'lifestyle':
          const advice = analysis.data.advice[0] // 取第一个建议
          reminderData = {
            type: 'lifestyle',
            title: `${advice.category}建议`,
            message: advice.message,
            frequency: advice.frequency,
            priority: analysis.priority
          }
          break
      }

      if (reminderData) {
        const reminderId = await this.createReminder(reminderData)
        return { ...reminderData, id: reminderId }
      }

      return null
    } catch (error) {
      console.error('根据分析创建提醒失败:', error)
      return null
    }
  }

  /**
   * 获取所有提醒
   * @returns {Array} 提醒列表
   */
  getAllReminders() {
    try {
      return uni.getStorageSync('health_reminders') || []
    } catch (error) {
      console.error('获取提醒列表失败:', error)
      return []
    }
  }

  /**
   * 获取活跃提醒
   * @returns {Array} 活跃提醒列表
   */
  getActiveReminders() {
    return this.getAllReminders().filter(reminder => reminder.isActive)
  }

  /**
   * 更新提醒
   * @param {string} reminderId 提醒ID
   * @param {Object} updateData 更新数据
   */
  async updateReminder(reminderId, updateData) {
    try {
      const reminders = this.getAllReminders()
      const index = reminders.findIndex(r => r.id === reminderId)
      
      if (index !== -1) {
        reminders[index] = { ...reminders[index], ...updateData }
        uni.setStorageSync('health_reminders', reminders)
        
        // 重新设置通知
        if (updateData.isActive !== false) {
          await this.scheduleNotification(reminders[index])
        }
      }
    } catch (error) {
      console.error('更新提醒失败:', error)
      throw error
    }
  }

  /**
   * 删除提醒
   * @param {string} reminderId 提醒ID
   */
  deleteReminder(reminderId) {
    try {
      const reminders = this.getAllReminders()
      const filteredReminders = reminders.filter(r => r.id !== reminderId)
      uni.setStorageSync('health_reminders', filteredReminders)
      
      // 取消系统通知
      this.cancelNotification(reminderId)
    } catch (error) {
      console.error('删除提醒失败:', error)
      throw error
    }
  }

  /**
   * 取消系统通知
   * @param {string} reminderId 提醒ID
   */
  cancelNotification(reminderId) {
    try {
      // #ifdef APP-PLUS
      plus.push.remove(reminderId)
      // #endif

      // #ifdef H5
      // H5平台无法直接取消定时器，需要在实际实现中维护定时器引用
      // #endif
    } catch (error) {
      console.error('取消通知失败:', error)
    }
  }

  /**
   * 触发提醒
   * @param {string} reminderId 提醒ID
   */
  async triggerReminder(reminderId) {
    try {
      const reminders = this.getAllReminders()
      const reminder = reminders.find(r => r.id === reminderId)
      
      if (reminder) {
        // 更新触发信息
        reminder.lastTriggered = new Date().toISOString()
        reminder.triggerCount = (reminder.triggerCount || 0) + 1
        
        // 如果是一次性提醒，设置为非活跃
        if (reminder.frequency === 'once') {
          reminder.isActive = false
        }
        
        await this.updateReminder(reminderId, reminder)
        
        // 如果仍然活跃，设置下次提醒
        if (reminder.isActive && reminder.frequency !== 'once') {
          await this.scheduleNotification(reminder)
        }
      }
    } catch (error) {
      console.error('触发提醒失败:', error)
    }
  }
}

export default new ReminderService()