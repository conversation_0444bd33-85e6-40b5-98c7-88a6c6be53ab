<template>
  <view class="report-detail-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading"></uni-load-more>
    </view>
    
    <!-- 报告详情内容 -->
    <view v-else-if="report" class="detail-content">
      <!-- 报告头部 -->
      <view class="report-header">
        <view class="header-main">
          <view class="hospital-info">
            <text class="hospital-name">{{ report.hospital }}</text>
            <text class="report-date">{{ formatDate(report.reportDate) }}</text>
          </view>
          <view class="overall-status">
            <view :class="['status-badge', overallStatus.type]">
              <uni-icons :type="overallStatus.icon" size="16" :color="overallStatus.color"></uni-icons>
              <text class="status-text">{{ overallStatus.text }}</text>
            </view>
          </view>
        </view>
        
        <!-- 快速统计 -->
        <view class="quick-stats">
          <view class="stat-item">
            <text class="stat-number">{{ report.items.length }}</text>
            <text class="stat-label">检查项目</text>
          </view>
          <view class="stat-item abnormal">
            <text class="stat-number">{{ abnormalCount }}</text>
            <text class="stat-label">异常指标</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ normalCount }}</text>
            <text class="stat-label">正常指标</text>
          </view>
        </view>
      </view>
      
      <!-- 异常指标警告 -->
      <view v-if="criticalAbnormalItems.length > 0" class="critical-alert">
        <view class="alert-header">
          <uni-icons type="warning-filled" size="20" color="#FF5722"></uni-icons>
          <text class="alert-title">严重异常指标</text>
        </view>
        <view class="alert-content">
          <text class="alert-desc">以下指标严重偏离正常范围，建议及时就医：</text>
          <view class="critical-items">
            <view 
              v-for="item in criticalAbnormalItems"
              :key="item.id"
              class="critical-item"
            >
              <text class="item-name">{{ item.name }}</text>
              <text class="item-value">{{ item.getDisplayValue() }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 基本信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">基本信息</text>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">医生姓名</text>
            <text class="info-value">{{ report.doctor || '未填写' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">科室</text>
            <text class="info-value">{{ report.department || '未填写' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">检查日期</text>
            <text class="info-value">{{ formatDate(report.checkDate) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">报告日期</text>
            <text class="info-value">{{ formatDate(report.reportDate) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 原始图片 -->
      <view v-if="report.originalImage" class="image-card">
        <view class="card-header">
          <text class="card-title">检查报告图片</text>
          <view class="image-actions">
            <button class="action-btn" @tap="previewImage">
              <uni-icons type="eye" size="16" color="#007AFF"></uni-icons>
              <text class="btn-text">查看</text>
            </button>
            <button class="action-btn" @tap="saveImage">
              <uni-icons type="download" size="16" color="#007AFF"></uni-icons>
              <text class="btn-text">保存</text>
            </button>
          </view>
        </view>
        <view class="image-container">
          <image 
            :src="report.originalImage" 
            class="report-image" 
            mode="aspectFit"
            @tap="previewImage"
            @error="handleImageError"
          />
        </view>
      </view>
      
      <!-- 检查项目详情 -->
      <view class="items-card">
        <view class="card-header">
          <text class="card-title">检查项目详情</text>
          <view class="filter-tabs">
            <view 
              v-for="tab in filterTabs"
              :key="tab.key"
              :class="['filter-tab', { active: activeTab === tab.key }]"
              @tap="switchTab(tab.key)"
            >
              <text class="tab-text">{{ tab.label }}</text>
              <text v-if="tab.count > 0" class="tab-count">{{ tab.count }}</text>
            </view>
          </view>
        </view>
        
        <!-- 项目列表 -->
        <view class="items-list">
          <view 
            v-for="item in filteredItems"
            :key="item.id"
            :class="['item-card', getItemSeverityClass(item)]"
            @tap="showItemDetail(item)"
          >
            <!-- 项目头部 -->
            <view class="item-header">
              <view class="item-name-section">
                <text class="item-name">{{ item.name }}</text>
                <view v-if="item.category" class="item-category">
                  <text class="category-text">{{ getCategoryName(item.category) }}</text>
                </view>
              </view>
              <view class="item-status">
                <view :class="['status-indicator', getStatusClass(item)]">
                  <uni-icons 
                    :type="getStatusIcon(item)" 
                    size="14" 
                    :color="getStatusColor(item)"
                  ></uni-icons>
                  <text class="status-text">{{ getStatusText(item) }}</text>
                </view>
              </view>
            </view>
            
            <!-- 项目值 -->
            <view class="item-value-section">
              <view class="value-display">
                <text :class="['value-number', { abnormal: item.isAbnormal }]">
                  {{ item.value }}
                </text>
                <text v-if="item.unit" class="value-unit">{{ item.unit }}</text>
              </view>
              <text v-if="item.referenceRange" class="reference-range">
                参考范围: {{ item.referenceRange }}
              </text>
            </view>
            
            <!-- 异常提示 -->
            <view v-if="item.isAbnormal" class="abnormal-hint">
              <view class="hint-content">
                <uni-icons type="info" size="14" color="#FF9800"></uni-icons>
                <text class="hint-text">{{ getAbnormalHint(item) }}</text>
              </view>
              <view v-if="getHealthAdvice(item)" class="health-advice">
                <text class="advice-text">{{ getHealthAdvice(item) }}</text>
              </view>
            </view>
            
            <!-- 趋势指示器 -->
            <view v-if="item.trend" class="trend-indicator">
              <uni-icons 
                :type="item.trend === 'up' ? 'arrow-up' : item.trend === 'down' ? 'arrow-down' : 'minus'"
                size="12"
                :color="getTrendColor(item.trend)"
              ></uni-icons>
              <text class="trend-text">{{ getTrendText(item.trend) }}</text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="filteredItems.length === 0" class="empty-state">
          <uni-icons type="info" size="32" color="#CCCCCC"></uni-icons>
          <text class="empty-text">暂无{{ getTabName(activeTab) }}项目</text>
        </view>
      </view>
      
      <!-- 健康建议 -->
      <view v-if="healthSuggestions.length > 0" class="suggestions-card">
        <view class="card-header">
          <text class="card-title">健康建议</text>
        </view>
        <view class="suggestions-list">
          <view 
            v-for="(suggestion, index) in healthSuggestions"
            :key="index"
            class="suggestion-item"
          >
            <view class="suggestion-icon">
              <uni-icons :type="suggestion.icon" size="16" :color="suggestion.color"></uni-icons>
            </view>
            <view class="suggestion-content">
              <text class="suggestion-title">{{ suggestion.title }}</text>
              <text class="suggestion-desc">{{ suggestion.description }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 备注信息 -->
      <view v-if="report.notes" class="notes-card">
        <view class="card-header">
          <text class="card-title">备注信息</text>
        </view>
        <text class="notes-content">{{ report.notes }}</text>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else class="error-state">
      <uni-icons type="close-circle" size="64" color="#CCCCCC"></uni-icons>
      <text class="error-title">报告不存在</text>
      <text class="error-desc">该报告可能已被删除或不存在</text>
      <button class="retry-btn" @tap="goBack">返回列表</button>
    </view>
    
    <!-- 底部操作栏 -->
    <view v-if="report" class="bottom-actions">
      <button class="action-btn secondary" @tap="shareReport">
        <uni-icons type="redo" size="18" color="#666"></uni-icons>
        <text class="btn-text">分享</text>
      </button>
      <button class="action-btn secondary" @tap="exportReport">
        <uni-icons type="download" size="18" color="#666"></uni-icons>
        <text class="btn-text">导出</text>
      </button>
      <button class="action-btn primary" @tap="editReport">
        <uni-icons type="compose" size="18" color="#FFFFFF"></uni-icons>
        <text class="btn-text">编辑</text>
      </button>
    </view>
    
    <!-- 项目详情弹窗 -->
    <uni-popup ref="itemDetailPopup" type="bottom">
      <view class="item-detail-modal">
        <view class="modal-header">
          <text class="modal-title">{{ selectedItem?.name }}</text>
          <button class="modal-close" @tap="hideItemDetail">
            <uni-icons type="close" size="20" color="#666"></uni-icons>
          </button>
        </view>
        
        <view v-if="selectedItem" class="modal-content">
          <!-- 详细信息 -->
          <view class="detail-section">
            <text class="section-title">检查结果</text>
            <view class="result-display">
              <text :class="['result-value', { abnormal: selectedItem.isAbnormal }]">
                {{ selectedItem.value }} {{ selectedItem.unit || '' }}
              </text>
              <text class="result-range">参考范围: {{ selectedItem.referenceRange || '未设置' }}</text>
            </view>
          </view>
          
          <!-- 异常分析 -->
          <view v-if="selectedItem.isAbnormal" class="analysis-section">
            <text class="section-title">异常分析</text>
            <view class="analysis-content">
              <text class="analysis-text">{{ getDetailedAnalysis(selectedItem) }}</text>
            </view>
          </view>
          
          <!-- 健康建议 -->
          <view class="advice-section">
            <text class="section-title">健康建议</text>
            <view class="advice-content">
              <text class="advice-text">{{ getDetailedAdvice(selectedItem) }}</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { useReportStore } from '@/stores/report.js'
import { Constants } from '@/types/index.js'

export default {
  name: 'ReportDetail',
  
  data() {
    return {
      // 报告数据
      report: null,
      reportId: '',
      
      // 状态
      loading: true,
      
      // 筛选标签
      activeTab: 'all',
      
      // 选中的项目
      selectedItem: null,
      
      // 筛选标签配置
      filterTabs: [
        { key: 'all', label: '全部', count: 0 },
        { key: 'abnormal', label: '异常', count: 0 },
        { key: 'normal', label: '正常', count: 0 },
        { key: 'critical', label: '严重', count: 0 }
      ]
    }
  },
  
  computed: {
    reportStore() {
      return useReportStore()
    },
    
    // 异常项目数量
    abnormalCount() {
      return this.report?.items.filter(item => item.isAbnormal).length || 0
    },
    
    // 正常项目数量
    normalCount() {
      return this.report?.items.filter(item => !item.isAbnormal).length || 0
    },
    
    // 严重异常项目
    criticalAbnormalItems() {
      return this.report?.items.filter(item => 
        item.isAbnormal && this.getAbnormalSeverity(item) === 'severe'
      ) || []
    },
    
    // 整体状态
    overallStatus() {
      if (!this.report) return { type: 'normal', icon: 'checkmarkempty', color: '#4CAF50', text: '正常' }
      
      const criticalCount = this.criticalAbnormalItems.length
      const abnormalCount = this.abnormalCount
      
      if (criticalCount > 0) {
        return { type: 'critical', icon: 'warning-filled', color: '#FF5722', text: '严重异常' }
      } else if (abnormalCount > 0) {
        return { type: 'abnormal', icon: 'info-filled', color: '#FF9800', text: '异常' }
      } else {
        return { type: 'normal', icon: 'checkmarkempty', color: '#4CAF50', text: '正常' }
      }
    },
    
    // 过滤后的项目
    filteredItems() {
      if (!this.report?.items) return []
      
      switch (this.activeTab) {
        case 'abnormal':
          return this.report.items.filter(item => item.isAbnormal)
        case 'normal':
          return this.report.items.filter(item => !item.isAbnormal)
        case 'critical':
          return this.report.items.filter(item => 
            item.isAbnormal && this.getAbnormalSeverity(item) === 'severe'
          )
        default:
          return this.report.items
      }
    },
    
    // 健康建议
    healthSuggestions() {
      if (!this.report) return []
      
      const suggestions = []
      const abnormalItems = this.report.items.filter(item => item.isAbnormal)
      
      if (abnormalItems.length > 0) {
        suggestions.push({
          icon: 'warning',
          color: '#FF9800',
          title: '定期复查',
          description: `建议在${this.getRecommendedFollowUpTime()}后复查异常指标`
        })
      }
      
      if (this.criticalAbnormalItems.length > 0) {
        suggestions.push({
          icon: 'help',
          color: '#FF5722',
          title: '及时就医',
          description: '存在严重异常指标，建议尽快咨询专科医生'
        })
      }
      
      // 根据具体异常项目添加建议
      this.addSpecificSuggestions(suggestions, abnormalItems)
      
      return suggestions
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.reportId = options.id
      this.loadReportDetail()
    } else {
      this.loading = false
    }
  },
  
  methods: {
    // 加载报告详情
    async loadReportDetail() {
      try {
        this.loading = true
        
        // 从store中获取报告
        const reports = this.reportStore.reports
        this.report = reports.find(r => r.id === this.reportId)
        
        if (!this.report) {
          throw new Error('报告不存在')
        }
        
        // 更新筛选标签计数
        this.updateTabCounts()
        
      } catch (error) {
        console.error('加载报告详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 更新标签计数
    updateTabCounts() {
      if (!this.report) return
      
      this.filterTabs.forEach(tab => {
        switch (tab.key) {
          case 'all':
            tab.count = this.report.items.length
            break
          case 'abnormal':
            tab.count = this.abnormalCount
            break
          case 'normal':
            tab.count = this.normalCount
            break
          case 'critical':
            tab.count = this.criticalAbnormalItems.length
            break
        }
      })
    },
    
    // 切换标签
    switchTab(tabKey) {
      this.activeTab = tabKey
    },
    
    // 获取标签名称
    getTabName(tabKey) {
      const tab = this.filterTabs.find(t => t.key === tabKey)
      return tab ? tab.label : ''
    },
    
    // 显示项目详情
    showItemDetail(item) {
      this.selectedItem = item
      this.$refs.itemDetailPopup.open()
    },
    
    // 隐藏项目详情
    hideItemDetail() {
      this.$refs.itemDetailPopup.close()
      this.selectedItem = null
    },
    
    // 获取项目严重程度样式类
    getItemSeverityClass(item) {
      if (!item.isAbnormal) return 'normal'
      
      const severity = this.getAbnormalSeverity(item)
      return severity
    },
    
    // 获取异常严重程度
    getAbnormalSeverity(item) {
      if (!item.isAbnormal) return 'normal'
      
      // 这里可以根据具体的医学标准来判断严重程度
      // 简化实现：根据偏离参考范围的程度
      if (item.numericValue && item.minReference && item.maxReference) {
        const value = item.numericValue
        const min = item.minReference
        const max = item.maxReference
        
        if (value < min) {
          const deviation = (min - value) / min
          if (deviation > 0.5) return 'severe'
          if (deviation > 0.2) return 'moderate'
          return 'mild'
        } else if (value > max) {
          const deviation = (value - max) / max
          if (deviation > 0.5) return 'severe'
          if (deviation > 0.2) return 'moderate'
          return 'mild'
        }
      }
      
      return 'mild'
    },
    
    // 获取状态样式类
    getStatusClass(item) {
      if (!item.isAbnormal) return 'normal'
      return this.getAbnormalSeverity(item)
    },
    
    // 获取状态图标
    getStatusIcon(item) {
      if (!item.isAbnormal) return 'checkmarkempty'
      
      const severity = this.getAbnormalSeverity(item)
      switch (severity) {
        case 'severe': return 'warning-filled'
        case 'moderate': return 'info-filled'
        default: return 'info'
      }
    },
    
    // 获取状态颜色
    getStatusColor(item) {
      if (!item.isAbnormal) return '#4CAF50'
      
      const severity = this.getAbnormalSeverity(item)
      switch (severity) {
        case 'severe': return '#FF5722'
        case 'moderate': return '#FF9800'
        default: return '#FFC107'
      }
    },
    
    // 获取状态文本
    getStatusText(item) {
      if (!item.isAbnormal) return '正常'
      
      const severity = this.getAbnormalSeverity(item)
      switch (severity) {
        case 'severe': return '严重异常'
        case 'moderate': return '中度异常'
        default: return '轻度异常'
      }
    },
    
    // 获取分类名称
    getCategoryName(category) {
      const categoryNames = {
        [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE]: '血常规',
        [Constants.REPORT_CATEGORIES.BIOCHEMISTRY]: '生化',
        [Constants.REPORT_CATEGORIES.IMMUNOLOGY]: '免疫',
        [Constants.REPORT_CATEGORIES.URINE_ROUTINE]: '尿常规',
        [Constants.REPORT_CATEGORIES.IMAGING]: '影像',
        [Constants.REPORT_CATEGORIES.OTHER]: '其他'
      }
      return categoryNames[category] || '未知'
    },
    
    // 获取异常提示
    getAbnormalHint(item) {
      const severity = this.getAbnormalSeverity(item)
      
      switch (severity) {
        case 'severe':
          return '该指标严重偏离正常范围，建议立即就医'
        case 'moderate':
          return '该指标中度异常，建议关注并咨询医生'
        default:
          return '该指标轻度异常，建议定期复查'
      }
    },
    
    // 获取健康建议
    getHealthAdvice(item) {
      // 根据具体项目给出建议
      const itemName = item.name.toLowerCase()
      
      if (itemName.includes('血糖') || itemName.includes('glucose')) {
        return '建议控制饮食，适量运动，定期监测血糖'
      } else if (itemName.includes('胆固醇') || itemName.includes('血脂')) {
        return '建议低脂饮食，增加运动，必要时药物治疗'
      } else if (itemName.includes('血压')) {
        return '建议低盐饮食，规律作息，适量运动'
      }
      
      return null
    },
    
    // 获取趋势颜色
    getTrendColor(trend) {
      switch (trend) {
        case 'up': return '#FF5722'
        case 'down': return '#4CAF50'
        default: return '#999999'
      }
    },
    
    // 获取趋势文本
    getTrendText(trend) {
      switch (trend) {
        case 'up': return '上升'
        case 'down': return '下降'
        default: return '稳定'
      }
    },
    
    // 获取详细分析
    getDetailedAnalysis(item) {
      if (!item.isAbnormal) return '该指标在正常范围内'
      
      const severity = this.getAbnormalSeverity(item)
      let analysis = `该指标${this.getStatusText(item)}。`
      
      if (item.numericValue && item.minReference && item.maxReference) {
        const value = item.numericValue
        const min = item.minReference
        const max = item.maxReference
        
        if (value < min) {
          const deviation = ((min - value) / min * 100).toFixed(1)
          analysis += `当前值比参考下限低${deviation}%。`
        } else if (value > max) {
          const deviation = ((value - max) / max * 100).toFixed(1)
          analysis += `当前值比参考上限高${deviation}%。`
        }
      }
      
      return analysis
    },
    
    // 获取详细建议
    getDetailedAdvice(item) {
      const baseAdvice = this.getHealthAdvice(item)
      if (baseAdvice) return baseAdvice
      
      const severity = this.getAbnormalSeverity(item)
      
      switch (severity) {
        case 'severe':
          return '建议立即就医，进行进一步检查和治疗。同时注意休息，避免剧烈运动。'
        case 'moderate':
          return '建议在1-2周内复查，并咨询专科医生。注意饮食调节和生活方式改善。'
        default:
          return '建议在1个月内复查，注意观察身体变化。保持健康的生活方式。'
      }
    },
    
    // 获取推荐复查时间
    getRecommendedFollowUpTime() {
      const criticalCount = this.criticalAbnormalItems.length
      const abnormalCount = this.abnormalCount
      
      if (criticalCount > 0) return '1-2周'
      if (abnormalCount > 3) return '2-4周'
      return '1-3个月'
    },
    
    // 添加特定建议
    addSpecificSuggestions(suggestions, abnormalItems) {
      const categories = new Set(abnormalItems.map(item => item.category))
      
      if (categories.has(Constants.REPORT_CATEGORIES.BLOOD_ROUTINE)) {
        suggestions.push({
          icon: 'heart',
          color: '#E91E63',
          title: '血液健康',
          description: '注意补充营养，保证充足睡眠，避免过度疲劳'
        })
      }
      
      if (categories.has(Constants.REPORT_CATEGORIES.BIOCHEMISTRY)) {
        suggestions.push({
          icon: 'nutrition',
          color: '#4CAF50',
          title: '代谢调节',
          description: '建议调整饮食结构，控制糖分和脂肪摄入'
        })
      }
    },
    
    // 预览图片
    previewImage() {
      if (!this.report.originalImage) return
      
      uni.previewImage({
        urls: [this.report.originalImage],
        current: this.report.originalImage
      })
    },
    
    // 保存图片
    async saveImage() {
      if (!this.report.originalImage) return
      
      try {
        const result = await uni.downloadFile({
          url: this.report.originalImage
        })
        
        await uni.saveImageToPhotosAlbum({
          filePath: result.tempFilePath
        })
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('保存图片失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    },
    
    // 处理图片错误
    handleImageError() {
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      })
    },
    
    // 分享报告
    shareReport() {
      const content = this.generateShareContent()
      
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        title: '健康检查报告',
        summary: content,
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        },
        fail: () => {
          // 降级到复制文本
          uni.setClipboardData({
            data: content,
            success: () => {
              uni.showToast({
                title: '内容已复制',
                icon: 'success'
              })
            }
          })
        }
      })
    },
    
    // 导出报告
    exportReport() {
      // 这里可以实现PDF导出功能
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    },
    
    // 编辑报告
    editReport() {
      uni.navigateTo({
        url: `/pages/report/add?reportId=${this.reportId}`
      })
    },
    
    // 生成分享内容
    generateShareContent() {
      let content = `【健康检查报告】\n`
      content += `医院：${this.report.hospital}\n`
      content += `日期：${this.formatDate(this.report.reportDate)}\n`
      content += `状态：${this.overallStatus.text}\n\n`
      
      if (this.abnormalCount > 0) {
        content += `【异常指标】\n`
        this.report.items.filter(item => item.isAbnormal).forEach(item => {
          content += `${item.name}：${item.value}${item.unit || ''} (${this.getStatusText(item)})\n`
        })
      }
      
      return content
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.report-detail-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.detail-content {
  padding: 20rpx;
}

// 报告头部
.report-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  color: #ffffff;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.hospital-info {
  flex: 1;
}

.hospital-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.report-date {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
}

.overall-status {
  flex-shrink: 0;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  
  &.critical {
    background: rgba(255, 87, 34, 0.2);
  }
  
  &.abnormal {
    background: rgba(255, 152, 0, 0.2);
  }
  
  &.normal {
    background: rgba(76, 175, 80, 0.2);
  }
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
}

.quick-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  
  &.abnormal .stat-number {
    color: #FFE082;
  }
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.9;
  display: block;
}

// 严重异常警告
.critical-alert {
  background: linear-gradient(135deg, #FF5722, #FF7043);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: #ffffff;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.alert-title {
  font-size: 28rpx;
  font-weight: 600;
}

.alert-desc {
  font-size: 24rpx;
  margin-bottom: 20rpx;
  display: block;
  opacity: 0.9;
}

.critical-items {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.critical-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
}

.item-name {
  font-size: 24rpx;
  font-weight: 500;
}

.item-value {
  font-size: 24rpx;
  font-weight: 600;
}

// 卡片样式
.info-card,
.image-card,
.items-card,
.suggestions-card,
.notes-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

// 基本信息
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  padding: 30rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 22rpx;
  color: #999999;
}

.info-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

// 图片卡片
.image-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.btn-text {
  color: #007AFF;
}

.image-container {
  padding: 30rpx;
}

.report-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}

// 筛选标签
.filter-tabs {
  display: flex;
  gap: 16rpx;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
  
  &.active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.tab-text {
  font-size: 24rpx;
  color: #333333;
  
  .active & {
    color: #1976d2;
  }
}

.tab-count {
  background: #007AFF;
  color: #ffffff;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 24rpx;
  text-align: center;
}

// 项目列表
.items-list {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.item-card {
  padding: 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
  background: #ffffff;
  
  &.normal {
    border-color: #e8f5e8;
    background: #fafffe;
  }
  
  &.mild {
    border-color: #fff3cd;
    background: #fffef5;
  }
  
  &.moderate {
    border-color: #ffeaa7;
    background: #fffbf0;
  }
  
  &.severe {
    border-color: #ffcdd2;
    background: #fff5f5;
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.item-name-section {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.item-category {
  display: inline-block;
}

.category-text {
  font-size: 20rpx;
  color: #007AFF;
  background: #e3f2fd;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.item-status {
  flex-shrink: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  
  &.normal {
    background: #e8f5e8;
  }
  
  &.mild {
    background: #fff3cd;
  }
  
  &.moderate {
    background: #ffeaa7;
  }
  
  &.severe {
    background: #ffcdd2;
  }
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
}

.item-value-section {
  margin-bottom: 16rpx;
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.value-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  
  &.abnormal {
    color: #FF5722;
  }
}

.value-unit {
  font-size: 24rpx;
  color: #666666;
}

.reference-range {
  font-size: 22rpx;
  color: #999999;
}

// 异常提示
.abnormal-hint {
  background: #fff3e0;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;
}

.hint-content {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.hint-text {
  font-size: 24rpx;
  color: #E65100;
  flex: 1;
}

.health-advice {
  padding-left: 26rpx;
}

.advice-text {
  font-size: 22rpx;
  color: #FF8F00;
  line-height: 1.5;
}

// 趋势指示器
.trend-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.trend-text {
  font-size: 20rpx;
  color: #666666;
}

// 健康建议
.suggestions-list {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-item {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.suggestion-icon {
  flex-shrink: 0;
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.suggestion-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

// 备注
.notes-content {
  padding: 30rpx;
  font-size: 26rpx;
  color: #333333;
  line-height: 1.6;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999999;
  margin-top: 20rpx;
}

// 错误状态
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
}

.error-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin: 30rpx 0 20rpx;
}

.error-desc {
  font-size: 26rpx;
  color: #999999;
  text-align: center;
  margin-bottom: 40rpx;
}

.retry-btn {
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  z-index: 999;
}

.bottom-actions .action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  
  &.primary {
    background: #007AFF;
    color: #ffffff;
  }
  
  &.secondary {
    background: #f8f9fa;
    color: #666666;
    border: 2rpx solid #e9ecef;
  }
}

// 项目详情弹窗
.item-detail-modal {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  background: none;
  border: none;
  padding: 0;
}

.modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section,
.analysis-section,
.advice-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.result-display {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.result-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
  
  &.abnormal {
    color: #FF5722;
  }
}

.result-range {
  font-size: 24rpx;
  color: #666666;
}

.analysis-content,
.advice-content {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.analysis-text,
.advice-text {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.6;
}
</style>