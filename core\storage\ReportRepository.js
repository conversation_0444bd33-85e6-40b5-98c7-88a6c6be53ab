/**
 * 报告数据访问层
 * 实现报告相关的数据库操作
 */

const { BaseRepository } = require('../base/BaseRepository.js')
const { IReportRepository } = require('../interfaces/IRepository.js')
const { Report } = require('../../models/Report.js')
const { Constants } = require('../../types/index.js')
const { logger } = require('../logger/Logger.js')

class ReportRepository extends BaseRepository {
  constructor(storageManager) {
    super(storageManager, Report)
  }
  
  /**
   * 获取表名
   * @returns {String} 表名
   */
  getTableName() {
    return 'reports'
  }
  
  /**
   * 根据用户ID查找报告
   * @param {String} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 报告列表
   */
  async findByUserId(userId, options = {}) {
    try {
      logger.debug('根据用户ID查找报告', { userId, options })
      
      const conditions = { userId }
      const queryOptions = {
        sort: { checkDate: -1 }, // 默认按检查日期倒序
        ...options
      }
      
      return await this.findBy(conditions, queryOptions)
    } catch (error) {
      throw this.handleError(error, 'findByUserId')
    }
  }
  
  /**
   * 根据时间范围查找报告
   * @param {String} userId - 用户ID
   * @param {Date} startDate - 开始时间
   * @param {Date} endDate - 结束时间
   * @returns {Promise<Array>} 报告列表
   */
  async findByDateRange(userId, startDate, endDate) {
    try {
      logger.debug('根据时间范围查找报告', { userId, startDate, endDate })
      
      const conditions = {
        userId,
        checkDate: {
          $gte: startDate,
          $lte: endDate
        }
      }
      
      return await this.findBy(conditions, { sort: { checkDate: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findByDateRange')
    }
  }
  
  /**
   * 根据分类查找报告
   * @param {String} userId - 用户ID
   * @param {Array} categories - 分类列表
   * @returns {Promise<Array>} 报告列表
   */
  async findByCategories(userId, categories) {
    try {
      logger.debug('根据分类查找报告', { userId, categories })
      
      const conditions = {
        userId,
        'items.category': { $in: categories }
      }
      
      return await this.findBy(conditions, { sort: { checkDate: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findByCategories')
    }
  }
  
  /**
   * 查找异常报告
   * @param {String} userId - 用户ID
   * @returns {Promise<Array>} 异常报告列表
   */
  async findAbnormalReports(userId) {
    try {
      logger.debug('查找异常报告', { userId })
      
      const conditions = {
        userId,
        'items.isAbnormal': true
      }
      
      return await this.findBy(conditions, { sort: { checkDate: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findAbnormalReports')
    }
  }
  
  /**
   * 更新同步状态
   * @param {String} reportId - 报告ID
   * @param {String} status - 同步状态
   * @returns {Promise<Boolean>} 更新是否成功
   */
  async updateSyncStatus(reportId, status) {
    try {
      logger.debug('更新同步状态', { reportId, status })
      
      const validStatuses = ['local', 'synced', 'pending', 'failed']
      if (!validStatuses.includes(status)) {
        throw new Error(`无效的同步状态: ${status}`)
      }
      
      await this.update(reportId, {
        syncStatus: status,
        lastSyncAt: new Date()
      })
      
      return true
    } catch (error) {
      throw this.handleError(error, 'updateSyncStatus')
    }
  }
  
  /**
   * 根据医院查找报告
   * @param {String} userId - 用户ID
   * @param {String} hospital - 医院名称
   * @returns {Promise<Array>} 报告列表
   */
  async findByHospital(userId, hospital) {
    try {
      logger.debug('根据医院查找报告', { userId, hospital })
      
      const conditions = {
        userId,
        hospital: { $regex: hospital, $options: 'i' } // 不区分大小写的模糊匹配
      }
      
      return await this.findBy(conditions, { sort: { checkDate: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findByHospital')
    }
  }
  
  /**
   * 根据医生查找报告
   * @param {String} userId - 用户ID
   * @param {String} doctor - 医生姓名
   * @returns {Promise<Array>} 报告列表
   */
  async findByDoctor(userId, doctor) {
    try {
      logger.debug('根据医生查找报告', { userId, doctor })
      
      const conditions = {
        userId,
        doctor: { $regex: doctor, $options: 'i' }
      }
      
      return await this.findBy(conditions, { sort: { checkDate: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findByDoctor')
    }
  }
  
  /**
   * 搜索报告
   * @param {String} userId - 用户ID
   * @param {String} keyword - 搜索关键词
   * @returns {Promise<Array>} 匹配的报告列表
   */
  async searchReports(userId, keyword) {
    try {
      logger.debug('搜索报告', { userId, keyword })
      
      const conditions = {
        userId,
        $or: [
          { title: { $regex: keyword, $options: 'i' } },
          { hospital: { $regex: keyword, $options: 'i' } },
          { doctor: { $regex: keyword, $options: 'i' } },
          { notes: { $regex: keyword, $options: 'i' } },
          { 'items.name': { $regex: keyword, $options: 'i' } }
        ]
      }
      
      return await this.findBy(conditions, { sort: { checkDate: -1 } })
    } catch (error) {
      throw this.handleError(error, 'searchReports')
    }
  }
  
  /**
   * 获取报告统计信息
   * @param {String} userId - 用户ID
   * @returns {Promise<Object>} 统计信息
   */
  async getReportStats(userId) {
    try {
      logger.debug('获取报告统计信息', { userId })
      
      const allReports = await this.findByUserId(userId)
      
      const stats = {
        totalReports: allReports.length,
        abnormalReports: 0,
        hospitalCount: new Set(),
        categoryCount: {},
        recentReports: 0,
        oldestReport: null,
        newestReport: null
      }
      
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      for (const report of allReports) {
        // 统计异常报告
        if (report.items.some(item => item.isAbnormal)) {
          stats.abnormalReports++
        }
        
        // 统计医院
        if (report.hospital) {
          stats.hospitalCount.add(report.hospital)
        }
        
        // 统计分类
        for (const item of report.items) {
          if (item.category) {
            stats.categoryCount[item.category] = (stats.categoryCount[item.category] || 0) + 1
          }
        }
        
        // 统计最近报告
        if (report.checkDate >= thirtyDaysAgo) {
          stats.recentReports++
        }
        
        // 找出最早和最新的报告
        if (!stats.oldestReport || report.checkDate < stats.oldestReport.checkDate) {
          stats.oldestReport = report
        }
        if (!stats.newestReport || report.checkDate > stats.newestReport.checkDate) {
          stats.newestReport = report
        }
      }
      
      stats.hospitalCount = stats.hospitalCount.size
      
      return stats
    } catch (error) {
      throw this.handleError(error, 'getReportStats')
    }
  }
  
  /**
   * 获取指定检查项目的历史数据
   * @param {String} userId - 用户ID
   * @param {String} itemName - 检查项目名称
   * @param {Number} limit - 限制数量
   * @returns {Promise<Array>} 历史数据
   */
  async getItemHistory(userId, itemName, limit = 10) {
    try {
      logger.debug('获取检查项目历史数据', { userId, itemName, limit })
      
      const reports = await this.findByUserId(userId, { limit: limit * 2 }) // 多取一些以防过滤后不够
      const history = []
      
      for (const report of reports) {
        const item = report.items.find(i => i.name === itemName)
        if (item) {
          history.push({
            reportId: report.id,
            checkDate: report.checkDate,
            hospital: report.hospital,
            value: item.value,
            unit: item.unit,
            referenceRange: item.referenceRange,
            isAbnormal: item.isAbnormal
          })
          
          if (history.length >= limit) {
            break
          }
        }
      }
      
      return history
    } catch (error) {
      throw this.handleError(error, 'getItemHistory')
    }
  }
  
  /**
   * 批量更新报告标签
   * @param {Array} reportIds - 报告ID列表
   * @param {Array} tags - 标签列表
   * @returns {Promise<Number>} 更新的报告数量
   */
  async batchUpdateTags(reportIds, tags) {
    try {
      logger.debug('批量更新报告标签', { reportIds, tags })
      
      let updatedCount = 0
      
      for (const reportId of reportIds) {
        try {
          await this.update(reportId, { tags })
          updatedCount++
        } catch (error) {
          logger.warn('更新报告标签失败', { reportId, error: error.message })
        }
      }
      
      return updatedCount
    } catch (error) {
      throw this.handleError(error, 'batchUpdateTags')
    }
  }
  
  /**
   * 获取需要同步的报告
   * @param {String} userId - 用户ID
   * @param {Number} limit - 限制数量
   * @returns {Promise<Array>} 需要同步的报告列表
   */
  async getPendingSyncReports(userId, limit = 50) {
    try {
      logger.debug('获取需要同步的报告', { userId, limit })
      
      const conditions = {
        userId,
        $or: [
          { syncStatus: 'local' },
          { syncStatus: 'pending' },
          { syncStatus: 'failed' }
        ]
      }
      
      return await this.findBy(conditions, {
        sort: { updatedAt: 1 }, // 按更新时间升序，优先同步较早的
        limit
      })
    } catch (error) {
      throw this.handleError(error, 'getPendingSyncReports')
    }
  }
  
  /**
   * 清理过期的临时报告
   * @param {Number} hoursToKeep - 保留小时数
   * @returns {Promise<Number>} 清理的报告数量
   */
  async cleanupTempReports(hoursToKeep = 24) {
    try {
      logger.debug('清理过期的临时报告', { hoursToKeep })
      
      const cutoffDate = new Date()
      cutoffDate.setHours(cutoffDate.getHours() - hoursToKeep)
      
      const conditions = {
        isTemporary: true,
        createdAt: { $lt: cutoffDate }
      }
      
      const tempReports = await this.findBy(conditions)
      let cleanedCount = 0
      
      for (const report of tempReports) {
        await this.delete(report.id)
        cleanedCount++
      }
      
      logger.info('清理临时报告完成', { cleanedCount })
      return cleanedCount
    } catch (error) {
      throw this.handleError(error, 'cleanupTempReports')
    }
  }
  
  /**
   * 导出用户的所有报告数据
   * @param {String} userId - 用户ID
   * @param {String} format - 导出格式
   * @returns {Promise<String>} 导出的数据
   */
  async exportUserReports(userId, format = 'json') {
    try {
      logger.debug('导出用户报告数据', { userId, format })
      
      const conditions = { userId }
      return await this.exportData(conditions, format)
    } catch (error) {
      throw this.handleError(error, 'exportUserReports')
    }
  }
}module.
exports = { ReportRepository }