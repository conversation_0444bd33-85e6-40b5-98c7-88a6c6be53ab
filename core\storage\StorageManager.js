/**
 * 存储管理器
 * 提供跨平台的数据存储功能
 */

import { Constants } from '../../types/index.js'
import { logger } from '../logger/Logger.js'

export class StorageManager {
  constructor(options = {}) {
    this.options = {
      prefix: options.prefix || 'health_app_',
      enableEncryption: options.enableEncryption || false,
      encryptionKey: options.encryptionKey || '',
      ...options
    }
    
    this.platform = this.detectPlatform()
    this.storage = this.initializeStorage()
    this.transactionStack = []
  }
  
  /**
   * 检测运行平台
   * @returns {String} 平台类型
   */
  detectPlatform() {
    // #ifdef APP-PLUS
    return 'app-plus'
    // #endif
    
    // #ifdef H5
    return 'h5'
    // #endif
    
    // #ifdef MP-WEIXIN
    return 'mp-weixin'
    // #endif
    
    // #ifdef MP-ALIPAY
    return 'mp-alipay'
    // #endif
    
    return 'unknown'
  }
  
  /**
   * 初始化存储
   * @returns {Object} 存储实例
   */
  initializeStorage() {
    switch (this.platform) {
      case 'app-plus':
        return this.initializeAppStorage()
      case 'h5':
        return this.initializeH5Storage()
      case 'mp-weixin':
      case 'mp-alipay':
        return this.initializeMPStorage()
      default:
        return this.initializeDefaultStorage()
    }
  }
  
  /**
   * 初始化APP存储
   * @returns {Object} 存储实例
   */
  initializeAppStorage() {
    // 使用SQLite数据库
    return {
      type: 'sqlite',
      db: null,
      
      async init() {
        // #ifdef APP-PLUS
        this.db = plus.sqlite.openDatabase({
          name: 'health_app.db',
          path: '_doc/health_app.db'
        })
        
        // 创建基础表
        await this.createTables()
        // #endif
      },
      
      async createTables() {
        const tables = [
          `CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            data TEXT,
            created_at INTEGER,
            updated_at INTEGER
          )`,
          `CREATE TABLE IF NOT EXISTS reports (
            id TEXT PRIMARY KEY,
            user_id TEXT,
            data TEXT,
            created_at INTEGER,
            updated_at INTEGER,
            INDEX(user_id)
          )`,
          `CREATE TABLE IF NOT EXISTS analysis (
            id TEXT PRIMARY KEY,
            user_id TEXT,
            type TEXT,
            data TEXT,
            created_at INTEGER,
            updated_at INTEGER,
            INDEX(user_id, type)
          )`
        ]
        
        for (const sql of tables) {
          await this.executeSql(sql)
        }
      },
      
      async executeSql(sql, params = []) {
        return new Promise((resolve, reject) => {
          // #ifdef APP-PLUS
          plus.sqlite.executeSql({
            name: 'health_app.db',
            sql,
            success: resolve,
            fail: reject
          })
          // #endif
        })
      }
    }
  }
  
  /**
   * 初始化H5存储
   * @returns {Object} 存储实例
   */
  initializeH5Storage() {
    return {
      type: 'indexeddb',
      db: null,
      
      async init() {
        return new Promise((resolve, reject) => {
          const request = indexedDB.open('health_app', 1)
          
          request.onerror = () => reject(request.error)
          request.onsuccess = () => {
            this.db = request.result
            resolve()
          }
          
          request.onupgradeneeded = (event) => {
            const db = event.target.result
            
            // 创建对象存储
            if (!db.objectStoreNames.contains('users')) {
              db.createObjectStore('users', { keyPath: 'id' })
            }
            
            if (!db.objectStoreNames.contains('reports')) {
              const reportStore = db.createObjectStore('reports', { keyPath: 'id' })
              reportStore.createIndex('user_id', 'user_id', { unique: false })
            }
            
            if (!db.objectStoreNames.contains('analysis')) {
              const analysisStore = db.createObjectStore('analysis', { keyPath: 'id' })
              analysisStore.createIndex('user_id', 'user_id', { unique: false })
              analysisStore.createIndex('type', 'type', { unique: false })
            }
          }
        })
      },
      
      async transaction(storeNames, mode = 'readonly') {
        return this.db.transaction(storeNames, mode)
      }
    }
  }
  
  /**
   * 初始化小程序存储
   * @returns {Object} 存储实例
   */
  initializeMPStorage() {
    return {
      type: 'mp-storage',
      
      async init() {
        // 小程序存储不需要初始化
      }
    }
  }
  
  /**
   * 初始化默认存储
   * @returns {Object} 存储实例
   */
  initializeDefaultStorage() {
    return {
      type: 'memory',
      data: new Map(),
      
      async init() {
        // 内存存储不需要初始化
      }
    }
  }
  
  /**
   * 初始化存储管理器
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this.storage.init()
      logger.info('存储管理器初始化成功', { platform: this.platform, type: this.storage.type })
    } catch (error) {
      logger.error('存储管理器初始化失败', { error: error.message, platform: this.platform })
      throw error
    }
  }
  
  /**
   * 插入数据
   * @param {String} tableName - 表名
   * @param {Object} data - 数据
   * @returns {Promise<Object>} 插入的数据
   */
  async insert(tableName, data) {
    try {
      const key = this.createKey(tableName, data.id)
      const processedData = this.processDataForStorage(data)
      
      switch (this.storage.type) {
        case 'sqlite':
          await this.insertToSQLite(tableName, processedData)
          break
        case 'indexeddb':
          await this.insertToIndexedDB(tableName, processedData)
          break
        case 'mp-storage':
          await this.insertToMPStorage(key, processedData)
          break
        case 'memory':
          this.storage.data.set(key, processedData)
          break
      }
      
      logger.debug('数据插入成功', { tableName, id: data.id })
      return data
    } catch (error) {
      logger.error('数据插入失败', { tableName, error: error.message })
      throw this.createStorageError('插入数据失败', error)
    }
  }
  
  /**
   * 根据ID查找数据
   * @param {String} tableName - 表名
   * @param {String} id - 数据ID
   * @returns {Promise<Object|null>} 找到的数据或null
   */
  async findById(tableName, id) {
    try {
      const key = this.createKey(tableName, id)
      let data = null
      
      switch (this.storage.type) {
        case 'sqlite':
          data = await this.findByIdFromSQLite(tableName, id)
          break
        case 'indexeddb':
          data = await this.findByIdFromIndexedDB(tableName, id)
          break
        case 'mp-storage':
          data = await this.findByIdFromMPStorage(key)
          break
        case 'memory':
          data = this.storage.data.get(key)
          break
      }
      
      return data ? this.processDataFromStorage(data) : null
    } catch (error) {
      logger.error('根据ID查找数据失败', { tableName, id, error: error.message })
      throw this.createStorageError('查找数据失败', error)
    }
  }
  
  /**
   * 查找所有数据
   * @param {String} tableName - 表名
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 数据列表
   */
  async findAll(tableName, options = {}) {
    try {
      let dataList = []
      
      switch (this.storage.type) {
        case 'sqlite':
          dataList = await this.findAllFromSQLite(tableName, options)
          break
        case 'indexeddb':
          dataList = await this.findAllFromIndexedDB(tableName, options)
          break
        case 'mp-storage':
          dataList = await this.findAllFromMPStorage(tableName, options)
          break
        case 'memory':
          dataList = this.findAllFromMemory(tableName, options)
          break
      }
      
      return dataList.map(data => this.processDataFromStorage(data))
    } catch (error) {
      logger.error('查找所有数据失败', { tableName, error: error.message })
      throw this.createStorageError('查找数据失败', error)
    }
  }
  
  /**
   * 根据条件查找数据
   * @param {String} tableName - 表名
   * @param {Object} conditions - 查询条件
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 匹配的数据列表
   */
  async findBy(tableName, conditions, options = {}) {
    try {
      const allData = await this.findAll(tableName, options)
      return this.filterData(allData, conditions)
    } catch (error) {
      logger.error('根据条件查找数据失败', { tableName, conditions, error: error.message })
      throw this.createStorageError('查找数据失败', error)
    }
  }
  
  /**
   * 更新数据
   * @param {String} tableName - 表名
   * @param {String} id - 数据ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新后的数据
   */
  async update(tableName, id, data) {
    try {
      const key = this.createKey(tableName, id)
      const processedData = this.processDataForStorage(data)
      
      switch (this.storage.type) {
        case 'sqlite':
          await this.updateInSQLite(tableName, id, processedData)
          break
        case 'indexeddb':
          await this.updateInIndexedDB(tableName, processedData)
          break
        case 'mp-storage':
          await this.updateInMPStorage(key, processedData)
          break
        case 'memory':
          this.storage.data.set(key, processedData)
          break
      }
      
      logger.debug('数据更新成功', { tableName, id })
      return data
    } catch (error) {
      logger.error('数据更新失败', { tableName, id, error: error.message })
      throw this.createStorageError('更新数据失败', error)
    }
  }
  
  /**
   * 删除数据
   * @param {String} tableName - 表名
   * @param {String} id - 数据ID
   * @returns {Promise<Boolean>} 删除是否成功
   */
  async delete(tableName, id) {
    try {
      const key = this.createKey(tableName, id)
      
      switch (this.storage.type) {
        case 'sqlite':
          await this.deleteFromSQLite(tableName, id)
          break
        case 'indexeddb':
          await this.deleteFromIndexedDB(tableName, id)
          break
        case 'mp-storage':
          await this.deleteFromMPStorage(key)
          break
        case 'memory':
          this.storage.data.delete(key)
          break
      }
      
      logger.debug('数据删除成功', { tableName, id })
      return true
    } catch (error) {
      logger.error('数据删除失败', { tableName, id, error: error.message })
      throw this.createStorageError('删除数据失败', error)
    }
  }
  
  /**
   * 统计数据数量
   * @param {String} tableName - 表名
   * @param {Object} conditions - 统计条件
   * @returns {Promise<Number>} 数据数量
   */
  async count(tableName, conditions = {}) {
    try {
      const data = await this.findBy(tableName, conditions)
      return data.length
    } catch (error) {
      logger.error('统计数据失败', { tableName, conditions, error: error.message })
      throw this.createStorageError('统计数据失败', error)
    }
  }
  
  /**
   * 开始事务
   * @returns {Promise<void>}
   */
  async beginTransaction() {
    this.transactionStack.push({
      operations: [],
      timestamp: Date.now()
    })
  }
  
  /**
   * 提交事务
   * @returns {Promise<void>}
   */
  async commitTransaction() {
    if (this.transactionStack.length === 0) {
      throw new Error('没有活动的事务')
    }
    
    const transaction = this.transactionStack.pop()
    // 事务操作已经在执行过程中完成，这里只是清理
    logger.debug('事务提交成功', { operationCount: transaction.operations.length })
  }
  
  /**
   * 回滚事务
   * @returns {Promise<void>}
   */
  async rollbackTransaction() {
    if (this.transactionStack.length === 0) {
      throw new Error('没有活动的事务')
    }
    
    const transaction = this.transactionStack.pop()
    // TODO: 实现事务回滚逻辑
    logger.warn('事务回滚', { operationCount: transaction.operations.length })
  }
  
  /**
   * 创建存储键
   * @param {String} tableName - 表名
   * @param {String} id - 数据ID
   * @returns {String} 存储键
   */
  createKey(tableName, id) {
    return `${this.options.prefix}${tableName}:${id}`
  }
  
  /**
   * 处理数据用于存储
   * @param {Object} data - 原始数据
   * @returns {Object} 处理后的数据
   */
  processDataForStorage(data) {
    const processed = { ...data }
    
    // 添加时间戳
    if (!processed.created_at) {
      processed.created_at = Date.now()
    }
    processed.updated_at = Date.now()
    
    // 加密敏感数据
    if (this.options.enableEncryption) {
      processed._encrypted = true
      // TODO: 实现数据加密
    }
    
    return processed
  }
  
  /**
   * 处理从存储读取的数据
   * @param {Object} data - 存储的数据
   * @returns {Object} 处理后的数据
   */
  processDataFromStorage(data) {
    if (!data) return null
    
    const processed = { ...data }
    
    // 解密数据
    if (processed._encrypted && this.options.enableEncryption) {
      // TODO: 实现数据解密
      delete processed._encrypted
    }
    
    return processed
  }
  
  /**
   * 过滤数据
   * @param {Array} dataList - 数据列表
   * @param {Object} conditions - 过滤条件
   * @returns {Array} 过滤后的数据
   */
  filterData(dataList, conditions) {
    return dataList.filter(item => {
      for (const [key, value] of Object.entries(conditions)) {
        if (typeof value === 'object' && value !== null) {
          // 处理复杂查询条件
          if (value.$eq !== undefined && item[key] !== value.$eq) return false
          if (value.$ne !== undefined && item[key] === value.$ne) return false
          if (value.$gt !== undefined && item[key] <= value.$gt) return false
          if (value.$gte !== undefined && item[key] < value.$gte) return false
          if (value.$lt !== undefined && item[key] >= value.$lt) return false
          if (value.$lte !== undefined && item[key] > value.$lte) return false
          if (value.$in !== undefined && !value.$in.includes(item[key])) return false
          if (value.$nin !== undefined && value.$in.includes(item[key])) return false
        } else {
          // 简单相等比较
          if (item[key] !== value) return false
        }
      }
      return true
    })
  }
  
  /**
   * 创建存储错误
   * @param {String} message - 错误消息
   * @param {Error} originalError - 原始错误
   * @returns {Error} 存储错误
   */
  createStorageError(message, originalError) {
    const error = new Error(message)
    error.name = 'StorageError'
    error.type = Constants.ERROR_TYPES.STORAGE_ERROR
    error.originalError = originalError
    return error
  }
  
  // SQLite相关方法
  async insertToSQLite(tableName, data) {
    const sql = `INSERT INTO ${tableName} (id, data, created_at, updated_at) VALUES (?, ?, ?, ?)`
    const params = [data.id, JSON.stringify(data), data.created_at, data.updated_at]
    await this.storage.executeSql(sql, params)
  }
  
  async findByIdFromSQLite(tableName, id) {
    const sql = `SELECT data FROM ${tableName} WHERE id = ?`
    const result = await this.storage.executeSql(sql, [id])
    return result.rows.length > 0 ? JSON.parse(result.rows[0].data) : null
  }
  
  async findAllFromSQLite(tableName, options) {
    let sql = `SELECT data FROM ${tableName}`
    const params = []
    
    if (options.orderBy) {
      sql += ` ORDER BY ${options.orderBy}`
      if (options.order === 'DESC') {
        sql += ' DESC'
      }
    }
    
    if (options.limit) {
      sql += ' LIMIT ?'
      params.push(options.limit)
    }
    
    const result = await this.storage.executeSql(sql, params)
    return Array.from(result.rows).map(row => JSON.parse(row.data))
  }
  
  async updateInSQLite(tableName, id, data) {
    const sql = `UPDATE ${tableName} SET data = ?, updated_at = ? WHERE id = ?`
    const params = [JSON.stringify(data), data.updated_at, id]
    await this.storage.executeSql(sql, params)
  }
  
  async deleteFromSQLite(tableName, id) {
    const sql = `DELETE FROM ${tableName} WHERE id = ?`
    await this.storage.executeSql(sql, [id])
  }
  
  // IndexedDB相关方法
  async insertToIndexedDB(tableName, data) {
    const transaction = this.storage.db.transaction([tableName], 'readwrite')
    const store = transaction.objectStore(tableName)
    await store.add(data)
  }
  
  async findByIdFromIndexedDB(tableName, id) {
    const transaction = this.storage.db.transaction([tableName], 'readonly')
    const store = transaction.objectStore(tableName)
    const result = await store.get(id)
    return result
  }
  
  async findAllFromIndexedDB(tableName, options) {
    const transaction = this.storage.db.transaction([tableName], 'readonly')
    const store = transaction.objectStore(tableName)
    const result = await store.getAll()
    return result
  }
  
  async updateInIndexedDB(tableName, data) {
    const transaction = this.storage.db.transaction([tableName], 'readwrite')
    const store = transaction.objectStore(tableName)
    await store.put(data)
  }
  
  async deleteFromIndexedDB(tableName, id) {
    const transaction = this.storage.db.transaction([tableName], 'readwrite')
    const store = transaction.objectStore(tableName)
    await store.delete(id)
  }
  
  // 小程序存储相关方法
  async insertToMPStorage(key, data) {
    uni.setStorageSync(key, data)
  }
  
  async findByIdFromMPStorage(key) {
    try {
      return uni.getStorageSync(key)
    } catch (error) {
      return null
    }
  }
  
  async findAllFromMPStorage(tableName, options) {
    const keys = uni.getStorageInfoSync().keys
    const tableKeys = keys.filter(key => key.startsWith(`${this.options.prefix}${tableName}:`))
    
    const dataList = []
    for (const key of tableKeys) {
      try {
        const data = uni.getStorageSync(key)
        if (data) {
          dataList.push(data)
        }
      } catch (error) {
        logger.warn('读取存储数据失败', { key, error: error.message })
      }
    }
    
    return dataList
  }
  
  async updateInMPStorage(key, data) {
    uni.setStorageSync(key, data)
  }
  
  async deleteFromMPStorage(key) {
    uni.removeStorageSync(key)
  }
  
  // 内存存储相关方法
  findAllFromMemory(tableName, options) {
    const prefix = `${this.options.prefix}${tableName}:`
    const dataList = []
    
    for (const [key, data] of this.storage.data.entries()) {
      if (key.startsWith(prefix)) {
        dataList.push(data)
      }
    }
    
    return dataList
  }
}