/**
 * 存储工具主入口文件
 * 统一导出数据库、加密和仓储功能
 */

import { getDatabase } from './database.js';
import { getEncryption } from './encryption.js';
import {
  userRepository,
  healthReportRepository,
  healthIndicatorRepository,
  syncRecordRepository,
  BaseRepository,
  UserRepository,
  HealthReportRepository,
  HealthIndicatorRepository,
  SyncRecordRepository
} from './repository.js';

// 数据库初始化函数
export async function initializeStorage() {
  try {
    const db = getDatabase();
    await db.initialize();
    console.log('存储系统初始化成功');
    return true;
  } catch (error) {
    console.error('存储系统初始化失败:', error);
    throw error;
  }
}

// 导出数据库实例
export { getDatabase };

// 导出加密工具
export { getEncryption };

// 导出仓储实例
export {
  userRepository,
  healthReportRepository,
  healthIndicatorRepository,
  syncRecordRepository
};

// 导出仓储类
export {
  BaseRepository,
  UserRepository,
  HealthReportRepository,
  HealthIndicatorRepository,
  SyncRecordRepository
};

// 导出表结构定义
export * from './schema.js';

// 便捷的存储操作接口
export const storage = {
  // 初始化
  async init() {
    return await initializeStorage();
  },

  // 用户操作
  users: userRepository,

  // 健康报告操作
  reports: healthReportRepository,

  // 健康指标操作
  indicators: healthIndicatorRepository,

  // 同步记录操作
  sync: syncRecordRepository,

  // 数据库直接操作
  db: getDatabase(),

  // 加密工具
  encryption: getEncryption(),

  // 获取所有表的统计信息
  async getStats() {
    const db = getDatabase();
    const tables = ['users', 'health_reports', 'health_indicators', 'sync_records'];
    const stats = {};
    
    for (const tableName of tables) {
      stats[tableName] = db.getTableStats(tableName);
    }
    
    return stats;
  },

  // 清空所有数据（谨慎使用）
  async clearAll() {
    const db = getDatabase();
    const tables = ['users', 'health_reports', 'health_indicators', 'sync_records'];
    
    for (const tableName of tables) {
      await db.truncate(tableName);
    }
    
    console.log('所有数据已清空');
  }
};