/**
 * 趋势分析综合服务
 * 整合趋势分析和图表生成功能
 * 实现需求 4.1 和 4.2
 */

import trendAnalysisService from './trendAnalysis.js'
import chartService from './chartService.js'

/**
 * 趋势服务类
 */
class TrendService {
  /**
   * 获取单个指标的趋势分析和图表
   * @param {string} indicatorName - 指标名称
   * @param {Array} reportData - 报告数据
   * @param {Object} options - 选项
   * @returns {Object} 趋势分析结果和图表配置
   */
  async getIndicatorTrend(indicatorName, reportData, options = {}) {
    try {
      // 提取指标数据点
      const dataPoints = this.extractIndicatorData(indicatorName, reportData)
      
      if (dataPoints.length === 0) {
        return {
          success: false,
          message: '未找到该指标的数据',
          data: null
        }
      }

      // 进行趋势分析
      const trendAnalysis = trendAnalysisService.analyzeTrend(dataPoints, options)
      
      // 生成图表配置
      const chartConfig = chartService.generateTrendLineChart(trendAnalysis, {
        title: indicatorName,
        unit: this.getIndicatorUnit(indicatorName),
        referenceRange: this.getIndicatorReferenceRange(indicatorName, reportData),
        showAbnormal: true,
        theme: options.theme || 'light'
      })

      return {
        success: true,
        data: {
          indicator: indicatorName,
          analysis: trendAnalysis,
          chart: chartConfig,
          dataPoints: dataPoints,
          recommendations: this.generateRecommendations(trendAnalysis, indicatorName)
        }
      }
    } catch (error) {
      console.error('获取指标趋势失败:', error)
      return {
        success: false,
        message: '趋势分析失败',
        error: error.message
      }
    }
  }

  /**
   * 获取多个指标的趋势对比
   * @param {Array} indicatorNames - 指标名称数组
   * @param {Array} reportData - 报告数据
   * @param {Object} options - 选项
   * @returns {Object} 多指标趋势分析结果
   */
  async getMultiIndicatorTrends(indicatorNames, reportData, options = {}) {
    try {
      const indicatorsData = {}
      
      // 提取每个指标的数据
      indicatorNames.forEach(indicatorName => {
        const dataPoints = this.extractIndicatorData(indicatorName, reportData)
        if (dataPoints.length > 0) {
          indicatorsData[indicatorName] = dataPoints
        }
      })

      if (Object.keys(indicatorsData).length === 0) {
        return {
          success: false,
          message: '未找到任何指标数据',
          data: null
        }
      }

      // 进行多指标趋势分析
      const multiTrendAnalysis = trendAnalysisService.analyzeMultipleIndicators(indicatorsData, options)
      
      // 生成对比图表
      const chartConfig = chartService.generateMultiIndicatorChart(multiTrendAnalysis, {
        title: '多指标趋势对比',
        theme: options.theme || 'light',
        showLegend: true
      })

      return {
        success: true,
        data: {
          indicators: Object.keys(indicatorsData),
          analysis: multiTrendAnalysis,
          chart: chartConfig,
          correlations: multiTrendAnalysis.correlations,
          recommendations: this.generateMultiIndicatorRecommendations(multiTrendAnalysis)
        }
      }
    } catch (error) {
      console.error('获取多指标趋势失败:', error)
      return {
        success: false,
        message: '多指标趋势分析失败',
        error: error.message
      }
    }
  }

  /**
   * 获取指标分布统计
   * @param {string} indicatorName - 指标名称
   * @param {Array} reportData - 报告数据
   * @returns {Object} 分布统计结果
   */
  async getIndicatorDistribution(indicatorName, reportData) {
    try {
      const dataPoints = this.extractIndicatorData(indicatorName, reportData)
      
      if (dataPoints.length === 0) {
        return {
          success: false,
          message: '未找到该指标的数据',
          data: null
        }
      }

      // 计算分布统计
      const distribution = this.calculateDistribution(dataPoints, indicatorName)
      
      // 生成饼图配置
      const chartConfig = chartService.generateDistributionPieChart(distribution.categories, {
        title: `${indicatorName} 分布统计`
      })

      return {
        success: true,
        data: {
          indicator: indicatorName,
          distribution: distribution,
          chart: chartConfig,
          statistics: this.calculateStatistics(dataPoints)
        }
      }
    } catch (error) {
      console.error('获取指标分布失败:', error)
      return {
        success: false,
        message: '分布统计失败',
        error: error.message
      }
    }
  }

  /**
   * 从报告数据中提取指标数据点
   * @param {string} indicatorName - 指标名称
   * @param {Array} reportData - 报告数据
   * @returns {Array} 数据点数组
   */
  extractIndicatorData(indicatorName, reportData) {
    const dataPoints = []
    
    reportData.forEach(report => {
      if (report.items && Array.isArray(report.items)) {
        const item = report.items.find(item => 
          item.name === indicatorName || 
          item.name.includes(indicatorName) ||
          indicatorName.includes(item.name)
        )
        
        if (item && item.value) {
          // 尝试解析数值
          const numericValue = this.parseNumericValue(item.value)
          if (numericValue !== null) {
            dataPoints.push({
              date: report.checkDate || report.reportDate,
              value: numericValue,
              originalValue: item.value,
              unit: item.unit || '',
              referenceRange: item.referenceRange || '',
              isAbnormal: item.isAbnormal || false,
              reportId: report.id
            })
          }
        }
      }
    })
    
    return dataPoints.sort((a, b) => new Date(a.date) - new Date(b.date))
  }

  /**
   * 解析数值
   * @param {string} value - 原始值
   * @returns {number|null} 解析后的数值
   */
  parseNumericValue(value) {
    if (typeof value === 'number') return value
    if (typeof value !== 'string') return null
    
    // 移除非数字字符，保留小数点和负号
    const cleanValue = value.replace(/[^\d.-]/g, '')
    const numericValue = parseFloat(cleanValue)
    
    return isNaN(numericValue) ? null : numericValue
  }

  /**
   * 获取指标单位
   * @param {string} indicatorName - 指标名称
   * @returns {string} 单位
   */
  getIndicatorUnit(indicatorName) {
    const unitMap = {
      '血糖': 'mmol/L',
      '血压': 'mmHg',
      '胆固醇': 'mmol/L',
      '甘油三酯': 'mmol/L',
      '白细胞': '×10⁹/L',
      '红细胞': '×10¹²/L',
      '血红蛋白': 'g/L',
      '血小板': '×10⁹/L',
      '尿酸': 'μmol/L',
      '肌酐': 'μmol/L',
      '尿素氮': 'mmol/L'
    }
    
    for (const [key, unit] of Object.entries(unitMap)) {
      if (indicatorName.includes(key)) {
        return unit
      }
    }
    
    return ''
  }

  /**
   * 获取指标参考范围
   * @param {string} indicatorName - 指标名称
   * @param {Array} reportData - 报告数据
   * @returns {Object|null} 参考范围
   */
  getIndicatorReferenceRange(indicatorName, reportData) {
    // 从最新的报告中获取参考范围
    for (let i = reportData.length - 1; i >= 0; i--) {
      const report = reportData[i]
      if (report.items) {
        const item = report.items.find(item => item.name === indicatorName)
        if (item && item.referenceRange) {
          return this.parseReferenceRange(item.referenceRange)
        }
      }
    }
    
    return null
  }

  /**
   * 解析参考范围
   * @param {string} rangeStr - 参考范围字符串
   * @returns {Object|null} 解析后的范围对象
   */
  parseReferenceRange(rangeStr) {
    if (!rangeStr) return null
    
    // 匹配各种格式的参考范围
    const patterns = [
      /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/, // 3.9-6.1
      /(\d+\.?\d*)\s*-\s*(\d+\.?\d*)/, // 3.9-6.1
      /<\s*(\d+\.?\d*)/, // <6.1
      />\s*(\d+\.?\d*)/, // >3.9
      /≤\s*(\d+\.?\d*)/, // ≤6.1
      /≥\s*(\d+\.?\d*)/ // ≥3.9
    ]
    
    for (const pattern of patterns) {
      const match = rangeStr.match(pattern)
      if (match) {
        if (match.length === 3) {
          // 范围格式
          return {
            min: parseFloat(match[1]),
            max: parseFloat(match[2])
          }
        } else if (match.length === 2) {
          // 单边界格式
          if (rangeStr.includes('<') || rangeStr.includes('≤')) {
            return { max: parseFloat(match[1]) }
          } else if (rangeStr.includes('>') || rangeStr.includes('≥')) {
            return { min: parseFloat(match[1]) }
          }
        }
      }
    }
    
    return null
  }

  /**
   * 计算分布统计
   * @param {Array} dataPoints - 数据点
   * @param {string} indicatorName - 指标名称
   * @returns {Object} 分布统计
   */
  calculateDistribution(dataPoints, indicatorName) {
    const categories = {
      '正常': 0,
      '偏高': 0,
      '偏低': 0,
      '异常': 0
    }
    
    dataPoints.forEach(point => {
      if (point.isAbnormal) {
        categories['异常']++
      } else {
        categories['正常']++
      }
    })
    
    return {
      categories,
      total: dataPoints.length,
      abnormalRate: (categories['异常'] / dataPoints.length * 100).toFixed(1)
    }
  }

  /**
   * 计算基础统计信息
   * @param {Array} dataPoints - 数据点
   * @returns {Object} 统计信息
   */
  calculateStatistics(dataPoints) {
    const values = dataPoints.map(point => parseFloat(point.value)).filter(v => !isNaN(v))
    
    if (values.length === 0) {
      return {
        count: 0,
        mean: 0,
        median: 0,
        min: 0,
        max: 0,
        std: 0
      }
    }
    
    const sorted = values.sort((a, b) => a - b)
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const median = sorted.length % 2 === 0
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)]
    
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    const std = Math.sqrt(variance)
    
    return {
      count: values.length,
      mean: parseFloat(mean.toFixed(2)),
      median: parseFloat(median.toFixed(2)),
      min: Math.min(...values),
      max: Math.max(...values),
      std: parseFloat(std.toFixed(2))
    }
  }

  /**
   * 生成单指标建议
   * @param {Object} trendAnalysis - 趋势分析结果
   * @param {string} indicatorName - 指标名称
   * @returns {Array} 建议数组
   */
  generateRecommendations(trendAnalysis, indicatorName) {
    const recommendations = []
    const { trend, changeRate } = trendAnalysis
    
    switch (trend) {
      case 'increasing':
        if (Math.abs(changeRate) > 20) {
          recommendations.push(`${indicatorName}呈明显上升趋势，建议咨询医生`)
        } else {
          recommendations.push(`${indicatorName}轻微上升，建议持续关注`)
        }
        break
        
      case 'decreasing':
        if (Math.abs(changeRate) > 20) {
          recommendations.push(`${indicatorName}呈明显下降趋势，建议咨询医生`)
        } else {
          recommendations.push(`${indicatorName}轻微下降，建议持续关注`)
        }
        break
        
      case 'stable':
        recommendations.push(`${indicatorName}保持稳定，继续保持良好的生活习惯`)
        break
        
      case 'fluctuating':
        recommendations.push(`${indicatorName}波动较大，建议规律检查并咨询医生`)
        break
        
      default:
        recommendations.push('建议定期检查，积累更多数据进行分析')
    }
    
    return recommendations
  }

  /**
   * 生成多指标建议
   * @param {Object} multiTrendAnalysis - 多指标趋势分析结果
   * @returns {Array} 建议数组
   */
  generateMultiIndicatorRecommendations(multiTrendAnalysis) {
    const recommendations = []
    const { indicators, correlations } = multiTrendAnalysis
    
    // 分析整体趋势
    const trendCounts = {
      increasing: 0,
      decreasing: 0,
      stable: 0,
      fluctuating: 0
    }
    
    Object.values(indicators).forEach(indicator => {
      if (trendCounts.hasOwnProperty(indicator.trend)) {
        trendCounts[indicator.trend]++
      }
    })
    
    if (trendCounts.increasing > trendCounts.stable) {
      recommendations.push('多项指标呈上升趋势，建议全面体检并咨询医生')
    }
    
    if (trendCounts.fluctuating > 2) {
      recommendations.push('多项指标波动较大，建议规律作息和饮食')
    }
    
    // 分析相关性
    const strongCorrelations = Object.entries(correlations)
      .filter(([key, value]) => Math.abs(value) > 0.7)
    
    if (strongCorrelations.length > 0) {
      recommendations.push('发现指标间存在强相关性，建议重点关注相关指标的变化')
    }
    
    return recommendations
  }
}

// 创建单例实例
const trendService = new TrendService()

export default trendService