/**
 * 会话管理服务
 * 处理用户会话的创建、维护、销毁等功能
 */

import tokenService from './tokenService.js'
import { useUserStore } from '../../stores/user.js'

class SessionService {
  constructor() {
    this.sessionCheckInterval = null
    this.isInitialized = false
  }

  /**
   * 初始化会话服务
   */
  async init() {
    if (this.isInitialized) return

    try {
      console.log('初始化会话服务...')
      
      // 初始化token服务
      await tokenService.init()
      
      // 恢复用户会话
      await this.restoreSession()
      
      // 开始会话检查
      this.startSessionCheck()
      
      // 监听token过期事件
      this.setupEventListeners()
      
      this.isInitialized = true
      console.log('会话服务初始化完成')
    } catch (error) {
      console.error('会话服务初始化失败:', error)
    }
  }

  /**
   * 创建新会话
   * @param {Object} loginData 登录数据
   */
  async createSession(loginData) {
    try {
      const { token, refreshToken, tokenExpiry, userInfo } = loginData
      
      // 保存token信息
      tokenService.saveTokens({ token, refreshToken, tokenExpiry })
      
      // 保存用户信息
      const userStore = useUserStore()
      userStore.setUserInfo(userInfo)
      userStore.setAuth({
        isLoggedIn: true,
        token,
        refreshToken,
        tokenExpiry
      })
      
      // 保存会话信息
      this.saveSessionInfo({
        userId: userInfo.id,
        loginTime: Date.now(),
        lastActiveTime: Date.now(),
        deviceInfo: await this.getDeviceInfo()
      })
      
      console.log('会话创建成功')
      
      return { success: true }
    } catch (error) {
      console.error('创建会话失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 恢复会话
   */
  async restoreSession() {
    try {
      const token = tokenService.getToken()
      const userInfo = uni.getStorageSync('user_info')
      const sessionInfo = uni.getStorageSync('session_info')
      
      if (!token || !userInfo) {
        console.log('没有找到有效的会话信息')
        return { success: false }
      }
      
      // 检查token有效性
      if (!tokenService.isTokenValid()) {
        console.log('Token已过期，尝试刷新...')
        const refreshResult = await tokenService.refreshToken()
        if (!refreshResult.success) {
          console.log('Token刷新失败，清除会话')
          await this.destroySession()
          return { success: false }
        }
      }
      
      // 恢复用户状态
      const userStore = useUserStore()
      userStore.setUserInfo(userInfo)
      userStore.setAuth({
        isLoggedIn: true,
        token: tokenService.getToken(),
        refreshToken: tokenService.getRefreshToken(),
        tokenExpiry: tokenService.getTokenExpiry()
      })
      
      // 更新最后活跃时间
      if (sessionInfo) {
        this.updateLastActiveTime()
      }
      
      console.log('会话恢复成功')
      return { success: true }
    } catch (error) {
      console.error('恢复会话失败:', error)
      await this.destroySession()
      return { success: false, message: error.message }
    }
  }

  /**
   * 销毁会话
   */
  async destroySession() {
    try {
      console.log('销毁会话...')
      
      // 清除token信息
      tokenService.clearTokens()
      
      // 清除用户状态
      const userStore = useUserStore()
      await userStore.logout()
      
      // 清除会话信息
      uni.removeStorageSync('session_info')
      uni.removeStorageSync('last_login_phone')
      
      // 停止会话检查
      this.stopSessionCheck()
      
      console.log('会话销毁完成')
      
      return { success: true }
    } catch (error) {
      console.error('销毁会话失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 检查会话有效性
   */
  async checkSession() {
    try {
      const userStore = useUserStore()
      
      if (!userStore.isAuthenticated) {
        return { valid: false, reason: '用户未登录' }
      }
      
      if (!tokenService.isTokenValid()) {
        console.log('Token无效，尝试刷新...')
        const refreshResult = await tokenService.refreshToken()
        
        if (!refreshResult.success) {
          await this.destroySession()
          return { valid: false, reason: 'Token刷新失败' }
        }
        
        // 更新用户状态中的token
        userStore.setAuth({
          token: refreshResult.token,
          tokenExpiry: tokenService.getTokenExpiry()
        })
      }
      
      // 更新最后活跃时间
      this.updateLastActiveTime()
      
      return { valid: true }
    } catch (error) {
      console.error('会话检查失败:', error)
      return { valid: false, reason: error.message }
    }
  }

  /**
   * 开始会话检查
   */
  startSessionCheck() {
    // 每5分钟检查一次会话
    this.sessionCheckInterval = setInterval(async () => {
      const result = await this.checkSession()
      if (!result.valid) {
        console.log('会话检查失败:', result.reason)
      }
    }, 5 * 60 * 1000)
  }

  /**
   * 停止会话检查
   */
  stopSessionCheck() {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval)
      this.sessionCheckInterval = null
    }
  }

  /**
   * 保存会话信息
   * @param {Object} sessionInfo 会话信息
   */
  saveSessionInfo(sessionInfo) {
    uni.setStorageSync('session_info', {
      ...sessionInfo,
      version: '1.0'
    })
  }

  /**
   * 更新最后活跃时间
   */
  updateLastActiveTime() {
    const sessionInfo = uni.getStorageSync('session_info')
    if (sessionInfo) {
      sessionInfo.lastActiveTime = Date.now()
      uni.setStorageSync('session_info', sessionInfo)
    }
  }

  /**
   * 获取设备信息
   */
  async getDeviceInfo() {
    return new Promise((resolve) => {
      uni.getSystemInfo({
        success: (res) => {
          resolve({
            platform: res.platform,
            system: res.system,
            model: res.model,
            brand: res.brand,
            screenWidth: res.screenWidth,
            screenHeight: res.screenHeight,
            version: res.version
          })
        },
        fail: () => {
          resolve({
            platform: 'unknown',
            system: 'unknown'
          })
        }
      })
    })
  }

  /**
   * 获取会话信息
   */
  getSessionInfo() {
    return uni.getStorageSync('session_info') || null
  }

  /**
   * 检查会话是否过期
   */
  isSessionExpired() {
    const sessionInfo = this.getSessionInfo()
    if (!sessionInfo) return true
    
    // 7天未活跃则认为会话过期
    const maxInactiveTime = 7 * 24 * 60 * 60 * 1000
    return Date.now() - sessionInfo.lastActiveTime > maxInactiveTime
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听token过期事件
    uni.$on('token-expired', async () => {
      console.log('收到token过期事件')
      await this.destroySession()
    })
    
    // 监听应用进入前台
    uni.onAppShow(() => {
      this.updateLastActiveTime()
      // 检查会话是否过期
      if (this.isSessionExpired()) {
        this.destroySession()
      }
    })
    
    // 监听应用进入后台
    uni.onAppHide(() => {
      this.updateLastActiveTime()
    })
  }

  /**
   * 延长会话
   */
  async extendSession() {
    try {
      // 如果token即将过期，刷新它
      if (tokenService.needsRefresh()) {
        await tokenService.refreshToken()
      }
      
      // 更新最后活跃时间
      this.updateLastActiveTime()
      
      return { success: true }
    } catch (error) {
      console.error('延长会话失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取会话状态
   */
  getSessionStatus() {
    const userStore = useUserStore()
    const sessionInfo = this.getSessionInfo()
    
    return {
      isLoggedIn: userStore.isAuthenticated,
      tokenValid: tokenService.isTokenValid(),
      sessionInfo: sessionInfo,
      lastActiveTime: sessionInfo?.lastActiveTime || null,
      loginTime: sessionInfo?.loginTime || null
    }
  }

  /**
   * 清理过期数据
   */
  cleanup() {
    try {
      // 清理过期的临时数据
      const keys = ['temp_login_data', 'temp_register_data', 'temp_reset_data']
      keys.forEach(key => {
        const data = uni.getStorageSync(key)
        if (data && data.expiry && Date.now() > data.expiry) {
          uni.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('清理过期数据失败:', error)
    }
  }
}

export default new SessionService()