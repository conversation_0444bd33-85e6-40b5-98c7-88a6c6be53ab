/**
 * 健康数据分析服务
 * 提供健康指标分析、异常检测、趋势分析等功能
 */

class HealthAnalyticsService {
  constructor() {
    // 正常值参考范围配置
    this.referenceRanges = {
      '血压': {
        '收缩压': { min: 90, max: 140, unit: 'mmHg' },
        '舒张压': { min: 60, max: 90, unit: 'mmHg' }
      },
      '血糖': {
        '空腹血糖': { min: 3.9, max: 6.1, unit: 'mmol/L' },
        '餐后血糖': { min: 3.9, max: 7.8, unit: 'mmol/L' }
      },
      '血脂': {
        '总胆固醇': { min: 2.8, max: 5.2, unit: 'mmol/L' },
        '甘油三酯': { min: 0.45, max: 1.7, unit: 'mmol/L' },
        '高密度脂蛋白': { min: 1.0, max: 999, unit: 'mmol/L' },
        '低密度脂蛋白': { min: 0, max: 3.4, unit: 'mmol/L' }
      },
      '肝功能': {
        'ALT': { min: 5, max: 40, unit: 'U/L' },
        'AST': { min: 8, max: 40, unit: 'U/L' }
      },
      '肾功能': {
        '肌酐': { min: 44, max: 133, unit: 'μmol/L' },
        '尿素氮': { min: 2.9, max: 8.2, unit: 'mmol/L' }
      }
    }
  }

  /**
   * 分析单个健康指标
   * @param {Object} indicator 健康指标数据
   * @returns {Object} 分析结果
   */
  analyzeIndicator(indicator) {
    const { category, name, value, unit } = indicator
    const reference = this.referenceRanges[category]?.[name]
    
    if (!reference) {
      return {
        ...indicator,
        isAbnormal: false,
        status: 'unknown',
        suggestion: '暂无参考范围'
      }
    }

    const numValue = parseFloat(value)
    let status = 'normal'
    let suggestion = '指标正常'
    let isAbnormal = false

    if (numValue < reference.min) {
      status = 'low'
      suggestion = `偏低，建议咨询医生`
      isAbnormal = true
    } else if (numValue > reference.max) {
      status = 'high'
      suggestion = `偏高，建议咨询医生`
      isAbnormal = true
    }

    return {
      ...indicator,
      isAbnormal,
      status,
      suggestion,
      referenceRange: `${reference.min}-${reference.max} ${reference.unit}`
    }
  }

  /**
   * 分析健康报告
   * @param {Object} report 健康报告数据
   * @returns {Object} 分析结果
   */
  analyzeReport(report) {
    const analyzedIndicators = report.indicators.map(indicator => 
      this.analyzeIndicator(indicator)
    )

    const abnormalIndicators = analyzedIndicators.filter(indicator => indicator.isAbnormal)
    const normalCount = analyzedIndicators.length - abnormalIndicators.length

    return {
      ...report,
      indicators: analyzedIndicators,
      analysis: {
        totalIndicators: analyzedIndicators.length,
        normalCount,
        abnormalCount: abnormalIndicators.length,
        abnormalRate: (abnormalIndicators.length / analyzedIndicators.length * 100).toFixed(1),
        riskLevel: this.calculateRiskLevel(abnormalIndicators.length, analyzedIndicators.length),
        summary: this.generateReportSummary(analyzedIndicators, abnormalIndicators)
      }
    }
  }

  /**
   * 计算风险等级
   * @param {number} abnormalCount 异常指标数量
   * @param {number} totalCount 总指标数量
   * @returns {string} 风险等级
   */
  calculateRiskLevel(abnormalCount, totalCount) {
    const abnormalRate = abnormalCount / totalCount
    
    if (abnormalRate === 0) return 'low'
    if (abnormalRate <= 0.3) return 'medium'
    return 'high'
  }

  /**
   * 生成报告摘要
   * @param {Array} indicators 所有指标
   * @param {Array} abnormalIndicators 异常指标
   * @returns {string} 报告摘要
   */
  generateReportSummary(indicators, abnormalIndicators) {
    if (abnormalIndicators.length === 0) {
      return '本次检查各项指标均在正常范围内，请继续保持健康的生活方式。'
    }

    const categories = [...new Set(abnormalIndicators.map(i => i.category))]
    const categoryText = categories.join('、')
    
    return `本次检查发现${abnormalIndicators.length}项异常指标，主要涉及${categoryText}。建议及时咨询医生，制定相应的治疗或调理方案。`
  }

  /**
   * 趋势分析
   * @param {Array} reports 历史报告列表
   * @param {string} indicatorName 指标名称
   * @returns {Object} 趋势分析结果
   */
  analyzeTrend(reports, indicatorName) {
    // 按时间排序
    const sortedReports = reports.sort((a, b) => new Date(a.reportDate) - new Date(b.reportDate))
    
    // 提取指标数据
    const dataPoints = []
    sortedReports.forEach(report => {
      report.indicators.forEach(indicator => {
        if (indicator.name === indicatorName) {
          dataPoints.push({
            date: report.reportDate,
            value: parseFloat(indicator.value),
            unit: indicator.unit
          })
        }
      })
    })

    if (dataPoints.length < 2) {
      return {
        trend: 'insufficient_data',
        message: '数据不足，无法分析趋势'
      }
    }

    // 计算趋势
    const firstValue = dataPoints[0].value
    const lastValue = dataPoints[dataPoints.length - 1].value
    const changeRate = ((lastValue - firstValue) / firstValue * 100).toFixed(1)
    
    let trend = 'stable'
    let message = '指标相对稳定'
    
    if (Math.abs(changeRate) > 10) {
      trend = changeRate > 0 ? 'increasing' : 'decreasing'
      message = `指标呈${changeRate > 0 ? '上升' : '下降'}趋势，变化幅度${Math.abs(changeRate)}%`
    }

    return {
      trend,
      message,
      changeRate: parseFloat(changeRate),
      dataPoints,
      latest: dataPoints[dataPoints.length - 1],
      earliest: dataPoints[0]
    }
  }

  /**
   * 生成阶段性健康摘要
   * @param {Array} reports 时间段内的报告
   * @param {Object} timeRange 时间范围
   * @returns {Object} 健康摘要
   */
  generateHealthSummary(reports, timeRange) {
    if (reports.length === 0) {
      return {
        period: timeRange,
        summary: '该时间段内暂无检查记录',
        recommendations: []
      }
    }

    // 分析所有报告
    const analyzedReports = reports.map(report => this.analyzeReport(report))
    
    // 统计异常指标
    const allAbnormalIndicators = []
    analyzedReports.forEach(report => {
      allAbnormalIndicators.push(...report.indicators.filter(i => i.isAbnormal))
    })

    // 按类别统计异常频率
    const categoryStats = {}
    allAbnormalIndicators.forEach(indicator => {
      if (!categoryStats[indicator.category]) {
        categoryStats[indicator.category] = {
          count: 0,
          indicators: new Set()
        }
      }
      categoryStats[indicator.category].count++
      categoryStats[indicator.category].indicators.add(indicator.name)
    })

    // 生成建议
    const recommendations = this.generateRecommendations(categoryStats)

    return {
      period: timeRange,
      totalReports: reports.length,
      abnormalReports: analyzedReports.filter(r => r.analysis.abnormalCount > 0).length,
      categoryStats,
      summary: this.generatePeriodSummary(analyzedReports, categoryStats),
      recommendations,
      riskLevel: this.calculateOverallRisk(analyzedReports)
    }
  }

  /**
   * 生成时期摘要
   * @param {Array} analyzedReports 已分析的报告
   * @param {Object} categoryStats 类别统计
   * @returns {string} 摘要文本
   */
  generatePeriodSummary(analyzedReports, categoryStats) {
    const totalReports = analyzedReports.length
    const abnormalReports = analyzedReports.filter(r => r.analysis.abnormalCount > 0).length
    
    if (abnormalReports === 0) {
      return `在${totalReports}次检查中，各项指标均正常，健康状况良好。`
    }

    const mainCategories = Object.keys(categoryStats)
      .sort((a, b) => categoryStats[b].count - categoryStats[a].count)
      .slice(0, 3)
      .join('、')

    return `在${totalReports}次检查中，有${abnormalReports}次出现异常指标，主要集中在${mainCategories}方面。`
  }

  /**
   * 生成健康建议
   * @param {Object} categoryStats 类别统计
   * @returns {Array} 建议列表
   */
  generateRecommendations(categoryStats) {
    const recommendations = []
    
    Object.keys(categoryStats).forEach(category => {
      const stat = categoryStats[category]
      if (stat.count > 0) {
        switch (category) {
          case '血压':
            recommendations.push('注意控制血压，减少盐分摄入，适量运动')
            break
          case '血糖':
            recommendations.push('控制血糖水平，注意饮食结构，定期监测')
            break
          case '血脂':
            recommendations.push('注意血脂管理，减少高脂食物，增加有氧运动')
            break
          case '肝功能':
            recommendations.push('保护肝脏健康，避免过量饮酒，规律作息')
            break
          case '肾功能':
            recommendations.push('保护肾脏功能，多饮水，避免过度劳累')
            break
          default:
            recommendations.push(`关注${category}相关指标，建议咨询专科医生`)
        }
      }
    })

    return recommendations
  }

  /**
   * 计算整体风险等级
   * @param {Array} analyzedReports 已分析的报告
   * @returns {string} 风险等级
   */
  calculateOverallRisk(analyzedReports) {
    const riskScores = analyzedReports.map(report => {
      switch (report.analysis.riskLevel) {
        case 'low': return 1
        case 'medium': return 2
        case 'high': return 3
        default: return 1
      }
    })

    const avgRisk = riskScores.reduce((sum, score) => sum + score, 0) / riskScores.length

    if (avgRisk <= 1.3) return 'low'
    if (avgRisk <= 2.3) return 'medium'
    return 'high'
  }
}

export default new HealthAnalyticsService()