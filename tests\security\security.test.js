/**
 * 安全和隐私保护功能专项测试
 */

import securityManager from '../../utils/security/encryption.js'
import loginSecurityManager from '../../utils/security/loginSecurity.js'
import dataCleanupManager from '../../utils/security/dataCleanup.js'
import riskMonitorManager from '../../utils/security/riskMonitor.js'
import wechatSecurityManager from '../../utils/security/wechatSecurity.js'

describe('安全和隐私保护功能测试', () => {
  
  beforeEach(() => {
    // 清理测试环境
    uni.clearStorageSync()
  })

  afterEach(() => {
    // 清理测试数据
    uni.clearStorageSync()
  })

  describe('数据加密功能测试', () => {
    test('AES加密和解密功能正常', () => {
      const testData = {
        username: 'testuser',
        password: 'testpass123',
        healthData: {
          bloodPressure: '120/80',
          heartRate: 75
        }
      }

      // 测试加密
      const encrypted = securityManager.encrypt(testData)
      expect(encrypted).toBeTruthy()
      expect(typeof encrypted).toBe('string')
      expect(encrypted).not.toContain('testuser')

      // 测试解密
      const decrypted = securityManager.decrypt(encrypted)
      expect(decrypted).toEqual(testData)
    })

    test('数据签名生成和验证', () => {
      const testData = { message: 'test data' }
      const timestamp = Date.now()

      // 生成签名
      const signature = securityManager.generateSignature(testData, timestamp)
      expect(signature).toBeTruthy()
      expect(typeof signature).toBe('string')

      // 验证签名
      const isValid = securityManager.verifySignature(testData, signature, timestamp)
      expect(isValid).toBe(true)

      // 验证篡改后的数据
      const tamperedData = { message: 'tampered data' }
      const isInvalid = securityManager.verifySignature(tamperedData, signature, timestamp)
      expect(isInvalid).toBe(false)
    })

    test('安全存储功能', () => {
      const testKey = 'test_secure_data'
      const testData = {
        sensitiveInfo: 'confidential data',
        timestamp: Date.now()
      }

      // 安全存储
      securityManager.setSecureStorage(testKey, testData)

      // 安全读取
      const retrievedData = securityManager.getSecureStorage(testKey)
      expect(retrievedData).toEqual(testData)

      // 验证数据已加密存储
      const rawData = uni.getStorageSync(`secure_${testKey}`)
      expect(rawData).not.toContain('confidential data')
    })

    test('密码强度检测', () => {
      // 弱密码
      const weakPassword = '123456'
      const weakResult = securityManager.checkPasswordStrength(weakPassword)
      expect(weakResult.strength).toBe('weak')
      expect(weakResult.score).toBeLessThan(3)

      // 中等强度密码
      const mediumPassword = 'Test123'
      const mediumResult = securityManager.checkPasswordStrength(mediumPassword)
      expect(mediumResult.strength).toBe('medium')
      expect(mediumResult.score).toBeGreaterThanOrEqual(3)

      // 强密码
      const strongPassword = 'Test123!@#'
      const strongResult = securityManager.checkPasswordStrength(strongPassword)
      expect(strongResult.strength).toBe('strong')
      expect(strongResult.score).toBeGreaterThanOrEqual(4)
    })

    test('设备指纹生成', () => {
      const fingerprint1 = securityManager.generateDeviceFingerprint()
      const fingerprint2 = securityManager.generateDeviceFingerprint()

      expect(fingerprint1).toBeTruthy()
      expect(typeof fingerprint1).toBe('string')
      expect(fingerprint1.length).toBeGreaterThan(0)
      
      // 同一设备应生成相同指纹
      expect(fingerprint1).toBe(fingerprint2)
    })
  })

  describe('登录安全检测测试', () => {
    const testPhone = '***********'
    const testDeviceInfo = {
      ip: '***********',
      location: 'Beijing',
      userAgent: 'Test Agent'
    }

    test('登录尝试记录功能', () => {
      // 记录成功登录
      const successAttempt = loginSecurityManager.recordLoginAttempt(
        testPhone, 
        true, 
        testDeviceInfo
      )

      expect(successAttempt).toBeTruthy()
      expect(successAttempt.phone).toBe(testPhone)
      expect(successAttempt.success).toBe(true)

      // 获取登录记录
      const attempts = loginSecurityManager.getLoginAttempts(testPhone)
      expect(attempts.length).toBe(1)
      expect(attempts[0]).toEqual(successAttempt)
    })

    test('连续登录失败检测', () => {
      // 模拟多次失败登录
      for (let i = 0; i < 6; i++) {
        loginSecurityManager.recordLoginAttempt(testPhone, false, testDeviceInfo)
      }

      // 检查是否触发失败检测
      const isLocked = loginSecurityManager.checkFailedAttempts(testPhone)
      expect(isLocked).toBe(true)

      // 验证账户被锁定
      const accountLocked = loginSecurityManager.isAccountLocked(testPhone)
      expect(accountLocked).toBe(true)
    })

    test('异常登录行为检测', () => {
      // 模拟异常登录模式
      const differentDevices = [
        { ...testDeviceInfo, ip: '***********' },
        { ...testDeviceInfo, ip: '********' },
        { ...testDeviceInfo, ip: '**********' },
        { ...testDeviceInfo, ip: '***********' }
      ]

      differentDevices.forEach(device => {
        loginSecurityManager.recordLoginAttempt(testPhone, true, device)
      })

      // 检查是否检测到异常
      const alerts = loginSecurityManager.getSecurityAlerts(testPhone)
      expect(alerts.length).toBeGreaterThan(0)
    })

    test('安全提醒发送功能', async () => {
      const alert = await loginSecurityManager.sendSecurityAlert(
        testPhone,
        'suspicious_activity',
        { anomalies: ['多设备登录', '异地登录'] }
      )

      expect(alert).toBeTruthy()
      expect(alert.phone).toBe(testPhone)
      expect(alert.type).toBe('suspicious_activity')

      // 验证提醒已保存
      const alerts = loginSecurityManager.getSecurityAlerts(testPhone)
      expect(alerts.length).toBe(1)
      expect(alerts[0].id).toBe(alert.id)
    })

    test('登录安全报告生成', () => {
      // 添加一些测试数据
      loginSecurityManager.recordLoginAttempt(testPhone, true, testDeviceInfo)
      loginSecurityManager.recordLoginAttempt(testPhone, false, testDeviceInfo)

      const report = loginSecurityManager.generateSecurityReport(testPhone)

      expect(report).toBeTruthy()
      expect(report.phone).toBe(testPhone)
      expect(report.summary).toBeTruthy()
      expect(report.summary.totalAttempts).toBe(2)
      expect(report.summary.successfulLogins).toBe(1)
      expect(report.summary.failedAttempts).toBe(1)
    })
  })

  describe('数据清理功能测试', () => {
    const testUserId = 'test_user_123'

    beforeEach(() => {
      // 创建测试数据
      uni.setStorageSync('user_info', { userId: testUserId, name: 'Test User' })
      uni.setStorageSync('health_reports', [{ id: 1, data: 'test' }])
      uni.setStorageSync('cache_data', { cached: true })
      uni.setStorageSync('secure_sensitive', 'encrypted_data')
    })

    test('本地存储数据清理', async () => {
      // 执行数据清理
      await dataCleanupManager.clearLocalStorage(testUserId)

      // 验证敏感数据已清除
      const userInfo = uni.getStorageSync('user_info')
      const healthReports = uni.getStorageSync('health_reports')
      const secureData = uni.getStorageSync('secure_sensitive')

      expect(userInfo).toBeFalsy()
      expect(healthReports).toBeFalsy()
      expect(secureData).toBeFalsy()

      // 验证非敏感数据仍存在
      const nonSensitiveData = uni.getStorageSync('app_settings')
      // 这个测试需要根据实际的非敏感数据来验证
    })

    test('完全数据清除', async () => {
      const result = await dataCleanupManager.completeDataWipe(testUserId)

      expect(result.success).toBe(true)
      expect(result.message).toContain('清除')

      // 验证所有相关数据已清除
      const storageInfo = uni.getStorageInfoSync()
      const remainingKeys = storageInfo.keys.filter(key => 
        key.includes(testUserId) || 
        dataCleanupManager.sensitiveDataKeys.some(sensitive => key.startsWith(sensitive))
      )
      expect(remainingKeys.length).toBe(0)
    })

    test('数据泄露风险检测', async () => {
      // 添加一些风险数据
      uni.setStorageSync('unencrypted_sensitive', { password: '123456' })
      uni.setStorageSync('old_data', { timestamp: Date.now() - 400 * 24 * 60 * 60 * 1000 }) // 400天前

      const riskReport = await dataCleanupManager.checkDataLeakageRisk()

      expect(riskReport.hasRisk).toBe(true)
      expect(riskReport.risks.length).toBeGreaterThan(0)
      expect(riskReport.risks.some(r => r.type === 'unencrypted_data')).toBe(true)
    })

    test('账户注销处理', async () => {
      const result = await dataCleanupManager.handleAccountDeletion(testUserId, false)

      expect(result.success).toBe(true)
      expect(result.backupCreated).toBe(false)

      // 验证注销记录已保存
      const deletionLogs = uni.getStorageSync('account_deletions')
      expect(deletionLogs).toBeTruthy()
      expect(deletionLogs.length).toBe(1)
      expect(deletionLogs[0].userId).toBe(testUserId)
    })

    test('卸载前检查', async () => {
      // 添加未同步数据
      uni.setStorageSync('unsynced_data', [{ id: 1, synced: false }])

      const checkResult = await dataCleanupManager.preUninstallCheck()

      expect(checkResult.hasUnsyncedData).toBe(true)
      expect(checkResult.unsyncedCount).toBe(1)
      expect(checkResult.recommendation).toContain('同步')
    })
  })

  describe('风险监控功能测试', () => {
    test('风险监控启动和停止', () => {
      // 启动监控
      riskMonitorManager.startMonitoring()
      let status = riskMonitorManager.getMonitoringStatus()
      expect(status.enabled).toBe(true)
      expect(status.isRunning).toBe(true)

      // 停止监控
      riskMonitorManager.stopMonitoring()
      status = riskMonitorManager.getMonitoringStatus()
      expect(status.isRunning).toBe(false)
    })

    test('数据完整性检查', async () => {
      // 添加正常数据
      uni.setStorageSync('user_info', { name: 'test', timestamp: Date.now() })

      const integrityRisk = await riskMonitorManager.checkDataIntegrity()
      expect(integrityRisk).toBeNull()

      // 添加损坏数据
      uni.setStorageSync('user_info', { name: 'test', timestamp: 'invalid' })

      const corruptedRisk = await riskMonitorManager.checkDataIntegrity()
      expect(corruptedRisk).toBeTruthy()
      expect(corruptedRisk.type).toBe('data_integrity')
    })

    test('异常访问检测', async () => {
      const testPhone = '***********'
      uni.setStorageSync('current_user', { phone: testPhone })

      // 模拟正常访问
      loginSecurityManager.recordLoginAttempt(testPhone, true, { fingerprint: 'device1' })

      const normalRisk = await riskMonitorManager.checkAbnormalAccess()
      expect(normalRisk).toBeNull()

      // 模拟异常高频访问
      for (let i = 0; i < 12; i++) {
        loginSecurityManager.recordLoginAttempt(testPhone, true, { fingerprint: `device${i}` })
      }

      const abnormalRisk = await riskMonitorManager.checkAbnormalAccess()
      expect(abnormalRisk).toBeTruthy()
      expect(abnormalRisk.type).toBe('abnormal_access')
    })

    test('风险报告生成', () => {
      // 添加一些测试数据
      uni.setStorageSync('security_risk_alerts', [
        { type: 'critical_risk', timestamp: Date.now() },
        { type: 'high_risk', timestamp: Date.now() }
      ])

      const report = riskMonitorManager.getRiskReport()

      expect(report).toBeTruthy()
      expect(report.summary).toBeTruthy()
      expect(report.summary.totalAlerts).toBe(2)
      expect(report.summary.criticalAlerts).toBe(1)
      expect(report.recentAlerts).toBeTruthy()
    })
  })

  describe('微信小程序安全合规测试', () => {
    test('微信小程序环境检测', () => {
      const config = wechatSecurityManager.getWechatSecurityConfig()
      
      // 在非微信环境下
      expect(config.platform).toBe('not-wechat')
    })

    test('数据加密存储', () => {
      const testKey = 'user_info'
      const testData = { name: 'test', phone: '***********' }

      // 测试敏感数据判断
      const isSensitive = wechatSecurityManager.isSensitiveData(testKey)
      expect(isSensitive).toBe(true)

      // 测试数据加密
      const encrypted = wechatSecurityManager.encryptData(testData)
      expect(encrypted).toBeTruthy()
      expect(typeof encrypted).toBe('string')
      expect(encrypted.startsWith('encrypted_')).toBe(true)

      // 测试数据解密
      const decrypted = wechatSecurityManager.decryptData(encrypted)
      expect(decrypted).toEqual(testData)
    })

    test('用户信息过滤', () => {
      const userInfo = {
        nickName: 'testuser',
        phoneNumber: '***********',
        idCard: '110101199001011234'
      }

      const filtered = wechatSecurityManager.filterSensitiveUserInfo(userInfo)

      expect(filtered.nickName).toBe('testuser')
      expect(filtered.phoneNumber).toBe('138****8000') // 脱敏处理
      expect(filtered.idCard).toBeUndefined() // 完全移除
    })

    test('合规性检查', async () => {
      // 设置一些测试数据
      uni.setStorageSync('privacy_policy_agreed', { agreed: true, timestamp: Date.now() })

      const complianceReport = await wechatSecurityManager.checkWechatCompliance()

      if (complianceReport && complianceReport.platform === 'wechat-miniprogram') {
        expect(complianceReport.checks).toBeTruthy()
        expect(complianceReport.checks.length).toBeGreaterThan(0)
        expect(complianceReport.overallScore).toBeGreaterThanOrEqual(0)
        expect(typeof complianceReport.isCompliant).toBe('boolean')
      }
    })

    test('安全报告生成', async () => {
      const report = await wechatSecurityManager.generateSecurityReport()

      if (report) {
        expect(report.timestamp).toBeTruthy()
        expect(report.platform).toBe('wechat-miniprogram')
        expect(report.securityConfig).toBeTruthy()
        expect(report.recommendations).toBeTruthy()
        expect(Array.isArray(report.recommendations)).toBe(true)
      }
    })
  })

  describe('HTTPS协议安全测试', () => {
    test('安全请求功能', async () => {
      const testUrl = 'https://api.example.com/test'
      const testData = { message: 'test' }

      try {
        // 这里应该mock网络请求
        // 由于测试环境限制，我们只测试请求配置
        const requestOptions = {
          url: testUrl,
          method: 'POST',
          data: testData,
          encrypt: true
        }

        // 验证URL使用HTTPS
        expect(requestOptions.url.startsWith('https://')).toBe(true)
        expect(requestOptions.encrypt).toBe(true)
      } catch (error) {
        // 网络请求在测试环境中可能失败，这是正常的
        console.log('网络请求测试跳过（测试环境限制）')
      }
    })

    test('HTTP到HTTPS自动转换', () => {
      const httpUrl = 'http://api.example.com/test'
      const expectedHttpsUrl = 'https://api.example.com/test'

      // 模拟URL转换逻辑
      const convertedUrl = httpUrl.replace('http://', 'https://')
      expect(convertedUrl).toBe(expectedHttpsUrl)
    })
  })

  describe('隐私政策合规测试', () => {
    test('隐私政策同意状态', () => {
      // 测试未同意状态
      let agreed = uni.getStorageSync('privacy_policy_agreed')
      expect(agreed).toBeFalsy()

      // 测试同意状态
      const agreementData = {
        agreed: true,
        timestamp: Date.now(),
        version: '1.0'
      }
      uni.setStorageSync('privacy_policy_agreed', agreementData)

      agreed = uni.getStorageSync('privacy_policy_agreed')
      expect(agreed).toEqual(agreementData)
      expect(agreed.agreed).toBe(true)
    })

    test('用户权利实现', () => {
      const testUserId = 'test_user_123'
      const testData = { name: 'Test User', email: '<EMAIL>' }

      // 测试数据访问权
      uni.setStorageSync(`user_data_${testUserId}`, testData)
      const userData = uni.getStorageSync(`user_data_${testUserId}`)
      expect(userData).toEqual(testData)

      // 测试数据删除权
      uni.removeStorageSync(`user_data_${testUserId}`)
      const deletedData = uni.getStorageSync(`user_data_${testUserId}`)
      expect(deletedData).toBeFalsy()

      // 测试数据更正权
      const updatedData = { ...testData, name: 'Updated Name' }
      uni.setStorageSync(`user_data_${testUserId}`, updatedData)
      const correctedData = uni.getStorageSync(`user_data_${testUserId}`)
      expect(correctedData.name).toBe('Updated Name')
    })
  })

  describe('安全性能测试', () => {
    test('加密解密性能', () => {
      const testData = { message: 'performance test data' }
      const iterations = 100

      const startTime = Date.now()

      for (let i = 0; i < iterations; i++) {
        const encrypted = securityManager.encrypt(testData)
        const decrypted = securityManager.decrypt(encrypted)
        expect(decrypted).toEqual(testData)
      }

      const endTime = Date.now()
      const totalTime = endTime - startTime
      const avgTime = totalTime / iterations

      console.log(`加密解密平均耗时: ${avgTime}ms`)
      
      // 确保性能在可接受范围内（每次操作不超过50ms）
      expect(avgTime).toBeLessThan(50)
    })

    test('大数据量加密性能', () => {
      // 创建大量测试数据
      const largeData = {
        reports: new Array(1000).fill(0).map((_, i) => ({
          id: i,
          data: `test data ${i}`,
          timestamp: Date.now() + i
        }))
      }

      const startTime = Date.now()
      const encrypted = securityManager.encrypt(largeData)
      const decrypted = securityManager.decrypt(encrypted)
      const endTime = Date.now()

      expect(decrypted).toEqual(largeData)
      
      const totalTime = endTime - startTime
      console.log(`大数据量加密解密耗时: ${totalTime}ms`)
      
      // 确保大数据量处理时间在合理范围内（不超过1秒）
      expect(totalTime).toBeLessThan(1000)
    })
  })

  describe('安全边界测试', () => {
    test('恶意数据输入处理', () => {
      const maliciousInputs = [
        null,
        undefined,
        '',
        '<script>alert("xss")</script>',
        'DROP TABLE users;',
        '../../etc/passwd',
        new Array(10000).fill('a').join('') // 超长字符串
      ]

      maliciousInputs.forEach(input => {
        try {
          const encrypted = securityManager.encrypt(input)
          if (encrypted) {
            const decrypted = securityManager.decrypt(encrypted)
            // 如果加密解密成功，验证数据一致性
            expect(decrypted).toEqual(input)
          }
        } catch (error) {
          // 对于无效输入，应该优雅地处理错误
          expect(error).toBeInstanceOf(Error)
        }
      })
    })

    test('密钥安全性测试', () => {
      const testData = { secret: 'confidential' }
      
      // 使用不同密钥加密
      const key1 = 'key1'
      const key2 = 'key2'
      
      const encrypted1 = securityManager.encrypt(testData, key1)
      const encrypted2 = securityManager.encrypt(testData, key2)
      
      // 不同密钥应产生不同的加密结果
      expect(encrypted1).not.toBe(encrypted2)
      
      // 使用错误密钥解密应该失败
      try {
        securityManager.decrypt(encrypted1, key2)
        fail('应该抛出解密错误')
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }
    })

    test('存储容量限制测试', () => {
      const testKey = 'capacity_test'
      
      try {
        // 尝试存储大量数据
        const largeData = new Array(100000).fill('test data').join('')
        uni.setStorageSync(testKey, largeData)
        
        const retrieved = uni.getStorageSync(testKey)
        expect(retrieved).toBe(largeData)
        
        // 清理测试数据
        uni.removeStorageSync(testKey)
      } catch (error) {
        // 如果存储失败，应该是由于容量限制
        console.log('存储容量限制测试:', error.message)
        expect(error).toBeInstanceOf(Error)
      }
    })
  })
})