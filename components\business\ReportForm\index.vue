<template>
  <view class="report-form">
    <!-- 基本信息区域 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-item">
        <text class="label required">医院名称</text>
        <input 
          class="input"
          v-model="formData.hospital"
          placeholder="请输入医院名称"
          @blur="validateField('hospital')"
        />
        <text class="error-text" v-if="errors.hospital">{{ errors.hospital }}</text>
      </view>

      <view class="form-item">
        <text class="label required">医生姓名</text>
        <input 
          class="input"
          v-model="formData.doctor"
          placeholder="请输入医生姓名"
          @blur="validateField('doctor')"
        />
        <text class="error-text" v-if="errors.doctor">{{ errors.doctor }}</text>
      </view>

      <view class="form-item">
        <text class="label required">检查日期</text>
        <picker 
          mode="date" 
          :value="formData.checkDate"
          @change="onDateChange"
        >
          <view class="picker-input">
            {{ formData.checkDate || '请选择检查日期' }}
          </view>
        </picker>
        <text class="error-text" v-if="errors.checkDate">{{ errors.checkDate }}</text>
      </view>

      <view class="form-item">
        <text class="label">报告标题</text>
        <input 
          class="input"
          v-model="formData.title"
          placeholder="请输入报告标题（可选）"
        />
      </view>

      <view class="form-item">
        <text class="label">备注</text>
        <textarea 
          class="textarea"
          v-model="formData.notes"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
        />
        <text class="char-count">{{ formData.notes.length }}/200</text>
      </view>
    </view>

    <!-- OCR识别结果区域 -->
    <view class="form-section" v-if="ocrResult">
      <view class="section-title">
        <text>识别结果</text>
        <text class="confidence-badge" :class="getConfidenceClass()">
          置信度: {{ Math.round(ocrResult.confidence * 100) }}%
        </text>
      </view>

      <view class="ocr-summary">
        <text class="summary-text">
          识别到 {{ ocrResult.items.length }} 个检查项目
        </text>
        <button class="btn-secondary" @click="showOCRDetails = !showOCRDetails">
          {{ showOCRDetails ? '收起详情' : '查看详情' }}
        </button>
      </view>

      <!-- OCR详细结果 -->
      <view class="ocr-details" v-if="showOCRDetails">
        <view class="ocr-item" v-for="(item, index) in ocrResult.items" :key="index">
          <view class="item-header">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-status" :class="item.isAbnormal ? 'abnormal' : 'normal'">
              {{ item.isAbnormal ? '异常' : '正常' }}
            </text>
          </view>
          <view class="item-content">
            <text class="item-value">{{ item.value }} {{ item.unit }}</text>
            <text class="item-reference" v-if="item.referenceRange">
              参考范围: {{ item.referenceRange }}
            </text>
          </view>
        </view>
      </view>

      <!-- OCR问题提示 -->
      <view class="ocr-issues" v-if="ocrResult.validation && !ocrResult.validation.isValid">
        <view class="issues-title">识别问题</view>
        <view class="issue-item" v-for="issue in ocrResult.validation.issues" :key="issue">
          <text class="issue-text">• {{ issue }}</text>
        </view>
        <view class="issues-actions">
          <button class="btn-secondary" @click="retryOCR">重新识别</button>
          <button class="btn-secondary" @click="manualInput">手动录入</button>
        </view>
      </view>
    </view>

    <!-- 检查项目编辑区域 -->
    <view class="form-section">
      <view class="section-title">
        <text>检查项目</text>
        <button class="btn-add" @click="addItem">+ 添加项目</button>
      </view>

      <view class="items-list">
        <view class="item-form" v-for="(item, index) in formData.items" :key="index">
          <view class="item-header">
            <text class="item-index">{{ index + 1 }}</text>
            <button class="btn-delete" @click="removeItem(index)">删除</button>
          </view>

          <view class="item-fields">
            <view class="field-row">
              <view class="field-group">
                <text class="field-label">项目名称</text>
                <input 
                  class="field-input"
                  v-model="item.name"
                  placeholder="如：白细胞计数"
                  @blur="validateItem(index, 'name')"
                />
              </view>
              <view class="field-group">
                <text class="field-label">分类</text>
                <picker 
                  :range="categoryOptions" 
                  :value="getCategoryIndex(item.category)"
                  @change="onCategoryChange(index, $event)"
                >
                  <view class="picker-field">
                    {{ item.category || '请选择' }}
                  </view>
                </picker>
              </view>
            </view>

            <view class="field-row">
              <view class="field-group">
                <text class="field-label">检查结果</text>
                <input 
                  class="field-input"
                  v-model="item.value"
                  placeholder="如：5.2"
                  type="digit"
                  @blur="validateItem(index, 'value')"
                />
              </view>
              <view class="field-group">
                <text class="field-label">单位</text>
                <input 
                  class="field-input"
                  v-model="item.unit"
                  placeholder="如：10^9/L"
                />
              </view>
            </view>

            <view class="field-row">
              <view class="field-group full-width">
                <text class="field-label">参考范围</text>
                <input 
                  class="field-input"
                  v-model="item.referenceRange"
                  placeholder="如：3.5-9.5"
                />
              </view>
            </view>

            <!-- 异常状态指示 -->
            <view class="abnormal-indicator" v-if="item.isAbnormal">
              <text class="abnormal-text">⚠️ 检测到异常值</text>
            </view>
          </view>

          <text class="item-error" v-if="itemErrors[index]">{{ itemErrors[index] }}</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="formData.items.length === 0">
          <text class="empty-text">暂无检查项目</text>
          <text class="empty-hint">点击"添加项目"或使用OCR识别添加</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="btn-cancel" @click="onCancel">取消</button>
      <button class="btn-save" @click="onSave" :disabled="!isFormValid">保存</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="loading">
      <text class="loading-text">{{ loadingText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ReportForm',
  props: {
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // OCR识别结果
    ocrResult: {
      type: Object,
      default: null
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        hospital: '',
        doctor: '',
        checkDate: '',
        title: '',
        notes: '',
        items: []
      },
      errors: {},
      itemErrors: {},
      showOCRDetails: false,
      loading: false,
      loadingText: '',
      categoryOptions: [
        '血常规',
        '生化检查',
        '免疫检查',
        '尿常规',
        '肝功能',
        '肾功能',
        '血脂',
        '血糖',
        '其他'
      ]
    }
  },
  computed: {
    isFormValid() {
      return this.formData.hospital && 
             this.formData.doctor && 
             this.formData.checkDate &&
             this.formData.items.length > 0 &&
             Object.keys(this.errors).length === 0 &&
             Object.keys(this.itemErrors).length === 0;
    }
  },
  watch: {
    initialData: {
      handler(newData) {
        if (newData) {
          this.initFormData(newData);
        }
      },
      immediate: true
    },
    ocrResult: {
      handler(newResult) {
        if (newResult && newResult.data) {
          this.applyOCRResult(newResult.data);
        }
      },
      immediate: true
    },
    'formData.items': {
      handler(items) {
        // 检查异常值
        items.forEach((item, index) => {
          this.checkAbnormalValue(index);
        });
      },
      deep: true
    }
  },
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData(data) {
      this.formData = {
        hospital: data.hospital || '',
        doctor: data.doctor || '',
        checkDate: data.checkDate || '',
        title: data.title || '',
        notes: data.notes || '',
        items: data.items ? [...data.items] : []
      };
    },

    /**
     * 应用OCR识别结果
     */
    applyOCRResult(ocrData) {
      if (!ocrData) return;

      // 填充基本信息
      if (ocrData.hospital && !this.formData.hospital) {
        this.formData.hospital = ocrData.hospital;
      }
      if (ocrData.doctor && !this.formData.doctor) {
        this.formData.doctor = ocrData.doctor;
      }
      if (ocrData.checkDate && !this.formData.checkDate) {
        this.formData.checkDate = ocrData.checkDate;
      }

      // 填充检查项目
      if (ocrData.items && ocrData.items.length > 0) {
        this.formData.items = ocrData.items.map(item => ({
          name: item.name || '',
          value: item.value || '',
          unit: item.unit || '',
          referenceRange: item.referenceRange || '',
          category: this.guessCategory(item.name),
          isAbnormal: item.isAbnormal || false
        }));
      }
    },

    /**
     * 根据项目名称猜测分类
     */
    guessCategory(itemName) {
      if (!itemName) return '';
      
      const categoryMap = {
        '血常规': ['白细胞', '红细胞', '血红蛋白', '血小板', '中性粒细胞', '淋巴细胞'],
        '生化检查': ['总蛋白', '白蛋白', '谷丙转氨酶', '谷草转氨酶'],
        '血脂': ['总胆固醇', '甘油三酯', '高密度脂蛋白', '低密度脂蛋白'],
        '血糖': ['血糖', '葡萄糖'],
        '肾功能': ['肌酐', '尿素氮', '尿酸'],
        '尿常规': ['尿蛋白', '尿糖', '尿比重']
      };

      for (const [category, keywords] of Object.entries(categoryMap)) {
        if (keywords.some(keyword => itemName.includes(keyword))) {
          return category;
        }
      }

      return '其他';
    },

    /**
     * 字段验证
     */
    validateField(fieldName) {
      const value = this.formData[fieldName];
      
      switch (fieldName) {
        case 'hospital':
          if (!value || value.trim() === '') {
            this.$set(this.errors, fieldName, '请输入医院名称');
          } else if (value.length < 2) {
            this.$set(this.errors, fieldName, '医院名称至少2个字符');
          } else {
            this.$delete(this.errors, fieldName);
          }
          break;
          
        case 'doctor':
          if (!value || value.trim() === '') {
            this.$set(this.errors, fieldName, '请输入医生姓名');
          } else if (value.length < 2) {
            this.$set(this.errors, fieldName, '医生姓名至少2个字符');
          } else {
            this.$delete(this.errors, fieldName);
          }
          break;
          
        case 'checkDate':
          if (!value) {
            this.$set(this.errors, fieldName, '请选择检查日期');
          } else {
            const checkDate = new Date(value);
            const today = new Date();
            if (checkDate > today) {
              this.$set(this.errors, fieldName, '检查日期不能晚于今天');
            } else {
              this.$delete(this.errors, fieldName);
            }
          }
          break;
      }
    },

    /**
     * 项目验证
     */
    validateItem(index, fieldName) {
      const item = this.formData.items[index];
      const key = `${index}_${fieldName}`;
      
      switch (fieldName) {
        case 'name':
          if (!item.name || item.name.trim() === '') {
            this.$set(this.itemErrors, key, '请输入项目名称');
          } else {
            this.$delete(this.itemErrors, key);
          }
          break;
          
        case 'value':
          if (!item.value || item.value.trim() === '') {
            this.$set(this.itemErrors, key, '请输入检查结果');
          } else if (isNaN(parseFloat(item.value))) {
            this.$set(this.itemErrors, key, '检查结果必须是数字');
          } else {
            this.$delete(this.itemErrors, key);
            this.checkAbnormalValue(index);
          }
          break;
      }
    },

    /**
     * 检查异常值
     */
    checkAbnormalValue(index) {
      const item = this.formData.items[index];
      if (!item.value || !item.referenceRange) {
        item.isAbnormal = false;
        return;
      }

      const value = parseFloat(item.value);
      if (isNaN(value)) {
        item.isAbnormal = false;
        return;
      }

      // 解析参考范围
      const rangeMatch = item.referenceRange.match(/(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/);
      if (rangeMatch) {
        const minValue = parseFloat(rangeMatch[1]);
        const maxValue = parseFloat(rangeMatch[2]);
        
        if (!isNaN(minValue) && !isNaN(maxValue)) {
          item.isAbnormal = value < minValue || value > maxValue;
        }
      }
    },

    /**
     * 日期选择
     */
    onDateChange(e) {
      this.formData.checkDate = e.detail.value;
      this.validateField('checkDate');
    },

    /**
     * 分类选择
     */
    onCategoryChange(index, e) {
      const categoryIndex = e.detail.value;
      this.formData.items[index].category = this.categoryOptions[categoryIndex];
    },

    /**
     * 获取分类索引
     */
    getCategoryIndex(category) {
      return this.categoryOptions.indexOf(category);
    },

    /**
     * 添加项目
     */
    addItem() {
      this.formData.items.push({
        name: '',
        value: '',
        unit: '',
        referenceRange: '',
        category: '',
        isAbnormal: false
      });
    },

    /**
     * 删除项目
     */
    removeItem(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个检查项目吗？',
        success: (res) => {
          if (res.confirm) {
            this.formData.items.splice(index, 1);
            // 清理相关错误信息
            Object.keys(this.itemErrors).forEach(key => {
              if (key.startsWith(`${index}_`)) {
                this.$delete(this.itemErrors, key);
              }
            });
          }
        }
      });
    },

    /**
     * 获取置信度样式类
     */
    getConfidenceClass() {
      if (!this.ocrResult || !this.ocrResult.confidence) return 'low';
      
      const confidence = this.ocrResult.confidence;
      if (confidence >= 0.8) return 'high';
      if (confidence >= 0.6) return 'medium';
      return 'low';
    },

    /**
     * 重新OCR识别
     */
    retryOCR() {
      this.$emit('retry-ocr');
    },

    /**
     * 手动录入
     */
    manualInput() {
      this.showOCRDetails = false;
      // 清空OCR结果，让用户手动输入
      this.$emit('manual-input');
    },

    /**
     * 取消操作
     */
    onCancel() {
      uni.showModal({
        title: '确认取消',
        content: '取消后将丢失已填写的内容，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            this.$emit('cancel');
          }
        }
      });
    },

    /**
     * 保存表单
     */
    async onSave() {
      // 验证所有字段
      this.validateField('hospital');
      this.validateField('doctor');
      this.validateField('checkDate');
      
      // 验证所有项目
      this.formData.items.forEach((item, index) => {
        this.validateItem(index, 'name');
        this.validateItem(index, 'value');
      });

      if (!this.isFormValid) {
        uni.showToast({
          title: '请完善必填信息',
          icon: 'none'
        });
        return;
      }

      try {
        this.loading = true;
        this.loadingText = '保存中...';

        const reportData = {
          ...this.formData,
          id: this.initialData.id || null,
          timestamp: new Date().toISOString()
        };

        this.$emit('save', reportData);
      } catch (error) {
        console.error('保存失败:', error);
        uni.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.loadingText = '';
      }
    },

    /**
     * 设置加载状态
     */
    setLoading(loading, text = '') {
      this.loading = loading;
      this.loadingText = text;
    }
  }
}
</script>

<style lang="scss" scoped>
.report-form {
  padding: 20rpx;
  background: #f8f8f8;
  min-height: 100vh;
}

.form-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  
  &.required::after {
    content: '*';
    color: #ff4757;
    margin-left: 4rpx;
  }
}

.input, .textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fafafa;
  
  &:focus {
    border-color: #2196F3;
    background: white;
  }
}

.textarea {
  height: 120rpx;
  resize: none;
}

.picker-input {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fafafa;
  color: #333;
  
  &:empty::before {
    content: attr(placeholder);
    color: #999;
  }
}

.error-text {
  display: block;
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.confidence-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  
  &.high {
    background: #4CAF50;
  }
  
  &.medium {
    background: #FF9800;
  }
  
  &.low {
    background: #f44336;
  }
}

.ocr-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f0f8ff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.summary-text {
  font-size: 28rpx;
  color: #2196F3;
}

.ocr-details {
  border-top: 2rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.ocr-item {
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.item-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.item-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
  
  &.normal {
    background: #4CAF50;
  }
  
  &.abnormal {
    background: #ff4757;
  }
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.item-reference {
  font-size: 24rpx;
  color: #666;
}

.ocr-issues {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.issues-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
  margin-bottom: 15rpx;
}

.issue-item {
  margin-bottom: 8rpx;
}

.issue-text {
  font-size: 26rpx;
  color: #856404;
}

.issues-actions {
  display: flex;
  gap: 15rpx;
  margin-top: 20rpx;
}

.btn-add {
  padding: 8rpx 16rpx;
  background: #2196F3;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.items-list {
  margin-top: 20rpx;
}

.item-form {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #fafafa;
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.item-index {
  width: 40rpx;
  height: 40rpx;
  background: #2196F3;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.btn-delete {
  padding: 8rpx 16rpx;
  background: #ff4757;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.item-fields {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.field-row {
  display: flex;
  gap: 20rpx;
}

.field-group {
  flex: 1;
  
  &.full-width {
    flex: none;
    width: 100%;
  }
}

.field-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.field-input {
  width: 100%;
  padding: 15rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 6rpx;
  font-size: 26rpx;
  background: white;
}

.picker-field {
  padding: 15rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 6rpx;
  background: white;
  color: #333;
}

.abnormal-indicator {
  background: #ffebee;
  border: 2rpx solid #ffcdd2;
  border-radius: 6rpx;
  padding: 15rpx;
  margin-top: 15rpx;
}

.abnormal-text {
  font-size: 26rpx;
  color: #d32f2f;
}

.item-error {
  display: block;
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 10rpx;
}

.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.empty-hint {
  display: block;
  font-size: 24rpx;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: white;
  position: sticky;
  bottom: 0;
  border-top: 2rpx solid #f0f0f0;
}

.btn-cancel, .btn-save {
  flex: 1;
  padding: 25rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  border: none;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-save {
  background: #2196F3;
  color: white;
  
  &[disabled] {
    background: #ccc;
    color: #999;
  }
}

.btn-secondary {
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 6rpx;
  font-size: 26rpx;
  border: none;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-text {
  color: white;
  font-size: 32rpx;
}
</style>