import { defineStore } from 'pinia'

export const useSyncStore = defineStore('sync', {
  state: () => ({
    // 同步状态
    syncStatus: {
      isEnabled: true,
      isRunning: false,
      lastSyncTime: null,
      nextSyncTime: null
    },
    
    // 同步配置
    syncConfig: {
      autoSync: true,
      syncInterval: 30, // 分钟
      wifiOnly: true,
      syncOnAppStart: true
    },
    
    // 同步记录
    syncHistory: [],
    
    // 冲突数据
    conflicts: [],
    
    // 待同步数据
    pendingSync: {
      upload: [], // 待上传的数据
      download: [], // 待下载的数据
      delete: [] // 待删除的数据
    },
    
    // 同步统计
    statistics: {
      totalSynced: 0,
      successCount: 0,
      failureCount: 0,
      conflictCount: 0
    }
  }),
  
  getters: {
    // 是否需要同步
    needSync: (state) => {
      return state.pendingSync.upload.length > 0 || 
             state.pendingSync.download.length > 0 || 
             state.pendingSync.delete.length > 0
    },
    
    // 同步成功率
    syncSuccessRate: (state) => {
      const total = state.statistics.successCount + state.statistics.failureCount
      return total > 0 ? (state.statistics.successCount / total * 100).toFixed(1) : 0
    },
    
    // 是否有冲突
    hasConflicts: (state) => state.conflicts.length > 0,
    
    // 最近同步记录
    recentSyncHistory: (state) => {
      return state.syncHistory
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 10)
    }
  },
  
  actions: {
    // 更新同步状态
    updateSyncStatus(status) {
      this.syncStatus = { ...this.syncStatus, ...status }
      this.saveToLocal()
    },
    
    // 更新同步配置
    updateSyncConfig(config) {
      this.syncConfig = { ...this.syncConfig, ...config }
      this.saveToLocal()
    },
    
    // 添加待同步数据
    addPendingSync(type, data) {
      if (!this.pendingSync[type]) return
      
      // 避免重复添加
      const exists = this.pendingSync[type].some(item => item.id === data.id)
      if (!exists) {
        this.pendingSync[type].push({
          ...data,
          timestamp: Date.now()
        })
        this.saveToLocal()
      }
    },
    
    // 移除待同步数据
    removePendingSync(type, dataId) {
      if (!this.pendingSync[type]) return
      
      this.pendingSync[type] = this.pendingSync[type].filter(item => item.id !== dataId)
      this.saveToLocal()
    },
    
    // 添加同步记录
    addSyncRecord(record) {
      const syncRecord = {
        id: Date.now().toString(),
        timestamp: Date.now(),
        type: record.type, // 'upload', 'download', 'conflict'
        status: record.status, // 'success', 'failed', 'partial'
        details: record.details || {},
        message: record.message || ''
      }
      
      this.syncHistory.unshift(syncRecord)
      
      // 只保留最近100条记录
      if (this.syncHistory.length > 100) {
        this.syncHistory = this.syncHistory.slice(0, 100)
      }
      
      // 更新统计
      this.updateStatistics(record.status)
      this.saveToLocal()
    },
    
    // 添加冲突数据
    addConflict(conflictData) {
      const conflict = {
        id: Date.now().toString(),
        timestamp: Date.now(),
        type: conflictData.type, // 'report', 'user_info'
        localData: conflictData.localData,
        remoteData: conflictData.remoteData,
        resolved: false
      }
      
      this.conflicts.push(conflict)
      this.statistics.conflictCount++
      this.saveToLocal()
    },
    
    // 解决冲突
    resolveConflict(conflictId, resolution) {
      const conflict = this.conflicts.find(c => c.id === conflictId)
      if (conflict) {
        conflict.resolved = true
        conflict.resolution = resolution
        conflict.resolvedAt = Date.now()
        this.saveToLocal()
      }
    },
    
    // 清除已解决的冲突
    clearResolvedConflicts() {
      this.conflicts = this.conflicts.filter(c => !c.resolved)
      this.saveToLocal()
    },
    
    // 更新统计信息
    updateStatistics(status) {
      this.statistics.totalSynced++
      
      if (status === 'success') {
        this.statistics.successCount++
      } else if (status === 'failed') {
        this.statistics.failureCount++
      }
    },
    
    // 开始同步
    async startSync(options = {}) {
      if (this.syncStatus.isRunning) {
        console.log('同步正在进行中')
        return { success: false, message: '同步正在进行中' }
      }
      
      try {
        this.updateSyncStatus({ isRunning: true })
        
        const syncResult = {
          success: true,
          uploaded: 0,
          downloaded: 0,
          conflicts: 0,
          errors: []
        }
        
        // 模拟同步过程
        // 实际实现中这里会调用云端API
        
        // 上传待同步数据
        for (const item of this.pendingSync.upload) {
          try {
            // await cloudService.upload(item)
            this.removePendingSync('upload', item.id)
            syncResult.uploaded++
          } catch (error) {
            syncResult.errors.push(`上传失败: ${error.message}`)
          }
        }
        
        // 下载远程数据
        for (const item of this.pendingSync.download) {
          try {
            // await cloudService.download(item)
            this.removePendingSync('download', item.id)
            syncResult.downloaded++
          } catch (error) {
            syncResult.errors.push(`下载失败: ${error.message}`)
          }
        }
        
        // 记录同步结果
        this.addSyncRecord({
          type: 'sync',
          status: syncResult.errors.length === 0 ? 'success' : 'partial',
          details: syncResult,
          message: `上传${syncResult.uploaded}条，下载${syncResult.downloaded}条`
        })
        
        this.updateSyncStatus({
          lastSyncTime: Date.now(),
          nextSyncTime: Date.now() + this.syncConfig.syncInterval * 60 * 1000
        })
        
        return syncResult
        
      } catch (error) {
        console.error('同步失败:', error)
        
        this.addSyncRecord({
          type: 'sync',
          status: 'failed',
          message: error.message
        })
        
        return { success: false, error: error.message }
        
      } finally {
        this.updateSyncStatus({ isRunning: false })
      }
    },
    
    // 保存到本地存储
    saveToLocal() {
      try {
        uni.setStorageSync('sync_status', this.syncStatus)
        uni.setStorageSync('sync_config', this.syncConfig)
        uni.setStorageSync('sync_history', this.syncHistory)
        uni.setStorageSync('sync_conflicts', this.conflicts)
        uni.setStorageSync('pending_sync', this.pendingSync)
        uni.setStorageSync('sync_statistics', this.statistics)
      } catch (error) {
        console.error('保存同步数据失败:', error)
      }
    },
    
    // 从本地存储加载
    loadFromLocal() {
      try {
        const savedStatus = uni.getStorageSync('sync_status')
        const savedConfig = uni.getStorageSync('sync_config')
        const savedHistory = uni.getStorageSync('sync_history')
        const savedConflicts = uni.getStorageSync('sync_conflicts')
        const savedPending = uni.getStorageSync('pending_sync')
        const savedStatistics = uni.getStorageSync('sync_statistics')
        
        if (savedStatus) {
          this.syncStatus = { ...this.syncStatus, ...savedStatus, isRunning: false }
        }
        
        if (savedConfig) {
          this.syncConfig = { ...this.syncConfig, ...savedConfig }
        }
        
        if (savedHistory && Array.isArray(savedHistory)) {
          this.syncHistory = savedHistory
        }
        
        if (savedConflicts && Array.isArray(savedConflicts)) {
          this.conflicts = savedConflicts
        }
        
        if (savedPending) {
          this.pendingSync = { ...this.pendingSync, ...savedPending }
        }
        
        if (savedStatistics) {
          this.statistics = { ...this.statistics, ...savedStatistics }
        }
        
      } catch (error) {
        console.error('加载同步数据失败:', error)
      }
    },
    
    // 初始化同步服务
    async initSync() {
      try {
        // 从本地存储加载数据
        this.loadFromLocal()
        
        // 如果启用自动同步且应用启动时同步
        if (this.syncConfig.autoSync && this.syncConfig.syncOnAppStart) {
          // 延迟启动同步，避免影响应用启动速度
          setTimeout(() => {
            this.startSync({ background: true })
          }, 3000)
        }
        
        // 设置定时同步
        if (this.syncConfig.autoSync) {
          this.scheduleNextSync()
        }
        
      } catch (error) {
        console.error('初始化同步服务失败:', error)
      }
    },
    
    // 安排下次同步
    scheduleNextSync() {
      if (!this.syncConfig.autoSync) return
      
      const interval = this.syncConfig.syncInterval * 60 * 1000
      
      setTimeout(() => {
        if (this.syncConfig.autoSync && this.needSync) {
          this.startSync({ background: true })
        }
        this.scheduleNextSync() // 递归安排下次同步
      }, interval)
    }
  }
})