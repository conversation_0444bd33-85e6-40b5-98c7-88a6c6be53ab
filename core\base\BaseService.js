/**
 * 基础Service类
 * 提供业务逻辑层的基础实现
 */

import { IService } from '../interfaces/IService.js'
import { Constants } from '../../types/index.js'

export class BaseService extends IService {
  constructor() {
    super()
    this.isInitialized = false
    this.eventListeners = new Map()
  }
  
  /**
   * 初始化服务
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return
    }
    
    try {
      await this.onInitialize()
      this.isInitialized = true
      this.emit('initialized')
    } catch (error) {
      throw this.handleError(error, 'initialize')
    }
  }
  
  /**
   * 初始化钩子方法
   * 子类可以重写此方法来执行特定的初始化逻辑
   * @returns {Promise<void>}
   */
  async onInitialize() {
    // 子类实现
  }
  
  /**
   * 销毁服务
   * @returns {Promise<void>}
   */
  async destroy() {
    if (!this.isInitialized) {
      return
    }
    
    try {
      await this.onDestroy()
      this.isInitialized = false
      this.eventListeners.clear()
      this.emit('destroyed')
    } catch (error) {
      throw this.handleError(error, 'destroy')
    }
  }
  
  /**
   * 销毁钩子方法
   * 子类可以重写此方法来执行特定的清理逻辑
   * @returns {Promise<void>}
   */
  async onDestroy() {
    // 子类实现
  }
  
  /**
   * 检查服务是否已初始化
   * @throws {Error} 如果服务未初始化
   */
  ensureInitialized() {
    if (!this.isInitialized) {
      throw new Error(`服务 ${this.constructor.name} 尚未初始化`)
    }
  }
  
  /**
   * 添加事件监听器
   * @param {String} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(listener)
  }
  
  /**
   * 移除事件监听器
   * @param {String} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  off(event, listener) {
    if (!this.eventListeners.has(event)) {
      return
    }
    
    const listeners = this.eventListeners.get(event)
    const index = listeners.indexOf(listener)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }
  
  /**
   * 触发事件
   * @param {String} event - 事件名称
   * @param {...*} args - 事件参数
   */
  emit(event, ...args) {
    if (!this.eventListeners.has(event)) {
      return
    }
    
    const listeners = this.eventListeners.get(event)
    for (const listener of listeners) {
      try {
        listener(...args)
      } catch (error) {
        console.error(`事件监听器执行失败 [${event}]:`, error)
      }
    }
  }
  
  /**
   * 处理错误
   * @param {Error} error - 原始错误
   * @param {String} operation - 操作名称
   * @returns {Error} 处理后的错误
   */
  handleError(error, operation) {
    const errorMessage = `${this.constructor.name} ${operation} 操作失败: ${error.message}`
    
    // 创建服务错误
    const serviceError = new Error(errorMessage)
    serviceError.type = error.type || Constants.ERROR_TYPES.UNKNOWN_ERROR
    serviceError.originalError = error
    serviceError.service = this.constructor.name
    serviceError.operation = operation
    serviceError.timestamp = new Date()
    
    // 触发错误事件
    this.emit('error', serviceError)
    
    return serviceError
  }
  
  /**
   * 记录日志
   * @param {String} level - 日志级别
   * @param {String} message - 日志消息
   * @param {Object} context - 上下文信息
   */
  log(level, message, context = {}) {
    const logEntry = {
      timestamp: new Date(),
      level,
      service: this.constructor.name,
      message,
      context
    }
    
    // 触发日志事件
    this.emit('log', logEntry)
    
    // 控制台输出
    switch (level.toLowerCase()) {
      case 'error':
        console.error(`[${this.constructor.name}] ${message}`, context)
        break
      case 'warn':
        console.warn(`[${this.constructor.name}] ${message}`, context)
        break
      case 'info':
        console.info(`[${this.constructor.name}] ${message}`, context)
        break
      case 'debug':
        console.debug(`[${this.constructor.name}] ${message}`, context)
        break
      default:
        console.log(`[${this.constructor.name}] ${message}`, context)
    }
  }
  
  /**
   * 验证参数
   * @param {Object} params - 参数对象
   * @param {Object} rules - 验证规则
   * @throws {Error} 验证失败时抛出错误
   */
  validateParams(params, rules) {
    const errors = []
    
    for (const [field, rule] of Object.entries(rules)) {
      const value = params[field]
      
      // 必填验证
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`参数 ${field} 是必填的`)
        continue
      }
      
      // 如果值为空且不是必填，跳过其他验证
      if (value === undefined || value === null || value === '') {
        continue
      }
      
      // 类型验证
      if (rule.type && typeof value !== rule.type) {
        errors.push(`参数 ${field} 类型应为 ${rule.type}`)
      }
      
      // 正则验证
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push(`参数 ${field} 格式不正确`)
      }
      
      // 自定义验证
      if (rule.validator && typeof rule.validator === 'function') {
        const result = rule.validator(value)
        if (result !== true) {
          errors.push(result || `参数 ${field} 验证失败`)
        }
      }
    }
    
    if (errors.length > 0) {
      const error = new Error(`参数验证失败: ${errors.join(', ')}`)
      error.type = Constants.ERROR_TYPES.VALIDATION_ERROR
      error.validationErrors = errors
      throw error
    }
  }
  
  /**
   * 执行带重试的操作
   * @param {Function} operation - 要执行的操作
   * @param {Object} options - 重试选项
   * @returns {Promise<*>} 操作结果
   */
  async retryOperation(operation, options = {}) {
    const {
      maxRetries = 3,
      delay = 1000,
      backoff = 2,
      shouldRetry = () => true
    } = options
    
    let lastError
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries || !shouldRetry(error)) {
          break
        }
        
        // 等待后重试
        const waitTime = delay * Math.pow(backoff, attempt)
        await this.sleep(waitTime)
        
        this.log('warn', `操作失败，${waitTime}ms后重试 (${attempt + 1}/${maxRetries})`, {
          error: error.message
        })
      }
    }
    
    throw lastError
  }
  
  /**
   * 睡眠指定时间
   * @param {Number} ms - 毫秒数
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * 创建缓存键
   * @param {...String} parts - 键的组成部分
   * @returns {String} 缓存键
   */
  createCacheKey(...parts) {
    return `${this.constructor.name}:${parts.join(':')}`
  }
  
  /**
   * 格式化响应数据
   * @param {*} data - 响应数据
   * @param {String} message - 响应消息
   * @returns {Object} 格式化的响应
   */
  formatResponse(data, message = 'success') {
    return {
      success: true,
      data,
      message,
      timestamp: new Date()
    }
  }
  
  /**
   * 格式化错误响应
   * @param {Error} error - 错误对象
   * @returns {Object} 格式化的错误响应
   */
  formatErrorResponse(error) {
    return {
      success: false,
      data: null,
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR',
      timestamp: new Date()
    }
  }
}