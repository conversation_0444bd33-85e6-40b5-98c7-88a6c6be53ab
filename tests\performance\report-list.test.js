/**
 * 报告列表性能测试
 * 测试虚拟滚动和大数据量展示性能
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ReportList from '@/pages/report/list.vue'
import { useReportStore } from '@/stores/report.js'
import { Report, ReportItem } from '@/models/Report.js'
import { Constants } from '@/types/index.js'

// 模拟uni-app API
global.uni = {
  navigateTo: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  stopPullDownRefresh: jest.fn(),
  createSelectorQuery: jest.fn(() => ({
    in: jest.fn(() => ({
      select: jest.fn(() => ({
        boundingClientRect: jest.fn(() => ({
          exec: jest.fn()
        }))
      }))
    }))
  }))
}

describe('报告列表性能测试', () => {
  let wrapper
  let reportStore
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    reportStore = useReportStore()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  /**
   * 生成测试报告数据
   * @param {number} count 报告数量
   * @returns {Array} 报告数组
   */
  function generateTestReports(count) {
    const reports = []
    const hospitals = ['北京协和医院', '上海华山医院', '广州中山医院', '深圳人民医院']
    const doctors = ['张医生', '李医生', '王医生', '刘医生']
    const categories = Object.values(Constants.REPORT_CATEGORIES)

    for (let i = 0; i < count; i++) {
      const reportData = {
        id: `report_${i}`,
        userId: 'test_user',
        title: `检查报告 ${i + 1}`,
        hospital: hospitals[i % hospitals.length],
        doctor: doctors[i % doctors.length],
        checkDate: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        reportDate: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        category: categories[i % categories.length],
        items: generateTestItems(Math.floor(Math.random() * 20) + 5),
        originalImage: `/images/report_${i}.jpg`,
        syncStatus: Constants.SYNC_STATUS.LOCAL
      }

      reports.push(new Report(reportData))
    }

    return reports
  }

  /**
   * 生成测试检查项目
   * @param {number} count 项目数量
   * @returns {Array} 项目数组
   */
  function generateTestItems(count) {
    const items = []
    const itemNames = [
      '白细胞计数', '红细胞计数', '血红蛋白', '血小板计数',
      '总胆固醇', '甘油三酯', '血糖', '尿酸',
      '肌酐', '尿素氮', '丙氨酸氨基转移酶', '天门冬氨酸氨基转移酶'
    ]

    for (let i = 0; i < count; i++) {
      const itemData = {
        id: `item_${i}`,
        name: itemNames[i % itemNames.length],
        value: (Math.random() * 100).toFixed(2),
        unit: 'mg/dL',
        referenceRange: '3.5-5.5',
        isAbnormal: Math.random() > 0.7,
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE
      }

      items.push(new ReportItem(itemData))
    }

    return items
  }

  describe('虚拟滚动性能测试', () => {
    it('应该能够处理1000条报告数据', async () => {
      const startTime = performance.now()
      
      // 生成大量测试数据
      const reports = generateTestReports(1000)
      reportStore.setReports(reports)

      // 挂载组件
      wrapper = mount(ReportList, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-load-more': true,
            'VirtualList': {
              template: '<div class="virtual-list-mock"><slot /></div>',
              props: ['items', 'itemHeight', 'containerHeight', 'loading', 'hasMore']
            },
            'ReportCard': {
              template: '<div class="report-card-mock">{{ report.title }}</div>',
              props: ['report', 'showActions']
            }
          }
        }
      })

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // 验证渲染时间应该在合理范围内（小于100ms）
      expect(renderTime).toBeLessThan(100)
      
      // 验证数据正确加载
      expect(wrapper.vm.filteredReports).toHaveLength(1000)
      expect(wrapper.vm.statistics.totalReports).toBe(1000)
    })

    it('应该能够快速响应筛选操作', async () => {
      // 生成测试数据
      const reports = generateTestReports(500)
      reportStore.setReports(reports)

      wrapper = mount(ReportList, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-load-more': true,
            'VirtualList': true,
            'ReportCard': true
          }
        }
      })

      const startTime = performance.now()

      // 应用筛选条件
      await wrapper.vm.reportStore.updateFilter({
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
        abnormalOnly: true
      })

      await wrapper.vm.$nextTick()

      const endTime = performance.now()
      const filterTime = endTime - startTime

      // 筛选操作应该在50ms内完成
      expect(filterTime).toBeLessThan(50)
      
      // 验证筛选结果
      const filteredReports = wrapper.vm.filteredReports
      expect(filteredReports.length).toBeGreaterThan(0)
      expect(filteredReports.length).toBeLessThan(500)
    })

    it('应该能够高效处理搜索操作', async () => {
      // 生成测试数据
      const reports = generateTestReports(300)
      reportStore.setReports(reports)

      wrapper = mount(ReportList, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-load-more': true,
            'VirtualList': true,
            'ReportCard': true
          }
        }
      })

      const startTime = performance.now()

      // 模拟搜索输入
      wrapper.vm.searchKeyword = '协和'
      wrapper.vm.handleSearch()

      // 等待防抖完成
      await new Promise(resolve => setTimeout(resolve, 350))

      const endTime = performance.now()
      const searchTime = endTime - startTime

      // 搜索操作应该在合理时间内完成
      expect(searchTime).toBeLessThan(400)
      
      // 验证搜索建议生成
      expect(wrapper.vm.searchSuggestions.length).toBeGreaterThan(0)
    })
  })

  describe('内存使用测试', () => {
    it('应该正确清理组件内存', async () => {
      // 生成大量数据
      const reports = generateTestReports(100)
      reportStore.setReports(reports)

      wrapper = mount(ReportList, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-load-more': true,
            'VirtualList': true,
            'ReportCard': true
          }
        }
      })

      // 记录初始内存使用（模拟）
      const initialMemory = wrapper.vm.filteredReports.length

      // 清理定时器
      if (wrapper.vm.searchTimer) {
        clearTimeout(wrapper.vm.searchTimer)
      }

      // 卸载组件
      wrapper.unmount()

      // 验证清理完成
      expect(wrapper.vm.searchTimer).toBeNull()
    })

    it('应该避免内存泄漏', async () => {
      const reports = generateTestReports(50)
      reportStore.setReports(reports)

      // 多次挂载和卸载组件
      for (let i = 0; i < 5; i++) {
        const testWrapper = mount(ReportList, {
          global: {
            plugins: [pinia],
            stubs: {
              'uni-icons': true,
              'uni-popup': true,
              'uni-load-more': true,
              'VirtualList': true,
              'ReportCard': true
            }
          }
        })

        // 模拟一些操作
        testWrapper.vm.searchKeyword = `test${i}`
        testWrapper.vm.handleSearch()

        // 立即卸载
        testWrapper.unmount()
      }

      // 如果没有内存泄漏，这个测试应该正常完成
      expect(true).toBe(true)
    })
  })

  describe('滚动性能测试', () => {
    it('应该能够流畅处理滚动事件', async () => {
      const reports = generateTestReports(200)
      reportStore.setReports(reports)

      wrapper = mount(ReportList, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-load-more': true,
            'VirtualList': {
              template: '<div class="virtual-list-mock" @scroll="$emit(\'scroll\', $event)"><slot /></div>',
              props: ['items', 'itemHeight', 'containerHeight', 'loading', 'hasMore'],
              emits: ['scroll', 'loadMore']
            },
            'ReportCard': true
          }
        }
      })

      const startTime = performance.now()

      // 模拟多次滚动事件
      for (let i = 0; i < 10; i++) {
        const scrollEvent = {
          detail: {
            scrollTop: i * 100
          }
        }
        
        // 触发滚动事件（通过VirtualList组件）
        const virtualList = wrapper.findComponent({ name: 'VirtualList' })
        if (virtualList.exists()) {
          virtualList.vm.$emit('scroll', scrollEvent)
        }
      }

      const endTime = performance.now()
      const scrollTime = endTime - startTime

      // 滚动处理应该很快
      expect(scrollTime).toBeLessThan(50)
    })
  })

  describe('数据更新性能测试', () => {
    it('应该能够快速响应数据变化', async () => {
      const reports = generateTestReports(100)
      reportStore.setReports(reports)

      wrapper = mount(ReportList, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-load-more': true,
            'VirtualList': true,
            'ReportCard': true
          }
        }
      })

      const startTime = performance.now()

      // 添加新报告
      const newReport = new Report({
        id: 'new_report',
        userId: 'test_user',
        title: '新报告',
        hospital: '测试医院',
        checkDate: new Date(),
        reportDate: new Date(),
        items: generateTestItems(5)
      })

      reportStore.addReport(newReport)
      await wrapper.vm.$nextTick()

      const endTime = performance.now()
      const updateTime = endTime - startTime

      // 数据更新应该很快
      expect(updateTime).toBeLessThan(30)
      
      // 验证数据正确更新
      expect(wrapper.vm.statistics.totalReports).toBe(101)
    })

    it('应该能够高效处理批量删除', async () => {
      const reports = generateTestReports(50)
      reportStore.setReports(reports)

      wrapper = mount(ReportList, {
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-load-more': true,
            'VirtualList': true,
            'ReportCard': true
          }
        }
      })

      const startTime = performance.now()

      // 删除多个报告
      for (let i = 0; i < 10; i++) {
        reportStore.deleteReport(`report_${i}`)
      }

      await wrapper.vm.$nextTick()

      const endTime = performance.now()
      const deleteTime = endTime - startTime

      // 批量删除应该在合理时间内完成
      expect(deleteTime).toBeLessThan(100)
      
      // 验证删除结果
      expect(wrapper.vm.statistics.totalReports).toBe(40)
    })
  })

  describe('组件渲染性能基准测试', () => {
    const performanceThresholds = {
      smallDataSet: { count: 50, maxTime: 50 },
      mediumDataSet: { count: 200, maxTime: 100 },
      largeDataSet: { count: 500, maxTime: 200 }
    }

    Object.entries(performanceThresholds).forEach(([testName, config]) => {
      it(`应该在${config.maxTime}ms内渲染${config.count}条数据 (${testName})`, async () => {
        const reports = generateTestReports(config.count)
        reportStore.setReports(reports)

        const startTime = performance.now()

        wrapper = mount(ReportList, {
          global: {
            plugins: [pinia],
            stubs: {
              'uni-icons': true,
              'uni-popup': true,
              'uni-load-more': true,
              'VirtualList': true,
              'ReportCard': true
            }
          }
        })

        await wrapper.vm.$nextTick()

        const endTime = performance.now()
        const renderTime = endTime - startTime

        expect(renderTime).toBeLessThan(config.maxTime)
        expect(wrapper.vm.filteredReports).toHaveLength(config.count)
      })
    })
  })
})