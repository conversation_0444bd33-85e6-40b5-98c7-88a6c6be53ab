/**
 * 健康分析服务单元测试
 */

// 健康分析算法测试
class HealthAnalyticsService {
  constructor() {
    this.referenceRanges = {
      '血压': {
        '收缩压': { min: 90, max: 140, unit: 'mmHg' },
        '舒张压': { min: 60, max: 90, unit: 'mmHg' }
      }
    }
  }

  analyzeIndicator(indicator) {
    const { category, name, value, unit } = indicator
    const reference = this.referenceRanges[category]?.[name]
    
    if (!reference) {
      return {
        ...indicator,
        isAbnormal: false,
        status: 'unknown',
        suggestion: '暂无参考范围'
      }
    }

    const numValue = parseFloat(value)
    let status = 'normal'
    let suggestion = '指标正常'
    let isAbnormal = false

    if (numValue < reference.min) {
      status = 'low'
      suggestion = '偏低，建议咨询医生'
      isAbnormal = true
    } else if (numValue > reference.max) {
      status = 'high'
      suggestion = '偏高，建议咨询医生'
      isAbnormal = true
    }

    return {
      ...indicator,
      isAbnormal,
      status,
      suggestion,
      referenceRange: `${reference.min}-${reference.max} ${reference.unit}`
    }
  }

  analyzeReport(report) {
    const analyzedIndicators = report.indicators.map(indicator => 
      this.analyzeIndicator(indicator)
    )

    const abnormalIndicators = analyzedIndicators.filter(indicator => indicator.isAbnormal)
    const normalCount = analyzedIndicators.length - abnormalIndicators.length

    return {
      ...report,
      indicators: analyzedIndicators,
      analysis: {
        totalIndicators: analyzedIndicators.length,
        normalCount,
        abnormalCount: abnormalIndicators.length,
        abnormalRate: (abnormalIndicators.length / analyzedIndicators.length * 100).toFixed(1),
        riskLevel: this.calculateRiskLevel(abnormalIndicators.length, analyzedIndicators.length)
      }
    }
  }

  calculateRiskLevel(abnormalCount, totalCount) {
    const abnormalRate = abnormalCount / totalCount
    
    if (abnormalRate === 0) return 'low'
    if (abnormalRate <= 0.3) return 'medium'
    return 'high'
  }

  analyzeTrend(reports, indicatorName) {
    const sortedReports = reports.sort((a, b) => new Date(a.reportDate) - new Date(b.reportDate))
    
    const dataPoints = []
    sortedReports.forEach(report => {
      report.indicators.forEach(indicator => {
        if (indicator.name === indicatorName) {
          dataPoints.push({
            date: report.reportDate,
            value: parseFloat(indicator.value),
            unit: indicator.unit
          })
        }
      })
    })

    if (dataPoints.length < 2) {
      return {
        trend: 'insufficient_data',
        message: '数据不足，无法分析趋势'
      }
    }

    const firstValue = dataPoints[0].value
    const lastValue = dataPoints[dataPoints.length - 1].value
    const changeRate = ((lastValue - firstValue) / firstValue * 100).toFixed(1)
    
    let trend = 'stable'
    
    if (Math.abs(changeRate) > 10) {
      trend = changeRate > 0 ? 'increasing' : 'decreasing'
    }

    return {
      trend,
      changeRate: parseFloat(changeRate),
      dataPoints
    }
  }
}

const healthAnalyticsService = new HealthAnalyticsService()

// Mock uni-app API
global.uni = {
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  setClipboardData: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  canvasToTempFilePath: jest.fn(),
  createCanvasContext: jest.fn()
}

// Mock plus API
global.plus = {
  share: {
    getServices: jest.fn(() => []),
    sendWithSystem: jest.fn()
  },
  push: {
    createMessage: jest.fn(),
    remove: jest.fn()
  },
  io: {
    requestFileSystem: jest.fn(),
    PRIVATE_DOC: 'PRIVATE_DOC'
  }
}

describe('HealthAnalyticsService', () => {
  describe('analyzeIndicator', () => {
    test('应该正确分析正常指标', () => {
      const indicator = {
        category: '血压',
        name: '收缩压',
        value: '120',
        unit: 'mmHg'
      }

      const result = healthAnalyticsService.analyzeIndicator(indicator)

      expect(result.isAbnormal).toBe(false)
      expect(result.status).toBe('normal')
      expect(result.suggestion).toBe('指标正常')
    })

    test('应该正确分析异常指标（偏高）', () => {
      const indicator = {
        category: '血压',
        name: '收缩压',
        value: '160',
        unit: 'mmHg'
      }

      const result = healthAnalyticsService.analyzeIndicator(indicator)

      expect(result.isAbnormal).toBe(true)
      expect(result.status).toBe('high')
      expect(result.suggestion).toBe('偏高，建议咨询医生')
    })

    test('应该正确分析异常指标（偏低）', () => {
      const indicator = {
        category: '血压',
        name: '收缩压',
        value: '80',
        unit: 'mmHg'
      }

      const result = healthAnalyticsService.analyzeIndicator(indicator)

      expect(result.isAbnormal).toBe(true)
      expect(result.status).toBe('low')
      expect(result.suggestion).toBe('偏低，建议咨询医生')
    })

    test('应该处理未知指标', () => {
      const indicator = {
        category: '未知类别',
        name: '未知指标',
        value: '100',
        unit: 'unit'
      }

      const result = healthAnalyticsService.analyzeIndicator(indicator)

      expect(result.isAbnormal).toBe(false)
      expect(result.status).toBe('unknown')
      expect(result.suggestion).toBe('暂无参考范围')
    })
  })

  describe('analyzeReport', () => {
    test('应该正确分析健康报告', () => {
      const report = {
        id: 'report1',
        reportDate: '2024-01-01',
        indicators: [
          {
            category: '血压',
            name: '收缩压',
            value: '120',
            unit: 'mmHg'
          },
          {
            category: '血压',
            name: '舒张压',
            value: '80',
            unit: 'mmHg'
          }
        ]
      }

      const result = healthAnalyticsService.analyzeReport(report)

      expect(result.analysis.totalIndicators).toBe(2)
      expect(result.analysis.normalCount).toBe(2)
      expect(result.analysis.abnormalCount).toBe(0)
      expect(result.analysis.riskLevel).toBe('low')
    })

    test('应该正确计算异常率', () => {
      const report = {
        id: 'report1',
        reportDate: '2024-01-01',
        indicators: [
          {
            category: '血压',
            name: '收缩压',
            value: '160',
            unit: 'mmHg'
          },
          {
            category: '血压',
            name: '舒张压',
            value: '80',
            unit: 'mmHg'
          }
        ]
      }

      const result = healthAnalyticsService.analyzeReport(report)

      expect(result.analysis.abnormalCount).toBe(1)
      expect(result.analysis.abnormalRate).toBe('50.0')
      expect(result.analysis.riskLevel).toBe('high')
    })
  })

  describe('analyzeTrend', () => {
    test('应该正确分析上升趋势', () => {
      const reports = [
        {
          reportDate: '2024-01-01',
          indicators: [{ name: '收缩压', value: '120', unit: 'mmHg' }]
        },
        {
          reportDate: '2024-02-01',
          indicators: [{ name: '收缩压', value: '140', unit: 'mmHg' }]
        }
      ]

      const result = healthAnalyticsService.analyzeTrend(reports, '收缩压')

      expect(result.trend).toBe('increasing')
      expect(result.changeRate).toBe(16.7)
    })

    test('应该处理数据不足的情况', () => {
      const reports = [
        {
          reportDate: '2024-01-01',
          indicators: [{ name: '收缩压', value: '120', unit: 'mmHg' }]
        }
      ]

      const result = healthAnalyticsService.analyzeTrend(reports, '收缩压')

      expect(result.trend).toBe('insufficient_data')
      expect(result.message).toBe('数据不足，无法分析趋势')
    })
  })
})

// 基础功能测试
describe('基础分析功能', () => {
  test('应该能够正确识别异常指标', () => {
    const abnormalIndicators = [
      { name: '收缩压', value: 160, isAbnormal: true },
      { name: '舒张压', value: 70, isAbnormal: false }
    ]
    
    const abnormalCount = abnormalIndicators.filter(i => i.isAbnormal).length
    expect(abnormalCount).toBe(1)
  })

  test('应该能够计算异常率', () => {
    const totalIndicators = 4
    const abnormalIndicators = 1
    const abnormalRate = (abnormalIndicators / totalIndicators * 100).toFixed(1)
    
    expect(abnormalRate).toBe('25.0')
  })

  test('应该能够生成健康建议', () => {
    const categories = ['血压', '血糖']
    const recommendations = []
    
    categories.forEach(category => {
      if (category === '血压') {
        recommendations.push('注意控制血压，减少盐分摄入')
      }
      if (category === '血糖') {
        recommendations.push('控制血糖水平，注意饮食结构')
      }
    })
    
    expect(recommendations).toHaveLength(2)
    expect(recommendations[0]).toContain('血压')
    expect(recommendations[1]).toContain('血糖')
  })
})