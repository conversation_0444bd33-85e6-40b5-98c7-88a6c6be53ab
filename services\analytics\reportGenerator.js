/**
 * 健康报告生成服务
 * 负责生成PDF格式的健康报告和数据导出功能
 */

class ReportGeneratorService {
  constructor() {
    this.reportTemplate = {
      title: '个人健康检查报告',
      subtitle: '健康数据分析与建议',
      footer: '本报告仅供参考，具体诊断请咨询专业医生'
    }
  }

  /**
   * 生成PDF健康报告
   * @param {Object} reportData 报告数据
   * @param {Object} options 生成选项
   * @returns {Promise<string>} PDF文件路径
   */
  async generatePDFReport(reportData, options = {}) {
    try {
      const {
        reports,
        timeRange,
        includeCharts = true,
        includeAnalysis = true,
        includeRecommendations = true
      } = reportData

      // 生成HTML内容
      const htmlContent = this.generateHTMLContent(reports, timeRange, {
        includeCharts,
        includeAnalysis,
        includeRecommendations
      })

      // 转换为PDF
      const pdfPath = await this.convertHTMLToPDF(htmlContent, options)
      
      return pdfPath
    } catch (error) {
      console.error('生成PDF报告失败:', error)
      throw new Error('PDF报告生成失败')
    }
  }

  /**
   * 生成HTML内容
   * @param {Array} reports 报告列表
   * @param {Object} timeRange 时间范围
   * @param {Object} options 选项
   * @returns {string} HTML内容
   */
  generateHTMLContent(reports, timeRange, options) {
    const { includeCharts, includeAnalysis, includeRecommendations } = options
    
    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${this.reportTemplate.title}</title>
        <style>
          ${this.getReportStyles()}
        </style>
      </head>
      <body>
        <div class="report-container">
          ${this.generateHeader(timeRange)}
          ${this.generateSummarySection(reports)}
          ${this.generateReportsSection(reports)}
          ${includeAnalysis ? this.generateAnalysisSection(reports) : ''}
          ${includeCharts ? this.generateChartsSection(reports) : ''}
          ${includeRecommendations ? this.generateRecommendationsSection(reports) : ''}
          ${this.generateFooter()}
        </div>
      </body>
      </html>
    `

    return html
  }

  /**
   * 获取报告样式
   * @returns {string} CSS样式
   */
  getReportStyles() {
    return `
      body {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      
      .report-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }
      
      .header {
        text-align: center;
        border-bottom: 2px solid #4CAF50;
        padding-bottom: 20px;
        margin-bottom: 30px;
      }
      
      .header h1 {
        color: #2E7D32;
        margin: 0;
        font-size: 28px;
      }
      
      .header h2 {
        color: #666;
        margin: 10px 0 0 0;
        font-size: 16px;
        font-weight: normal;
      }
      
      .section {
        margin-bottom: 30px;
      }
      
      .section-title {
        color: #2E7D32;
        font-size: 20px;
        margin-bottom: 15px;
        border-left: 4px solid #4CAF50;
        padding-left: 10px;
      }
      
      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
      }
      
      .summary-card {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        text-align: center;
      }
      
      .summary-card .number {
        font-size: 24px;
        font-weight: bold;
        color: #2E7D32;
      }
      
      .summary-card .label {
        color: #666;
        font-size: 14px;
        margin-top: 5px;
      }
      
      .report-item {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        margin-bottom: 20px;
        overflow: hidden;
      }
      
      .report-header {
        background: #f5f5f5;
        padding: 15px;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .report-date {
        font-weight: bold;
        color: #2E7D32;
      }
      
      .report-hospital {
        color: #666;
        font-size: 14px;
      }
      
      .indicators-table {
        width: 100%;
        border-collapse: collapse;
      }
      
      .indicators-table th,
      .indicators-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .indicators-table th {
        background: #f8f9fa;
        font-weight: bold;
        color: #333;
      }
      
      .indicator-normal {
        color: #4CAF50;
      }
      
      .indicator-abnormal {
        color: #F44336;
        font-weight: bold;
      }
      
      .analysis-item {
        background: #fff3e0;
        border-left: 4px solid #FF9800;
        padding: 15px;
        margin-bottom: 15px;
      }
      
      .recommendation-item {
        background: #e8f5e8;
        border-left: 4px solid #4CAF50;
        padding: 15px;
        margin-bottom: 10px;
      }
      
      .footer {
        text-align: center;
        color: #666;
        font-size: 12px;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e0e0e0;
      }
      
      .chart-placeholder {
        background: #f5f5f5;
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        border: 1px dashed #ccc;
        margin: 20px 0;
      }
      
      @media print {
        body { background: white; }
        .report-container { box-shadow: none; }
      }
    `
  }

  /**
   * 生成报告头部
   * @param {Object} timeRange 时间范围
   * @returns {string} HTML内容
   */
  generateHeader(timeRange) {
    const startDate = new Date(timeRange.start).toLocaleDateString('zh-CN')
    const endDate = new Date(timeRange.end).toLocaleDateString('zh-CN')
    
    return `
      <div class="header">
        <h1>${this.reportTemplate.title}</h1>
        <h2>${this.reportTemplate.subtitle}</h2>
        <p>报告时间范围：${startDate} - ${endDate}</p>
        <p>生成时间：${new Date().toLocaleString('zh-CN')}</p>
      </div>
    `
  }

  /**
   * 生成摘要部分
   * @param {Array} reports 报告列表
   * @returns {string} HTML内容
   */
  generateSummarySection(reports) {
    const totalReports = reports.length
    const totalIndicators = reports.reduce((sum, report) => sum + report.indicators.length, 0)
    const abnormalReports = reports.filter(report => 
      report.indicators.some(indicator => indicator.isAbnormal)
    ).length
    const abnormalIndicators = reports.reduce((sum, report) => 
      sum + report.indicators.filter(indicator => indicator.isAbnormal).length, 0
    )

    return `
      <div class="section">
        <h3 class="section-title">检查概况</h3>
        <div class="summary-grid">
          <div class="summary-card">
            <div class="number">${totalReports}</div>
            <div class="label">检查次数</div>
          </div>
          <div class="summary-card">
            <div class="number">${totalIndicators}</div>
            <div class="label">检查项目</div>
          </div>
          <div class="summary-card">
            <div class="number">${abnormalReports}</div>
            <div class="label">异常报告</div>
          </div>
          <div class="summary-card">
            <div class="number">${abnormalIndicators}</div>
            <div class="label">异常指标</div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * 生成报告详情部分
   * @param {Array} reports 报告列表
   * @returns {string} HTML内容
   */
  generateReportsSection(reports) {
    let html = `
      <div class="section">
        <h3 class="section-title">检查详情</h3>
    `

    reports.forEach(report => {
      html += `
        <div class="report-item">
          <div class="report-header">
            <div class="report-date">${new Date(report.reportDate).toLocaleDateString('zh-CN')}</div>
            <div class="report-hospital">${report.hospitalName}</div>
          </div>
          <table class="indicators-table">
            <thead>
              <tr>
                <th>检查项目</th>
                <th>检查结果</th>
                <th>参考范围</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
      `

      report.indicators.forEach(indicator => {
        const statusClass = indicator.isAbnormal ? 'indicator-abnormal' : 'indicator-normal'
        const statusText = indicator.isAbnormal ? '异常' : '正常'
        
        html += `
          <tr>
            <td>${indicator.name}</td>
            <td>${indicator.value} ${indicator.unit}</td>
            <td>${indicator.referenceRange || '-'}</td>
            <td class="${statusClass}">${statusText}</td>
          </tr>
        `
      })

      html += `
            </tbody>
          </table>
        </div>
      `
    })

    html += `</div>`
    return html
  }

  /**
   * 生成分析部分
   * @param {Array} reports 报告列表
   * @returns {string} HTML内容
   */
  generateAnalysisSection(reports) {
    // 这里可以调用分析服务生成分析结果
    const analysisResults = [
      '血压指标在近期检查中呈上升趋势，建议注意控制',
      '血糖水平相对稳定，继续保持良好的饮食习惯',
      '肝功能指标正常，肝脏健康状况良好'
    ]

    let html = `
      <div class="section">
        <h3 class="section-title">健康分析</h3>
    `

    analysisResults.forEach(analysis => {
      html += `<div class="analysis-item">${analysis}</div>`
    })

    html += `</div>`
    return html
  }

  /**
   * 生成图表部分
   * @param {Array} reports 报告列表
   * @returns {string} HTML内容
   */
  generateChartsSection(reports) {
    return `
      <div class="section">
        <h3 class="section-title">趋势图表</h3>
        <div class="chart-placeholder">
          图表将在实际实现中显示指标趋势
        </div>
      </div>
    `
  }

  /**
   * 生成建议部分
   * @param {Array} reports 报告列表
   * @returns {string} HTML内容
   */
  generateRecommendationsSection(reports) {
    const recommendations = [
      '定期进行健康检查，建议每3-6个月检查一次',
      '保持均衡饮食，减少高盐、高糖、高脂食物摄入',
      '适量运动，每周至少150分钟中等强度有氧运动',
      '保持良好作息，避免熬夜和过度劳累',
      '如有异常指标，及时咨询专业医生'
    ]

    let html = `
      <div class="section">
        <h3 class="section-title">健康建议</h3>
    `

    recommendations.forEach(recommendation => {
      html += `<div class="recommendation-item">${recommendation}</div>`
    })

    html += `</div>`
    return html
  }

  /**
   * 生成页脚
   * @returns {string} HTML内容
   */
  generateFooter() {
    return `
      <div class="footer">
        <p>${this.reportTemplate.footer}</p>
        <p>报告生成时间：${new Date().toLocaleString('zh-CN')}</p>
      </div>
    `
  }

  /**
   * 将HTML转换为PDF
   * @param {string} htmlContent HTML内容
   * @param {Object} options 选项
   * @returns {Promise<string>} PDF文件路径
   */
  async convertHTMLToPDF(htmlContent, options = {}) {
    try {
      // #ifdef APP-PLUS
      return await this.convertHTMLToPDFApp(htmlContent, options)
      // #endif

      // #ifdef H5
      return await this.convertHTMLToPDFH5(htmlContent, options)
      // #endif

      // #ifdef MP-WEIXIN
      // 微信小程序不支持直接生成PDF，返回HTML内容用于分享
      return this.saveHTMLForShare(htmlContent)
      // #endif

      throw new Error('当前平台不支持PDF生成')
    } catch (error) {
      console.error('PDF转换失败:', error)
      throw error
    }
  }

  /**
   * APP平台PDF转换
   * @param {string} htmlContent HTML内容
   * @param {Object} options 选项
   * @returns {Promise<string>} PDF文件路径
   */
  async convertHTMLToPDFApp(htmlContent, options) {
    return new Promise((resolve, reject) => {
      const fileName = `health_report_${Date.now()}.pdf`
      const filePath = `_doc/reports/${fileName}`

      // 使用plus.io创建文件
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
        fs.root.getDirectory('reports', { create: true }, (dirEntry) => {
          dirEntry.getFile(fileName, { create: true }, (fileEntry) => {
            fileEntry.createWriter((writer) => {
              // 这里需要使用第三方库将HTML转换为PDF
              // 由于uni-app限制，这里提供一个简化的实现
              writer.write(htmlContent)
              writer.onwriteend = () => {
                resolve(fileEntry.fullPath)
              }
              writer.onerror = reject
            })
          }, reject)
        }, reject)
      }, reject)
    })
  }

  /**
   * H5平台PDF转换
   * @param {string} htmlContent HTML内容
   * @param {Object} options 选项
   * @returns {Promise<string>} PDF文件路径
   */
  async convertHTMLToPDFH5(htmlContent, options) {
    // H5平台可以使用jsPDF或html2pdf.js
    // 这里提供一个简化的实现
    const blob = new Blob([htmlContent], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `health_report_${Date.now()}.html`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    return url
  }

  /**
   * 保存HTML用于分享
   * @param {string} htmlContent HTML内容
   * @returns {string} 文件路径
   */
  saveHTMLForShare(htmlContent) {
    try {
      const fileName = `health_report_${Date.now()}.html`
      uni.setStorageSync(`report_${fileName}`, htmlContent)
      return fileName
    } catch (error) {
      console.error('保存HTML文件失败:', error)
      throw error
    }
  }

  /**
   * 导出JSON格式数据
   * @param {Object} data 要导出的数据
   * @param {string} fileName 文件名
   * @returns {Promise<string>} 文件路径
   */
  async exportToJSON(data, fileName = null) {
    try {
      const exportData = {
        exportTime: new Date().toISOString(),
        version: '1.0',
        data: data
      }

      const jsonString = JSON.stringify(exportData, null, 2)
      const finalFileName = fileName || `health_data_${Date.now()}.json`

      // #ifdef APP-PLUS
      return await this.saveJSONFileApp(jsonString, finalFileName)
      // #endif

      // #ifdef H5
      return this.downloadJSONFileH5(jsonString, finalFileName)
      // #endif

      // #ifdef MP-WEIXIN
      // 微信小程序保存到本地存储
      uni.setStorageSync(`export_${finalFileName}`, jsonString)
      return finalFileName
      // #endif

    } catch (error) {
      console.error('JSON导出失败:', error)
      throw new Error('数据导出失败')
    }
  }

  /**
   * APP平台保存JSON文件
   * @param {string} jsonString JSON字符串
   * @param {string} fileName 文件名
   * @returns {Promise<string>} 文件路径
   */
  async saveJSONFileApp(jsonString, fileName) {
    return new Promise((resolve, reject) => {
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
        fs.root.getDirectory('exports', { create: true }, (dirEntry) => {
          dirEntry.getFile(fileName, { create: true }, (fileEntry) => {
            fileEntry.createWriter((writer) => {
              writer.write(jsonString)
              writer.onwriteend = () => resolve(fileEntry.fullPath)
              writer.onerror = reject
            })
          }, reject)
        }, reject)
      }, reject)
    })
  }

  /**
   * H5平台下载JSON文件
   * @param {string} jsonString JSON字符串
   * @param {string} fileName 文件名
   * @returns {string} 下载URL
   */
  downloadJSONFileH5(jsonString, fileName) {
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    return url
  }
}

export default new ReportGeneratorService()