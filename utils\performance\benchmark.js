/**
 * 性能基准测试和优化验证工具
 */

class PerformanceBenchmark {
    constructor() {
        this.benchmarks = new Map()
        this.results = []
        this.isRunning = false
        this.currentTest = null
    }

    /**
     * 注册基准测试
     */
    registerBenchmark(name, testFunction, options = {}) {
        this.benchmarks.set(name, {
            name,
            testFunction,
            iterations: options.iterations || 100,
            warmupIterations: options.warmupIterations || 10,
            timeout: options.timeout || 5000,
            description: options.description || '',
            category: options.category || 'general'
        })
    }

    /**
     * 运行单个基准测试
     */
    async runBenchmark(name) {
        const benchmark = this.benchmarks.get(name)
        if (!benchmark) {
            throw new Error(`基准测试不存在: ${name}`)
        }

        console.log(`开始运行基准测试: ${name}`)
        this.currentTest = name

        const result = {
            name,
            category: benchmark.category,
            description: benchmark.description,
            startTime: Date.now(),
            endTime: null,
            iterations: benchmark.iterations,
            times: [],
            stats: null,
            error: null
        }

        try {
            // 预热阶段
            await this.warmupBenchmark(benchmark)

            // 执行测试
            for (let i = 0; i < benchmark.iterations; i++) {
                const startTime = performance.now()

                await Promise.race([
                    benchmark.testFunction(),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('测试超时')), benchmark.timeout)
                    )
                ])

                const endTime = performance.now()
                result.times.push(endTime - startTime)

                // 给主线程一些时间
                if (i % 10 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 1))
                }
            }

            result.endTime = Date.now()
            result.stats = this.calculateStats(result.times)

            console.log(`基准测试完成: ${name}`, result.stats)

        } catch (error) {
            result.error = error.message
            result.endTime = Date.now()
            console.error(`基准测试失败: ${name}`, error)
        }

        this.results.push(result)
        this.currentTest = null
        return result
    }

    /**
     * 预热基准测试
     */
    async warmupBenchmark(benchmark) {
        console.log(`预热基准测试: ${benchmark.name}`)

        for (let i = 0; i < benchmark.warmupIterations; i++) {
            try {
                await benchmark.testFunction()
            } catch (error) {
                // 预热阶段忽略错误
            }
        }
    }

    /**
     * 运行所有基准测试
     */
    async runAllBenchmarks() {
        if (this.isRunning) {
            throw new Error('基准测试正在运行中')
        }

        this.isRunning = true
        this.results = []

        try {
            const benchmarkNames = Array.from(this.benchmarks.keys())

            for (const name of benchmarkNames) {
                await this.runBenchmark(name)

                // 测试间隔，避免影响下一个测试
                await new Promise(resolve => setTimeout(resolve, 1000))
            }

            return this.generateReport()

        } finally {
            this.isRunning = false
        }
    }

    /**
     * 计算统计数据
     */
    calculateStats(times) {
        if (times.length === 0) return null

        const sorted = [...times].sort((a, b) => a - b)
        const sum = times.reduce((a, b) => a + b, 0)

        return {
            count: times.length,
            min: Math.min(...times),
            max: Math.max(...times),
            mean: sum / times.length,
            median: this.calculateMedian(sorted),
            p95: this.calculatePercentile(sorted, 0.95),
            p99: this.calculatePercentile(sorted, 0.99),
            stdDev: this.calculateStandardDeviation(times, sum / times.length)
        }
    }

    /**
     * 计算中位数
     */
    calculateMedian(sortedArray) {
        const mid = Math.floor(sortedArray.length / 2)
        return sortedArray.length % 2 === 0
            ? (sortedArray[mid - 1] + sortedArray[mid]) / 2
            : sortedArray[mid]
    }

    /**
     * 计算百分位数
     */
    calculatePercentile(sortedArray, percentile) {
        const index = Math.ceil(sortedArray.length * percentile) - 1
        return sortedArray[Math.max(0, index)]
    }

    /**
     * 计算标准差
     */
    calculateStandardDeviation(values, mean) {
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2))
        const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length
        return Math.sqrt(avgSquaredDiff)
    }

    /**
     * 生成测试报告
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalTests: this.results.length,
            passedTests: this.results.filter(r => !r.error).length,
            failedTests: this.results.filter(r => r.error).length,
            categories: {},
            results: this.results,
            summary: {}
        }

        // 按类别分组
        this.results.forEach(result => {
            if (!report.categories[result.category]) {
                report.categories[result.category] = []
            }
            report.categories[result.category].push(result)
        })

        // 生成摘要
        report.summary = this.generateSummary(this.results)

        return report
    }

    /**
     * 生成摘要
     */
    generateSummary(results) {
        const validResults = results.filter(r => !r.error && r.stats)

        if (validResults.length === 0) {
            return { message: '没有有效的测试结果' }
        }

        const allTimes = validResults.flatMap(r => r.times)
        const overallStats = this.calculateStats(allTimes)

        return {
            totalIterations: allTimes.length,
            overallStats,
            slowestTest: validResults.reduce((slowest, current) =>
                current.stats.mean > slowest.stats.mean ? current : slowest
            ),
            fastestTest: validResults.reduce((fastest, current) =>
                current.stats.mean < fastest.stats.mean ? current : fastest
            )
        }
    }

    /**
     * 比较两次测试结果
     */
    compareResults(oldResults, newResults) {
        const comparison = {
            timestamp: new Date().toISOString(),
            improvements: [],
            regressions: [],
            unchanged: []
        }

        newResults.forEach(newResult => {
            const oldResult = oldResults.find(r => r.name === newResult.name)

            if (!oldResult || !oldResult.stats || !newResult.stats) {
                return
            }

            const oldMean = oldResult.stats.mean
            const newMean = newResult.stats.mean
            const changePercent = ((newMean - oldMean) / oldMean) * 100

            const changeData = {
                name: newResult.name,
                oldMean,
                newMean,
                changePercent: Math.round(changePercent * 100) / 100,
                changeMs: Math.round((newMean - oldMean) * 100) / 100
            }

            if (Math.abs(changePercent) < 5) {
                comparison.unchanged.push(changeData)
            } else if (changePercent < 0) {
                comparison.improvements.push(changeData)
            } else {
                comparison.regressions.push(changeData)
            }
        })

        return comparison
    }

    /**
     * 获取测试结果
     */
    getResults() {
        return this.results
    }

    /**
     * 清除测试结果
     */
    clearResults() {
        this.results = []
    }

    /**
     * 停止当前测试
     */
    stopCurrentTest() {
        if (this.currentTest) {
            console.log(`停止当前测试: ${this.currentTest}`)
            this.isRunning = false
            this.currentTest = null
        }
    }
}

// 应用性能基准测试套件
class AppPerformanceBenchmark extends PerformanceBenchmark {
    constructor() {
        super()
        this.setupAppBenchmarks()
    }

    /**
     * 设置应用基准测试
     */
    setupAppBenchmarks() {
        // 页面加载性能测试
        this.registerBenchmark('pageLoad', async () => {
            const startTime = Date.now()

            // 模拟页面加载
            await new Promise(resolve => {
                setTimeout(() => {
                    // 模拟DOM渲染和数据加载
                    const mockData = new Array(100).fill(0).map((_, i) => ({
                        id: i,
                        name: `测试数据${i}`,
                        value: Math.random() * 1000
                    }))
                    resolve(mockData)
                }, Math.random() * 100 + 50)
            })

            return Date.now() - startTime
        }, {
            category: 'ui',
            description: '页面加载性能测试',
            iterations: 50
        })

        // 数据库查询性能测试
        this.registerBenchmark('databaseQuery', async () => {
            const startTime = Date.now()

            // 模拟数据库查询
            await new Promise(resolve => {
                setTimeout(() => {
                    // 模拟查询结果
                    const results = new Array(50).fill(0).map((_, i) => ({
                        id: i,
                        data: `查询结果${i}`
                    }))
                    resolve(results)
                }, Math.random() * 50 + 10)
            })

            return Date.now() - startTime
        }, {
            category: 'database',
            description: '数据库查询性能测试',
            iterations: 100
        })

        // 图片加载性能测试
        this.registerBenchmark('imageLoad', async () => {
            const startTime = Date.now()

            // 模拟图片加载
            return new Promise((resolve) => {
                const img = new Image()
                img.onload = () => {
                    resolve(Date.now() - startTime)
                }
                img.onerror = () => {
                    resolve(Date.now() - startTime)
                }
                // 使用一个小的测试图片
                img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
            })
        }, {
            category: 'network',
            description: '图片加载性能测试',
            iterations: 30
        })

        // 列表渲染性能测试
        this.registerBenchmark('listRender', async () => {
            const startTime = Date.now()

            // 模拟大列表渲染
            const listData = new Array(1000).fill(0).map((_, i) => ({
                id: i,
                title: `列表项${i}`,
                content: `这是第${i}个列表项的内容`,
                timestamp: Date.now() + i
            }))

            // 模拟DOM操作时间
            await new Promise(resolve => setTimeout(resolve, Math.random() * 20 + 5))

            return Date.now() - startTime
        }, {
            category: 'ui',
            description: '列表渲染性能测试',
            iterations: 20
        })

        // 缓存性能测试
        this.registerBenchmark('cachePerformance', async () => {
            const startTime = Date.now()

            // 模拟缓存操作
            const cacheKey = `test_${Math.random()}`
            const cacheData = { data: new Array(100).fill('测试数据') }

            // 写入缓存
            uni.setStorageSync(cacheKey, cacheData)

            // 读取缓存
            const retrieved = uni.getStorageSync(cacheKey)

            // 删除缓存
            uni.removeStorageSync(cacheKey)

            return Date.now() - startTime
        }, {
            category: 'storage',
            description: '缓存操作性能测试',
            iterations: 200
        })

        // 网络请求性能测试
        this.registerBenchmark('networkRequest', async () => {
            const startTime = Date.now()

            // 模拟网络请求
            return new Promise((resolve) => {
                uni.request({
                    url: 'https://httpbin.org/delay/0',
                    method: 'GET',
                    success: () => {
                        resolve(Date.now() - startTime)
                    },
                    fail: () => {
                        resolve(Date.now() - startTime)
                    }
                })
            })
        }, {
            category: 'network',
            description: '网络请求性能测试',
            iterations: 10,
            timeout: 10000
        })
    }

    /**
     * 运行核心性能测试
     */
    async runCorePerformanceTests() {
        const coreTests = ['pageLoad', 'databaseQuery', 'listRender', 'cachePerformance']
        const results = []

        for (const testName of coreTests) {
            try {
                const result = await this.runBenchmark(testName)
                results.push(result)
            } catch (error) {
                console.error(`核心性能测试失败: ${testName}`, error)
            }
        }

        return results
    }

    /**
     * 生成性能报告
     */
    generatePerformanceReport() {
        const report = this.generateReport()

        // 添加性能评级
        report.performanceGrade = this.calculatePerformanceGrade(report.results)

        // 添加优化建议
        report.optimizationSuggestions = this.generateOptimizationSuggestions(report.results)

        return report
    }

    /**
     * 计算性能评级
     */
    calculatePerformanceGrade(results) {
        const validResults = results.filter(r => !r.error && r.stats)

        if (validResults.length === 0) {
            return { grade: 'N/A', score: 0 }
        }

        let totalScore = 0
        let maxScore = 0

        validResults.forEach(result => {
            const category = result.category
            const mean = result.stats.mean

            let score = 0
            let weight = 1

            // 根据类别和性能指标计算分数
            switch (category) {
                case 'ui':
                    weight = 2 // UI性能权重更高
                    score = mean < 100 ? 100 : Math.max(0, 200 - mean)
                    break
                case 'database':
                    score = mean < 50 ? 100 : Math.max(0, 150 - mean)
                    break
                case 'network':
                    score = mean < 200 ? 100 : Math.max(0, 500 - mean)
                    break
                case 'storage':
                    score = mean < 10 ? 100 : Math.max(0, 50 - mean)
                    break
                default:
                    score = mean < 100 ? 100 : Math.max(0, 200 - mean)
            }

            totalScore += score * weight
            maxScore += 100 * weight
        })

        const finalScore = Math.round((totalScore / maxScore) * 100)

        let grade = 'F'
        if (finalScore >= 90) grade = 'A'
        else if (finalScore >= 80) grade = 'B'
        else if (finalScore >= 70) grade = 'C'
        else if (finalScore >= 60) grade = 'D'

        return { grade, score: finalScore }
    }

    /**
     * 生成优化建议
     */
    generateOptimizationSuggestions(results) {
        const suggestions = []

        results.forEach(result => {
            if (result.error || !result.stats) return

            const mean = result.stats.mean
            const category = result.category

            switch (category) {
                case 'ui':
                    if (mean > 200) {
                        suggestions.push({
                            category: 'UI性能',
                            issue: `${result.name} 渲染时间过长 (${mean.toFixed(2)}ms)`,
                            suggestion: '考虑使用虚拟滚动、懒加载或减少DOM操作'
                        })
                    }
                    break
                case 'database':
                    if (mean > 100) {
                        suggestions.push({
                            category: '数据库性能',
                            issue: `${result.name} 查询时间过长 (${mean.toFixed(2)}ms)`,
                            suggestion: '考虑添加索引、优化查询语句或使用缓存'
                        })
                    }
                    break
                case 'network':
                    if (mean > 1000) {
                        suggestions.push({
                            category: '网络性能',
                            issue: `${result.name} 网络请求时间过长 (${mean.toFixed(2)}ms)`,
                            suggestion: '考虑使用CDN、压缩数据或实现请求缓存'
                        })
                    }
                    break
                case 'storage':
                    if (mean > 20) {
                        suggestions.push({
                            category: '存储性能',
                            issue: `${result.name} 存储操作时间过长 (${mean.toFixed(2)}ms)`,
                            suggestion: '考虑减少存储数据量或使用更高效的存储方式'
                        })
                    }
                    break
            }
        })

        return suggestions
    }
}

// 性能基准测试混入
export const benchmarkMixin = {
    data() {
        return {
            benchmark: null,
            benchmarkResults: null
        }
    },

    created() {
        this.benchmark = new AppPerformanceBenchmark()
    },

    methods: {
        // 运行性能测试
        async runPerformanceTest(testName) {
            try {
                const result = await this.benchmark.runBenchmark(testName)
                return result
            } catch (error) {
                console.error('性能测试失败:', error)
                throw error
            }
        },

        // 运行所有性能测试
        async runAllPerformanceTests() {
            try {
                this.benchmarkResults = await this.benchmark.runAllBenchmarks()
                return this.benchmarkResults
            } catch (error) {
                console.error('性能测试套件失败:', error)
                throw error
            }
        },

        // 运行核心性能测试
        async runCoreTests() {
            try {
                const results = await this.benchmark.runCorePerformanceTests()
                return results
            } catch (error) {
                console.error('核心性能测试失败:', error)
                throw error
            }
        },

        // 生成性能报告
        generatePerformanceReport() {
            return this.benchmark.generatePerformanceReport()
        },

        // 比较性能结果
        comparePerformance(oldResults, newResults) {
            return this.benchmark.compareResults(oldResults, newResults)
        }
    }
}

export { PerformanceBenchmark, AppPerformanceBenchmark }