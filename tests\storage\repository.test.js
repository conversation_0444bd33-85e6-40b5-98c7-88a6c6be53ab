/**
 * 仓储类单元测试
 */

import {
  UserRepository,
  HealthReportRepository,
  HealthIndicatorRepository,
  SyncRecordRepository
} from '../../utils/storage/repository.js';

// Mock uni-app API
global.uni = {
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn()
};

// Mock crypto API
global.crypto = {
  getRandomValues: jest.fn((array) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  })
};

global.btoa = jest.fn((str) => Buffer.from(str, 'binary').toString('base64'));
global.atob = jest.fn((str) => Buffer.from(str, 'base64').toString('binary'));

describe('Repository Classes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    global.uni.getStorageSync.mockReturnValue(null);
  });

  describe('UserRepository', () => {
    let userRepo;

    beforeEach(() => {
      userRepo = new UserRepository();
    });

    test('应该能够创建用户', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      };

      const user = await userRepo.createUser(userData);

      expect(user.username).toBe('testuser');
      expect(user.email).toBe('<EMAIL>');
      expect(user.password).toBeUndefined(); // 密码应该被移除
      expect(user.id).toBeDefined();
    });

    test('应该能够验证用户密码', async () => {
      // 先创建用户
      const userData = {
        username: 'testuser',
        password: 'password123'
      };
      await userRepo.createUser(userData);

      // 验证正确密码
      const validUser = await userRepo.validatePassword('testuser', 'password123');
      expect(validUser).not.toBeNull();
      expect(validUser.username).toBe('testuser');

      // 验证错误密码
      const invalidUser = await userRepo.validatePassword('testuser', 'wrongpassword');
      expect(invalidUser).toBeNull();
    });

    test('应该能够根据用户名查找用户', async () => {
      const userData = {
        username: 'testuser',
        password: 'password123'
      };
      await userRepo.createUser(userData);

      const user = await userRepo.findByUsername('testuser');
      expect(user).not.toBeNull();
      expect(user.username).toBe('testuser');
    });

    test('应该能够根据手机号查找用户', async () => {
      const userData = {
        username: 'testuser',
        phone: '13800138000',
        password: 'password123'
      };
      await userRepo.createUser(userData);

      const user = await userRepo.findByPhone('13800138000');
      expect(user).not.toBeNull();
      expect(user.phone).toBe('13800138000');
    });

    test('应该能够更新用户密码', async () => {
      const userData = {
        username: 'testuser',
        password: 'oldpassword'
      };
      const user = await userRepo.createUser(userData);

      const result = await userRepo.updatePassword(user.id, 'newpassword');
      expect(result).toBe(true);

      // 验证新密码
      const validUser = await userRepo.validatePassword('testuser', 'newpassword');
      expect(validUser).not.toBeNull();

      // 验证旧密码失效
      const invalidUser = await userRepo.validatePassword('testuser', 'oldpassword');
      expect(invalidUser).toBeNull();
    });
  });

  describe('HealthReportRepository', () => {
    let reportRepo;
    let userRepo;

    beforeEach(() => {
      reportRepo = new HealthReportRepository();
      userRepo = new UserRepository();
    });

    test('应该能够创建健康报告', async () => {
      const reportData = {
        user_id: 1,
        report_title: '体检报告',
        report_date: '2024-01-01',
        hospital_name: '测试医院'
      };

      const report = await reportRepo.create(reportData);

      expect(report.report_title).toBe('体检报告');
      expect(report.user_id).toBe(1);
      expect(report.id).toBeDefined();
    });

    test('应该能够根据用户ID查找报告', async () => {
      // 创建测试报告
      await reportRepo.create({
        user_id: 1,
        report_title: '报告1',
        report_date: '2024-01-01'
      });
      await reportRepo.create({
        user_id: 1,
        report_title: '报告2',
        report_date: '2024-01-02'
      });
      await reportRepo.create({
        user_id: 2,
        report_title: '报告3',
        report_date: '2024-01-03'
      });

      const userReports = await reportRepo.findByUserId(1);
      expect(userReports).toHaveLength(2);
      expect(userReports[0].report_title).toBe('报告2'); // 按日期倒序
    });

    test('应该能够根据日期范围查找报告', async () => {
      await reportRepo.create({
        user_id: 1,
        report_title: '报告1',
        report_date: '2024-01-01'
      });
      await reportRepo.create({
        user_id: 1,
        report_title: '报告2',
        report_date: '2024-01-15'
      });
      await reportRepo.create({
        user_id: 1,
        report_title: '报告3',
        report_date: '2024-02-01'
      });

      const reports = await reportRepo.findByDateRange(1, '2024-01-01', '2024-01-31');
      expect(reports).toHaveLength(2);
    });

    test('应该能够创建加密报告', async () => {
      const reportData = {
        user_id: 1,
        report_title: '加密报告',
        report_date: '2024-01-01',
        ocr_text: '敏感的OCR文本内容'
      };

      const report = await reportRepo.createReport(reportData, true);

      expect(report.is_encrypted).toBe(1);
      expect(report.ocr_text).not.toBe('敏感的OCR文本内容'); // 应该被加密
      expect(report.encryption_key).toBeDefined();
    });

    test('应该能够获取解密后的报告', async () => {
      const reportData = {
        user_id: 1,
        report_title: '加密报告',
        report_date: '2024-01-01',
        ocr_text: '敏感的OCR文本内容'
      };

      const encryptedReport = await reportRepo.createReport(reportData, true);
      const decryptedReport = await reportRepo.getDecryptedReport(encryptedReport.id);

      expect(decryptedReport.ocr_text).toBe('敏感的OCR文本内容');
      expect(decryptedReport.encryption_key).toBeUndefined(); // 密钥应该被移除
    });
  });

  describe('HealthIndicatorRepository', () => {
    let indicatorRepo;

    beforeEach(() => {
      indicatorRepo = new HealthIndicatorRepository();
    });

    test('应该能够根据报告ID查找指标', async () => {
      await indicatorRepo.create({
        report_id: 1,
        indicator_name: '血糖',
        indicator_value: '5.6',
        category: '血液'
      });
      await indicatorRepo.create({
        report_id: 1,
        indicator_name: '血压',
        indicator_value: '120/80',
        category: '生理'
      });
      await indicatorRepo.create({
        report_id: 2,
        indicator_name: '血糖',
        indicator_value: '6.0',
        category: '血液'
      });

      const indicators = await indicatorRepo.findByReportId(1);
      expect(indicators).toHaveLength(2);
    });

    test('应该能够查找异常指标', async () => {
      await indicatorRepo.create({
        report_id: 1,
        indicator_name: '血糖',
        indicator_value: '8.0',
        is_abnormal: 1,
        abnormal_level: 2
      });
      await indicatorRepo.create({
        report_id: 1,
        indicator_name: '血压',
        indicator_value: '120/80',
        is_abnormal: 0
      });

      const abnormalIndicators = await indicatorRepo.findAbnormalIndicators(1);
      expect(abnormalIndicators).toHaveLength(1);
      expect(abnormalIndicators[0].indicator_name).toBe('血糖');
    });

    test('应该能够批量创建指标', async () => {
      const indicators = [
        {
          report_id: 1,
          indicator_name: '血糖',
          indicator_value: '5.6'
        },
        {
          report_id: 1,
          indicator_name: '血压',
          indicator_value: '120/80'
        }
      ];

      const results = await indicatorRepo.createBatch(indicators);
      expect(results).toHaveLength(2);
      expect(results[0].id).toBeDefined();
      expect(results[1].id).toBeDefined();
    });
  });

  describe('SyncRecordRepository', () => {
    let syncRepo;

    beforeEach(() => {
      syncRepo = new SyncRecordRepository();
    });

    test('应该能够创建同步记录', async () => {
      const record = await syncRepo.createSyncRecord(1, 'users', 1, 'INSERT');

      expect(record.user_id).toBe(1);
      expect(record.table_name).toBe('users');
      expect(record.record_id).toBe(1);
      expect(record.operation_type).toBe('INSERT');
      expect(record.sync_status).toBe(0); // 待同步
    });

    test('应该能够获取待同步记录', async () => {
      await syncRepo.createSyncRecord(1, 'users', 1, 'INSERT');
      await syncRepo.createSyncRecord(1, 'health_reports', 1, 'UPDATE');
      
      // 创建已同步记录
      const syncedRecord = await syncRepo.createSyncRecord(1, 'users', 2, 'INSERT');
      await syncRepo.updateSyncStatus(syncedRecord.id, 1); // 标记为已同步

      const pendingRecords = await syncRepo.getPendingSyncRecords(1);
      expect(pendingRecords).toHaveLength(2); // 只返回待同步的记录
    });

    test('应该能够更新同步状态', async () => {
      const record = await syncRepo.createSyncRecord(1, 'users', 1, 'INSERT');

      // 更新为同步成功
      const updatedRecord = await syncRepo.updateSyncStatus(record.id, 1);
      expect(updatedRecord.sync_status).toBe(1);
      expect(updatedRecord.last_sync_at).toBeDefined();

      // 更新为同步失败
      const failedRecord = await syncRepo.updateSyncStatus(record.id, 2, '网络错误');
      expect(failedRecord.sync_status).toBe(2);
      expect(failedRecord.error_message).toBe('网络错误');
      expect(failedRecord.sync_attempts).toBe(1);
    });
  });

  describe('BaseRepository通用功能', () => {
    let userRepo;

    beforeEach(() => {
      userRepo = new UserRepository();
    });

    test('应该支持分页查询', async () => {
      // 创建测试数据
      for (let i = 1; i <= 10; i++) {
        await userRepo.create({
          username: `user${i}`,
          age: 20 + i
        });
      }

      const page1 = await userRepo.paginate(1, 3);
      expect(page1.data).toHaveLength(3);
      expect(page1.pagination.page).toBe(1);
      expect(page1.pagination.total).toBe(10);
      expect(page1.pagination.totalPages).toBe(4);
      expect(page1.pagination.hasNext).toBe(true);
      expect(page1.pagination.hasPrev).toBe(false);

      const page2 = await userRepo.paginate(2, 3);
      expect(page2.data).toHaveLength(3);
      expect(page2.pagination.hasNext).toBe(true);
      expect(page2.pagination.hasPrev).toBe(true);
    });

    test('应该能够统计记录数', async () => {
      await userRepo.create({ username: 'user1', age: 25 });
      await userRepo.create({ username: 'user2', age: 30 });

      const totalCount = await userRepo.count();
      expect(totalCount).toBe(2);

      const youngCount = await userRepo.count({ age: 25 });
      expect(youngCount).toBe(1);
    });

    test('应该能够检查记录是否存在', async () => {
      await userRepo.create({ username: 'testuser' });

      const exists = await userRepo.exists({ username: 'testuser' });
      expect(exists).toBe(true);

      const notExists = await userRepo.exists({ username: 'nonexistent' });
      expect(notExists).toBe(false);
    });

    test('应该能够批量更新', async () => {
      await userRepo.create({ username: 'user1', status: 'active' });
      await userRepo.create({ username: 'user2', status: 'active' });
      await userRepo.create({ username: 'user3', status: 'inactive' });

      const updatedCount = await userRepo.updateMany(
        { status: 'active' },
        { status: 'verified' }
      );

      expect(updatedCount).toBe(2);
    });

    test('应该能够批量删除', async () => {
      await userRepo.create({ username: 'user1', status: 'active' });
      await userRepo.create({ username: 'user2', status: 'active' });
      await userRepo.create({ username: 'user3', status: 'inactive' });

      const deletedCount = await userRepo.deleteMany({ status: 'active' });
      expect(deletedCount).toBe(2);

      const remainingCount = await userRepo.count();
      expect(remainingCount).toBe(1);
    });
  });
});