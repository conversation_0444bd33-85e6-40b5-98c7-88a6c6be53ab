/**
 * 平台适配器类
 * 封装跨平台API差异，提供统一的接口
 */

import { 
  getCurrentPlatform, 
  isApp, 
  isWeixinMp, 
  isH5, 
  getPlatformConfig 
} from './detector.js'

import { 
  PLATFORM_TYPES, 
  CAMERA_CONFIG, 
  STORAGE_CONFIG, 
  SHARE_CONFIG,
  FILE_CONFIG,
  ERROR_CODES 
} from './constants.js'

class PlatformAdapter {
  constructor() {
    this.platform = getCurrentPlatform()
    this.config = getPlatformConfig()
  }

  /**
   * 相机相关适配方法
   */
  
  /**
   * 选择图片（拍照或从相册选择）
   * @param {Object} options 选择选项
   * @returns {Promise<Object>} 图片信息
   */
  async chooseImage(options = {}) {
    const defaultOptions = {
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album']
    }
    
    const mergedOptions = { ...defaultOptions, ...options }
    
    // 根据平台调整选项
    if (this.platform === PLATFORM_TYPES.MP_WEIXIN) {
      mergedOptions.count = Math.min(mergedOptions.count, 1)
    } else if (this.platform === PLATFORM_TYPES.H5) {
      mergedOptions.sourceType = ['album'] // H5只支持相册
    }
    
    return new Promise((resolve, reject) => {
      uni.chooseImage({
        ...mergedOptions,
        success: (res) => {
          resolve({
            success: true,
            tempFilePaths: res.tempFilePaths,
            tempFiles: res.tempFiles
          })
        },
        fail: (error) => {
          reject({
            success: false,
            error: error,
            code: ERROR_CODES.FEATURE_NOT_AVAILABLE
          })
        }
      })
    })
  }

  /**
   * 拍照
   * @param {Object} options 拍照选项
   * @returns {Promise<Object>} 照片信息
   */
  async takePhoto(options = {}) {
    if (this.platform === PLATFORM_TYPES.H5) {
      throw new Error('H5平台不支持直接拍照功能')
    }
    
    return this.chooseImage({
      ...options,
      sourceType: ['camera']
    })
  }

  /**
   * 从相册选择图片
   * @param {Object} options 选择选项
   * @returns {Promise<Object>} 图片信息
   */
  async chooseFromAlbum(options = {}) {
    return this.chooseImage({
      ...options,
      sourceType: ['album']
    })
  }

  /**
   * 存储相关适配方法
   */
  
  /**
   * 设置本地存储
   * @param {string} key 存储键
   * @param {any} value 存储值
   * @param {string} type 存储类型
   * @returns {Promise<boolean>} 是否成功
   */
  async setStorage(key, value, type = STORAGE_CONFIG.STORAGE_TYPES.LOCAL) {
    const fullKey = STORAGE_CONFIG.KEY_PREFIX + key
    
    try {
      if (type === STORAGE_CONFIG.STORAGE_TYPES.SECURE && this.config.supportBiometric) {
        // APP平台支持安全存储
        return this._setSecureStorage(fullKey, value)
      } else {
        // 普通存储
        return new Promise((resolve, reject) => {
          uni.setStorage({
            key: fullKey,
            data: value,
            success: () => resolve(true),
            fail: (error) => reject(error)
          })
        })
      }
    } catch (error) {
      console.error('存储数据失败:', error)
      return false
    }
  }

  /**
   * 获取本地存储
   * @param {string} key 存储键
   * @param {string} type 存储类型
   * @returns {Promise<any>} 存储值
   */
  async getStorage(key, type = STORAGE_CONFIG.STORAGE_TYPES.LOCAL) {
    const fullKey = STORAGE_CONFIG.KEY_PREFIX + key
    
    try {
      if (type === STORAGE_CONFIG.STORAGE_TYPES.SECURE && this.config.supportBiometric) {
        return this._getSecureStorage(fullKey)
      } else {
        return new Promise((resolve, reject) => {
          uni.getStorage({
            key: fullKey,
            success: (res) => resolve(res.data),
            fail: (error) => reject(error)
          })
        })
      }
    } catch (error) {
      console.error('获取存储数据失败:', error)
      return null
    }
  }

  /**
   * 删除本地存储
   * @param {string} key 存储键
   * @returns {Promise<boolean>} 是否成功
   */
  async removeStorage(key) {
    const fullKey = STORAGE_CONFIG.KEY_PREFIX + key
    
    return new Promise((resolve, reject) => {
      uni.removeStorage({
        key: fullKey,
        success: () => resolve(true),
        fail: (error) => reject(error)
      })
    })
  }

  /**
   * 清空所有存储
   * @returns {Promise<boolean>} 是否成功
   */
  async clearStorage() {
    return new Promise((resolve, reject) => {
      uni.clearStorage({
        success: () => resolve(true),
        fail: (error) => reject(error)
      })
    })
  }

  /**
   * 分享相关适配方法
   */
  
  /**
   * 分享内容
   * @param {Object} options 分享选项
   * @returns {Promise<Object>} 分享结果
   */
  async share(options = {}) {
    const { type, title, content, imageUrl, url } = options
    
    if (this.platform === PLATFORM_TYPES.H5) {
      // H5平台使用Web Share API或降级处理
      return this._shareOnH5(options)
    } else if (this.platform === PLATFORM_TYPES.MP_WEIXIN) {
      // 微信小程序分享
      return this._shareOnWeixin(options)
    } else if (this.platform === PLATFORM_TYPES.APP_PLUS) {
      // APP平台分享
      return this._shareOnApp(options)
    }
    
    throw new Error('当前平台不支持分享功能')
  }

  /**
   * 文件相关适配方法
   */
  
  /**
   * 保存图片到相册
   * @param {string} filePath 文件路径
   * @returns {Promise<boolean>} 是否成功
   */
  async saveImageToPhotosAlbum(filePath) {
    if (this.platform === PLATFORM_TYPES.H5) {
      // H5平台不支持保存到相册
      throw new Error('H5平台不支持保存图片到相册')
    }
    
    return new Promise((resolve, reject) => {
      uni.saveImageToPhotosAlbum({
        filePath: filePath,
        success: () => resolve(true),
        fail: (error) => reject(error)
      })
    })
  }

  /**
   * 获取文件信息
   * @param {string} filePath 文件路径
   * @returns {Promise<Object>} 文件信息
   */
  async getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      uni.getFileInfo({
        filePath: filePath,
        success: (res) => resolve(res),
        fail: (error) => reject(error)
      })
    })
  }

  /**
   * 权限相关适配方法
   */
  
  /**
   * 检查权限
   * @param {string} permission 权限名称
   * @returns {Promise<boolean>} 是否有权限
   */
  async checkPermission(permission) {
    if (this.platform !== PLATFORM_TYPES.APP_PLUS) {
      return true // 非APP平台默认有权限
    }
    
    // APP平台权限检查
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      const permissionID = permission
      const result = plus.android.checkPermission(permissionID)
      resolve(result === 0) // 0表示有权限
      // #endif
      
      // #ifndef APP-PLUS
      resolve(true)
      // #endif
    })
  }

  /**
   * 请求权限
   * @param {string} permission 权限名称
   * @returns {Promise<boolean>} 是否授权成功
   */
  async requestPermission(permission) {
    if (this.platform !== PLATFORM_TYPES.APP_PLUS) {
      return true
    }
    
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.android.requestPermissions(
        [permission],
        (result) => {
          resolve(result.granted.length > 0)
        },
        (error) => {
          console.error('权限请求失败:', error)
          resolve(false)
        }
      )
      // #endif
      
      // #ifndef APP-PLUS
      resolve(true)
      // #endif
    })
  }

  /**
   * 私有方法
   */
  
  /**
   * 安全存储（APP平台）
   * @private
   */
  async _setSecureStorage(key, value) {
    // 这里可以集成第三方安全存储库
    // 暂时使用普通存储
    return new Promise((resolve, reject) => {
      uni.setStorage({
        key: key,
        data: value,
        success: () => resolve(true),
        fail: (error) => reject(error)
      })
    })
  }

  /**
   * 获取安全存储（APP平台）
   * @private
   */
  async _getSecureStorage(key) {
    return new Promise((resolve, reject) => {
      uni.getStorage({
        key: key,
        success: (res) => resolve(res.data),
        fail: (error) => reject(error)
      })
    })
  }

  /**
   * H5平台分享
   * @private
   */
  async _shareOnH5(options) {
    if (navigator.share) {
      try {
        await navigator.share({
          title: options.title,
          text: options.content,
          url: options.url
        })
        return { success: true }
      } catch (error) {
        console.error('H5分享失败:', error)
        return { success: false, error }
      }
    } else {
      // 降级处理：复制到剪贴板
      try {
        await navigator.clipboard.writeText(options.content || options.url)
        uni.showToast({
          title: '内容已复制到剪贴板',
          icon: 'success'
        })
        return { success: true }
      } catch (error) {
        return { success: false, error }
      }
    }
  }

  /**
   * 微信小程序分享
   * @private
   */
  async _shareOnWeixin(options) {
    // 微信小程序需要在页面的onShareAppMessage中处理
    return new Promise((resolve) => {
      // 设置分享信息到全局
      getApp().globalData.shareInfo = options
      resolve({ success: true })
    })
  }

  /**
   * APP平台分享
   * @private
   */
  async _shareOnApp(options) {
    return new Promise((resolve, reject) => {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: options.type || 0,
        title: options.title,
        summary: options.content,
        href: options.url,
        imageUrl: options.imageUrl,
        success: () => resolve({ success: true }),
        fail: (error) => reject({ success: false, error })
      })
    })
  }
}

// 创建单例实例
const platformAdapter = new PlatformAdapter()

export default platformAdapter
export { PlatformAdapter }
export { PlatformAdapter as PlatformAdapterClass }