const ImageProcessor = require('@/utils/image/processor.js')

// Mock uni API
global.uni = {
  compressImage: jest.fn(),
  getImageInfo: jest.fn(),
  chooseImage: jest.fn(),
  previewImage: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn()
}

describe('ImageProcessor', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('compressImage', () => {
    it('应该成功压缩图片', async () => {
      const mockImagePath = '/test/image.jpg'
      const mockCompressedPath = '/test/compressed.jpg'
      
      uni.compressImage.mockImplementation(({ success }) => {
        success({ tempFilePath: mockCompressedPath })
      })

      const result = await ImageProcessor.compressImage(mockImagePath, 0.8)
      
      expect(uni.compressImage).toHaveBeenCalledWith({
        src: mockImagePath,
        quality: 0.8,
        success: expect.any(Function),
        fail: expect.any(Function)
      })
      expect(result).toBe(mockCompressedPath)
    })

    it('压缩失败时应该返回原图片路径', async () => {
      const mockImagePath = '/test/image.jpg'
      
      uni.compressImage.mockImplementation(({ fail }) => {
        fail(new Error('压缩失败'))
      })

      const result = await ImageProcessor.compressImage(mockImagePath)
      
      expect(result).toBe(mockImagePath)
    })

    it('应该使用默认质量参数', async () => {
      const mockImagePath = '/test/image.jpg'
      
      uni.compressImage.mockImplementation(({ success }) => {
        success({ tempFilePath: '/test/compressed.jpg' })
      })

      await ImageProcessor.compressImage(mockImagePath)
      
      expect(uni.compressImage).toHaveBeenCalledWith(
        expect.objectContaining({
          quality: 0.8 // 默认质量
        })
      )
    })
  })

  describe('validateImageQuality', () => {
    it('应该通过高质量图片的验证', async () => {
      const mockImagePath = '/test/good-image.jpg'
      
      uni.getImageInfo.mockImplementation(({ success }) => {
        success({
          width: 1920,
          height: 1080,
          size: 2 * 1024 * 1024 // 2MB
        })
      })

      const result = await ImageProcessor.validateImageQuality(mockImagePath)
      
      expect(result.passed).toBe(true)
      expect(result.issues).toHaveLength(0)
      expect(result.width).toBe(1920)
      expect(result.height).toBe(1080)
    })

    it('应该检测出分辨率过低的问题', async () => {
      const mockImagePath = '/test/low-res-image.jpg'
      
      uni.getImageInfo.mockImplementation(({ success }) => {
        success({
          width: 200,
          height: 150,
          size: 100 * 1024 // 100KB
        })
      })

      const result = await ImageProcessor.validateImageQuality(mockImagePath)
      
      expect(result.passed).toBe(false)
      expect(result.issues).toContain('图片分辨率过低，建议重新拍摄更清晰的照片')
    })

    it('应该检测出文件过大的问题', async () => {
      const mockImagePath = '/test/large-image.jpg'
      
      uni.getImageInfo.mockImplementation(({ success }) => {
        success({
          width: 1920,
          height: 1080,
          size: 10 * 1024 * 1024 // 10MB
        })
      })

      const result = await ImageProcessor.validateImageQuality(mockImagePath)
      
      expect(result.passed).toBe(false)
      expect(result.issues).toContain('图片文件过大，将自动压缩')
    })

    it('应该检测出异常宽高比', async () => {
      const mockImagePath = '/test/weird-ratio-image.jpg'
      
      uni.getImageInfo.mockImplementation(({ success }) => {
        success({
          width: 1000,
          height: 3000, // 宽高比 1:3
          size: 1 * 1024 * 1024
        })
      })

      const result = await ImageProcessor.validateImageQuality(mockImagePath)
      
      expect(result.issues).toContain('图片宽高比异常，可能影响识别效果')
    })

    it('获取图片信息失败时应该返回错误结果', async () => {
      const mockImagePath = '/test/invalid-image.jpg'
      
      uni.getImageInfo.mockImplementation(({ fail }) => {
        fail(new Error('获取图片信息失败'))
      })

      const result = await ImageProcessor.validateImageQuality(mockImagePath)
      
      expect(result.passed).toBe(false)
      expect(result.issues).toContain('无法获取图片信息，请检查图片是否有效')
    })
  })

  describe('preprocessForOCR', () => {
    it('应该对大文件进行压缩处理', async () => {
      const mockImagePath = '/test/large-image.jpg'
      const mockCompressedPath = '/test/compressed.jpg'
      
      // Mock 图片信息获取
      uni.getImageInfo.mockImplementation(({ success }) => {
        success({
          width: 1920,
          height: 1080,
          size: 10 * 1024 * 1024 // 10MB，超过限制
        })
      })
      
      // Mock 压缩
      uni.compressImage.mockImplementation(({ success }) => {
        success({ tempFilePath: mockCompressedPath })
      })

      const result = await ImageProcessor.preprocessForOCR(mockImagePath)
      
      expect(uni.compressImage).toHaveBeenCalled()
      expect(result).toBe(mockCompressedPath)
    })

    it('应该对高分辨率图片进行尺寸调整', async () => {
      const mockImagePath = '/test/high-res-image.jpg'
      
      // Mock 图片信息获取 - 分辨率过高
      uni.getImageInfo
        .mockImplementationOnce(({ success }) => {
          success({
            width: 4000,
            height: 3000,
            size: 2 * 1024 * 1024
          })
        })
        .mockImplementationOnce(({ success }) => {
          success({
            width: 4000,
            height: 3000
          })
        })

      const result = await ImageProcessor.preprocessForOCR(mockImagePath)
      
      // 应该调用了尺寸调整
      expect(uni.getImageInfo).toHaveBeenCalledTimes(2)
    })

    it('处理失败时应该返回原图片路径', async () => {
      const mockImagePath = '/test/image.jpg'
      
      uni.getImageInfo.mockImplementation(({ fail }) => {
        fail(new Error('处理失败'))
      })

      const result = await ImageProcessor.preprocessForOCR(mockImagePath)
      
      expect(result).toBe(mockImagePath)
    })
  })

  describe('resizeImage', () => {
    it('应该正确计算新尺寸并保持宽高比', async () => {
      const mockImagePath = '/test/large-image.jpg'
      
      uni.getImageInfo.mockImplementation(({ success }) => {
        success({
          width: 4000,
          height: 2000 // 宽高比 2:1
        })
      })

      await ImageProcessor.resizeImage(mockImagePath)
      
      // 验证调用了getImageInfo
      expect(uni.getImageInfo).toHaveBeenCalledWith({
        src: mockImagePath,
        success: expect.any(Function),
        fail: expect.any(Function)
      })
    })

    it('图片尺寸在限制范围内时不应该调整', async () => {
      const mockImagePath = '/test/normal-image.jpg'
      
      uni.getImageInfo.mockImplementation(({ success }) => {
        success({
          width: 1920,
          height: 1080
        })
      })

      const result = await ImageProcessor.resizeImage(mockImagePath)
      
      // 尺寸在范围内，应该返回原路径
      expect(result).toBe(mockImagePath)
    })
  })

  describe('generateThumbnail', () => {
    it('应该生成指定尺寸的缩略图', async () => {
      const mockImagePath = '/test/image.jpg'
      const mockCompressedPath = '/test/compressed.jpg'
      const thumbnailSize = 200
      
      uni.compressImage.mockImplementation(({ success }) => {
        success({ tempFilePath: mockCompressedPath })
      })

      await ImageProcessor.generateThumbnail(mockImagePath, thumbnailSize)
      
      expect(uni.compressImage).toHaveBeenCalledWith({
        src: mockImagePath,
        quality: 0.6,
        success: expect.any(Function),
        fail: expect.any(Function)
      })
    })

    it('生成失败时应该返回原图片路径', async () => {
      const mockImagePath = '/test/image.jpg'
      
      uni.compressImage.mockImplementation(({ fail }) => {
        fail(new Error('压缩失败'))
      })

      const result = await ImageProcessor.generateThumbnail(mockImagePath)
      
      expect(result).toBe(mockImagePath)
    })

    it('应该使用默认缩略图尺寸', async () => {
      const mockImagePath = '/test/image.jpg'
      
      uni.compressImage.mockImplementation(({ success }) => {
        success({ tempFilePath: '/test/thumbnail.jpg' })
      })

      await ImageProcessor.generateThumbnail(mockImagePath)
      
      // 默认尺寸应该是150
      expect(uni.compressImage).toHaveBeenCalled()
    })
  })

  describe('边界条件测试', () => {
    it('应该正确处理空路径', async () => {
      const result = await ImageProcessor.validateImageQuality('')
      
      expect(result.passed).toBe(false)
      expect(result.issues.length).toBeGreaterThan(0)
    })

    it('应该正确处理null路径', async () => {
      const result = await ImageProcessor.validateImageQuality(null)
      
      expect(result.passed).toBe(false)
    })

    it('压缩质量参数应该在有效范围内', async () => {
      const mockImagePath = '/test/image.jpg'
      
      uni.compressImage.mockImplementation(({ success }) => {
        success({ tempFilePath: '/test/compressed.jpg' })
      })

      // 测试边界值
      await ImageProcessor.compressImage(mockImagePath, 0)
      expect(uni.compressImage).toHaveBeenLastCalledWith(
        expect.objectContaining({ quality: 0 })
      )

      await ImageProcessor.compressImage(mockImagePath, 1)
      expect(uni.compressImage).toHaveBeenLastCalledWith(
        expect.objectContaining({ quality: 1 })
      )
    })
  })
})