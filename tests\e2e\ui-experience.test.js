/**
 * 用户界面优化和交互体验端到端测试
 */

describe('用户界面优化和交互体验测试', () => {
  let page

  beforeAll(async () => {
    // 启动应用
    page = await program.reLaunch('/pages/index/index')
    await page.waitFor(2000)
  })

  afterAll(async () => {
    await program.close()
  })

  describe('应用引导页面测试', () => {
    test('首次启动应显示引导页面', async () => {
      // 清除引导完成标记
      await page.callMethod('clearGuideFlag')
      
      // 重新启动应用
      await program.reLaunch('/pages/guide/index')
      await page.waitFor(1000)
      
      // 验证引导页面元素
      const guideTitle = await page.$('.guide-title')
      expect(guideTitle).toBeTruthy()
      
      const guideDesc = await page.$('.guide-desc')
      expect(guideDesc).toBeTruthy()
      
      const nextBtn = await page.$('.next-btn')
      expect(nextBtn).toBeTruthy()
    })

    test('引导页面滑动功能正常', async () => {
      await program.navigateTo('/pages/guide/index')
      await page.waitFor(1000)
      
      // 测试滑动到下一页
      await page.swipeLeft()
      await page.waitFor(500)
      
      // 验证页面内容变化
      const currentIndex = await page.data('currentIndex')
      expect(currentIndex).toBe(1)
    })

    test('跳过引导功能正常', async () => {
      await program.navigateTo('/pages/guide/index')
      await page.waitFor(1000)
      
      // 点击跳过按钮
      await page.tap('.skip-btn')
      await page.waitFor(1000)
      
      // 验证跳转到首页
      const currentPath = await page.path
      expect(currentPath).toBe('pages/index/index')
    })

    test('完成引导流程', async () => {
      await program.navigateTo('/pages/guide/index')
      await page.waitFor(1000)
      
      // 逐页浏览引导
      for (let i = 0; i < 3; i++) {
        await page.tap('.next-btn')
        await page.waitFor(500)
      }
      
      // 点击开始使用
      await page.tap('.start-btn')
      await page.waitFor(1000)
      
      // 验证跳转到首页
      const currentPath = await page.path
      expect(currentPath).toBe('pages/index/index')
      
      // 验证引导完成标记已设置
      const hasCompletedGuide = await page.callMethod('checkGuideFlag')
      expect(hasCompletedGuide).toBe(true)
    })
  })

  describe('拍照指引功能测试', () => {
    test('拍照指引组件正常显示', async () => {
      await program.navigateTo('/pages/report/add')
      await page.waitFor(1000)
      
      // 点击拍照按钮
      await page.tap('.camera-btn')
      await page.waitFor(500)
      
      // 验证拍照指引显示
      const guideOverlay = await page.$('.guide-overlay')
      expect(guideOverlay).toBeTruthy()
      
      const guideTips = await page.$$('.tip-item')
      expect(guideTips.length).toBeGreaterThan(0)
    })

    test('拍照指引提示内容完整', async () => {
      await program.navigateTo('/pages/report/add')
      await page.waitFor(1000)
      
      await page.tap('.camera-btn')
      await page.waitFor(500)
      
      // 验证指引提示内容
      const tipTexts = await page.$$eval('.tip-text', elements => 
        elements.map(el => el.textContent)
      )
      
      expect(tipTexts).toContain('保持手机稳定，避免抖动')
      expect(tipTexts).toContain('确保光线充足，避免阴影')
      expect(tipTexts).toContain('报告完整显示在取景框内')
      expect(tipTexts).toContain('文字清晰可见，无模糊')
    })

    test('拍照界面交互功能', async () => {
      await program.navigateTo('/pages/report/add')
      await page.waitFor(1000)
      
      await page.tap('.camera-btn')
      await page.waitFor(500)
      
      // 开始拍摄
      await page.tap('.btn-primary')
      await page.waitFor(1000)
      
      // 验证相机界面显示
      const cameraContainer = await page.$('.camera-container')
      expect(cameraContainer).toBeTruthy()
      
      const viewfinder = await page.$('.viewfinder')
      expect(viewfinder).toBeTruthy()
      
      const captureBtn = await page.$('.capture-btn')
      expect(captureBtn).toBeTruthy()
    })

    test('图片质量检测功能', async () => {
      await program.navigateTo('/pages/report/add')
      await page.waitFor(1000)
      
      await page.tap('.camera-btn')
      await page.waitFor(500)
      await page.tap('.btn-primary')
      await page.waitFor(1000)
      
      // 模拟拍照
      await page.callMethod('mockCapturePhoto')
      await page.waitFor(2000)
      
      // 验证预览界面显示
      const previewContainer = await page.$('.preview-container')
      expect(previewContainer).toBeTruthy()
      
      // 验证质量检测结果
      const qualityCheck = await page.$('.quality-check')
      expect(qualityCheck).toBeTruthy()
      
      const qualityItems = await page.$$('.quality-item')
      expect(qualityItems.length).toBe(3) // 清晰度、亮度、完整性
    })
  })

  describe('实时数据验证测试', () => {
    test('表单字段实时验证', async () => {
      await program.navigateTo('/pages/auth/register')
      await page.waitFor(1000)
      
      // 测试手机号验证
      const phoneInput = await page.$('.phone-input input')
      await phoneInput.input('123') // 输入无效手机号
      await page.waitFor(500)
      
      // 验证错误提示显示
      const errorMessage = await page.$('.message-error')
      expect(errorMessage).toBeTruthy()
      
      const errorText = await errorMessage.text()
      expect(errorText).toContain('手机号')
    })

    test('密码强度实时检测', async () => {
      await program.navigateTo('/pages/auth/register')
      await page.waitFor(1000)
      
      const passwordInput = await page.$('.password-input input')
      
      // 测试弱密码
      await passwordInput.input('123')
      await page.waitFor(500)
      
      let errorMessage = await page.$('.message-error')
      expect(errorMessage).toBeTruthy()
      
      // 测试强密码
      await passwordInput.input('abc123456')
      await page.waitFor(500)
      
      const successMessage = await page.$('.message-success')
      expect(successMessage).toBeTruthy()
    })

    test('健康指标异常检测', async () => {
      await program.navigateTo('/pages/report/add')
      await page.waitFor(1000)
      
      // 输入异常血压值
      const bpHighInput = await page.$('.bp-high-input input')
      await bpHighInput.input('200') // 高血压
      await page.waitFor(500)
      
      // 验证异常提示
      const abnormalIndicator = await page.$('.abnormal-indicator')
      expect(abnormalIndicator).toBeTruthy()
      
      const indicatorText = await abnormalIndicator.text()
      expect(indicatorText).toContain('偏高')
    })

    test('BMI计算和评估', async () => {
      await program.navigateTo('/pages/report/add')
      await page.waitFor(1000)
      
      // 输入身高体重
      const heightInput = await page.$('.height-input input')
      const weightInput = await page.$('.weight-input input')
      
      await heightInput.input('170')
      await weightInput.input('70')
      await page.waitFor(500)
      
      // 验证BMI计算结果
      const bmiResult = await page.$('.bmi-result')
      expect(bmiResult).toBeTruthy()
      
      const bmiValue = await page.$('.bmi-value')
      const bmiText = await bmiValue.text()
      expect(bmiText).toContain('24.2') // 70/(1.7*1.7) ≈ 24.2
    })
  })

  describe('主题切换功能测试', () => {
    test('主题设置页面正常显示', async () => {
      await program.navigateTo('/pages/settings/theme')
      await page.waitFor(1000)
      
      // 验证主题选项显示
      const themeOptions = await page.$$('.theme-option')
      expect(themeOptions.length).toBe(3) // 浅色、深色、跟随系统
      
      // 验证字体大小选项
      const fontOptions = await page.$$('.font-option')
      expect(fontOptions.length).toBe(3) // 小、中、大
      
      // 验证语言选项
      const languageOptions = await page.$$('.language-option')
      expect(languageOptions.length).toBe(3) // 简体中文、繁体中文、English
    })

    test('主题切换功能', async () => {
      await program.navigateTo('/pages/settings/theme')
      await page.waitFor(1000)
      
      // 切换到深色主题
      await page.tap('.theme-option:nth-child(2)')
      await page.waitFor(500)
      
      // 验证主题切换成功
      const activeTheme = await page.$('.theme-option.active')
      const themeText = await activeTheme.$eval('.theme-name', el => el.textContent)
      expect(themeText).toBe('深色主题')
      
      // 验证设置已保存
      const savedTheme = await page.callMethod('getSavedTheme')
      expect(savedTheme).toBe('dark')
    })

    test('字体大小调整功能', async () => {
      await program.navigateTo('/pages/settings/theme')
      await page.waitFor(1000)
      
      // 选择大字体
      await page.tap('.font-option:nth-child(3)')
      await page.waitFor(500)
      
      // 验证字体大小设置
      const activeFontOption = await page.$('.font-option.active')
      const fontText = await activeFontOption.$eval('.font-name', el => el.textContent)
      expect(fontText).toBe('大')
      
      // 验证设置已保存
      const savedFontSize = await page.callMethod('getSavedFontSize')
      expect(savedFontSize).toBe('large')
    })

    test('语言切换功能', async () => {
      await program.navigateTo('/pages/settings/theme')
      await page.waitFor(1000)
      
      // 切换到英文
      await page.tap('.language-option:nth-child(3)')
      await page.waitFor(500)
      
      // 验证确认对话框显示
      const modal = await page.$('.uni-modal')
      expect(modal).toBeTruthy()
      
      // 确认切换
      await page.tap('.uni-modal .uni-modal-btn-primary')
      await page.waitFor(1000)
      
      // 验证应用重启
      const currentPath = await page.path
      expect(currentPath).toBe('pages/index/index')
    })
  })

  describe('网络状态优化测试', () => {
    test('网络状态组件显示', async () => {
      await program.navigateTo('/pages/index/index')
      await page.waitFor(1000)
      
      // 模拟网络断开
      await page.callMethod('mockNetworkOffline')
      await page.waitFor(1000)
      
      // 验证网络状态栏显示
      const networkStatus = await page.$('.network-status')
      expect(networkStatus).toBeTruthy()
      
      const statusText = await page.$('.status-main')
      const statusContent = await statusText.text()
      expect(statusContent).toContain('网络连接不可用')
    })

    test('离线模式提示', async () => {
      await program.navigateTo('/pages/index/index')
      await page.waitFor(1000)
      
      // 模拟网络断开
      await page.callMethod('mockNetworkOffline')
      await page.waitFor(1000)
      
      // 验证离线横幅显示
      const offlineBanner = await page.$('.offline-banner')
      expect(offlineBanner).toBeTruthy()
      
      const bannerTitle = await page.$('.banner-title')
      const titleText = await bannerTitle.text()
      expect(titleText).toBe('离线模式')
    })

    test('网络恢复处理', async () => {
      await program.navigateTo('/pages/index/index')
      await page.waitFor(1000)
      
      // 模拟网络断开再恢复
      await page.callMethod('mockNetworkOffline')
      await page.waitFor(1000)
      
      await page.callMethod('mockNetworkOnline')
      await page.waitFor(1000)
      
      // 验证网络恢复提示
      const toast = await page.$('.uni-toast')
      expect(toast).toBeTruthy()
      
      const toastText = await toast.text()
      expect(toastText).toContain('网络已恢复')
    })

    test('请求重试机制', async () => {
      await program.navigateTo('/pages/report/list')
      await page.waitFor(1000)
      
      // 模拟网络不稳定
      await page.callMethod('mockNetworkUnstable')
      
      // 触发数据加载
      await page.pullDownRefresh()
      await page.waitFor(3000)
      
      // 验证重试机制工作
      const retryCount = await page.callMethod('getRetryCount')
      expect(retryCount).toBeGreaterThan(0)
    })
  })

  describe('页面加载性能测试', () => {
    test('页面加载时间监控', async () => {
      const startTime = Date.now()
      
      await program.navigateTo('/pages/analysis/index')
      await page.waitFor(2000)
      
      const loadTime = Date.now() - startTime
      
      // 验证页面加载时间不超过3秒
      expect(loadTime).toBeLessThan(3000)
      
      // 验证性能监控记录
      const performanceMetrics = await page.callMethod('getPerformanceMetrics')
      expect(performanceMetrics).toBeTruthy()
      expect(performanceMetrics.loadTime).toBeLessThan(3000)
    })

    test('图片懒加载功能', async () => {
      await program.navigateTo('/pages/report/list')
      await page.waitFor(1000)
      
      // 验证初始只加载可见图片
      const loadedImages = await page.$$eval('image[src]', images => 
        images.filter(img => img.src && !img.src.includes('placeholder')).length
      )
      
      // 滚动页面
      await page.scrollTo(0, 1000)
      await page.waitFor(1000)
      
      // 验证更多图片被加载
      const newLoadedImages = await page.$$eval('image[src]', images => 
        images.filter(img => img.src && !img.src.includes('placeholder')).length
      )
      
      expect(newLoadedImages).toBeGreaterThan(loadedImages)
    })

    test('缓存机制有效性', async () => {
      await program.navigateTo('/pages/report/list')
      await page.waitFor(2000)
      
      // 记录首次加载时间
      const firstLoadStart = Date.now()
      await page.callMethod('loadReportData')
      const firstLoadTime = Date.now() - firstLoadStart
      
      // 再次加载相同数据
      const secondLoadStart = Date.now()
      await page.callMethod('loadReportData')
      const secondLoadTime = Date.now() - secondLoadStart
      
      // 验证缓存生效，第二次加载更快
      expect(secondLoadTime).toBeLessThan(firstLoadTime)
    })
  })

  describe('用户交互体验测试', () => {
    test('按钮点击反馈', async () => {
      await program.navigateTo('/pages/index/index')
      await page.waitFor(1000)
      
      // 测试按钮点击动画
      const actionItem = await page.$('.action-item')
      await actionItem.tap()
      
      // 验证点击动画效果
      const transform = await actionItem.getComputedStyle('transform')
      expect(transform).toContain('scale')
    })

    test('表单输入体验', async () => {
      await program.navigateTo('/pages/auth/login')
      await page.waitFor(1000)
      
      const phoneInput = await page.$('.phone-input input')
      
      // 测试输入聚焦效果
      await phoneInput.focus()
      await page.waitFor(200)
      
      const inputContainer = await page.$('.field-input')
      const borderColor = await inputContainer.getComputedStyle('border-color')
      expect(borderColor).toContain('rgb(0, 122, 255)') // #007AFF
    })

    test('页面切换动画', async () => {
      await program.navigateTo('/pages/index/index')
      await page.waitFor(1000)
      
      // 记录页面切换开始时间
      const startTime = Date.now()
      
      await page.tap('.action-item:first-child')
      await page.waitFor(1000)
      
      const switchTime = Date.now() - startTime
      
      // 验证页面切换流畅（不超过1秒）
      expect(switchTime).toBeLessThan(1000)
      
      // 验证页面切换成功
      const currentPath = await page.path
      expect(currentPath).toBe('pages/report/add')
    })

    test('下拉刷新体验', async () => {
      await program.navigateTo('/pages/report/list')
      await page.waitFor(1000)
      
      // 执行下拉刷新
      await page.pullDownRefresh()
      await page.waitFor(2000)
      
      // 验证刷新完成
      const refreshStatus = await page.callMethod('getRefreshStatus')
      expect(refreshStatus).toBe('completed')
    })

    test('上拉加载更多', async () => {
      await program.navigateTo('/pages/report/list')
      await page.waitFor(1000)
      
      // 滚动到底部
      await page.scrollTo(0, 10000)
      await page.waitFor(2000)
      
      // 验证加载更多触发
      const loadMoreStatus = await page.callMethod('getLoadMoreStatus')
      expect(loadMoreStatus).toBe('loading')
    })
  })

  describe('错误处理和用户提示测试', () => {
    test('网络错误提示', async () => {
      await program.navigateTo('/pages/report/list')
      await page.waitFor(1000)
      
      // 模拟网络错误
      await page.callMethod('mockNetworkError')
      
      // 触发网络请求
      await page.pullDownRefresh()
      await page.waitFor(2000)
      
      // 验证错误提示显示
      const toast = await page.$('.uni-toast')
      expect(toast).toBeTruthy()
      
      const toastText = await toast.text()
      expect(toastText).toContain('网络')
    })

    test('表单验证错误提示', async () => {
      await program.navigateTo('/pages/auth/register')
      await page.waitFor(1000)
      
      // 提交空表单
      await page.tap('.submit-btn')
      await page.waitFor(500)
      
      // 验证错误提示显示
      const errorMessages = await page.$$('.message-error')
      expect(errorMessages.length).toBeGreaterThan(0)
    })

    test('操作成功提示', async () => {
      await program.navigateTo('/pages/settings/theme')
      await page.waitFor(1000)
      
      // 切换主题
      await page.tap('.theme-option:nth-child(2)')
      await page.waitFor(1000)
      
      // 验证成功提示
      const toast = await page.$('.uni-toast')
      expect(toast).toBeTruthy()
      
      const toastText = await toast.text()
      expect(toastText).toContain('主题已切换')
    })
  })
})