<template>
  <view class="sync-settings-page">
    <!-- 导航栏 -->
    <uni-nav-bar 
      title="同步设置" 
      left-icon="back" 
      @clickLeft="goBack"
      background-color="#007AFF"
      color="#FFFFFF"
    />

    <!-- 同步状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <text class="status-title">同步状态</text>
        <view class="status-indicator" :class="statusClass">
          <text class="status-dot"></text>
          <text class="status-text">{{ statusText }}</text>
        </view>
      </view>
      
      <view class="status-details">
        <view class="detail-item">
          <text class="detail-label">上次同步</text>
          <text class="detail-value">{{ lastSyncText }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">同步成功率</text>
          <text class="detail-value">{{ syncSuccessRate }}%</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">待同步数据</text>
          <text class="detail-value">{{ pendingCount }}条</text>
        </view>
      </view>

      <!-- 同步进度 -->
      <view v-if="syncStatus.isRunning" class="sync-progress">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progress.percentage + '%' }"></view>
        </view>
        <text class="progress-text">{{ progressText }}</text>
      </view>

      <!-- 同步按钮 -->
      <view class="sync-actions">
        <button 
          class="sync-btn" 
          :class="{ 'syncing': syncStatus.isRunning }"
          :disabled="syncStatus.isRunning || !networkStatus.isOnline"
          @click="startManualSync"
        >
          {{ syncButtonText }}
        </button>
      </view>
    </view>

    <!-- 同步配置 -->
    <view class="config-section">
      <view class="section-title">同步配置</view>
      
      <!-- 自动同步开关 -->
      <view class="config-item">
        <view class="config-info">
          <text class="config-label">自动同步</text>
          <text class="config-desc">启用后将定期自动同步数据</text>
        </view>
        <switch 
          :checked="syncConfig.autoSync" 
          @change="onAutoSyncChange"
          color="#007AFF"
        />
      </view>

      <!-- 同步间隔 -->
      <view class="config-item" v-if="syncConfig.autoSync">
        <view class="config-info">
          <text class="config-label">同步间隔</text>
          <text class="config-desc">自动同步的时间间隔</text>
        </view>
        <picker 
          :value="syncIntervalIndex" 
          :range="syncIntervalOptions"
          range-key="label"
          @change="onSyncIntervalChange"
        >
          <view class="picker-value">
            {{ syncIntervalOptions[syncIntervalIndex].label }}
          </view>
        </picker>
      </view>

      <!-- 仅WiFi同步 -->
      <view class="config-item">
        <view class="config-info">
          <text class="config-label">仅WiFi同步</text>
          <text class="config-desc">仅在WiFi环境下进行同步</text>
        </view>
        <switch 
          :checked="syncConfig.wifiOnly" 
          @change="onWifiOnlyChange"
          color="#007AFF"
        />
      </view>

      <!-- 应用启动时同步 -->
      <view class="config-item">
        <view class="config-info">
          <text class="config-label">启动时同步</text>
          <text class="config-desc">应用启动时自动进行同步</text>
        </view>
        <switch 
          :checked="syncConfig.syncOnAppStart" 
          @change="onSyncOnAppStartChange"
          color="#007AFF"
        />
      </view>
    </view>

    <!-- 网络状态 -->
    <view class="network-section">
      <view class="section-title">网络状态</view>
      
      <view class="network-info">
        <view class="network-item">
          <text class="network-label">连接状态</text>
          <view class="network-status" :class="networkStatusClass">
            <text class="network-dot"></text>
            <text class="network-text">{{ networkStatusText }}</text>
          </view>
        </view>
        
        <view class="network-item" v-if="networkStatus.isOnline">
          <text class="network-label">网络类型</text>
          <text class="network-value">{{ networkType }}</text>
        </view>
        
        <view class="network-item" v-if="!networkStatus.isOnline">
          <text class="network-label">离线队列</text>
          <text class="network-value">{{ networkStatus.offlineQueueSize }}条</text>
        </view>
      </view>
    </view>

    <!-- 同步历史 -->
    <view class="history-section">
      <view class="section-title">
        <text>同步历史</text>
        <text class="clear-btn" @click="clearSyncHistory">清空</text>
      </view>
      
      <view class="history-list">
        <view 
          v-for="record in syncHistory" 
          :key="record.id"
          class="history-item"
        >
          <view class="history-info">
            <text class="history-type">{{ getHistoryTypeText(record.type) }}</text>
            <text class="history-time">{{ formatTime(record.timestamp) }}</text>
          </view>
          <view class="history-status" :class="record.status">
            <text class="history-status-text">{{ getStatusText(record.status) }}</text>
          </view>
          <text class="history-message">{{ record.message }}</text>
        </view>
        
        <view v-if="syncHistory.length === 0" class="empty-history">
          <text>暂无同步记录</text>
        </view>
      </view>
    </view>

    <!-- 冲突管理 -->
    <view class="conflict-section" v-if="conflicts.length > 0">
      <view class="section-title">
        <text>数据冲突 ({{ conflicts.length }})</text>
        <text class="resolve-all-btn" @click="resolveAllConflicts">全部解决</text>
      </view>
      
      <view class="conflict-list">
        <view 
          v-for="conflict in conflicts" 
          :key="conflict.id"
          class="conflict-item"
          @click="showConflictDetail(conflict)"
        >
          <view class="conflict-info">
            <text class="conflict-type">{{ getConflictTypeText(conflict.type) }}</text>
            <text class="conflict-time">{{ formatTime(conflict.timestamp) }}</text>
          </view>
          <view class="conflict-severity" :class="conflict.severity">
            <text>{{ getSeverityText(conflict.severity) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 高级设置 -->
    <view class="advanced-section">
      <view class="section-title">高级设置</view>
      
      <view class="advanced-item" @click="exportSyncData">
        <text class="advanced-label">导出同步数据</text>
        <text class="advanced-arrow">></text>
      </view>
      
      <view class="advanced-item" @click="importSyncData">
        <text class="advanced-label">导入同步数据</text>
        <text class="advanced-arrow">></text>
      </view>
      
      <view class="advanced-item" @click="resetSyncSettings">
        <text class="advanced-label">重置同步设置</text>
        <text class="advanced-arrow">></text>
      </view>
      
      <view class="advanced-item danger" @click="clearAllSyncData">
        <text class="advanced-label">清空所有同步数据</text>
        <text class="advanced-arrow">></text>
      </view>
    </view>

    <!-- 冲突详情弹窗 -->
    <uni-popup ref="conflictPopup" type="bottom">
      <view class="conflict-detail">
        <view class="conflict-detail-header">
          <text class="conflict-detail-title">数据冲突详情</text>
          <text class="conflict-detail-close" @click="closeConflictDetail">×</text>
        </view>
        
        <view class="conflict-detail-content" v-if="selectedConflict">
          <view class="conflict-field" v-for="field in selectedConflict.conflictFields" :key="field.field">
            <text class="field-name">{{ field.field }}</text>
            <view class="field-values">
              <view class="field-value local">
                <text class="value-label">本地值</text>
                <text class="value-text">{{ field.localValue }}</text>
              </view>
              <view class="field-value remote">
                <text class="value-label">远程值</text>
                <text class="value-text">{{ field.remoteValue }}</text>
              </view>
            </view>
          </view>
          
          <view class="conflict-actions">
            <button class="conflict-btn local" @click="resolveConflict('local_wins')">使用本地数据</button>
            <button class="conflict-btn remote" @click="resolveConflict('remote_wins')">使用远程数据</button>
            <button class="conflict-btn merge" @click="resolveConflict('merge')">智能合并</button>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { useSyncStore } from '../../stores/sync.js';
import { syncService } from '../../services/sync/syncService.js';
import { syncStatusManager } from '../../services/sync/syncStatusManager.js';
import { offlineManager } from '../../services/sync/offlineManager.js';
import { conflictResolver } from '../../services/sync/conflictResolver.js';

export default {
  name: 'SyncSettings',
  data() {
    return {
      syncStore: null,
      syncStatus: {
        isRunning: false,
        lastSyncTime: null,
        isOffline: false
      },
      syncConfig: {
        autoSync: true,
        syncInterval: 30,
        wifiOnly: true,
        syncOnAppStart: true
      },
      progress: {
        percentage: 0,
        message: ''
      },
      networkStatus: {
        isOnline: true,
        offlineQueueSize: 0
      },
      networkType: '未知',
      syncHistory: [],
      conflicts: [],
      selectedConflict: null,
      syncIntervalOptions: [
        { label: '5分钟', value: 5 },
        { label: '15分钟', value: 15 },
        { label: '30分钟', value: 30 },
        { label: '1小时', value: 60 },
        { label: '2小时', value: 120 },
        { label: '6小时', value: 360 }
      ]
    };
  },
  
  computed: {
    statusClass() {
      if (this.syncStatus.isRunning) return 'syncing';
      if (!this.networkStatus.isOnline) return 'offline';
      return 'online';
    },
    
    statusText() {
      if (this.syncStatus.isRunning) return '同步中';
      if (!this.networkStatus.isOnline) return '离线';
      return '在线';
    },
    
    lastSyncText() {
      return syncStatusManager.formatSyncTime(this.syncStatus.lastSyncTime);
    },
    
    syncSuccessRate() {
      return this.syncStore ? this.syncStore.syncSuccessRate : '0';
    },
    
    pendingCount() {
      if (!this.syncStore) return 0;
      const pending = this.syncStore.pendingSync;
      return pending.upload.length + pending.download.length + pending.delete.length;
    },
    
    progressText() {
      return syncStatusManager.getProgressText();
    },
    
    syncButtonText() {
      if (this.syncStatus.isRunning) return '同步中...';
      if (!this.networkStatus.isOnline) return '网络不可用';
      return '立即同步';
    },
    
    syncIntervalIndex() {
      return this.syncIntervalOptions.findIndex(option => 
        option.value === this.syncConfig.syncInterval
      );
    },
    
    networkStatusClass() {
      return this.networkStatus.isOnline ? 'online' : 'offline';
    },
    
    networkStatusText() {
      return this.networkStatus.isOnline ? '已连接' : '已断开';
    }
  },
  
  async onLoad() {
    await this.initializePage();
  },
  
  onUnload() {
    this.cleanup();
  },
  
  methods: {
    async initializePage() {
      try {
        // 初始化同步存储
        this.syncStore = useSyncStore();
        await this.syncStore.initSync();
        
        // 初始化服务
        await syncService.initialize();
        await offlineManager.initialize();
        
        // 加载数据
        await this.loadSyncData();
        
        // 设置监听器
        this.setupListeners();
        
        // 获取网络类型
        await this.getNetworkType();
        
      } catch (error) {
        console.error('初始化同步设置页面失败:', error);
        uni.showToast({
          title: '初始化失败',
          icon: 'error'
        });
      }
    },
    
    async loadSyncData() {
      // 加载同步状态
      this.syncStatus = syncStatusManager.getSyncStatus();
      
      // 加载同步配置
      this.syncConfig = { ...this.syncStore.syncConfig };
      
      // 加载同步历史
      this.syncHistory = this.syncStore.recentSyncHistory;
      
      // 加载冲突数据
      this.conflicts = this.syncStore.conflicts.filter(c => !c.resolved);
      
      // 加载网络状态
      this.networkStatus = offlineManager.getNetworkStatus();
    },
    
    setupListeners() {
      // 监听同步进度
      this.progressUnsubscribe = syncStatusManager.onProgressUpdate((progress) => {
        this.progress = progress;
      });
      
      // 监听同步状态
      this.statusUnsubscribe = syncStatusManager.onStatusUpdate((status) => {
        this.syncStatus = status;
      });
      
      // 监听网络状态
      this.networkUnsubscribe = offlineManager.onNetworkStatusChange((status) => {
        this.networkStatus = status;
      });
    },
    
    async getNetworkType() {
      uni.getNetworkType({
        success: (res) => {
          this.networkType = res.networkType;
        }
      });
    },
    
    async startManualSync() {
      try {
        const userId = this.getCurrentUserId();
        if (!userId) {
          uni.showToast({
            title: '请先登录',
            icon: 'error'
          });
          return;
        }
        
        const result = await syncService.startSync({
          userId,
          syncType: 'full',
          background: false
        });
        
        if (result.success) {
          uni.showToast({
            title: '同步完成',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: result.error || '同步失败',
            icon: 'error'
          });
        }
        
        // 刷新数据
        await this.loadSyncData();
        
      } catch (error) {
        console.error('手动同步失败:', error);
        uni.showToast({
          title: '同步失败',
          icon: 'error'
        });
      }
    },
    
    onAutoSyncChange(e) {
      this.syncConfig.autoSync = e.detail.value;
      this.updateSyncConfig();
    },
    
    onSyncIntervalChange(e) {
      const index = e.detail.value;
      this.syncConfig.syncInterval = this.syncIntervalOptions[index].value;
      this.updateSyncConfig();
    },
    
    onWifiOnlyChange(e) {
      this.syncConfig.wifiOnly = e.detail.value;
      this.updateSyncConfig();
    },
    
    onSyncOnAppStartChange(e) {
      this.syncConfig.syncOnAppStart = e.detail.value;
      this.updateSyncConfig();
    },
    
    updateSyncConfig() {
      this.syncStore.updateSyncConfig(this.syncConfig);
      
      // 重新启动自动同步
      if (this.syncConfig.autoSync) {
        offlineManager.startAutoSync();
      } else {
        offlineManager.stopAutoSync();
      }
    },
    
    clearSyncHistory() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空同步历史记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.syncStore.syncHistory = [];
            this.syncStore.saveToLocal();
            this.syncHistory = [];
            
            uni.showToast({
              title: '已清空',
              icon: 'success'
            });
          }
        }
      });
    },
    
    showConflictDetail(conflict) {
      this.selectedConflict = conflict;
      this.$refs.conflictPopup.open();
    },
    
    closeConflictDetail() {
      this.$refs.conflictPopup.close();
      this.selectedConflict = null;
    },
    
    async resolveConflict(strategy) {
      try {
        const result = await conflictResolver.resolveConflict(this.selectedConflict, strategy);
        
        if (result.success) {
          uni.showToast({
            title: '冲突已解决',
            icon: 'success'
          });
          
          // 刷新冲突列表
          await this.loadSyncData();
          this.closeConflictDetail();
        } else {
          uni.showToast({
            title: result.error || '解决失败',
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('解决冲突失败:', error);
        uni.showToast({
          title: '解决失败',
          icon: 'error'
        });
      }
    },
    
    async resolveAllConflicts() {
      uni.showModal({
        title: '批量解决冲突',
        content: '确定要自动解决所有冲突吗？系统将使用智能合并策略。',
        success: async (res) => {
          if (res.confirm) {
            try {
              const results = await conflictResolver.resolveConflicts(this.conflicts, 'auto');
              const successCount = results.filter(r => r.success).length;
              
              uni.showToast({
                title: `已解决${successCount}个冲突`,
                icon: 'success'
              });
              
              await this.loadSyncData();
            } catch (error) {
              console.error('批量解决冲突失败:', error);
              uni.showToast({
                title: '解决失败',
                icon: 'error'
              });
            }
          }
        }
      });
    },
    
    exportSyncData() {
      // 导出同步数据功能
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    },
    
    importSyncData() {
      // 导入同步数据功能
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    },
    
    resetSyncSettings() {
      uni.showModal({
        title: '重置设置',
        content: '确定要重置所有同步设置吗？',
        success: (res) => {
          if (res.confirm) {
            // 重置为默认配置
            this.syncConfig = {
              autoSync: true,
              syncInterval: 30,
              wifiOnly: true,
              syncOnAppStart: true
            };
            this.updateSyncConfig();
            
            uni.showToast({
              title: '已重置',
              icon: 'success'
            });
          }
        }
      });
    },
    
    clearAllSyncData() {
      uni.showModal({
        title: '危险操作',
        content: '确定要清空所有同步数据吗？此操作不可恢复！',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            // 清空所有同步相关数据
            this.syncStore.syncHistory = [];
            this.syncStore.conflicts = [];
            this.syncStore.pendingSync = { upload: [], download: [], delete: [] };
            this.syncStore.statistics = {
              totalSynced: 0,
              successCount: 0,
              failureCount: 0,
              conflictCount: 0
            };
            this.syncStore.saveToLocal();
            
            // 清空离线队列
            offlineManager.clearOfflineQueue();
            
            this.loadSyncData();
            
            uni.showToast({
              title: '已清空',
              icon: 'success'
            });
          }
        }
      });
    },
    
    getCurrentUserId() {
      try {
        const userInfo = uni.getStorageSync('user_info');
        return userInfo ? userInfo.id : null;
      } catch (error) {
        return null;
      }
    },
    
    getHistoryTypeText(type) {
      const typeMap = {
        'sync': '数据同步',
        'upload': '数据上传',
        'download': '数据下载',
        'conflict': '冲突解决'
      };
      return typeMap[type] || type;
    },
    
    getStatusText(status) {
      const statusMap = {
        'success': '成功',
        'failed': '失败',
        'partial': '部分成功'
      };
      return statusMap[status] || status;
    },
    
    getConflictTypeText(type) {
      const typeMap = {
        'user_info': '用户信息',
        'health_report': '健康报告',
        'health_indicator': '健康指标'
      };
      return typeMap[type] || type;
    },
    
    getSeverityText(severity) {
      const severityMap = {
        'low': '轻微',
        'medium': '中等',
        'high': '严重'
      };
      return severityMap[severity] || severity;
    },
    
    formatTime(timestamp) {
      return syncStatusManager.formatSyncTime(timestamp);
    },
    
    goBack() {
      uni.navigateBack();
    },
    
    cleanup() {
      // 清理监听器
      if (this.progressUnsubscribe) {
        this.progressUnsubscribe();
      }
      if (this.statusUnsubscribe) {
        this.statusUnsubscribe();
      }
      if (this.networkUnsubscribe) {
        this.networkUnsubscribe();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.sync-settings-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.status-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  display: flex;
  align-items: center;
  
  &.online .status-dot {
    background-color: #34C759;
  }
  
  &.offline .status-dot {
    background-color: #FF3B30;
  }
  
  &.syncing .status-dot {
    background-color: #007AFF;
    animation: pulse 1.5s infinite;
  }
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-text {
  font-size: 28rpx;
  color: #666;
}

.status-details {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.sync-progress {
  margin-bottom: 30rpx;
}

.progress-bar {
  height: 8rpx;
  background-color: #E5E5EA;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background-color: #007AFF;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

.sync-actions {
  text-align: center;
}

.sync-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 60rpx;
  font-size: 32rpx;
  
  &:disabled {
    background-color: #C7C7CC;
  }
  
  &.syncing {
    background-color: #FF9500;
  }
}

.config-section, .network-section, .history-section, .conflict-section, .advanced-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #E5E5EA;
}

.clear-btn, .resolve-all-btn {
  font-size: 28rpx;
  color: #007AFF;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #E5E5EA;
  
  &:last-child {
    border-bottom: none;
  }
}

.config-info {
  flex: 1;
}

.config-label {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.config-desc {
  font-size: 24rpx;
  color: #666;
}

.picker-value {
  font-size: 28rpx;
  color: #007AFF;
  padding: 16rpx 0;
}

.network-info {
  padding: 30rpx;
}

.network-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.network-label {
  font-size: 28rpx;
  color: #666;
}

.network-status {
  display: flex;
  align-items: center;
  
  &.online .network-dot {
    background-color: #34C759;
  }
  
  &.offline .network-dot {
    background-color: #FF3B30;
  }
}

.network-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.network-text, .network-value {
  font-size: 28rpx;
  color: #333;
}

.history-list, .conflict-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.history-item, .conflict-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #E5E5EA;
  
  &:last-child {
    border-bottom: none;
  }
}

.history-info, .conflict-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.history-type, .conflict-type {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.history-time, .conflict-time {
  font-size: 24rpx;
  color: #666;
}

.history-status {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  
  &.success {
    background-color: #D1F2EB;
    color: #00C851;
  }
  
  &.failed {
    background-color: #FADBD8;
    color: #FF3B30;
  }
  
  &.partial {
    background-color: #FCF3CF;
    color: #FF9500;
  }
}

.history-message {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.conflict-severity {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  
  &.low {
    background-color: #D1F2EB;
    color: #00C851;
  }
  
  &.medium {
    background-color: #FCF3CF;
    color: #FF9500;
  }
  
  &.high {
    background-color: #FADBD8;
    color: #FF3B30;
  }
}

.empty-history {
  padding: 60rpx;
  text-align: center;
  color: #666;
  font-size: 28rpx;
}

.advanced-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #E5E5EA;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.danger .advanced-label {
    color: #FF3B30;
  }
}

.advanced-label {
  font-size: 32rpx;
  color: #333;
}

.advanced-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
}

.conflict-detail {
  background: white;
  border-radius: 16rpx 16rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.conflict-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.conflict-detail-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.conflict-detail-close {
  font-size: 48rpx;
  color: #666;
}

.conflict-detail-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.conflict-field {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.field-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.field-values {
  display: flex;
  gap: 20rpx;
}

.field-value {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  
  &.local {
    background-color: #E3F2FD;
  }
  
  &.remote {
    background-color: #FFF3E0;
  }
}

.value-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.value-text {
  font-size: 28rpx;
  color: #333;
}

.conflict-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.conflict-btn {
  flex: 1;
  padding: 24rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  
  &.local {
    background-color: #2196F3;
    color: white;
  }
  
  &.remote {
    background-color: #FF9800;
    color: white;
  }
  
  &.merge {
    background-color: #4CAF50;
    color: white;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>