/**
 * 认证服务
 * 处理用户登录、注册、密码重置等认证相关功能
 */

// Mock PlatformAdapter for testing
class MockPlatformAdapter {
  isWechatMiniProgram() { return false }
}

let PlatformAdapter
try {
  // 尝试导入真实的PlatformAdapter
  if (typeof require !== 'undefined') {
    PlatformAdapter = require('../../utils/platform/platformAdapter.js').PlatformAdapter || MockPlatformAdapter
  } else {
    PlatformAdapter = MockPlatformAdapter
  }
} catch (error) {
  PlatformAdapter = MockPlatformAdapter
}

class AuthService {
  constructor() {
    this.baseURL = 'https://api.example.com' // 实际项目中应该从配置文件读取
    this.platformAdapter = new PlatformAdapter()
  }

  /**
   * 发送验证码
   * @param {string} phone 手机号
   * @param {string} type 验证码类型：register, login, reset
   */
  async sendVerificationCode(phone, type = 'login') {
    try {
      // 验证手机号格式
      if (!this.validatePhone(phone)) {
        throw new Error('手机号格式不正确')
      }

      // 检查发送频率限制
      const lastSendTime = uni.getStorageSync(`last_send_code_${phone}`)
      const now = Date.now()
      if (lastSendTime && (now - lastSendTime) < 60000) {
        throw new Error('验证码发送过于频繁，请稍后再试')
      }

      // 模拟API调用
      const response = await this.request('/auth/send-code', {
        method: 'POST',
        data: { phone, type }
      })

      // 记录发送时间（如果存储失败，不影响主要功能）
      try {
        uni.setStorageSync(`last_send_code_${phone}`, now)
      } catch (storageError) {
        console.warn('存储发送时间失败:', storageError)
      }

      return {
        success: true,
        message: '验证码已发送',
        data: response.data
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      return {
        success: false,
        message: error.message || '发送验证码失败'
      }
    }
  }

  /**
   * 手机号验证码注册
   * @param {Object} registerData 注册数据
   */
  async registerWithPhone(registerData) {
    try {
      const { phone, code, password, nickname } = registerData

      // 验证必填字段
      if (!phone || !code || !password) {
        throw new Error('请填写完整信息')
      }

      if (!this.validatePhone(phone)) {
        throw new Error('手机号格式不正确')
      }

      if (!this.validatePassword(password)) {
        throw new Error('密码格式不正确，至少8位包含字母和数字')
      }

      if (!this.validateVerificationCode(code)) {
        throw new Error('验证码格式不正确')
      }

      // 检查手机号是否已注册
      const phoneExists = await this.checkPhoneExists(phone)
      if (phoneExists) {
        throw new Error('该手机号已注册，请直接登录')
      }

      // 模拟API调用
      const response = await this.request('/auth/register', {
        method: 'POST',
        data: { phone, code, password, nickname: nickname || phone }
      })

      return {
        success: true,
        message: '注册成功',
        data: {
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000, // 2小时
          userInfo: response.data.userInfo
        }
      }
    } catch (error) {
      console.error('注册失败:', error)
      return {
        success: false,
        message: error.message || '注册失败'
      }
    }
  }

  /**
   * 密码登录
   * @param {Object} loginData 登录数据
   */
  async loginWithPassword(loginData) {
    try {
      const { phone, password } = loginData

      if (!phone || !password) {
        throw new Error('请输入手机号和密码')
      }

      // 模拟API调用
      const response = await this.request('/auth/login', {
        method: 'POST',
        data: { phone, password }
      })

      return {
        success: true,
        message: '登录成功',
        data: {
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000, // 2小时
          userInfo: response.data.userInfo
        }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return {
        success: false,
        message: error.message || '登录失败'
      }
    }
  }

  /**
   * 验证码登录
   * @param {Object} loginData 登录数据
   */
  async loginWithCode(loginData) {
    try {
      const { phone, code } = loginData

      if (!phone || !code) {
        throw new Error('请输入手机号和验证码')
      }

      // 模拟API调用
      const response = await this.request('/auth/login-code', {
        method: 'POST',
        data: { phone, code }
      })

      return {
        success: true,
        message: '登录成功',
        data: {
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000, // 2小时
          userInfo: response.data.userInfo
        }
      }
    } catch (error) {
      console.error('验证码登录失败:', error)
      return {
        success: false,
        message: error.message || '验证码登录失败'
      }
    }
  }

  /**
   * 微信小程序授权登录
   */
  async loginWithWechat() {
    try {
      // 检查是否在微信小程序环境
      if (!this.platformAdapter.isWechatMiniProgram()) {
        throw new Error('当前环境不支持微信登录')
      }

      // 获取微信授权
      const loginResult = await this.getWechatLogin()
      if (!loginResult.success) {
        throw new Error(loginResult.message)
      }

      // 获取用户信息授权
      const userInfoResult = await this.getWechatUserInfo()
      
      // 调用后端API进行微信登录
      const response = await this.request('/auth/wechat-login', {
        method: 'POST',
        data: {
          code: loginResult.code,
          userInfo: userInfoResult.userInfo,
          encryptedData: userInfoResult.encryptedData,
          iv: userInfoResult.iv
        }
      })

      return {
        success: true,
        message: '微信登录成功',
        data: {
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000,
          userInfo: response.data.userInfo,
          wechatInfo: response.data.wechatInfo
        }
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      return {
        success: false,
        message: error.message || '微信登录失败'
      }
    }
  }

  /**
   * 重置密码
   * @param {Object} resetData 重置数据
   */
  async resetPassword(resetData) {
    try {
      const { phone, code, newPassword } = resetData

      // 验证必填字段
      if (!phone || !code || !newPassword) {
        throw new Error('请填写完整信息')
      }

      // 验证手机号格式
      if (!this.validatePhone(phone)) {
        throw new Error('手机号格式不正确')
      }

      // 验证验证码格式
      if (!this.validateVerificationCode(code)) {
        throw new Error('验证码格式不正确')
      }

      // 验证新密码格式
      if (!this.validatePassword(newPassword)) {
        throw new Error('密码格式不正确，至少8位包含字母和数字')
      }

      // 检查重置频率限制（防止暴力破解）
      const resetAttemptKey = `reset_attempt_${phone}`
      const lastResetAttempt = uni.getStorageSync(resetAttemptKey)
      const now = Date.now()
      
      if (lastResetAttempt && (now - lastResetAttempt) < 60000) {
        throw new Error('重置密码过于频繁，请稍后再试')
      }

      // 记录重置尝试时间
      uni.setStorageSync(resetAttemptKey, now)

      // 验证手机号是否已注册
      const phoneExists = await this.checkPhoneExists(phone)
      if (!phoneExists) {
        throw new Error('该手机号未注册，请先注册')
      }

      // 调用API重置密码
      const response = await this.request('/auth/reset-password', {
        method: 'POST',
        data: { phone, code, newPassword }
      })

      // 重置成功后清除尝试记录
      uni.removeStorageSync(resetAttemptKey)

      // 清除可能存在的旧登录状态
      this.clearAuthData()

      return {
        success: true,
        message: '密码重置成功',
        data: response.data
      }
    } catch (error) {
      console.error('密码重置失败:', error)
      
      // 记录失败尝试（用于安全监控）
      this.logSecurityEvent('password_reset_failed', {
        phone: resetData.phone,
        error: error.message,
        timestamp: Date.now()
      })

      return {
        success: false,
        message: error.message || '密码重置失败'
      }
    }
  }

  /**
   * 清除认证数据
   */
  clearAuthData() {
    try {
      uni.removeStorageSync('auth_token')
      uni.removeStorageSync('refresh_token')
      uni.removeStorageSync('token_expiry')
      uni.removeStorageSync('user_info')
    } catch (error) {
      console.error('清除认证数据失败:', error)
    }
  }

  /**
   * 记录安全事件
   * @param {string} eventType 事件类型
   * @param {Object} eventData 事件数据
   */
  logSecurityEvent(eventType, eventData) {
    try {
      const securityLogs = uni.getStorageSync('security_logs') || []
      securityLogs.push({
        type: eventType,
        data: eventData,
        timestamp: Date.now()
      })
      
      // 只保留最近100条记录
      if (securityLogs.length > 100) {
        securityLogs.splice(0, securityLogs.length - 100)
      }
      
      uni.setStorageSync('security_logs', securityLogs)
    } catch (error) {
      console.error('记录安全事件失败:', error)
    }
  }

  /**
   * 刷新token
   * @param {string} refreshToken 刷新token
   */
  async refreshToken(refreshToken) {
    try {
      if (!refreshToken) {
        throw new Error('刷新token不存在')
      }

      const response = await this.request('/auth/refresh-token', {
        method: 'POST',
        data: { refreshToken }
      })

      return {
        success: true,
        data: {
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000
        }
      }
    } catch (error) {
      console.error('刷新token失败:', error)
      return {
        success: false,
        message: error.message || '刷新token失败'
      }
    }
  }

  /**
   * 生物识别登录
   */
  async loginWithBiometric() {
    try {
      // 检查生物识别支持
      const biometricSupport = await this.checkBiometricSupport()
      if (!biometricSupport.success) {
        throw new Error(biometricSupport.message)
      }

      // 进行生物识别验证
      const verifyResult = await this.verifyBiometric()
      if (!verifyResult.success) {
        throw new Error(verifyResult.message)
      }

      // 获取本地存储的用户信息
      const savedUserInfo = uni.getStorageSync('user_info')
      const savedToken = uni.getStorageSync('auth_token')

      if (!savedUserInfo || !savedToken) {
        throw new Error('未找到本地用户信息，请重新登录')
      }

      // 验证token有效性
      const tokenValid = await this.validateToken(savedToken)
      if (!tokenValid.success) {
        throw new Error('登录状态已过期，请重新登录')
      }

      return {
        success: true,
        message: '生物识别登录成功',
        data: {
          token: savedToken,
          userInfo: savedUserInfo
        }
      }
    } catch (error) {
      console.error('生物识别登录失败:', error)
      return {
        success: false,
        message: error.message || '生物识别登录失败'
      }
    }
  }

  // 私有方法

  /**
   * 获取微信登录授权
   */
  async getWechatLogin() {
    return new Promise((resolve) => {
      uni.login({
        provider: 'weixin',
        success: (res) => {
          resolve({
            success: true,
            code: res.code
          })
        },
        fail: (err) => {
          resolve({
            success: false,
            message: '微信授权失败'
          })
        }
      })
    })
  }

  /**
   * 获取微信用户信息
   */
  async getWechatUserInfo() {
    return new Promise((resolve) => {
      uni.getUserInfo({
        provider: 'weixin',
        success: (res) => {
          resolve({
            success: true,
            userInfo: res.userInfo,
            encryptedData: res.encryptedData,
            iv: res.iv
          })
        },
        fail: (err) => {
          resolve({
            success: false,
            message: '获取用户信息失败'
          })
        }
      })
    })
  }

  /**
   * 检查生物识别支持
   */
  async checkBiometricSupport() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.fingerprint.isKeyguardSecure({
        success: () => {
          plus.fingerprint.isSupported({
            success: () => {
              resolve({ success: true })
            },
            fail: () => {
              resolve({ success: false, message: '设备不支持生物识别' })
            }
          })
        },
        fail: () => {
          resolve({ success: false, message: '设备未设置锁屏密码' })
        }
      })
      // #endif
      
      // #ifndef APP-PLUS
      resolve({ success: false, message: '当前平台不支持生物识别' })
      // #endif
    })
  }

  /**
   * 进行生物识别验证
   */
  async verifyBiometric() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.fingerprint.authenticate({
        success: () => {
          resolve({ success: true })
        },
        fail: (err) => {
          resolve({ success: false, message: '生物识别验证失败' })
        }
      })
      // #endif
      
      // #ifndef APP-PLUS
      resolve({ success: false, message: '当前平台不支持生物识别' })
      // #endif
    })
  }

  /**
   * 验证token有效性
   */
  async validateToken(token) {
    try {
      const response = await this.request('/auth/validate-token', {
        method: 'POST',
        data: { token }
      })
      
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, message: 'Token无效' }
    }
  }

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  /**
   * 验证密码格式
   */
  validatePassword(password) {
    // 至少8位，包含字母和数字
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/
    return passwordRegex.test(password)
  }

  /**
   * 验证验证码格式
   */
  validateVerificationCode(code) {
    // 6位数字
    const codeRegex = /^\d{6}$/
    return codeRegex.test(code)
  }

  /**
   * 检查手机号是否已存在
   */
  async checkPhoneExists(phone) {
    try {
      const response = await this.request('/auth/check-phone', {
        method: 'POST',
        data: { phone }
      })
      return response.data.exists
    } catch (error) {
      console.error('检查手机号失败:', error)
      return false // 默认不存在，允许注册
    }
  }

  /**
   * 通用请求方法
   */
  async request(url, options = {}) {
    return new Promise((resolve, reject) => {
      // 模拟网络请求延迟
      const delay = Math.random() * 1000 + 500 // 500-1500ms
      
      setTimeout(() => {
        // 根据不同的URL模拟不同的响应
        if (url === '/auth/check-phone') {
          // 模拟手机号检查，某些特定号码已存在
          const existingPhones = ['13800138000', '13900139000']
          const exists = existingPhones.includes(options.data?.phone)
          resolve({ data: { exists } })
          return
        }

        if (url === '/auth/send-code') {
          // 模拟验证码发送，95%成功率
          if (Math.random() > 0.05) {
            resolve({
              data: {
                codeId: 'code_' + Date.now(),
                expiry: Date.now() + 5 * 60 * 1000 // 5分钟过期
              }
            })
          } else {
            reject(new Error('验证码发送失败'))
          }
          return
        }

        if (url === '/auth/register') {
          // 模拟注册，验证码123456为有效验证码
          if (options.data?.code === '123456') {
            resolve({
              data: {
                token: 'mock_token_' + Date.now(),
                refreshToken: 'mock_refresh_token_' + Date.now(),
                userInfo: {
                  id: 'user_' + Date.now(),
                  phone: options.data?.phone || '13800138000',
                  nickname: options.data?.nickname || '用户',
                  avatar: '',
                  gender: '',
                  birthday: null,
                  height: null,
                  weight: null,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  settings: {
                    biometricEnabled: false,
                    autoSync: true,
                    notificationEnabled: true,
                    theme: 'light'
                  }
                }
              }
            })
          } else {
            reject(new Error('验证码错误'))
          }
          return
        }

        if (url === '/auth/reset-password') {
          // 模拟密码重置
          const { phone, code, newPassword } = options.data || {}
          
          // 检查手机号是否存在（已注册用户）
          const existingPhones = ['13800138000', '13900139000', '15800158000']
          if (!existingPhones.includes(phone)) {
            reject(new Error('该手机号未注册，请先注册'))
            return
          }
          
          // 验证码123456为有效验证码
          if (code !== '123456') {
            reject(new Error('验证码错误或已过期'))
            return
          }
          
          // 模拟密码强度检查
          if (!newPassword || newPassword.length < 8) {
            reject(new Error('密码至少8位'))
            return
          }
          
          if (newPassword.length > 20) {
            reject(new Error('密码不能超过20位'))
            return
          }
          
          if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,20}$/.test(newPassword)) {
            reject(new Error('密码必须包含字母和数字'))
            return
          }
          
          // 检查常见弱密码
          const weakPasswords = ['12345678', '87654321', 'password', 'password123', '123456789']
          if (weakPasswords.includes(newPassword.toLowerCase())) {
            reject(new Error('密码强度过低，请使用更复杂的密码'))
            return
          }
          
          // 模拟成功响应
          resolve({
            data: {
              success: true,
              message: '密码重置成功',
              resetTime: new Date().toISOString()
            }
          })
          return
        }

        // 其他请求的通用模拟
        if (Math.random() > 0.1) { // 90%成功率
          resolve({
            data: {
              token: 'mock_token_' + Date.now(),
              refreshToken: 'mock_refresh_token_' + Date.now(),
              userInfo: {
                id: 'user_' + Date.now(),
                phone: options.data?.phone || '13800138000',
                nickname: options.data?.nickname || '用户',
                avatar: '',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              },
              wechatInfo: {
                openid: 'mock_openid',
                unionid: 'mock_unionid'
              }
            }
          })
        } else {
          // 模拟失败响应
          reject(new Error('网络请求失败'))
        }
      }, delay)
    })
  }
}

const authServiceInstance = new AuthService()

// 支持ES模块和CommonJS
if (typeof module !== 'undefined' && module.exports) {
  module.exports = authServiceInstance
}

// 为了向后兼容，也设置default属性
if (typeof module !== 'undefined' && module.exports) {
  module.exports.default = authServiceInstance
}