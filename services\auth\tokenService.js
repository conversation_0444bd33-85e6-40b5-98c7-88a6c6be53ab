/**
 * Token管理服务
 * 处理JWT token的存储、刷新、验证等功能
 */

import authService from './authService.js'

class TokenService {
  constructor() {
    this.refreshTimer = null
    this.isRefreshing = false
    this.refreshPromise = null
  }

  /**
   * 保存token信息
   * @param {Object} tokenData token数据
   */
  saveTokens(tokenData) {
    const { token, refreshToken, tokenExpiry } = tokenData
    
    // 保存到本地存储
    uni.setStorageSync('auth_token', token)
    uni.setStorageSync('refresh_token', refreshToken)
    uni.setStorageSync('token_expiry', tokenExpiry)
    
    // 设置自动刷新
    this.scheduleTokenRefresh(tokenExpiry)
  }

  /**
   * 获取当前token
   */
  getToken() {
    return uni.getStorageSync('auth_token') || ''
  }

  /**
   * 获取刷新token
   */
  getRefreshToken() {
    return uni.getStorageSync('refresh_token') || ''
  }

  /**
   * 获取token过期时间
   */
  getTokenExpiry() {
    return uni.getStorageSync('token_expiry') || 0
  }

  /**
   * 检查token是否有效
   */
  isTokenValid() {
    const token = this.getToken()
    const expiry = this.getTokenExpiry()
    
    if (!token || !expiry) {
      return false
    }
    
    // 提前5分钟判断为过期，确保有足够时间刷新
    return Date.now() < (expiry - 5 * 60 * 1000)
  }

  /**
   * 检查是否需要刷新token
   */
  needsRefresh() {
    const expiry = this.getTokenExpiry()
    if (!expiry) return false
    
    // 提前10分钟开始刷新
    return Date.now() > (expiry - 10 * 60 * 1000)
  }

  /**
   * 刷新token
   */
  async refreshToken() {
    // 如果正在刷新，返回现有的Promise
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise
    }

    this.isRefreshing = true
    
    this.refreshPromise = this.performTokenRefresh()
    
    try {
      const result = await this.refreshPromise
      return result
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  /**
   * 执行token刷新
   */
  async performTokenRefresh() {
    try {
      const refreshToken = this.getRefreshToken()
      
      if (!refreshToken) {
        throw new Error('没有刷新token')
      }

      console.log('开始刷新token...')
      
      const result = await authService.refreshToken(refreshToken)
      
      if (result.success) {
        // 保存新的token信息
        this.saveTokens(result.data)
        
        console.log('Token刷新成功')
        
        return {
          success: true,
          token: result.data.token
        }
      } else {
        throw new Error(result.message || 'Token刷新失败')
      }
    } catch (error) {
      console.error('Token刷新失败:', error)
      
      // 刷新失败，清除所有token信息
      this.clearTokens()
      
      // 通知用户需要重新登录
      this.notifyTokenExpired()
      
      return {
        success: false,
        message: error.message || 'Token刷新失败'
      }
    }
  }

  /**
   * 安排token自动刷新
   * @param {number} expiry token过期时间
   */
  scheduleTokenRefresh(expiry) {
    // 清除之前的定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }

    if (!expiry) return

    // 计算刷新时间（提前10分钟）
    const refreshTime = expiry - Date.now() - 10 * 60 * 1000
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(() => {
        this.refreshToken()
      }, refreshTime)
      
      console.log(`Token将在${Math.round(refreshTime / 1000 / 60)}分钟后自动刷新`)
    } else {
      // 如果已经需要刷新，立即执行
      this.refreshToken()
    }
  }

  /**
   * 清除所有token信息
   */
  clearTokens() {
    uni.removeStorageSync('auth_token')
    uni.removeStorageSync('refresh_token')
    uni.removeStorageSync('token_expiry')
    
    // 清除定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  /**
   * 通知token过期
   */
  notifyTokenExpired() {
    // 发送全局事件
    uni.$emit('token-expired')
    
    // 显示提示
    uni.showModal({
      title: '登录已过期',
      content: '您的登录状态已过期，请重新登录',
      showCancel: false,
      success: () => {
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/auth/login'
        })
      }
    })
  }

  /**
   * 初始化token服务
   */
  async init() {
    try {
      const token = this.getToken()
      const expiry = this.getTokenExpiry()
      
      if (token && expiry) {
        if (this.isTokenValid()) {
          // Token有效，设置自动刷新
          this.scheduleTokenRefresh(expiry)
        } else if (this.getRefreshToken()) {
          // Token过期但有刷新token，尝试刷新
          await this.refreshToken()
        } else {
          // 没有有效token，清除所有信息
          this.clearTokens()
        }
      }
    } catch (error) {
      console.error('Token服务初始化失败:', error)
      this.clearTokens()
    }
  }

  /**
   * 获取带有token的请求头
   */
  getAuthHeaders() {
    const token = this.getToken()
    
    if (token) {
      return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
    
    return {
      'Content-Type': 'application/json'
    }
  }

  /**
   * 检查并刷新token（用于请求拦截器）
   */
  async ensureValidToken() {
    if (!this.isTokenValid()) {
      if (this.getRefreshToken()) {
        const result = await this.refreshToken()
        if (!result.success) {
          throw new Error('Token刷新失败')
        }
        return result.token
      } else {
        throw new Error('没有有效的认证信息')
      }
    }
    
    return this.getToken()
  }
}

export default new TokenService()