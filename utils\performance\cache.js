/**
 * 多级缓存策略实现
 */

// L1缓存：内存缓存
class MemoryCache {
  constructor(options = {}) {
    this.cache = new Map()
    this.maxSize = options.maxSize || 100
    this.defaultTTL = options.defaultTTL || 300000 // 5分钟
    this.accessOrder = new Map() // LRU访问顺序
  }

  set(key, value, ttl = this.defaultTTL) {
    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU()
    }

    const item = {
      value,
      timestamp: Date.now(),
      ttl,
      accessCount: 0
    }

    this.cache.set(key, item)
    this.accessOrder.set(key, Date.now())
  }

  get(key) {
    const item = this.cache.get(key)
    if (!item) return null

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      this.accessOrder.delete(key)
      return null
    }

    // 更新访问信息
    item.accessCount++
    this.accessOrder.set(key, Date.now())

    return item.value
  }

  has(key) {
    return this.cache.has(key) && this.get(key) !== null
  }

  delete(key) {
    this.cache.delete(key)
    this.accessOrder.delete(key)
  }

  clear() {
    this.cache.clear()
    this.accessOrder.clear()
  }

  // 淘汰最少使用的项
  evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, time] of this.accessOrder.entries()) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  // 获取缓存统计
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate()
    }
  }

  calculateHitRate() {
    let totalAccess = 0
    for (const item of this.cache.values()) {
      totalAccess += item.accessCount
    }
    return totalAccess > 0 ? (this.cache.size / totalAccess) : 0
  }
}

// L2缓存：本地存储缓存
class StorageCache {
  constructor(options = {}) {
    this.prefix = options.prefix || 'cache_'
    this.defaultTTL = options.defaultTTL || 86400000 // 24小时
    this.maxSize = options.maxSize || 200
    this.compressionThreshold = options.compressionThreshold || 1024 // 1KB
  }

  set(key, value, ttl = this.defaultTTL) {
    try {
      const item = {
        value: this.compressIfNeeded(value),
        timestamp: Date.now(),
        ttl,
        compressed: this.shouldCompress(value)
      }

      const storageKey = this.prefix + key
      uni.setStorageSync(storageKey, item)

      // 更新索引
      this.updateIndex(key)
    } catch (error) {
      console.error('设置存储缓存失败:', error)
      // 如果存储失败，尝试清理过期缓存后重试
      this.cleanExpired()
      try {
        uni.setStorageSync(this.prefix + key, {
          value: this.compressIfNeeded(value),
          timestamp: Date.now(),
          ttl,
          compressed: this.shouldCompress(value)
        })
      } catch (retryError) {
        console.error('重试设置存储缓存失败:', retryError)
      }
    }
  }

  get(key) {
    try {
      const storageKey = this.prefix + key
      const item = uni.getStorageSync(storageKey)
      
      if (!item) return null

      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        uni.removeStorageSync(storageKey)
        this.removeFromIndex(key)
        return null
      }

      // 解压缩数据
      return item.compressed ? this.decompress(item.value) : item.value
    } catch (error) {
      console.error('获取存储缓存失败:', error)
      return null
    }
  }

  has(key) {
    return this.get(key) !== null
  }

  delete(key) {
    try {
      uni.removeStorageSync(this.prefix + key)
      this.removeFromIndex(key)
    } catch (error) {
      console.error('删除存储缓存失败:', error)
    }
  }

  clear() {
    try {
      const index = this.getIndex()
      index.forEach(key => {
        uni.removeStorageSync(this.prefix + key)
      })
      this.clearIndex()
    } catch (error) {
      console.error('清除存储缓存失败:', error)
    }
  }

  // 压缩数据
  compressIfNeeded(value) {
    if (!this.shouldCompress(value)) {
      return value
    }
    
    // 简单的JSON压缩（实际项目中可以使用更好的压缩算法）
    try {
      const jsonString = JSON.stringify(value)
      return this.simpleCompress(jsonString)
    } catch (error) {
      console.error('压缩数据失败:', error)
      return value
    }
  }

  // 判断是否需要压缩
  shouldCompress(value) {
    try {
      const size = JSON.stringify(value).length
      return size > this.compressionThreshold
    } catch (error) {
      return false
    }
  }

  // 简单压缩算法
  simpleCompress(str) {
    // 这里使用简单的重复字符压缩
    return str.replace(/(.)\1+/g, (match, char) => {
      return char + match.length
    })
  }

  // 解压缩
  decompress(compressed) {
    try {
      // 解压缩逻辑
      const decompressed = compressed.replace(/(.)\d+/g, (match, char, count) => {
        return char.repeat(parseInt(count))
      })
      return JSON.parse(decompressed)
    } catch (error) {
      console.error('解压缩数据失败:', error)
      return compressed
    }
  }

  // 更新索引
  updateIndex(key) {
    try {
      const index = this.getIndex()
      if (!index.includes(key)) {
        index.push(key)
        
        // 如果超过最大大小，删除最旧的项
        if (index.length > this.maxSize) {
          const oldestKey = index.shift()
          this.delete(oldestKey)
        }
        
        uni.setStorageSync(this.prefix + 'index', index)
      }
    } catch (error) {
      console.error('更新缓存索引失败:', error)
    }
  }

  // 获取索引
  getIndex() {
    try {
      return uni.getStorageSync(this.prefix + 'index') || []
    } catch (error) {
      return []
    }
  }

  // 从索引中移除
  removeFromIndex(key) {
    try {
      const index = this.getIndex()
      const newIndex = index.filter(k => k !== key)
      uni.setStorageSync(this.prefix + 'index', newIndex)
    } catch (error) {
      console.error('从索引移除失败:', error)
    }
  }

  // 清除索引
  clearIndex() {
    try {
      uni.removeStorageSync(this.prefix + 'index')
    } catch (error) {
      console.error('清除索引失败:', error)
    }
  }

  // 清理过期缓存
  cleanExpired() {
    try {
      const index = this.getIndex()
      const validKeys = []
      
      index.forEach(key => {
        const item = uni.getStorageSync(this.prefix + key)
        if (item && Date.now() - item.timestamp <= item.ttl) {
          validKeys.push(key)
        } else {
          uni.removeStorageSync(this.prefix + key)
        }
      })
      
      uni.setStorageSync(this.prefix + 'index', validKeys)
    } catch (error) {
      console.error('清理过期缓存失败:', error)
    }
  }

  // 获取缓存统计
  getStats() {
    const index = this.getIndex()
    return {
      size: index.length,
      maxSize: this.maxSize
    }
  }
}

// 多级缓存管理器
class MultiLevelCache {
  constructor(options = {}) {
    this.l1Cache = new MemoryCache(options.l1 || {})
    this.l2Cache = new StorageCache(options.l2 || {})
    this.stats = {
      l1Hits: 0,
      l2Hits: 0,
      misses: 0,
      sets: 0
    }
  }

  async set(key, value, options = {}) {
    const { l1TTL, l2TTL, skipL1, skipL2 } = options
    
    this.stats.sets++

    // 设置L1缓存
    if (!skipL1) {
      this.l1Cache.set(key, value, l1TTL)
    }

    // 设置L2缓存
    if (!skipL2) {
      this.l2Cache.set(key, value, l2TTL)
    }
  }

  async get(key) {
    // 先尝试L1缓存
    let value = this.l1Cache.get(key)
    if (value !== null) {
      this.stats.l1Hits++
      return value
    }

    // 尝试L2缓存
    value = this.l2Cache.get(key)
    if (value !== null) {
      this.stats.l2Hits++
      
      // 将数据提升到L1缓存
      this.l1Cache.set(key, value)
      return value
    }

    this.stats.misses++
    return null
  }

  async has(key) {
    return this.l1Cache.has(key) || this.l2Cache.has(key)
  }

  async delete(key) {
    this.l1Cache.delete(key)
    this.l2Cache.delete(key)
  }

  async clear() {
    this.l1Cache.clear()
    this.l2Cache.clear()
    this.resetStats()
  }

  // 预热缓存
  async warmup(dataLoader, keys) {
    const promises = keys.map(async key => {
      try {
        const value = await dataLoader(key)
        if (value !== null) {
          await this.set(key, value)
        }
      } catch (error) {
        console.error(`预热缓存失败 ${key}:`, error)
      }
    })

    await Promise.all(promises)
  }

  // 批量获取
  async getMultiple(keys) {
    const results = {}
    const missingKeys = []

    // 先从缓存获取
    for (const key of keys) {
      const value = await this.get(key)
      if (value !== null) {
        results[key] = value
      } else {
        missingKeys.push(key)
      }
    }

    return { results, missingKeys }
  }

  // 批量设置
  async setMultiple(keyValuePairs, options = {}) {
    const promises = Object.entries(keyValuePairs).map(([key, value]) =>
      this.set(key, value, options)
    )
    
    await Promise.all(promises)
  }

  // 获取缓存统计
  getStats() {
    const total = this.stats.l1Hits + this.stats.l2Hits + this.stats.misses
    
    return {
      ...this.stats,
      total,
      hitRate: total > 0 ? ((this.stats.l1Hits + this.stats.l2Hits) / total) : 0,
      l1HitRate: total > 0 ? (this.stats.l1Hits / total) : 0,
      l2HitRate: total > 0 ? (this.stats.l2Hits / total) : 0,
      l1Stats: this.l1Cache.getStats(),
      l2Stats: this.l2Cache.getStats()
    }
  }

  // 重置统计
  resetStats() {
    this.stats = {
      l1Hits: 0,
      l2Hits: 0,
      misses: 0,
      sets: 0
    }
  }

  // 清理过期缓存
  async cleanup() {
    // L1缓存会自动清理过期项
    // 手动清理L2缓存
    this.l2Cache.cleanExpired()
  }
}

// 缓存装饰器
export function cached(options = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    const cacheKey = options.key || `${target.constructor.name}_${propertyKey}`
    const ttl = options.ttl || 300000 // 5分钟
    
    descriptor.value = async function(...args) {
      const cache = this.cache || new MultiLevelCache()
      const key = `${cacheKey}_${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      let result = await cache.get(key)
      if (result !== null) {
        return result
      }
      
      // 执行原方法
      result = await originalMethod.apply(this, args)
      
      // 缓存结果
      if (result !== null && result !== undefined) {
        await cache.set(key, result, { l1TTL: ttl, l2TTL: ttl * 2 })
      }
      
      return result
    }
    
    return descriptor
  }
}

// 缓存混入
export const cacheMixin = {
  data() {
    return {
      cache: null
    }
  },
  
  created() {
    this.cache = new MultiLevelCache({
      l1: { maxSize: 50, defaultTTL: 300000 }, // 5分钟
      l2: { maxSize: 200, defaultTTL: 86400000 } // 24小时
    })
  },
  
  methods: {
    // 缓存数据
    async cacheData(key, value, options) {
      return await this.cache.set(key, value, options)
    },
    
    // 获取缓存数据
    async getCachedData(key) {
      return await this.cache.get(key)
    },
    
    // 批量获取缓存
    async getCachedDataMultiple(keys) {
      return await this.cache.getMultiple(keys)
    },
    
    // 预热缓存
    async warmupCache(dataLoader, keys) {
      return await this.cache.warmup(dataLoader, keys)
    },
    
    // 清理缓存
    async cleanupCache() {
      return await this.cache.cleanup()
    },
    
    // 获取缓存统计
    getCacheStats() {
      return this.cache.getStats()
    }
  },
  
  beforeDestroy() {
    if (this.cache) {
      this.cache.cleanup()
    }
  }
}

export { MemoryCache, StorageCache, MultiLevelCache }