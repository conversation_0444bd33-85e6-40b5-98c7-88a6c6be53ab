/**
 * AnalysisRepository 单元测试
 * 测试分析数据访问层功能
 */

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { AnalysisRepository } = require('../../core/storage/AnalysisRepository.js')
const { Analysis } = require('../../models/Analysis.js')

// Mock StorageManager
const mockStorageManager = {
  insert: jest.fn(),
  findById: jest.fn(),
  findAll: jest.fn(),
  findBy: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  beginTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn()
}

// Mock logger
jest.mock('../../core/logger/Logger.js', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}))

describe('AnalysisRepository', () => {
  let analysisRepository
  
  beforeEach(() => {
    jest.clearAllMocks()
    analysisRepository = new AnalysisRepository(mockStorageManager)
  })
  
  describe('构造函数', () => {
    it('应该正确初始化', () => {
      expect(analysisRepository.storageManager).toBe(mockStorageManager)
      expect(analysisRepository.modelClass).toBe(Analysis)
      expect(analysisRepository.tableName).toBe('analysis')
    })
  })
  
  describe('findByUserIdAndType', () => {
    it('应该根据用户ID和类型查找分析结果', async () => {
      const mockAnalyses = [
        { id: 'analysis1', userId: 'user1', type: 'trend' },
        { id: 'analysis2', userId: 'user1', type: 'trend' }
      ]
      
      mockStorageManager.findBy.mockResolvedValue(mockAnalyses)
      Analysis.fromJSON = jest.fn()
        .mockReturnValueOnce(mockAnalyses[0])
        .mockReturnValueOnce(mockAnalyses[1])
      
      const result = await analysisRepository.findByUserIdAndType('user1', 'trend')
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('analysis',
        { userId: 'user1', type: 'trend' },
        { sort: { createdAt: -1 } }
      )
      expect(result).toHaveLength(2)
    })
  })
  
  describe('getLatestAnalysis', () => {
    it('应该获取最新分析结果', async () => {
      const mockAnalysis = { id: 'analysis1', userId: 'user1', type: 'trend' }
      
      analysisRepository.findByUserIdAndType = jest.fn().mockResolvedValue([mockAnalysis])
      
      const result = await analysisRepository.getLatestAnalysis('user1', 'trend')
      
      expect(analysisRepository.findByUserIdAndType).toHaveBeenCalledWith('user1', 'trend')
      expect(result).toBe(mockAnalysis)
    })
    
    it('应该在没有分析结果时返回null', async () => {
      analysisRepository.findByUserIdAndType = jest.fn().mockResolvedValue([])
      
      const result = await analysisRepository.getLatestAnalysis('user1', 'trend')
      
      expect(result).toBeNull()
    })
  })
  
  describe('cleanupExpiredAnalysis', () => {
    it('应该清理过期分析结果', async () => {
      const expiredAnalyses = [
        { id: 'analysis1', createdAt: new Date('2023-01-01') },
        { id: 'analysis2', createdAt: new Date('2023-01-02') }
      ]
      
      analysisRepository.findBy = jest.fn().mockResolvedValue(expiredAnalyses)
      analysisRepository.delete = jest.fn().mockResolvedValue(true)
      
      const result = await analysisRepository.cleanupExpiredAnalysis(30)
      
      expect(analysisRepository.findBy).toHaveBeenCalledWith({
        createdAt: { $lt: expect.any(Date) }
      })
      expect(analysisRepository.delete).toHaveBeenCalledTimes(2)
      expect(result).toBe(2)
    })
  })
  
  describe('findByDateRange', () => {
    it('应该根据时间范围查找分析结果', async () => {
      const startDate = new Date('2023-01-01')
      const endDate = new Date('2023-12-31')
      
      mockStorageManager.findBy.mockResolvedValue([])
      
      await analysisRepository.findByDateRange('user1', startDate, endDate)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('analysis', {
        userId: 'user1',
        'timeRange.start': { $gte: startDate },
        'timeRange.end': { $lte: endDate }
      }, { sort: { createdAt: -1 } })
    })
  })
  
  describe('findByItems', () => {
    it('应该根据分析项目查找结果', async () => {
      const items = ['白细胞计数', '红细胞计数']
      
      mockStorageManager.findBy.mockResolvedValue([])
      
      await analysisRepository.findByItems('user1', items)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('analysis', {
        userId: 'user1',
        items: { $in: items }
      }, { sort: { createdAt: -1 } })
    })
  })
  
  describe('getTrendAnalysis', () => {
    it('应该获取趋势分析结果', async () => {
      mockStorageManager.findBy.mockResolvedValue([])
      
      await analysisRepository.getTrendAnalysis('user1', '白细胞计数', 5)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('analysis', {
        userId: 'user1',
        type: 'trend',
        items: '白细胞计数'
      }, {
        sort: { createdAt: -1 },
        limit: 5
      })
    })
  })
  
  describe('getComparisonAnalysis', () => {
    it('应该获取对比分析结果', async () => {
      const items = ['白细胞计数', '红细胞计数']
      
      mockStorageManager.findBy.mockResolvedValue([])
      
      await analysisRepository.getComparisonAnalysis('user1', items)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('analysis', {
        userId: 'user1',
        type: 'comparison',
        items: { $all: items }
      }, { sort: { createdAt: -1 } })
    })
  })
  
  describe('getHealthSummary', () => {
    it('应该获取健康摘要分析', async () => {
      mockStorageManager.findBy.mockResolvedValue([])
      
      await analysisRepository.getHealthSummary('user1', 3)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('analysis', {
        userId: 'user1',
        type: 'summary'
      }, {
        sort: { createdAt: -1 },
        limit: 3
      })
    })
  })
  
  describe('saveAnalysis', () => {
    it('应该保存分析结果', async () => {
      const analysisData = {
        timeRange: {
          start: new Date('2023-01-01'),
          end: new Date('2023-12-31')
        },
        items: ['白细胞计数'],
        results: {
          trends: [],
          summary: '健康状况良好'
        }
      }
      
      const expectedData = {
        userId: 'user1',
        type: 'trend',
        ...analysisData,
        createdAt: expect.any(Date)
      }
      
      analysisRepository.create = jest.fn().mockResolvedValue(expectedData)
      
      const result = await analysisRepository.saveAnalysis('user1', 'trend', analysisData)
      
      expect(analysisRepository.create).toHaveBeenCalledWith(expectedData)
      expect(result).toBe(expectedData)
    })
  })
  
  describe('updateAnalysis', () => {
    it('应该更新分析结果', async () => {
      const updateData = { summary: '更新的摘要' }
      const updatedAnalysis = { id: 'analysis1', ...updateData }
      
      analysisRepository.update = jest.fn().mockResolvedValue(updatedAnalysis)
      
      const result = await analysisRepository.updateAnalysis('analysis1', updateData)
      
      expect(analysisRepository.update).toHaveBeenCalledWith('analysis1', updateData)
      expect(result).toBe(updatedAnalysis)
    })
  })
  
  describe('deleteUserAnalyses', () => {
    it('应该删除用户的所有分析结果', async () => {
      const userAnalyses = [
        { id: 'analysis1', userId: 'user1' },
        { id: 'analysis2', userId: 'user1' }
      ]
      
      analysisRepository.findBy = jest.fn().mockResolvedValue(userAnalyses)
      analysisRepository.delete = jest.fn().mockResolvedValue(true)
      
      const result = await analysisRepository.deleteUserAnalyses('user1')
      
      expect(analysisRepository.findBy).toHaveBeenCalledWith({ userId: 'user1' })
      expect(analysisRepository.delete).toHaveBeenCalledTimes(2)
      expect(result).toBe(2)
    })
  })
  
  describe('getAnalysisStats', () => {
    it('应该获取分析统计信息', async () => {
      const mockAnalyses = [
        {
          id: 'analysis1',
          type: 'trend',
          createdAt: new Date() // 使用当前时间，确保在最近30天内
        },
        {
          id: 'analysis2',
          type: 'comparison',
          createdAt: new Date('2023-11-01')
        },
        {
          id: 'analysis3',
          type: 'trend',
          createdAt: new Date('2023-10-01')
        }
      ]
      
      analysisRepository.findBy = jest.fn().mockResolvedValue(mockAnalyses)
      
      const result = await analysisRepository.getAnalysisStats('user1')
      
      expect(result.totalAnalyses).toBe(3)
      expect(result.typeCount.trend).toBe(2)
      expect(result.typeCount.comparison).toBe(1)
      expect(result.recentAnalyses).toBe(1) // 只有一个在最近30天内
      expect(result.oldestAnalysis.id).toBe('analysis3')
      expect(result.newestAnalysis.id).toBe('analysis1')
    })
  })
  
  describe('batchCreateAnalyses', () => {
    it('应该批量创建分析结果', async () => {
      const analysisDataList = [
        { userId: 'user1', type: 'trend' },
        { userId: 'user1', type: 'comparison' }
      ]
      
      const expectedOperations = [
        { type: 'create', data: analysisDataList[0] },
        { type: 'create', data: analysisDataList[1] }
      ]
      
      analysisRepository.batch = jest.fn().mockResolvedValue(['result1', 'result2'])
      
      const result = await analysisRepository.batchCreateAnalyses(analysisDataList)
      
      expect(analysisRepository.batch).toHaveBeenCalledWith(expectedOperations)
      expect(result).toEqual(['result1', 'result2'])
    })
  })
  
  describe('findSimilarAnalyses', () => {
    it('应该查找相似的分析结果', async () => {
      const items = ['白细胞计数']
      const timeRange = {
        start: new Date('2023-06-01'),
        end: new Date('2023-06-30')
      }
      
      mockStorageManager.findBy.mockResolvedValue([])
      
      await analysisRepository.findSimilarAnalyses('user1', 'trend', items, timeRange)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('analysis', {
        userId: 'user1',
        type: 'trend',
        items: { $in: items },
        'timeRange.start': {
          $gte: expect.any(Date), // 前一周
          $lte: expect.any(Date)  // 后一周
        }
      }, { sort: { createdAt: -1 } })
    })
  })
  
  describe('exportAnalyses', () => {
    it('应该导出所有分析结果', async () => {
      analysisRepository.exportData = jest.fn().mockResolvedValue('{"analyses": []}')
      
      const result = await analysisRepository.exportAnalyses('user1', null, 'json')
      
      expect(analysisRepository.exportData).toHaveBeenCalledWith({ userId: 'user1' }, 'json')
      expect(result).toBe('{"analyses": []}')
    })
    
    it('应该导出指定类型的分析结果', async () => {
      analysisRepository.exportData = jest.fn().mockResolvedValue('{"analyses": []}')
      
      const result = await analysisRepository.exportAnalyses('user1', 'trend', 'json')
      
      expect(analysisRepository.exportData).toHaveBeenCalledWith(
        { userId: 'user1', type: 'trend' }, 
        'json'
      )
      expect(result).toBe('{"analyses": []}')
    })
  })
})