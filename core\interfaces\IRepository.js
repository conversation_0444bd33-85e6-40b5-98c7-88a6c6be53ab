/**
 * Repository接口定义
 * 定义数据访问层的标准接口
 */

/**
 * 基础Repository接口
 * 所有数据访问类都应该实现这个接口
 */
class IRepository {
  /**
   * 创建记录
   * @param {Object} data - 要创建的数据
   * @returns {Promise<Object>} 创建的记录
   */
  async create(data) {
    throw new Error('Method create must be implemented')
  }
  
  /**
   * 根据ID查找记录
   * @param {String} id - 记录ID
   * @returns {Promise<Object|null>} 找到的记录或null
   */
  async findById(id) {
    throw new Error('Method findById must be implemented')
  }
  
  /**
   * 查找所有记录
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 记录列表
   */
  async findAll(options = {}) {
    throw new Error('Method findAll must be implemented')
  }
  
  /**
   * 根据条件查找记录
   * @param {Object} conditions - 查询条件
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 匹配的记录列表
   */
  async findBy(conditions, options = {}) {
    throw new Error('Method findBy must be implemented')
  }
  
  /**
   * 更新记录
   * @param {String} id - 记录ID
   * @param {Object} data - 要更新的数据
   * @returns {Promise<Object>} 更新后的记录
   */
  async update(id, data) {
    throw new Error('Method update must be implemented')
  }
  
  /**
   * 删除记录
   * @param {String} id - 记录ID
   * @returns {Promise<Boolean>} 删除是否成功
   */
  async delete(id) {
    throw new Error('Method delete must be implemented')
  }
  
  /**
   * 统计记录数量
   * @param {Object} conditions - 统计条件
   * @returns {Promise<Number>} 记录数量
   */
  async count(conditions = {}) {
    throw new Error('Method count must be implemented')
  }
  
  /**
   * 批量操作
   * @param {Array} operations - 操作列表
   * @returns {Promise<Array>} 操作结果
   */
  async batch(operations) {
    throw new Error('Method batch must be implemented')
  }
}

/**
 * 用户Repository接口
 */
class IUserRepository extends IRepository {
  /**
   * 根据手机号查找用户
   * @param {String} phone - 手机号
   * @returns {Promise<Object|null>} 用户信息或null
   */
  async findByPhone(phone) {
    throw new Error('Method findByPhone must be implemented')
  }
  
  /**
   * 验证用户凭据
   * @param {String} phone - 手机号
   * @param {String} password - 密码
   * @returns {Promise<Object|null>} 用户信息或null
   */
  async validateCredentials(phone, password) {
    throw new Error('Method validateCredentials must be implemented')
  }
  
  /**
   * 更新用户设置
   * @param {String} userId - 用户ID
   * @param {Object} settings - 设置信息
   * @returns {Promise<Object>} 更新后的用户信息
   */
  async updateSettings(userId, settings) {
    throw new Error('Method updateSettings must be implemented')
  }
}

/**
 * 报告Repository接口
 */
class IReportRepository extends IRepository {
  /**
   * 根据用户ID查找报告
   * @param {String} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 报告列表
   */
  async findByUserId(userId, options = {}) {
    throw new Error('Method findByUserId must be implemented')
  }
  
  /**
   * 根据时间范围查找报告
   * @param {String} userId - 用户ID
   * @param {Date} startDate - 开始时间
   * @param {Date} endDate - 结束时间
   * @returns {Promise<Array>} 报告列表
   */
  async findByDateRange(userId, startDate, endDate) {
    throw new Error('Method findByDateRange must be implemented')
  }
  
  /**
   * 根据分类查找报告
   * @param {String} userId - 用户ID
   * @param {Array} categories - 分类列表
   * @returns {Promise<Array>} 报告列表
   */
  async findByCategories(userId, categories) {
    throw new Error('Method findByCategories must be implemented')
  }
  
  /**
   * 查找异常报告
   * @param {String} userId - 用户ID
   * @returns {Promise<Array>} 异常报告列表
   */
  async findAbnormalReports(userId) {
    throw new Error('Method findAbnormalReports must be implemented')
  }
  
  /**
   * 更新同步状态
   * @param {String} reportId - 报告ID
   * @param {String} status - 同步状态
   * @returns {Promise<Boolean>} 更新是否成功
   */
  async updateSyncStatus(reportId, status) {
    throw new Error('Method updateSyncStatus must be implemented')
  }
}

/**
 * 分析Repository接口
 */
class IAnalysisRepository extends IRepository {
  /**
   * 根据用户ID查找分析结果
   * @param {String} userId - 用户ID
   * @param {String} type - 分析类型
   * @returns {Promise<Array>} 分析结果列表
   */
  async findByUserIdAndType(userId, type) {
    throw new Error('Method findByUserIdAndType must be implemented')
  }
  
  /**
   * 获取最新分析结果
   * @param {String} userId - 用户ID
   * @param {String} type - 分析类型
   * @returns {Promise<Object|null>} 最新分析结果或null
   */
  async getLatestAnalysis(userId, type) {
    throw new Error('Method getLatestAnalysis must be implemented')
  }
  
  /**
   * 清理过期分析结果
   * @param {Number} daysToKeep - 保留天数
   * @returns {Promise<Number>} 清理的记录数量
   */
  async cleanupExpiredAnalysis(daysToKeep = 30) {
    throw new Error('Method cleanupExpiredAnalysis must be implemented')
  }
}

module.exports = { 
  IRepository, 
  IUserRepository, 
  IReportRepository, 
  IAnalysisRepository 
}