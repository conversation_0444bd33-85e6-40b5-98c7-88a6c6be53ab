/**
 * 数据加密服务
 * 提供数据传输和存储的加密功能
 */

import CryptoJS from 'crypto-js'

class EncryptionService {
  constructor() {
    // 从环境变量或配置中获取密钥
    this.secretKey = this.getSecretKey()
    this.iv = CryptoJS.lib.WordArray.random(16)
  }

  /**
   * 获取加密密钥
   */
  getSecretKey() {
    // 在实际应用中，应该从安全的地方获取密钥
    // 这里使用设备ID和应用标识生成密钥
    const deviceId = this.getDeviceId()
    const appId = 'heath-report-app'
    return CryptoJS.SHA256(deviceId + appId).toString()
  }

  /**
   * 获取设备ID
   */
  getDeviceId() {
    try {
      // 尝试获取设备唯一标识
      const systemInfo = uni.getSystemInfoSync()
      return systemInfo.deviceId || systemInfo.system + systemInfo.model
    } catch (error) {
      // 如果获取失败，使用随机生成的ID
      let deviceId = uni.getStorageSync('device_id')
      if (!deviceId) {
        deviceId = CryptoJS.lib.WordArray.random(16).toString()
        uni.setStorageSync('device_id', deviceId)
      }
      return deviceId
    }
  }

  /**
   * AES加密
   */
  encrypt(data) {
    try {
      const dataStr = typeof data === 'string' ? data : JSON.stringify(data)
      const encrypted = CryptoJS.AES.encrypt(dataStr, this.secretKey, {
        iv: this.iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })
      
      return {
        data: encrypted.toString(),
        iv: this.iv.toString()
      }
    } catch (error) {
      console.error('数据加密失败:', error)
      throw new Error('数据加密失败')
    }
  }

  /**
   * AES解密
   */
  decrypt(encryptedData, ivStr) {
    try {
      const iv = CryptoJS.enc.Hex.parse(ivStr)
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.secretKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })
      
      const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8)
      
      try {
        return JSON.parse(decryptedStr)
      } catch {
        return decryptedStr
      }
    } catch (error) {
      console.error('数据解密失败:', error)
      throw new Error('数据解密失败')
    }
  }

  /**
   * 生成数据签名
   */
  generateSignature(data, timestamp) {
    const dataStr = typeof data === 'string' ? data : JSON.stringify(data)
    const signData = dataStr + timestamp + this.secretKey
    return CryptoJS.SHA256(signData).toString()
  }

  /**
   * 验证数据签名
   */
  verifySignature(data, timestamp, signature) {
    const expectedSignature = this.generateSignature(data, timestamp)
    return expectedSignature === signature
  }

  /**
   * 加密敏感存储数据
   */
  encryptStorageData(key, data) {
    try {
      const encrypted = this.encrypt(data)
      uni.setStorageSync(key, encrypted)
      return true
    } catch (error) {
      console.error('存储数据加密失败:', error)
      return false
    }
  }

  /**
   * 解密敏感存储数据
   */
  decryptStorageData(key) {
    try {
      const encrypted = uni.getStorageSync(key)
      if (!encrypted || !encrypted.data || !encrypted.iv) {
        return null
      }
      return this.decrypt(encrypted.data, encrypted.iv)
    } catch (error) {
      console.error('存储数据解密失败:', error)
      return null
    }
  }

  /**
   * 生成随机盐值
   */
  generateSalt() {
    return CryptoJS.lib.WordArray.random(32).toString()
  }

  /**
   * 密码哈希
   */
  hashPassword(password, salt) {
    return CryptoJS.PBKDF2(password, salt, {
      keySize: 256/32,
      iterations: 10000
    }).toString()
  }

  /**
   * 验证密码
   */
  verifyPassword(password, salt, hash) {
    const computedHash = this.hashPassword(password, salt)
    return computedHash === hash
  }
}

export default new EncryptionService()