import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import globalErrorHandler from './utils/errorHandler.js'
import sessionService from './services/auth/sessionService.js'
import './utils/network/requestInterceptor.js'

export function createApp() {
  const app = createSSRApp(App)
  const pinia = createPinia()
  
  // 安装Pinia状态管理
  app.use(pinia)
  
  // 安装全局错误处理器
  globalErrorHandler.install(app)
  
  // 初始化会话服务
  sessionService.init().catch(error => {
    console.error('会话服务初始化失败:', error)
  })
  
  return {
    app,
    Pinia: pinia
  }
}
// #endif