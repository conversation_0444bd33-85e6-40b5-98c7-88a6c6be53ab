/**
 * EncryptionService 单元测试
 * 测试数据加密服务功能
 */

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { EncryptionService, encryptionService } = require('../../core/security/EncryptionService.js')

// Mock logger
jest.mock('../../core/logger/Logger.js', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}))

describe('EncryptionService', () => {
  let service
  
  beforeEach(() => {
    jest.clearAllMocks()
    service = new EncryptionService({
      masterKey: 'test-master-key-12345'
    })
  })
  
  describe('构造函数', () => {
    it('应该正确初始化默认选项', () => {
      const defaultService = new EncryptionService()
      
      expect(defaultService.options.algorithm).toBe('AES-256-CBC')
      expect(defaultService.options.keySize).toBe(256)
      expect(defaultService.options.ivSize).toBe(128)
      expect(defaultService.options.saltSize).toBe(128)
      expect(defaultService.options.iterations).toBe(10000)
      expect(defaultService.masterKey).toBeDefined()
    })
    
    it('应该正确设置自定义选项', () => {
      const customService = new EncryptionService({
        keySize: 128,
        iterations: 5000,
        masterKey: 'custom-key'
      })
      
      expect(customService.options.keySize).toBe(128)
      expect(customService.options.iterations).toBe(5000)
      expect(customService.masterKey).toBe('custom-key')
    })
    
    it('应该包含默认的敏感字段', () => {
      expect(service.sensitiveFields).toContain('password')
      expect(service.sensitiveFields).toContain('passwordHash')
      expect(service.sensitiveFields).toContain('phone')
      expect(service.sensitiveFields).toContain('notes')
    })
  })
  
  describe('基础加密解密', () => {
    it('应该成功加密和解密字符串', () => {
      const originalData = 'Hello, World!'
      
      const encrypted = service.encrypt(originalData)
      expect(encrypted).toBeDefined()
      expect(encrypted).not.toBe(originalData)
      
      const decrypted = service.decrypt(encrypted)
      expect(decrypted).toBe(originalData)
    })
    
    it('应该使用自定义密钥加密和解密', () => {
      const originalData = 'Secret message'
      const customKey = 'custom-encryption-key'
      
      const encrypted = service.encrypt(originalData, customKey)
      const decrypted = service.decrypt(encrypted, customKey)
      
      expect(decrypted).toBe(originalData)
    })
    
    it('应该在加密空数据时抛出错误', () => {
      expect(() => service.encrypt('')).toThrow('数据必须是非空字符串')
      expect(() => service.encrypt(null)).toThrow('数据必须是非空字符串')
      expect(() => service.encrypt(undefined)).toThrow('数据必须是非空字符串')
    })
    
    it('应该在解密空数据时抛出错误', () => {
      expect(() => service.decrypt('')).toThrow('加密数据必须是非空字符串')
      expect(() => service.decrypt(null)).toThrow('加密数据必须是非空字符串')
    })
    
    it('应该在使用错误密钥解密时抛出错误', () => {
      const originalData = 'Test data'
      const encrypted = service.encrypt(originalData, 'key1')
      
      expect(() => service.decrypt(encrypted, 'key2')).toThrow('解密失败')
    })
  })
  
  describe('对象加密解密', () => {
    it('应该加密对象中的敏感字段', () => {
      const originalObj = {
        id: 'user123',
        name: 'John Doe',
        password: 'secret123',
        phone: '13800138000',
        email: '<EMAIL>'
      }
      
      const encrypted = service.encryptObject(originalObj)
      
      expect(encrypted.id).toBe(originalObj.id) // 非敏感字段保持不变
      expect(encrypted.name).toBe(originalObj.name)
      expect(encrypted.email).toBe(originalObj.email)
      expect(encrypted.password).not.toBe(originalObj.password) // 敏感字段被加密
      expect(encrypted.phone).not.toBe(originalObj.phone)
      expect(encrypted.password_encrypted).toBe(true)
      expect(encrypted.phone_encrypted).toBe(true)
    })
    
    it('应该解密对象中的敏感字段', () => {
      const originalObj = {
        id: 'user123',
        password: 'secret123',
        phone: '13800138000'
      }
      
      const encrypted = service.encryptObject(originalObj)
      const decrypted = service.decryptObject(encrypted)
      
      expect(decrypted.id).toBe(originalObj.id)
      expect(decrypted.password).toBe(originalObj.password)
      expect(decrypted.phone).toEqual(originalObj.phone)
      expect(decrypted.password_encrypted).toBeUndefined()
      expect(decrypted.phone_encrypted).toBeUndefined()
    })
    
    it('应该加密指定的字段', () => {
      const originalObj = {
        name: 'John',
        email: '<EMAIL>',
        notes: 'Private notes'
      }
      
      const encrypted = service.encryptObject(originalObj, ['email'])
      
      expect(encrypted.name).toBe(originalObj.name)
      expect(encrypted.notes).toBe(originalObj.notes) // 不在指定列表中
      expect(encrypted.email).not.toBe(originalObj.email) // 在指定列表中
      expect(encrypted.email_encrypted).toBe(true)
    })
    
    it('应该处理对象类型的字段', () => {
      const originalObj = {
        id: 'test',
        settings: {
          theme: 'dark',
          notifications: true
        }
      }
      
      // 将settings添加到敏感字段
      service.addSensitiveFields(['settings'])
      
      const encrypted = service.encryptObject(originalObj)
      const decrypted = service.decryptObject(encrypted)
      
      expect(decrypted.settings).toEqual(originalObj.settings)
    })
    
    it('应该在输入无效对象时抛出错误', () => {
      expect(() => service.encryptObject(null)).toThrow('输入必须是对象')
      expect(() => service.encryptObject('string')).toThrow('输入必须是对象')
      expect(() => service.decryptObject(null)).toThrow('输入必须是对象')
    })
  })
  
  describe('哈希功能', () => {
    it('应该生成数据哈希', () => {
      const data = 'test data'
      const hash = service.hash(data)
      
      expect(hash).toBeDefined()
      expect(typeof hash).toBe('string')
      expect(hash.length).toBeGreaterThan(0)
    })
    
    it('应该为相同数据生成相同哈希', () => {
      const data = 'consistent data'
      const hash1 = service.hash(data)
      const hash2 = service.hash(data)
      
      expect(hash1).toBe(hash2)
    })
    
    it('应该为不同数据生成不同哈希', () => {
      const hash1 = service.hash('data1')
      const hash2 = service.hash('data2')
      
      expect(hash1).not.toBe(hash2)
    })
    
    it('应该验证数据完整性', () => {
      const data = 'integrity test'
      const hash = service.hash(data)
      
      expect(service.verifyHash(data, hash)).toBe(true)
      expect(service.verifyHash('modified data', hash)).toBe(false)
    })
    
    it('应该在哈希空数据时抛出错误', () => {
      expect(() => service.hash('')).toThrow('数据必须是非空字符串')
      expect(() => service.hash(null)).toThrow('数据必须是非空字符串')
    })
  })
  
  describe('文件加密解密', () => {
    it('应该加密和解密文件内容', () => {
      const content = 'This is file content'
      const filename = 'test.txt'
      
      const encrypted = service.encryptFile(content, filename)
      
      expect(encrypted.encryptedContent).toBeDefined()
      expect(encrypted.contentHash).toBeDefined()
      expect(encrypted.filename).toBe(filename)
      expect(encrypted.encryptedAt).toBeDefined()
      
      const decrypted = service.decryptFile(encrypted)
      expect(decrypted).toBe(content)
    })
    
    it('应该验证文件完整性', () => {
      const content = 'File content for integrity test'
      const filename = 'integrity.txt'
      
      const encrypted = service.encryptFile(content, filename)
      
      // 篡改内容哈希
      encrypted.contentHash = 'invalid-hash'
      
      expect(() => service.decryptFile(encrypted)).toThrow('文件完整性验证失败')
    })
    
    it('应该在加密无效文件内容时抛出错误', () => {
      expect(() => service.encryptFile('', 'test.txt')).toThrow('文件内容必须是非空字符串')
      expect(() => service.encryptFile(null, 'test.txt')).toThrow('文件内容必须是非空字符串')
    })
    
    it('应该在解密无效文件对象时抛出错误', () => {
      expect(() => service.decryptFile(null)).toThrow('无效的加密文件对象')
      expect(() => service.decryptFile({})).toThrow('无效的加密文件对象')
    })
  })
  
  describe('批量操作', () => {
    it('应该批量加密数据', async () => {
      const dataList = [
        { id: '1', password: 'pass1' },
        { id: '2', password: 'pass2' },
        { id: '3', password: 'pass3' }
      ]
      
      const encrypted = await service.batchEncrypt(dataList)
      
      expect(encrypted).toHaveLength(3)
      expect(encrypted[0].password).not.toBe('pass1')
      expect(encrypted[0].password_encrypted).toBe(true)
    })
    
    it('应该批量解密数据', async () => {
      const dataList = [
        { id: '1', password: 'pass1' },
        { id: '2', password: 'pass2' }
      ]
      
      const encrypted = await service.batchEncrypt(dataList)
      const decrypted = await service.batchDecrypt(encrypted)
      
      expect(decrypted).toHaveLength(2)
      expect(decrypted[0].password).toBe('pass1')
      expect(decrypted[1].password).toBe('pass2')
      expect(decrypted[0].password_encrypted).toBeUndefined()
    })
    
    it('应该使用自定义加密函数', async () => {
      const dataList = ['data1', 'data2']
      const customEncrypt = (data) => service.encrypt(data)
      
      const encrypted = await service.batchEncrypt(dataList, customEncrypt)
      
      expect(encrypted).toHaveLength(2)
      expect(encrypted[0]).not.toBe('data1')
      expect(encrypted[1]).not.toBe('data2')
    })
    
    it('应该在输入无效数据时抛出错误', async () => {
      await expect(service.batchEncrypt('not array')).rejects.toThrow('数据列表必须是数组')
      await expect(service.batchDecrypt('not array')).rejects.toThrow('加密数据列表必须是数组')
    })
  })
  
  describe('敏感字段管理', () => {
    it('应该更新敏感字段列表', () => {
      const newFields = ['field1', 'field2']
      
      service.updateSensitiveFields(newFields)
      
      expect(service.sensitiveFields).toEqual(newFields)
    })
    
    it('应该添加敏感字段', () => {
      const originalLength = service.sensitiveFields.length
      
      service.addSensitiveFields('newField')
      
      expect(service.sensitiveFields).toHaveLength(originalLength + 1)
      expect(service.sensitiveFields).toContain('newField')
    })
    
    it('应该添加多个敏感字段', () => {
      const originalLength = service.sensitiveFields.length
      const newFields = ['field1', 'field2']
      
      service.addSensitiveFields(newFields)
      
      expect(service.sensitiveFields).toHaveLength(originalLength + 2)
      expect(service.sensitiveFields).toContain('field1')
      expect(service.sensitiveFields).toContain('field2')
    })
    
    it('应该移除敏感字段', () => {
      service.addSensitiveFields('tempField')
      expect(service.sensitiveFields).toContain('tempField')
      
      service.removeSensitiveFields('tempField')
      expect(service.sensitiveFields).not.toContain('tempField')
    })
    
    it('应该移除多个敏感字段', () => {
      service.addSensitiveFields(['temp1', 'temp2'])
      
      service.removeSensitiveFields(['temp1', 'temp2'])
      
      expect(service.sensitiveFields).not.toContain('temp1')
      expect(service.sensitiveFields).not.toContain('temp2')
    })
    
    it('应该在更新敏感字段时验证输入', () => {
      expect(() => service.updateSensitiveFields('not array')).toThrow('敏感字段列表必须是数组')
    })
  })
  
  describe('服务信息', () => {
    it('应该返回服务信息', () => {
      const info = service.getServiceInfo()
      
      expect(info).toHaveProperty('algorithm')
      expect(info).toHaveProperty('keySize')
      expect(info).toHaveProperty('ivSize')
      expect(info).toHaveProperty('sensitiveFields')
      expect(info).toHaveProperty('hasMasterKey')
      expect(info.hasMasterKey).toBe(true)
    })
  })
  
  describe('密钥生成', () => {
    it('应该生成随机密钥', () => {
      const key1 = service.generateKey()
      const key2 = service.generateKey()
      
      expect(key1).toBeDefined()
      expect(key2).toBeDefined()
      expect(key1).not.toBe(key2)
    })
    
    it('应该生成随机IV', () => {
      const iv1 = service.generateIV()
      const iv2 = service.generateIV()
      
      expect(iv1).toBeDefined()
      expect(iv2).toBeDefined()
      expect(iv1).not.toBe(iv2)
    })
    
    it('应该生成主密钥', () => {
      const masterKey = service.generateMasterKey()
      
      expect(masterKey).toBeDefined()
      expect(typeof masterKey).toBe('string')
      expect(masterKey.length).toBeGreaterThan(0)
    })
  })
  
  describe('默认实例', () => {
    it('应该提供默认的加密服务实例', () => {
      expect(encryptionService).toBeInstanceOf(EncryptionService)
      expect(encryptionService.masterKey).toBeDefined()
    })
    
    it('默认实例应该能正常工作', () => {
      const data = 'test with default instance'
      const encrypted = encryptionService.encrypt(data)
      const decrypted = encryptionService.decrypt(encrypted)
      
      expect(decrypted).toBe(data)
    })
  })
})