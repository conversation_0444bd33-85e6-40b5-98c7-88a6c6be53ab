/**
 * Jest测试环境设置
 */

// 设置全局变量
global.console = {
  ...console,
  // 在测试中静默某些日志
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}

// Mock uni-app全局对象
global.uni = {
  chooseImage: jest.fn(),
  setStorage: jest.fn(),
  getStorage: jest.fn(),
  removeStorage: jest.fn(),
  clearStorage: jest.fn(),
  saveImageToPhotosAlbum: jest.fn(),
  getFileInfo: jest.fn(),
  share: jest.fn(),
  showToast: jest.fn(),
  getSystemInfo: jest.fn()
}

// Mock plus对象（APP平台）
global.plus = {
  android: {
    checkPermission: jest.fn(),
    requestPermissions: jest.fn()
  }
}

// Mock navigator对象
Object.defineProperty(global, 'navigator', {
  value: {
    share: jest.fn(),
    clipboard: {
      writeText: jest.fn()
    }
  },
  writable: true
})

// Mock getApp函数
global.getApp = jest.fn(() => ({
  globalData: {}
}))

// 在每个测试前重置所有mock
beforeEach(() => {
  jest.clearAllMocks()
})