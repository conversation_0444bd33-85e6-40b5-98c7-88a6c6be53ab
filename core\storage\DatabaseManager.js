/**
 * 数据库连接和管理工具
 * 提供跨平台的数据库连接管理、初始化和迁移功能
 */

const { Constants } = require('../../types/index.js')
const { logger } = require('../logger/Logger.js')
const { errorHandler } = require('../errors/ErrorHandler.js')

class DatabaseManager {
  constructor(options = {}) {
    this.options = {
      dbName: options.dbName || 'health_app.db',
      version: options.version || 1,
      enableWAL: options.enableWAL || true,
      timeout: options.timeout || 30000,
      ...options
    }
    
    this.platform = this.detectPlatform()
    this.connection = null
    this.isInitialized = false
    this.migrationHistory = []
    this.connectionPool = new Map()
  }
  
  /**
   * 检测运行平台
   * @returns {String} 平台类型
   */
  detectPlatform() {
    // #ifdef APP-PLUS
    return 'app-plus'
    // #endif
    
    // #ifdef H5
    return 'h5'
    // #endif
    
    // #ifdef MP-WEIXIN
    return 'mp-weixin'
    // #endif
    
    // #ifdef MP-ALIPAY
    return 'mp-alipay'
    // #endif
    
    return 'unknown'
  }
  
  /**
   * 初始化数据库连接
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return
    }
    
    try {
      logger.info('开始初始化数据库连接', { 
        platform: this.platform, 
        dbName: this.options.dbName 
      })
      
      // 根据平台初始化不同的数据库连接
      switch (this.platform) {
        case 'app-plus':
          await this.initializeAppDatabase()
          break
        case 'h5':
          await this.initializeH5Database()
          break
        case 'mp-weixin':
        case 'mp-alipay':
          await this.initializeMPDatabase()
          break
        default:
          await this.initializeMemoryDatabase()
          break
      }
      
      // 执行数据库迁移
      await this.runMigrations()
      
      this.isInitialized = true
      logger.info('数据库连接初始化成功', { platform: this.platform })
      
    } catch (error) {
      logger.error('数据库连接初始化失败', { 
        error: error.message, 
        platform: this.platform 
      })
      throw errorHandler.createError(
        Constants.ERROR_TYPES.STORAGE_ERROR,
        `数据库初始化失败: ${error.message}`,
        { originalError: error }
      )
    }
  }
  
  /**
   * 初始化APP平台数据库 (SQLite)
   * @returns {Promise<void>}
   */
  async initializeAppDatabase() {
    // #ifdef APP-PLUS
    return new Promise((resolve, reject) => {
      try {
        // 打开SQLite数据库
        this.connection = plus.sqlite.openDatabase({
          name: this.options.dbName,
          path: `_doc/${this.options.dbName}`,
          success: () => {
            logger.debug('SQLite数据库连接成功')
            
            // 启用WAL模式以提高并发性能
            if (this.options.enableWAL) {
              this.executeSql('PRAGMA journal_mode=WAL')
                .then(() => logger.debug('WAL模式已启用'))
                .catch(error => logger.warn('启用WAL模式失败', { error: error.message }))
            }
            
            // 设置其他优化参数
            this.executeSql('PRAGMA synchronous=NORMAL')
            this.executeSql('PRAGMA cache_size=10000')
            this.executeSql('PRAGMA temp_store=MEMORY')
            
            resolve()
          },
          fail: (error) => {
            logger.error('SQLite数据库连接失败', { error })
            reject(new Error(`SQLite连接失败: ${JSON.stringify(error)}`))
          }
        })
      } catch (error) {
        reject(error)
      }
    })
    // #endif
    
    // #ifndef APP-PLUS
    throw new Error('SQLite仅在APP平台可用')
    // #endif
  }
  
  /**
   * 初始化H5平台数据库 (IndexedDB)
   * @returns {Promise<void>}
   */
  async initializeH5Database() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.options.dbName, this.options.version)
      
      request.onerror = () => {
        const error = request.error
        logger.error('IndexedDB连接失败', { error })
        reject(new Error(`IndexedDB连接失败: ${error.message}`))
      }
      
      request.onsuccess = () => {
        this.connection = request.result
        logger.debug('IndexedDB连接成功')
        
        // 监听数据库版本变化
        this.connection.onversionchange = () => {
          logger.warn('数据库版本发生变化，需要刷新页面')
          this.connection.close()
        }
        
        resolve()
      }
      
      request.onupgradeneeded = (event) => {
        this.connection = event.target.result
        logger.info('IndexedDB需要升级', { 
          oldVersion: event.oldVersion, 
          newVersion: event.newVersion 
        })
        
        // 在onupgradeneeded中创建对象存储
        this.createIndexedDBStores()
      }
    })
  }
  
  /**
   * 创建IndexedDB对象存储
   */
  createIndexedDBStores() {
    const db = this.connection
    
    // 创建用户表
    if (!db.objectStoreNames.contains('users')) {
      const userStore = db.createObjectStore('users', { keyPath: 'id' })
      userStore.createIndex('phone', 'phone', { unique: true })
      userStore.createIndex('createdAt', 'createdAt', { unique: false })
    }
    
    // 创建报告表
    if (!db.objectStoreNames.contains('reports')) {
      const reportStore = db.createObjectStore('reports', { keyPath: 'id' })
      reportStore.createIndex('userId', 'userId', { unique: false })
      reportStore.createIndex('checkDate', 'checkDate', { unique: false })
      reportStore.createIndex('hospital', 'hospital', { unique: false })
      reportStore.createIndex('createdAt', 'createdAt', { unique: false })
    }
    
    // 创建分析表
    if (!db.objectStoreNames.contains('analysis')) {
      const analysisStore = db.createObjectStore('analysis', { keyPath: 'id' })
      analysisStore.createIndex('userId', 'userId', { unique: false })
      analysisStore.createIndex('type', 'type', { unique: false })
      analysisStore.createIndex('createdAt', 'createdAt', { unique: false })
    }
    
    // 创建迁移历史表
    if (!db.objectStoreNames.contains('migrations')) {
      db.createObjectStore('migrations', { keyPath: 'version' })
    }
  }
  
  /**
   * 初始化小程序数据库
   * @returns {Promise<void>}
   */
  async initializeMPDatabase() {
    // 小程序使用本地存储，不需要特殊初始化
    this.connection = {
      type: 'mp-storage',
      storage: uni.getStorageSync || wx.getStorageSync
    }
    
    logger.debug('小程序存储初始化成功')
  }
  
  /**
   * 初始化内存数据库
   * @returns {Promise<void>}
   */
  async initializeMemoryDatabase() {
    this.connection = {
      type: 'memory',
      data: new Map(),
      indexes: new Map()
    }
    
    logger.debug('内存数据库初始化成功')
  }
  
  /**
   * 执行SQL语句 (仅SQLite)
   * @param {String} sql - SQL语句
   * @param {Array} params - 参数
   * @returns {Promise<Object>} 执行结果
   */
  async executeSql(sql, params = []) {
    if (this.platform !== 'app-plus') {
      throw new Error('executeSql仅在APP平台可用')
    }
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('SQL执行超时'))
      }, this.options.timeout)
      
      // #ifdef APP-PLUS
      plus.sqlite.executeSql({
        name: this.options.dbName,
        sql,
        success: (result) => {
          clearTimeout(timeout)
          logger.debug('SQL执行成功', { sql, params, rowsAffected: result.rowsAffected })
          resolve(result)
        },
        fail: (error) => {
          clearTimeout(timeout)
          logger.error('SQL执行失败', { sql, params, error })
          reject(new Error(`SQL执行失败: ${JSON.stringify(error)}`))
        }
      })
      // #endif
    })
  }
  
  /**
   * 执行IndexedDB事务
   * @param {Array|String} storeNames - 对象存储名称
   * @param {String} mode - 事务模式
   * @param {Function} callback - 事务回调
   * @returns {Promise<*>} 事务结果
   */
  async executeTransaction(storeNames, mode = 'readonly', callback) {
    if (this.platform !== 'h5') {
      throw new Error('executeTransaction仅在H5平台可用')
    }
    
    return new Promise((resolve, reject) => {
      const transaction = this.connection.transaction(storeNames, mode)
      
      transaction.oncomplete = () => {
        logger.debug('IndexedDB事务完成', { storeNames, mode })
      }
      
      transaction.onerror = () => {
        const error = transaction.error
        logger.error('IndexedDB事务失败', { storeNames, mode, error })
        reject(new Error(`事务失败: ${error.message}`))
      }
      
      transaction.onabort = () => {
        logger.warn('IndexedDB事务被中止', { storeNames, mode })
        reject(new Error('事务被中止'))
      }
      
      try {
        const result = callback(transaction)
        if (result instanceof Promise) {
          result.then(resolve).catch(reject)
        } else {
          resolve(result)
        }
      } catch (error) {
        reject(error)
      }
    })
  }
  
  /**
   * 运行数据库迁移
   * @returns {Promise<void>}
   */
  async runMigrations() {
    try {
      logger.info('开始执行数据库迁移')
      
      // 获取已执行的迁移
      const executedMigrations = await this.getExecutedMigrations()
      
      // 获取所有迁移脚本
      const migrations = this.getMigrationScripts()
      
      // 执行未执行的迁移
      for (const migration of migrations) {
        if (!executedMigrations.includes(migration.version)) {
          await this.executeMigration(migration)
          await this.recordMigration(migration.version)
          logger.info(`迁移 ${migration.version} 执行成功`)
        }
      }
      
      logger.info('数据库迁移完成')
    } catch (error) {
      logger.error('数据库迁移失败', { error: error.message })
      throw error
    }
  }
  
  /**
   * 获取已执行的迁移
   * @returns {Promise<Array>} 已执行的迁移版本列表
   */
  async getExecutedMigrations() {
    try {
      switch (this.platform) {
        case 'app-plus':
          return await this.getExecutedMigrationsFromSQLite()
        case 'h5':
          return await this.getExecutedMigrationsFromIndexedDB()
        case 'mp-weixin':
        case 'mp-alipay':
          return await this.getExecutedMigrationsFromMP()
        default:
          return []
      }
    } catch (error) {
      // 如果迁移表不存在，返回空数组
      logger.debug('获取迁移历史失败，可能是首次初始化', { error: error.message })
      return []
    }
  }
  
  /**
   * 从SQLite获取已执行的迁移
   * @returns {Promise<Array>}
   */
  async getExecutedMigrationsFromSQLite() {
    try {
      // 创建迁移表（如果不存在）
      await this.executeSql(`
        CREATE TABLE IF NOT EXISTS migrations (
          version TEXT PRIMARY KEY,
          executed_at INTEGER NOT NULL
        )
      `)
      
      const result = await this.executeSql('SELECT version FROM migrations ORDER BY version')
      return result.rows ? Array.from(result.rows).map(row => row.version) : []
    } catch (error) {
      logger.error('从SQLite获取迁移历史失败', { error: error.message })
      return []
    }
  }
  
  /**
   * 从IndexedDB获取已执行的迁移
   * @returns {Promise<Array>}
   */
  async getExecutedMigrationsFromIndexedDB() {
    return this.executeTransaction('migrations', 'readonly', (transaction) => {
      return new Promise((resolve, reject) => {
        const store = transaction.objectStore('migrations')
        const request = store.getAll()
        
        request.onsuccess = () => {
          const migrations = request.result.map(item => item.version).sort()
          resolve(migrations)
        }
        
        request.onerror = () => reject(request.error)
      })
    })
  }
  
  /**
   * 从小程序存储获取已执行的迁移
   * @returns {Promise<Array>}
   */
  async getExecutedMigrationsFromMP() {
    try {
      const migrations = uni.getStorageSync('db_migrations') || []
      return migrations.sort()
    } catch (error) {
      return []
    }
  }
  
  /**
   * 获取迁移脚本
   * @returns {Array} 迁移脚本列表
   */
  getMigrationScripts() {
    return [
      {
        version: '001_initial_schema',
        description: '创建初始数据库结构',
        up: async () => {
          await this.createInitialSchema()
        }
      },
      {
        version: '002_add_indexes',
        description: '添加性能优化索引',
        up: async () => {
          await this.addPerformanceIndexes()
        }
      },
      {
        version: '003_add_sync_fields',
        description: '添加数据同步字段',
        up: async () => {
          await this.addSyncFields()
        }
      }
    ]
  }
  
  /**
   * 执行迁移
   * @param {Object} migration - 迁移对象
   * @returns {Promise<void>}
   */
  async executeMigration(migration) {
    try {
      logger.info(`执行迁移: ${migration.version} - ${migration.description}`)
      await migration.up()
      this.migrationHistory.push({
        version: migration.version,
        description: migration.description,
        executedAt: new Date()
      })
    } catch (error) {
      logger.error(`迁移执行失败: ${migration.version}`, { error: error.message })
      throw error
    }
  }
  
  /**
   * 记录迁移执行
   * @param {String} version - 迁移版本
   * @returns {Promise<void>}
   */
  async recordMigration(version) {
    const executedAt = Date.now()
    
    switch (this.platform) {
      case 'app-plus':
        await this.executeSql(
          'INSERT INTO migrations (version, executed_at) VALUES (?, ?)',
          [version, executedAt]
        )
        break
        
      case 'h5':
        await this.executeTransaction('migrations', 'readwrite', (transaction) => {
          const store = transaction.objectStore('migrations')
          store.add({ version, executedAt })
        })
        break
        
      case 'mp-weixin':
      case 'mp-alipay':
        const migrations = uni.getStorageSync('db_migrations') || []
        migrations.push(version)
        uni.setStorageSync('db_migrations', migrations)
        break
    }
  }
  
  /**
   * 创建初始数据库结构
   * @returns {Promise<void>}
   */
  async createInitialSchema() {
    switch (this.platform) {
      case 'app-plus':
        await this.createSQLiteSchema()
        break
      case 'h5':
        // IndexedDB结构在初始化时已创建
        break
      case 'mp-weixin':
      case 'mp-alipay':
        // 小程序不需要创建结构
        break
    }
  }
  
  /**
   * 创建SQLite数据库结构
   * @returns {Promise<void>}
   */
  async createSQLiteSchema() {
    const tables = [
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        phone TEXT UNIQUE NOT NULL,
        nickname TEXT,
        avatar TEXT,
        gender TEXT,
        birthday INTEGER,
        height REAL,
        weight REAL,
        settings TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS reports (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        title TEXT,
        hospital TEXT,
        doctor TEXT,
        check_date INTEGER,
        report_date INTEGER,
        original_image TEXT,
        items TEXT,
        tags TEXT,
        notes TEXT,
        sync_status TEXT DEFAULT 'local',
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS analysis (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        type TEXT NOT NULL,
        time_range_start INTEGER,
        time_range_end INTEGER,
        items TEXT,
        results TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`
    ]
    
    for (const sql of tables) {
      await this.executeSql(sql)
    }
  }
  
  /**
   * 添加性能优化索引
   * @returns {Promise<void>}
   */
  async addPerformanceIndexes() {
    if (this.platform === 'app-plus') {
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_reports_user_id ON reports (user_id)',
        'CREATE INDEX IF NOT EXISTS idx_reports_check_date ON reports (check_date)',
        'CREATE INDEX IF NOT EXISTS idx_reports_hospital ON reports (hospital)',
        'CREATE INDEX IF NOT EXISTS idx_reports_created_at ON reports (created_at)',
        'CREATE INDEX IF NOT EXISTS idx_analysis_user_id ON analysis (user_id)',
        'CREATE INDEX IF NOT EXISTS idx_analysis_type ON analysis (type)',
        'CREATE INDEX IF NOT EXISTS idx_analysis_created_at ON analysis (created_at)'
      ]
      
      for (const sql of indexes) {
        await this.executeSql(sql)
      }
    }
  }
  
  /**
   * 添加数据同步字段
   * @returns {Promise<void>}
   */
  async addSyncFields() {
    if (this.platform === 'app-plus') {
      const alterStatements = [
        'ALTER TABLE users ADD COLUMN sync_status TEXT DEFAULT "local"',
        'ALTER TABLE users ADD COLUMN last_sync_at INTEGER',
        'ALTER TABLE reports ADD COLUMN last_sync_at INTEGER',
        'ALTER TABLE analysis ADD COLUMN sync_status TEXT DEFAULT "local"',
        'ALTER TABLE analysis ADD COLUMN last_sync_at INTEGER'
      ]
      
      for (const sql of alterStatements) {
        try {
          await this.executeSql(sql)
        } catch (error) {
          // 忽略字段已存在的错误
          if (!error.message.includes('duplicate column name')) {
            throw error
          }
        }
      }
    }
  }
  
  /**
   * 关闭数据库连接
   * @returns {Promise<void>}
   */
  async close() {
    try {
      if (!this.connection) {
        return
      }
      
      switch (this.platform) {
        case 'app-plus':
          // #ifdef APP-PLUS
          plus.sqlite.closeDatabase({
            name: this.options.dbName,
            success: () => logger.debug('SQLite数据库连接已关闭'),
            fail: (error) => logger.error('关闭SQLite数据库失败', { error })
          })
          // #endif
          break
          
        case 'h5':
          if (this.connection && typeof this.connection.close === 'function') {
            this.connection.close()
            logger.debug('IndexedDB连接已关闭')
          }
          break
      }
      
      this.connection = null
      this.isInitialized = false
      
    } catch (error) {
      logger.error('关闭数据库连接失败', { error: error.message })
      throw error
    }
  }
  
  /**
   * 检查数据库连接状态
   * @returns {Boolean} 连接是否正常
   */
  isConnected() {
    return this.isInitialized && this.connection !== null
  }
  
  /**
   * 获取数据库信息
   * @returns {Object} 数据库信息
   */
  getDatabaseInfo() {
    return {
      platform: this.platform,
      dbName: this.options.dbName,
      version: this.options.version,
      isInitialized: this.isInitialized,
      isConnected: this.isConnected(),
      migrationHistory: this.migrationHistory
    }
  }
  
  /**
   * 数据库健康检查
   * @returns {Promise<Object>} 健康检查结果
   */
  async healthCheck() {
    const result = {
      status: 'healthy',
      checks: {},
      timestamp: new Date()
    }
    
    try {
      // 检查连接状态
      result.checks.connection = {
        status: this.isConnected() ? 'pass' : 'fail',
        message: this.isConnected() ? '数据库连接正常' : '数据库连接异常'
      }
      
      // 检查基本操作
      if (this.platform === 'app-plus') {
        try {
          await this.executeSql('SELECT 1')
          result.checks.query = {
            status: 'pass',
            message: '数据库查询正常'
          }
        } catch (error) {
          result.checks.query = {
            status: 'fail',
            message: `数据库查询失败: ${error.message}`
          }
          result.status = 'unhealthy'
        }
      }
      
      // 检查存储空间（如果支持）
      if (this.platform === 'h5' && 'storage' in navigator && 'estimate' in navigator.storage) {
        try {
          const estimate = await navigator.storage.estimate()
          result.checks.storage = {
            status: 'pass',
            message: '存储空间检查正常',
            quota: estimate.quota,
            usage: estimate.usage,
            usagePercentage: Math.round((estimate.usage / estimate.quota) * 100)
          }
        } catch (error) {
          result.checks.storage = {
            status: 'warn',
            message: `存储空间检查失败: ${error.message}`
          }
        }
      }
      
    } catch (error) {
      result.status = 'unhealthy'
      result.error = error.message
    }
    
    return result
  }
}
m
odule.exports = { DatabaseManager }