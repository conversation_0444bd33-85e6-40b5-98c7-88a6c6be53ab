<template>
	<view class="page-container">
		<view class="register-container">
			<view class="header-section">
				<text class="page-title">注册账户</text>
				<text class="page-subtitle">创建您的健康档案</text>
			</view>
			
			<view class="form-section">
				<view class="form-item">
					<text class="form-label">手机号</text>
					<input 
						class="form-input" 
						type="number" 
						placeholder="请输入手机号" 
						v-model="formData.phone"
						maxlength="11"
						@blur="validatePhone"
					/>
					<text v-if="errors.phone" class="error-text">{{ errors.phone }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">验证码</text>
					<view class="code-input-container">
						<input 
							class="form-input code-input" 
							type="number" 
							placeholder="请输入验证码" 
							v-model="formData.code"
							maxlength="6"
						/>
						<button 
							class="code-btn" 
							:class="{ disabled: codeCountdown > 0 || !isPhoneValid }"
							:disabled="codeCountdown > 0 || !isPhoneValid"
							@click="sendCode"
						>
							{{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
						</button>
					</view>
					<text v-if="errors.code" class="error-text">{{ errors.code }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">密码</text>
					<view class="password-input-container">
						<input 
							class="form-input" 
							:type="showPassword ? 'text' : 'password'" 
							placeholder="请输入密码（至少8位，包含字母和数字）" 
							v-model="formData.password"
							@blur="validatePassword"
						/>
						<text class="password-toggle" @click="togglePassword">
							{{ showPassword ? '隐藏' : '显示' }}
						</text>
					</view>
					<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">确认密码</text>
					<input 
						class="form-input" 
						:type="showConfirmPassword ? 'text' : 'password'" 
						placeholder="请再次输入密码" 
						v-model="formData.confirmPassword"
						@blur="validateConfirmPassword"
					/>
					<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">昵称（可选）</text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入昵称" 
						v-model="formData.nickname"
						maxlength="20"
					/>
				</view>
				
				<view class="agreement-section">
					<label class="agreement-item" @click="toggleAgreement">
						<checkbox 
							:checked="agreed" 
							color="#007AFF"
						/>
						<text class="agreement-text">
							我已阅读并同意
							<text class="link-text" @click.stop="showPrivacyPolicy">《隐私政策》</text>
							和
							<text class="link-text" @click.stop="showUserAgreement">《用户协议》</text>
						</text>
					</label>
				</view>
				
				<button 
					class="register-btn" 
					:class="{ disabled: !canRegister }"
					:disabled="!canRegister"
					@click="handleRegister"
					:loading="loading"
				>
					{{ loading ? '注册中...' : '注册' }}
				</button>
				
				<view class="login-link">
					<text class="link-text" @click="goToLogin">已有账户？立即登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { useUserStore } from '../../stores/user.js'
	import authService from '../../services/auth/authService.js'
	
	export default {
		name: 'Register',
		data() {
			return {
				formData: {
					phone: '',
					code: '',
					password: '',
					confirmPassword: '',
					nickname: ''
				},
				errors: {},
				showPassword: false,
				showConfirmPassword: false,
				agreed: false,
				loading: false,
				codeCountdown: 0,
				countdownTimer: null
			}
		},
		computed: {
			isPhoneValid() {
				return /^1[3-9]\d{9}$/.test(this.formData.phone)
			},
			canRegister() {
				return this.isPhoneValid && 
					   this.formData.code && 
					   this.formData.password && 
					   this.formData.confirmPassword && 
					   this.formData.password === this.formData.confirmPassword &&
					   this.agreed &&
					   !this.loading
			}
		},
		methods: {
			// 验证手机号
			validatePhone() {
				if (!this.formData.phone) {
					this.errors.phone = '请输入手机号'
				} else if (!this.isPhoneValid) {
					this.errors.phone = '手机号格式不正确'
				} else {
					delete this.errors.phone
				}
				this.$forceUpdate()
				return !this.errors.phone
			},
			
			// 验证密码
			validatePassword() {
				const password = this.formData.password
				if (!password) {
					this.errors.password = '请输入密码'
				} else if (password.length < 8) {
					this.errors.password = '密码至少8位'
				} else if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(password)) {
					this.errors.password = '密码必须包含字母和数字'
				} else {
					delete this.errors.password
				}
				this.$forceUpdate()
				return !this.errors.password
			},
			
			// 验证确认密码
			validateConfirmPassword() {
				if (!this.formData.confirmPassword) {
					this.errors.confirmPassword = '请确认密码'
				} else if (this.formData.password !== this.formData.confirmPassword) {
					this.errors.confirmPassword = '两次输入的密码不一致'
				} else {
					delete this.errors.confirmPassword
				}
				this.$forceUpdate()
				return !this.errors.confirmPassword
			},
			
			// 验证验证码
			validateCode() {
				if (!this.formData.code) {
					this.errors.code = '请输入验证码'
				} else if (!/^\d{6}$/.test(this.formData.code)) {
					this.errors.code = '验证码格式不正确'
				} else {
					delete this.errors.code
				}
				this.$forceUpdate()
				return !this.errors.code
			},
			
			// 验证所有表单字段
			validateAllFields() {
				const phoneValid = this.validatePhone()
				const codeValid = this.validateCode()
				const passwordValid = this.validatePassword()
				const confirmPasswordValid = this.validateConfirmPassword()
				
				return phoneValid && codeValid && passwordValid && confirmPasswordValid && this.agreed
			},
			
			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword
			},
			
			// 切换确认密码显示
			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword
			},
			
			// 切换协议同意状态
			toggleAgreement() {
				this.agreed = !this.agreed
			},
			
			// 发送验证码
			async sendCode() {
				if (!this.isPhoneValid) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}
				
				try {
					const result = await authService.sendVerificationCode(this.formData.phone, 'register')
					
					if (result.success) {
						uni.showToast({
							title: '验证码已发送',
							icon: 'success'
						})
						
						// 开始倒计时
						this.startCountdown()
					} else {
						uni.showToast({
							title: result.message,
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('发送验证码失败:', error)
					uni.showToast({
						title: '发送验证码失败',
						icon: 'none'
					})
				}
			},
			
			// 开始倒计时
			startCountdown() {
				this.codeCountdown = 60
				this.countdownTimer = setInterval(() => {
					this.codeCountdown--
					if (this.codeCountdown <= 0) {
						clearInterval(this.countdownTimer)
						this.countdownTimer = null
					}
				}, 1000)
			},
			
			// 处理注册
			async handleRegister() {
				if (!this.canRegister) return
				
				// 验证所有字段
				if (!this.validateAllFields()) {
					uni.showToast({
						title: '请检查输入信息',
						icon: 'none'
					})
					return
				}
				
				this.loading = true
				
				try {
					const userStore = useUserStore()
					const result = await authService.registerWithPhone({
						phone: this.formData.phone,
						code: this.formData.code,
						password: this.formData.password,
						nickname: this.formData.nickname || this.formData.phone
					})
					
					if (result.success) {
						// 保存用户信息到store
						await userStore.login(result.data)
						
						uni.showToast({
							title: '注册成功',
							icon: 'success'
						})
						
						// 延迟跳转到首页
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/index/index'
							})
						}, 1500)
					} else {
						uni.showToast({
							title: result.message || '注册失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('注册失败:', error)
					uni.showToast({
						title: '注册失败，请重试',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			
			// 跳转到登录页
			goToLogin() {
				uni.navigateTo({
					url: '/pages/auth/login'
				})
			},
			
			// 显示隐私政策
			showPrivacyPolicy() {
				uni.showModal({
					title: '隐私政策',
					content: '这里是隐私政策的内容...',
					showCancel: false
				})
			},
			
			// 显示用户协议
			showUserAgreement() {
				uni.showModal({
					title: '用户协议',
					content: '这里是用户协议的内容...',
					showCancel: false
				})
			}
		},
		
		onUnload() {
			// 清理倒计时器
			if (this.countdownTimer) {
				clearInterval(this.countdownTimer)
			}
		}
	}
</script>

<style scoped>
	.register-container {
		padding: 40px 30px;
		min-height: 100vh;
	}
	
	.header-section {
		text-align: center;
		margin-bottom: 40px;
	}
	
	.page-title {
		font-size: 24px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 8px;
	}
	
	.page-subtitle {
		font-size: 14px;
		color: #8E8E93;
		display: block;
	}
	
	.form-section {
		flex: 1;
	}
	
	.form-item {
		margin-bottom: 20px;
	}
	
	.form-label {
		display: block;
		font-size: 16px;
		color: #333333;
		margin-bottom: 8px;
		font-weight: 500;
	}
	
	.form-input {
		width: 100%;
		height: 44px;
		padding: 0 15px;
		border: 1px solid #E5E5E5;
		border-radius: 8px;
		font-size: 16px;
		background-color: #FFFFFF;
		box-sizing: border-box;
	}
	
	.form-input:focus {
		border-color: #007AFF;
	}
	
	.code-input-container {
		display: flex;
		align-items: center;
		gap: 10px;
	}
	
	.code-input {
		flex: 1;
	}
	
	.code-btn {
		height: 44px;
		padding: 0 16px;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 8px;
		font-size: 14px;
		white-space: nowrap;
	}
	
	.code-btn.disabled {
		background-color: #C7C7CC;
		color: #8E8E93;
	}
	
	.password-input-container {
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.password-toggle {
		position: absolute;
		right: 15px;
		color: #007AFF;
		font-size: 14px;
		cursor: pointer;
	}
	
	.error-text {
		display: block;
		color: #FF3B30;
		font-size: 12px;
		margin-top: 5px;
	}
	
	.agreement-section {
		margin: 30px 0;
	}
	
	.agreement-item {
		display: flex;
		align-items: flex-start;
		gap: 8px;
	}
	
	.agreement-text {
		font-size: 14px;
		color: #8E8E93;
		line-height: 1.4;
		flex: 1;
	}
	
	.link-text {
		color: #007AFF;
		text-decoration: underline;
	}
	
	.register-btn {
		width: 100%;
		height: 50px;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 25px;
		font-size: 18px;
		font-weight: 600;
		margin-bottom: 20px;
	}
	
	.register-btn.disabled {
		background-color: #C7C7CC;
		color: #8E8E93;
	}
	
	.login-link {
		text-align: center;
		margin-top: 20px;
	}
	
	.login-link .link-text {
		font-size: 16px;
		color: #007AFF;
	}
</style>