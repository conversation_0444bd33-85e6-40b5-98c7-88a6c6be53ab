/**
 * OCR服务单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import ocrService, { OCRService } from '../../services/ocr/ocrService.js'

// Mock uni API
global.uni = {
  request: vi.fn(),
  getFileSystemManager: vi.fn(() => ({
    readFile: vi.fn()
  }))
}

describe('OCRService', () => {
  let service

  beforeEach(() => {
    service = new OCRService()
    vi.clearAllMocks()
  })

  describe('构造函数', () => {
    it('应该正确初始化配置', () => {
      expect(service.apiConfig.apiKey).toBe('YOUR_BAIDU_API_KEY')
      expect(service.apiConfig.secretKey).toBe('YOUR_BAIDU_SECRET_KEY')
      expect(service.accessToken).toBeNull()
      expect(service.tokenExpireTime).toBe(0)
    })

    it('应该包含健康报告识别模式', () => {
      expect(service.healthReportPatterns.itemName).toBeInstanceOf(Array)
      expect(service.healthReportPatterns.value).toBeInstanceOf(RegExp)
      expect(service.healthReportPatterns.referenceRange).toBeInstanceOf(RegExp)
      expect(service.healthReportPatterns.date).toBeInstanceOf(RegExp)
    })
  })

  describe('recognizeHealthReport', () => {
    beforeEach(() => {
      service._ensureAccessToken = vi.fn().mockResolvedValue()
      service._imageToBase64 = vi.fn().mockResolvedValue('base64string')
      service._callOCRAPI = vi.fn()
      service._parseHealthReportData = vi.fn()
      service._calculateConfidence = vi.fn().mockReturnValue(85)
    })

    it('应该成功识别健康报告', async () => {
      const mockOcrData = {
        words_result: [
          { words: '血红蛋白 120 g/L 110-160', probability: { average: 0.9 } },
          { words: '2023年12月15日', probability: { average: 0.95 } },
          { words: '北京协和医院', probability: { average: 0.88 } }
        ]
      }

      const mockParsedData = {
        items: [
          {
            name: '血红蛋白',
            value: '120',
            unit: 'g/L',
            referenceRange: { min: 110, max: 160 },
            isAbnormal: false
          }
        ],
        date: '2023-12-15',
        hospital: '北京协和医院',
        doctor: null
      }

      service._callOCRAPI.mockResolvedValue({
        success: true,
        data: mockOcrData
      })

      service._parseHealthReportData.mockResolvedValue(mockParsedData)

      const result = await service.recognizeHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockParsedData)
      expect(result.confidence).toBe(85)
      expect(service._ensureAccessToken).toHaveBeenCalled()
      expect(service._imageToBase64).toHaveBeenCalledWith('/mock/path/report.jpg')
    })

    it('应该处理OCR API调用失败', async () => {
      service._callOCRAPI.mockResolvedValue({
        success: false,
        error: 'API调用失败',
        code: 'OCR_API_FAILED'
      })

      const result = await service.recognizeHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(false)
      expect(result.error).toBe('API调用失败')
      expect(result.fallback).toBeDefined()
      expect(result.fallback.manualInput).toBe(true)
    })

    it('应该处理图片转换失败', async () => {
      service._imageToBase64.mockRejectedValue(new Error('图片读取失败'))

      const result = await service.recognizeHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(false)
      expect(result.fallback).toBeDefined()
    })
  })

  describe('recognizeMultipleImages', () => {
    it('应该批量处理多张图片', async () => {
      service.recognizeHealthReport = vi.fn()
        .mockResolvedValueOnce({
          success: true,
          data: { items: [], date: '2023-12-15' }
        })
        .mockResolvedValueOnce({
          success: true,
          data: { items: [], date: '2023-12-16' }
        })

      const imagePaths = ['/path/1.jpg', '/path/2.jpg']
      const result = await service.recognizeMultipleImages(imagePaths)

      expect(result.success).toBe(true)
      expect(result.results).toHaveLength(2)
      expect(result.totalCount).toBe(2)
      expect(result.successCount).toBe(2)
      expect(result.errorCount).toBe(0)
    })

    it('应该处理部分图片识别失败', async () => {
      service.recognizeHealthReport = vi.fn()
        .mockResolvedValueOnce({
          success: true,
          data: { items: [], date: '2023-12-15' }
        })
        .mockRejectedValueOnce(new Error('识别失败'))

      const imagePaths = ['/path/1.jpg', '/path/2.jpg']
      const result = await service.recognizeMultipleImages(imagePaths)

      expect(result.success).toBe(true)
      expect(result.results).toHaveLength(1)
      expect(result.errors).toHaveLength(1)
      expect(result.successCount).toBe(1)
      expect(result.errorCount).toBe(1)
    })
  })

  describe('validateOCRResult', () => {
    it('应该验证完整的OCR结果', () => {
      const ocrData = {
        items: [
          { name: '血红蛋白', value: '120' },
          { name: '血糖', value: '5.5' }
        ],
        date: '2023-12-15',
        hospital: '北京协和医院'
      }

      const validation = service.validateOCRResult(ocrData)

      expect(validation.isValid).toBe(true)
      expect(validation.issues).toHaveLength(0)
      expect(validation.confidence).toBe(100)
    })

    it('应该检测缺少必要字段', () => {
      const ocrData = {
        items: [],
        date: null,
        hospital: null
      }

      const validation = service.validateOCRResult(ocrData)

      expect(validation.isValid).toBe(false)
      expect(validation.issues).toContain('缺少items字段')
      expect(validation.issues).toContain('缺少date字段')
      expect(validation.issues).toContain('缺少hospital字段')
    })

    it('应该检测无效的检查项目', () => {
      const ocrData = {
        items: [
          { name: '血红蛋白', value: '' }, // 缺少值
          { name: '', value: '120' }       // 缺少名称
        ],
        date: '2023-12-15',
        hospital: '北京协和医院'
      }

      const validation = service.validateOCRResult(ocrData)

      expect(validation.confidence).toBe(0) // 没有有效项目
      expect(validation.issues).toContain('未识别到有效的检查项目')
    })
  })

  describe('_getAccessToken', () => {
    it('应该成功获取访问令牌', async () => {
      global.uni.request.mockResolvedValue({
        statusCode: 200,
        data: {
          access_token: 'mock_token',
          expires_in: 2592000
        }
      })

      await service._getAccessToken()

      expect(service.accessToken).toBe('mock_token')
      expect(service.tokenExpireTime).toBeGreaterThan(Date.now())
    })

    it('应该处理获取令牌失败', async () => {
      global.uni.request.mockResolvedValue({
        statusCode: 400,
        data: {
          error: 'invalid_client'
        }
      })

      await expect(service._getAccessToken()).rejects.toThrow('OCR服务初始化失败')
    })
  })

  describe('_imageToBase64', () => {
    it('应该成功转换图片为base64', async () => {
      const mockFileManager = {
        readFile: vi.fn().mockImplementation((options) => {
          options.success({ data: 'base64string' })
        })
      }
      global.uni.getFileSystemManager.mockReturnValue(mockFileManager)

      const result = await service._imageToBase64('/mock/path/image.jpg')

      expect(result).toBe('base64string')
      expect(mockFileManager.readFile).toHaveBeenCalledWith({
        filePath: '/mock/path/image.jpg',
        encoding: 'base64',
        success: expect.any(Function),
        fail: expect.any(Function)
      })
    })

    it('应该处理文件读取失败', async () => {
      const mockFileManager = {
        readFile: vi.fn().mockImplementation((options) => {
          options.fail({ errMsg: '文件不存在' })
        })
      }
      global.uni.getFileSystemManager.mockReturnValue(mockFileManager)

      await expect(service._imageToBase64('/mock/path/nonexistent.jpg'))
        .rejects.toThrow('图片转换失败: 文件不存在')
    })
  })

  describe('_parseHealthReportData', () => {
    it('应该解析健康报告数据', async () => {
      const mockOcrData = {
        words_result: [
          { words: '血红蛋白 120 g/L 110-160', probability: { average: 0.9 } },
          { words: '2023年12月15日', probability: { average: 0.95 } },
          { words: '北京协和医院', probability: { average: 0.88 } },
          { words: '医师：张医生', probability: { average: 0.85 } }
        ]
      }

      const result = await service._parseHealthReportData(mockOcrData)

      expect(result.rawText).toContain('血红蛋白')
      expect(result.rawText).toContain('2023年12月15日')
      expect(result.metadata.totalWords).toBe(4)
      expect(result.metadata.averageConfidence).toBeCloseTo(0.895)
    })

    it('应该处理空的OCR结果', async () => {
      const mockOcrData = { words_result: [] }

      const result = await service._parseHealthReportData(mockOcrData)

      expect(result.items).toHaveLength(0)
      expect(result.date).toBeNull()
      expect(result.hospital).toBeNull()
      expect(result.doctor).toBeNull()
      expect(result.rawText).toBe('')
    })
  })

  describe('_extractHealthItems', () => {
    it('应该提取健康检查项目', () => {
      const text = '血红蛋白 120 g/L 110-160\n血糖 5.5 mmol/L 3.9-6.1'

      const items = service._extractHealthItems(text)

      expect(items).toHaveLength(2)
      expect(items[0].name).toBe('血红蛋白')
      expect(items[0].value).toBe('120')
      expect(items[0].unit).toBe('g/L')
    })
  })

  describe('_parseHealthItem', () => {
    it('应该解析单个健康项目', () => {
      const line = '血红蛋白 120 g/L 110-160'

      const item = service._parseHealthItem(line)

      expect(item).not.toBeNull()
      expect(item.name).toBe('血红蛋白')
      expect(item.value).toBe('120')
      expect(item.unit).toBe('g/L')
      expect(item.referenceRange.min).toBe(110)
      expect(item.referenceRange.max).toBe(160)
      expect(item.isAbnormal).toBe(false)
    })

    it('应该检测异常值', () => {
      const line = '血红蛋白 90 g/L 110-160' // 低于正常范围

      const item = service._parseHealthItem(line)

      expect(item.isAbnormal).toBe(true)
    })

    it('应该处理无效的行', () => {
      const line = '无效的行内容'

      const item = service._parseHealthItem(line)

      expect(item).toBeNull()
    })
  })

  describe('_extractDate', () => {
    it('应该提取日期', () => {
      const text = '检查日期：2023年12月15日'

      const date = service._extractDate(text)

      expect(date).toBe('2023-12-15')
    })

    it('应该处理不同的日期格式', () => {
      expect(service._extractDate('2023-12-15')).toBe('2023-12-15')
      expect(service._extractDate('2023/12/15')).toBe('2023-12-15')
      expect(service._extractDate('2023年12月15日')).toBe('2023-12-15')
    })

    it('应该处理没有日期的情况', () => {
      const text = '没有日期信息的文本'

      const date = service._extractDate(text)

      expect(date).toBeNull()
    })
  })

  describe('_extractHospital', () => {
    it('应该提取医院名称', () => {
      const text = '北京协和医院\n体检报告'

      const hospital = service._extractHospital(text)

      expect(hospital).toBe('北京协和医院')
    })

    it('应该处理没有医院信息的情况', () => {
      const text = '没有医院信息的文本'

      const hospital = service._extractHospital(text)

      expect(hospital).toBeNull()
    })
  })

  describe('_extractDoctor', () => {
    it('应该提取医生姓名', () => {
      const text = '医师：张医生'

      const doctor = service._extractDoctor(text)

      expect(doctor).toBe('张医生')
    })

    it('应该处理没有医生信息的情况', () => {
      const text = '没有医生信息的文本'

      const doctor = service._extractDoctor(text)

      expect(doctor).toBeNull()
    })
  })

  describe('_calculateConfidence', () => {
    it('应该计算高置信度', () => {
      const parsedData = {
        items: [{ name: '血红蛋白', value: '120' }],
        date: '2023-12-15',
        hospital: '北京协和医院',
        doctor: '张医生',
        metadata: { averageConfidence: 0.9 }
      }

      const confidence = service._calculateConfidence(parsedData)

      expect(confidence).toBe(100) // 40 + 20 + 20 + 10 + 10
    })

    it('应该计算低置信度', () => {
      const parsedData = {
        items: [],
        date: null,
        hospital: null,
        doctor: null,
        metadata: { averageConfidence: 0.5 }
      }

      const confidence = service._calculateConfidence(parsedData)

      expect(confidence).toBe(0)
    })
  })

  describe('_isValidDate', () => {
    it('应该验证有效日期', () => {
      expect(service._isValidDate('2023-12-15')).toBe(true)
      expect(service._isValidDate('2023/12/15')).toBe(true)
    })

    it('应该检测无效日期', () => {
      expect(service._isValidDate('invalid-date')).toBe(false)
      expect(service._isValidDate('2023-13-32')).toBe(false)
    })
  })
})

describe('ocrService 单例', () => {
  it('应该导出单例实例', () => {
    expect(ocrService).toBeInstanceOf(OCRService)
  })

  it('应该在多次导入时返回同一个实例', async () => {
    const { default: ocrService2 } = await import('../../services/ocr/ocrService.js')
    expect(ocrService).toBe(ocrService2)
  })
})