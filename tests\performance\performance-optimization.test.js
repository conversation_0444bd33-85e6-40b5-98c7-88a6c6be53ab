/**
 * 性能优化功能测试
 */

import {
  performanceMonitor,
  lazyLoadManager,
  cacheManager,
  multiLevelCache,
  bundleOptimizer,
  memoryManager,
  appBenchmark,
  LazyImageLoader,
  VirtualScrollManager,
  DatabaseOptimizer,
  MultiLevelCache,
  BundleOptimizer,
  MemoryManager,
  AppPerformanceBenchmark,
  debounce,
  throttle
} from '../../utils/performance/index.js'

describe('性能优化功能测试', () => {
  
  describe('性能监控器测试', () => {
    test('应该能够监控页面加载性能', () => {
      const pageName = 'TestPage'
      
      // 开始监控
      const startTime = performanceMonitor.startPageLoad(pageName)
      expect(startTime).toBeGreaterThan(0)
      
      // 标记渲染完成
      performanceMonitor.markRenderComplete(pageName)
      
      // 标记交互就绪
      performanceMonitor.markInteractive(pageName)
      
      // 结束监控
      const loadTime = performanceMonitor.endPageLoad(pageName)
      expect(loadTime).toBeGreaterThan(0)
      
      // 获取指标
      const metrics = performanceMonitor.getPageMetrics(pageName)
      expect(metrics).toBeDefined()
      expect(metrics.loadTime).toBeGreaterThan(0)
    })

    test('应该能够记录Web指标', () => {
      const metric = {
        name: 'FCP',
        value: 1500
      }
      
      performanceMonitor.recordWebVitals(metric)
      
      const webVitals = performanceMonitor.getWebVitals()
      expect(webVitals.FCP).toBeDefined()
      expect(webVitals.FCP.value).toBe(1500)
      expect(webVitals.FCP.rating).toBe('good')
    })
  })

  describe('懒加载管理器测试', () => {
    test('应该能够创建懒加载图片', () => {
      const lazyLoader = new LazyImageLoader()
      const lazyImage = lazyLoader.createLazyImage('test.jpg')
      
      expect(lazyImage).toBeDefined()
      expect(lazyImage.id).toBeDefined()
      expect(lazyImage.realSrc).toBe('test.jpg')
      expect(lazyImage.loaded).toBe(false)
    })

    test('应该能够预加载图片列表', async () => {
      const lazyLoader = new LazyImageLoader()
      const images = [
        'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
      ]
      
      const results = await lazyLoader.preloadImages(images, { concurrency: 2 })
      expect(results).toHaveLength(2)
    })
  })

  describe('虚拟滚动管理器测试', () => {
    test('应该能够计算可见范围', () => {
      const virtualScroll = new VirtualScrollManager({
        itemHeight: 100,
        bufferSize: 5
      })
      
      virtualScroll.setTotalItems(1000)
      virtualScroll.updateScrollPosition(500, 800)
      
      const visibleItems = virtualScroll.getVisibleItems()
      expect(visibleItems.length).toBeGreaterThan(0)
      
      const totalHeight = virtualScroll.getTotalHeight()
      expect(totalHeight).toBe(100000) // 1000 * 100
    })
  })

  describe('多级缓存测试', () => {
    test('应该能够设置和获取缓存', async () => {
      const cache = new MultiLevelCache()
      const key = 'test_key'
      const value = { data: '测试数据' }
      
      await cache.set(key, value)
      const retrieved = await cache.get(key)
      
      expect(retrieved).toEqual(value)
    })

    test('应该能够批量操作缓存', async () => {
      const cache = new MultiLevelCache()
      const keyValuePairs = {
        'key1': 'value1',
        'key2': 'value2',
        'key3': 'value3'
      }
      
      await cache.setMultiple(keyValuePairs)
      
      const { results, missingKeys } = await cache.getMultiple(['key1', 'key2', 'key4'])
      
      expect(results.key1).toBe('value1')
      expect(results.key2).toBe('value2')
      expect(missingKeys).toContain('key4')
    })

    test('应该能够获取缓存统计', async () => {
      const cache = new MultiLevelCache()
      
      await cache.set('test1', 'data1')
      await cache.set('test2', 'data2')
      await cache.get('test1')
      
      const stats = cache.getStats()
      expect(stats.sets).toBe(2)
      expect(stats.l1Hits).toBeGreaterThanOrEqual(0)
    })
  })

  describe('包优化器测试', () => {
    test('应该能够分析未使用的模块', () => {
      const optimizer = new BundleOptimizer()
      optimizer.buildDependencyGraph()
      
      const unusedModules = optimizer.analyzeUnusedModules()
      expect(Array.isArray(unusedModules)).toBe(true)
    })

    test('应该能够获取模块依赖', () => {
      const optimizer = new BundleOptimizer()
      optimizer.buildDependencyGraph()
      
      const dependencies = optimizer.getModuleDependencies('pages/report/list')
      expect(Array.isArray(dependencies)).toBe(true)
    })

    test('应该能够获取加载统计', () => {
      const optimizer = new BundleOptimizer()
      const stats = optimizer.getLoadingStats()
      
      expect(stats).toHaveProperty('loadedModules')
      expect(stats).toHaveProperty('cachedModules')
      expect(stats).toHaveProperty('totalAvailable')
    })
  })

  describe('内存管理器测试', () => {
    test('应该能够创建对象池', () => {
      const manager = new MemoryManager()
      
      const pool = manager.createObjectPool('testPool', () => ({
        data: null,
        reset(data) { this.data = data },
        cleanup() { this.data = null }
      }))
      
      expect(pool).toBeDefined()
      expect(pool.objects).toEqual([])
    })

    test('应该能够从对象池获取和返回对象', () => {
      const manager = new MemoryManager()
      
      manager.createObjectPool('testPool', () => ({
        data: null,
        reset(data) { this.data = data },
        cleanup() { this.data = null }
      }))
      
      const obj1 = manager.getFromPool('testPool', 'test data')
      expect(obj1).toBeDefined()
      
      const returned = manager.returnToPool('testPool', obj1)
      expect(returned).toBe(true)
      
      const obj2 = manager.getFromPool('testPool')
      expect(obj2).toBe(obj1) // 应该是同一个对象
    })

    test('应该能够获取内存统计', () => {
      const manager = new MemoryManager()
      const stats = manager.getMemoryStats()
      
      expect(stats).toHaveProperty('current')
      expect(stats).toHaveProperty('peak')
      expect(stats).toHaveProperty('poolStats')
    })
  })

  describe('数据库优化器测试', () => {
    test('应该能够优化查询语句', () => {
      const optimizer = new DatabaseOptimizer()
      
      const sql = 'SELECT * FROM health_reports WHERE user_id = ?'
      const params = [123]
      
      const { sql: optimizedSql, params: optimizedParams } = 
        optimizer.optimizeQuery(sql, params)
      
      expect(optimizedSql).toContain('LIMIT')
      expect(optimizedParams).toEqual(params)
    })

    test('应该能够生成缓存键', () => {
      const optimizer = new DatabaseOptimizer()
      
      const sql = 'SELECT * FROM users WHERE id = ?'
      const params = [1]
      
      const cacheKey = optimizer.generateCacheKey(sql, params)
      expect(cacheKey).toBe('SELECT * FROM users WHERE id = ?_[1]')
    })
  })

  describe('性能基准测试', () => {
    test('应该能够注册和运行基准测试', async () => {
      const benchmark = new AppPerformanceBenchmark()
      
      benchmark.registerBenchmark('testBenchmark', async () => {
        await new Promise(resolve => setTimeout(resolve, 10))
        return 10
      }, {
        iterations: 5,
        category: 'test'
      })
      
      const result = await benchmark.runBenchmark('testBenchmark')
      
      expect(result).toBeDefined()
      expect(result.name).toBe('testBenchmark')
      expect(result.stats).toBeDefined()
      expect(result.times).toHaveLength(5)
    }, 10000)

    test('应该能够计算性能评级', async () => {
      const benchmark = new AppPerformanceBenchmark()
      
      // 模拟一些测试结果
      const mockResults = [
        {
          name: 'fastTest',
          category: 'ui',
          stats: { mean: 50 },
          error: null
        },
        {
          name: 'slowTest',
          category: 'database',
          stats: { mean: 200 },
          error: null
        }
      ]
      
      const grade = benchmark.calculatePerformanceGrade(mockResults)
      
      expect(grade).toHaveProperty('grade')
      expect(grade).toHaveProperty('score')
      expect(['A', 'B', 'C', 'D', 'F']).toContain(grade.grade)
    })
  })

  describe('工具函数测试', () => {
    test('防抖函数应该正常工作', (done) => {
      let callCount = 0
      const debouncedFn = debounce(() => {
        callCount++
      }, 100)
      
      // 快速调用多次
      debouncedFn()
      debouncedFn()
      debouncedFn()
      
      // 应该只执行一次
      setTimeout(() => {
        expect(callCount).toBe(1)
        done()
      }, 150)
    })

    test('节流函数应该正常工作', (done) => {
      let callCount = 0
      const throttledFn = throttle(() => {
        callCount++
      }, 100)
      
      // 快速调用多次
      throttledFn() // 立即执行
      throttledFn() // 被节流
      throttledFn() // 被节流
      
      setTimeout(() => {
        throttledFn() // 应该执行
        
        setTimeout(() => {
          expect(callCount).toBe(2)
          done()
        }, 50)
      }, 150)
    })
  })

  describe('集成测试', () => {
    test('所有性能工具应该能够协同工作', async () => {
      // 启动性能监控
      const pageName = 'IntegrationTest'
      performanceMonitor.startPageLoad(pageName)
      
      // 使用多级缓存
      await multiLevelCache.set('test_data', { value: 'integration test' })
      const cachedData = await multiLevelCache.get('test_data')
      expect(cachedData.value).toBe('integration test')
      
      // 使用内存管理
      memoryManager.createObjectPool('integrationPool', () => ({ data: null }))
      const pooledObj = memoryManager.getFromPool('integrationPool')
      expect(pooledObj).toBeDefined()
      
      // 结束性能监控
      const loadTime = performanceMonitor.endPageLoad(pageName)
      expect(loadTime).toBeGreaterThan(0)
      
      // 获取综合统计
      const metrics = performanceMonitor.getPageMetrics(pageName)
      const memoryStats = memoryManager.getMemoryStats()
      const cacheStats = multiLevelCache.getStats()
      
      expect(metrics).toBeDefined()
      expect(memoryStats).toBeDefined()
      expect(cacheStats).toBeDefined()
    })
  })
})

// 性能测试辅助函数
export function measurePerformance(fn, iterations = 100) {
  const times = []
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now()
    fn()
    const end = performance.now()
    times.push(end - start)
  }
  
  const sum = times.reduce((a, b) => a + b, 0)
  const mean = sum / times.length
  const sorted = times.sort((a, b) => a - b)
  const median = sorted[Math.floor(sorted.length / 2)]
  
  return {
    mean,
    median,
    min: Math.min(...times),
    max: Math.max(...times),
    times
  }
}

// 内存使用测试辅助函数
export function measureMemoryUsage(fn) {
  const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0
  
  fn()
  
  // 强制垃圾回收（如果支持）
  if (window.gc) {
    window.gc()
  }
  
  const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0
  
  return {
    initial: initialMemory,
    final: finalMemory,
    delta: finalMemory - initialMemory
  }
}