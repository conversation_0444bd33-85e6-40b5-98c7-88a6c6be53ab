/**
 * 加密工具单元测试
 */

import { getEncryption } from '../../utils/storage/encryption.js';

// Mock crypto API
global.crypto = {
  getRandomValues: jest.fn((array) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  })
};

// Mock btoa/atob
global.btoa = jest.fn((str) => Buffer.from(str, 'binary').toString('base64'));
global.atob = jest.fn((str) => Buffer.from(str, 'base64').toString('binary'));

describe('AESEncryption', () => {
  let encryption;

  beforeEach(() => {
    encryption = getEncryption();
    jest.clearAllMocks();
  });

  describe('密钥生成', () => {
    test('应该生成32字节的密钥', () => {
      const key = encryption.generateKey();
      
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(0);
      expect(global.btoa).toHaveBeenCalled();
    });

    test('应该生成不同的密钥', () => {
      const key1 = encryption.generateKey();
      const key2 = encryption.generateKey();
      
      expect(key1).not.toBe(key2);
    });

    test('应该生成12字节的IV', () => {
      const iv = encryption.generateIV();
      
      expect(typeof iv).toBe('string');
      expect(iv.length).toBeGreaterThan(0);
    });
  });

  describe('数据加密和解密', () => {
    test('应该能够加密和解密文本', async () => {
      const originalText = '这是一段测试文本';
      const key = encryption.generateKey();

      const encrypted = await encryption.encrypt(originalText, key);
      expect(encrypted).not.toBe(originalText);
      expect(typeof encrypted).toBe('string');

      const decrypted = await encryption.decrypt(encrypted, key);
      expect(decrypted).toBe(originalText);
    });

    test('应该能够处理空字符串', async () => {
      const originalText = '';
      const key = encryption.generateKey();

      const encrypted = await encryption.encrypt(originalText, key);
      const decrypted = await encryption.decrypt(encrypted, key);
      
      expect(decrypted).toBe(originalText);
    });

    test('应该能够处理特殊字符', async () => {
      const originalText = '测试中文！@#$%^&*()_+{}|:"<>?[]\\;\',./ 123';
      const key = encryption.generateKey();

      const encrypted = await encryption.encrypt(originalText, key);
      const decrypted = await encryption.decrypt(encrypted, key);
      
      expect(decrypted).toBe(originalText);
    });

    test('使用错误密钥解密应该失败', async () => {
      const originalText = '测试文本';
      const key1 = encryption.generateKey();
      const key2 = encryption.generateKey();

      const encrypted = await encryption.encrypt(originalText, key1);
      
      // 使用错误密钥解密应该得到错误结果
      const decrypted = await encryption.decrypt(encrypted, key2);
      expect(decrypted).not.toBe(originalText);
    });

    test('应该处理加密错误', async () => {
      // Mock base64ToArrayBuffer抛出错误
      const originalMethod = encryption.base64ToArrayBuffer;
      encryption.base64ToArrayBuffer = jest.fn(() => {
        throw new Error('解析错误');
      });

      await expect(encryption.encrypt('test', 'invalid-key')).rejects.toThrow('数据加密失败');
      
      // 恢复原方法
      encryption.base64ToArrayBuffer = originalMethod;
    });

    test('应该处理解密错误', async () => {
      await expect(encryption.decrypt('invalid-data', 'key')).rejects.toThrow('数据解密失败');
    });
  });

  describe('密码哈希', () => {
    test('应该生成密码哈希', async () => {
      const password = 'testpassword123';
      const salt = encryption.generateSalt();

      const hash = await encryption.hashPassword(password, salt);
      
      expect(typeof hash).toBe('string');
      expect(hash.length).toBe(64); // 64位十六进制字符串
    });

    test('相同密码和盐应该生成相同哈希', async () => {
      const password = 'testpassword123';
      const salt = encryption.generateSalt();

      const hash1 = await encryption.hashPassword(password, salt);
      const hash2 = await encryption.hashPassword(password, salt);
      
      expect(hash1).toBe(hash2);
    });

    test('不同密码应该生成不同哈希', async () => {
      const salt = encryption.generateSalt();

      const hash1 = await encryption.hashPassword('password1', salt);
      const hash2 = await encryption.hashPassword('password2', salt);
      
      expect(hash1).not.toBe(hash2);
    });

    test('不同盐值应该生成不同哈希', async () => {
      const password = 'testpassword123';
      const salt1 = encryption.generateSalt();
      const salt2 = encryption.generateSalt();

      const hash1 = await encryption.hashPassword(password, salt1);
      const hash2 = await encryption.hashPassword(password, salt2);
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('盐值生成', () => {
    test('应该生成16字节的盐值', () => {
      const salt = encryption.generateSalt();
      
      expect(typeof salt).toBe('string');
      expect(salt.length).toBeGreaterThan(0);
    });

    test('应该生成不同的盐值', () => {
      const salt1 = encryption.generateSalt();
      const salt2 = encryption.generateSalt();
      
      expect(salt1).not.toBe(salt2);
    });
  });

  describe('工具方法', () => {
    test('arrayBufferToBase64应该正确转换', () => {
      const array = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
      const base64 = encryption.arrayBufferToBase64(array);
      
      expect(typeof base64).toBe('string');
      expect(global.btoa).toHaveBeenCalled();
    });

    test('base64ToArrayBuffer应该正确转换', () => {
      const base64 = 'SGVsbG8='; // "Hello" in base64
      global.atob.mockReturnValue('Hello');
      
      const array = encryption.base64ToArrayBuffer(base64);
      
      expect(array).toBeInstanceOf(Uint8Array);
      expect(global.atob).toHaveBeenCalledWith(base64);
    });
  });

  describe('降级处理', () => {
    test('没有crypto API时应该使用Math.random', () => {
      // 临时移除crypto API
      const originalCrypto = global.crypto;
      delete global.crypto;

      const key = encryption.generateKey();
      
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(0);

      // 恢复crypto API
      global.crypto = originalCrypto;
    });
  });
});