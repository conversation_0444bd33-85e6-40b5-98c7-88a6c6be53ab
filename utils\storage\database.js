/**
 * 数据库连接和初始化工具类
 * 基于uni-app的存储API实现SQLite功能
 */

import { ALL_TABLES, DATABASE_VERSION, MIGRATION_SCRIPTS } from './schema.js';
import { getEncryption } from './encryption.js';

class Database {
  constructor() {
    this.dbName = 'heath_report_db';
    this.isInitialized = false;
    this.encryption = getEncryption();
    this.tables = new Map(); // 内存中的表数据缓存
  }

  /**
   * 初始化数据库
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      console.log('开始初始化数据库...');
      
      // 检查数据库版本
      const currentVersion = await this.getDatabaseVersion();
      console.log('当前数据库版本:', currentVersion);
      
      if (currentVersion < DATABASE_VERSION) {
        await this.migrate(currentVersion, DATABASE_VERSION);
      }
      
      // 初始化表结构
      await this.createTables();
      
      // 加载现有数据到内存
      await this.loadTablesData();
      
      this.isInitialized = true;
      console.log('数据库初始化完成');
      return true;
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw new Error(`数据库初始化失败: ${error.message}`);
    }
  }

  /**
   * 获取数据库版本
   * @returns {Promise<number>}
   */
  async getDatabaseVersion() {
    try {
      const version = uni.getStorageSync(`${this.dbName}_version`);
      return version || 0;
    } catch (error) {
      console.error('获取数据库版本失败:', error);
      return 0;
    }
  }

  /**
   * 设置数据库版本
   * @param {number} version 
   */
  async setDatabaseVersion(version) {
    try {
      uni.setStorageSync(`${this.dbName}_version`, version);
    } catch (error) {
      console.error('设置数据库版本失败:', error);
    }
  }

  /**
   * 数据库迁移
   * @param {number} fromVersion 
   * @param {number} toVersion 
   */
  async migrate(fromVersion, toVersion) {
    console.log(`数据库迁移: ${fromVersion} -> ${toVersion}`);
    
    for (let version = fromVersion + 1; version <= toVersion; version++) {
      const scripts = MIGRATION_SCRIPTS[version];
      if (scripts) {
        console.log(`执行版本 ${version} 的迁移脚本`);
        // 在实际的SQLite环境中，这里会执行SQL脚本
        // 在uni-app环境中，我们通过重新创建表结构来模拟
      }
    }
    
    await this.setDatabaseVersion(toVersion);
  }

  /**
   * 创建表结构
   */
  async createTables() {
    for (const table of ALL_TABLES) {
      if (!this.tables.has(table.name)) {
        this.tables.set(table.name, []);
        console.log(`创建表: ${table.name}`);
      }
    }
  }

  /**
   * 加载表数据到内存
   */
  async loadTablesData() {
    for (const table of ALL_TABLES) {
      try {
        const data = uni.getStorageSync(`${this.dbName}_${table.name}`);
        if (data) {
          this.tables.set(table.name, Array.isArray(data) ? data : []);
        }
      } catch (error) {
        console.error(`加载表 ${table.name} 数据失败:`, error);
        this.tables.set(table.name, []);
      }
    }
  }

  /**
   * 保存表数据到存储
   * @param {string} tableName 
   */
  async saveTableData(tableName) {
    try {
      const data = this.tables.get(tableName) || [];
      uni.setStorageSync(`${this.dbName}_${tableName}`, data);
    } catch (error) {
      console.error(`保存表 ${tableName} 数据失败:`, error);
      throw error;
    }
  }

  /**
   * 获取表数据
   * @param {string} tableName 
   * @returns {Array}
   */
  getTableData(tableName) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }
    return this.tables.get(tableName) || [];
  }

  /**
   * 生成自增ID
   * @param {string} tableName 
   * @returns {number}
   */
  generateId(tableName) {
    const data = this.getTableData(tableName);
    if (data.length === 0) {
      return 1;
    }
    const maxId = Math.max(...data.map(row => row.id || 0));
    return maxId + 1;
  }

  /**
   * 执行查询
   * @param {string} tableName 
   * @param {Object} conditions 查询条件
   * @param {Object} options 查询选项
   * @returns {Array}
   */
  query(tableName, conditions = {}, options = {}) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }

    let data = this.getTableData(tableName);
    
    // 应用查询条件
    if (Object.keys(conditions).length > 0) {
      data = data.filter(row => {
        return Object.entries(conditions).every(([key, value]) => {
          if (typeof value === 'object' && value !== null) {
            // 支持操作符查询，如 { age: { $gt: 18 } }
            if (value.$gt !== undefined) return row[key] > value.$gt;
            if (value.$lt !== undefined) return row[key] < value.$lt;
            if (value.$gte !== undefined) return row[key] >= value.$gte;
            if (value.$lte !== undefined) return row[key] <= value.$lte;
            if (value.$ne !== undefined) return row[key] !== value.$ne;
            if (value.$in !== undefined) return value.$in.includes(row[key]);
            if (value.$like !== undefined) return String(row[key]).includes(value.$like);
          }
          return row[key] === value;
        });
      });
    }
    
    // 应用排序
    if (options.orderBy) {
      const [field, direction = 'ASC'] = options.orderBy.split(' ');
      data.sort((a, b) => {
        const aVal = a[field];
        const bVal = b[field];
        const result = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        return direction.toUpperCase() === 'DESC' ? -result : result;
      });
    }
    
    // 应用分页
    if (options.limit) {
      const offset = options.offset || 0;
      data = data.slice(offset, offset + options.limit);
    }
    
    return data;
  }

  /**
   * 插入数据
   * @param {string} tableName 
   * @param {Object} data 
   * @returns {Promise<Object>}
   */
  async insert(tableName, data) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }

    const tableData = this.getTableData(tableName);
    const newRecord = {
      ...data,
      id: this.generateId(tableName),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    tableData.push(newRecord);
    await this.saveTableData(tableName);
    
    console.log(`插入数据到表 ${tableName}:`, newRecord.id);
    return newRecord;
  }

  /**
   * 更新数据
   * @param {string} tableName 
   * @param {Object} conditions 
   * @param {Object} updateData 
   * @returns {Promise<number>} 受影响的行数
   */
  async update(tableName, conditions, updateData) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }

    const tableData = this.getTableData(tableName);
    let updatedCount = 0;

    for (let i = 0; i < tableData.length; i++) {
      const row = tableData[i];
      const matches = Object.entries(conditions).every(([key, value]) => row[key] === value);
      
      if (matches) {
        tableData[i] = {
          ...row,
          ...updateData,
          updated_at: new Date().toISOString()
        };
        updatedCount++;
      }
    }

    if (updatedCount > 0) {
      await this.saveTableData(tableName);
      console.log(`更新表 ${tableName} 中 ${updatedCount} 条记录`);
    }

    return updatedCount;
  }

  /**
   * 删除数据
   * @param {string} tableName 
   * @param {Object} conditions 
   * @returns {Promise<number>} 受影响的行数
   */
  async delete(tableName, conditions) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }

    const tableData = this.getTableData(tableName);
    const originalLength = tableData.length;

    const filteredData = tableData.filter(row => {
      return !Object.entries(conditions).every(([key, value]) => row[key] === value);
    });

    const deletedCount = originalLength - filteredData.length;
    
    if (deletedCount > 0) {
      this.tables.set(tableName, filteredData);
      await this.saveTableData(tableName);
      console.log(`从表 ${tableName} 删除 ${deletedCount} 条记录`);
    }

    return deletedCount;
  }

  /**
   * 清空表
   * @param {string} tableName 
   */
  async truncate(tableName) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }

    this.tables.set(tableName, []);
    await this.saveTableData(tableName);
    console.log(`清空表: ${tableName}`);
  }

  /**
   * 关闭数据库连接
   */
  async close() {
    // 保存所有表数据
    for (const tableName of this.tables.keys()) {
      await this.saveTableData(tableName);
    }
    
    this.isInitialized = false;
    console.log('数据库连接已关闭');
  }

  /**
   * 获取表统计信息
   * @param {string} tableName 
   * @returns {Object}
   */
  getTableStats(tableName) {
    const data = this.getTableData(tableName);
    return {
      tableName,
      recordCount: data.length,
      lastUpdated: data.length > 0 ? Math.max(...data.map(row => new Date(row.updated_at || row.created_at).getTime())) : null
    };
  }
}

// 单例模式
let databaseInstance = null;

export function getDatabase() {
  if (!databaseInstance) {
    databaseInstance = new Database();
  }
  return databaseInstance;
}

export default Database;