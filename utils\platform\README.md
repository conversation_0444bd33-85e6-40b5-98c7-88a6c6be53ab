# 平台适配层 (Platform Adapter)

平台适配层是为uni-app项目设计的跨平台API封装工具，用于统一处理不同平台（APP、微信小程序、H5等）之间的API差异。

## 功能特性

- 🔧 **统一API接口** - 封装跨平台API差异，提供一致的调用方式
- 📱 **平台检测** - 自动检测当前运行平台，支持条件编译
- 📷 **相机适配** - 统一的拍照和图片选择接口
- 💾 **存储适配** - 跨平台的本地存储解决方案
- 📤 **分享适配** - 统一的内容分享接口
- 🔐 **权限管理** - 统一的权限检查和请求机制
- 📁 **文件操作** - 跨平台的文件处理功能

## 目录结构

```
utils/platform/
├── index.js           # 统一入口文件
├── detector.js        # 平台检测工具
├── constants.js       # 平台常量定义
├── PlatformAdapter.js # 核心适配器类
├── example.js         # 使用示例
└── README.md          # 说明文档
```

## 快速开始

### 1. 导入适配器

```javascript
// 导入默认实例（推荐）
import PlatformAdapter from '@/utils/platform'

// 或者导入特定功能
import { 
  getCurrentPlatform, 
  isApp, 
  PLATFORM_TYPES 
} from '@/utils/platform'
```

### 2. 基本使用

```javascript
// 检查当前平台
const platform = getCurrentPlatform()
console.log('当前平台:', platform)

// 获取平台配置
const config = PlatformAdapter.config
console.log('平台配置:', config)

// 拍照
const result = await PlatformAdapter.takePhoto()
if (result.success) {
  console.log('拍照成功:', result.tempFilePaths)
}
```

## API 文档

### 平台检测

#### getCurrentPlatform()
获取当前平台类型

```javascript
const platform = getCurrentPlatform()
// 返回: 'app-plus' | 'mp-weixin' | 'h5' | 'mp' 等
```

#### 平台判断函数
```javascript
isApp()        // 是否为APP平台
isWeixinMp()   // 是否为微信小程序
isMp()         // 是否为小程序平台
isH5()         // 是否为H5平台
isIOS()        // 是否为iOS系统 (异步)
isAndroid()    // 是否为Android系统 (异步)
```

### 相机功能

#### chooseImage(options)
选择图片（拍照或从相册选择）

```javascript
const result = await PlatformAdapter.chooseImage({
  count: 1,                    // 选择数量
  sizeType: ['compressed'],    // 图片尺寸
  sourceType: ['camera', 'album'] // 来源类型
})
```

#### takePhoto(options)
拍照（H5平台不支持）

```javascript
const result = await PlatformAdapter.takePhoto({
  quality: 80
})
```

#### chooseFromAlbum(options)
从相册选择图片

```javascript
const result = await PlatformAdapter.chooseFromAlbum({
  count: 1
})
```

### 存储功能

#### setStorage(key, value, type)
设置本地存储

```javascript
// 普通存储
await PlatformAdapter.setStorage('user_info', userData)

// 安全存储（仅APP平台支持）
await PlatformAdapter.setStorage('token', 'secret', 'secure')
```

#### getStorage(key, type)
获取本地存储

```javascript
const data = await PlatformAdapter.getStorage('user_info')
```

#### removeStorage(key)
删除存储项

```javascript
await PlatformAdapter.removeStorage('user_info')
```

#### clearStorage()
清空所有存储

```javascript
await PlatformAdapter.clearStorage()
```

### 分享功能

#### share(options)
分享内容

```javascript
await PlatformAdapter.share({
  title: '分享标题',
  content: '分享内容',
  url: 'https://example.com',
  imageUrl: '/path/to/image.jpg'
})
```

### 文件操作

#### saveImageToPhotosAlbum(filePath)
保存图片到相册（H5平台不支持）

```javascript
await PlatformAdapter.saveImageToPhotosAlbum('/temp/image.jpg')
```

#### getFileInfo(filePath)
获取文件信息

```javascript
const fileInfo = await PlatformAdapter.getFileInfo('/temp/file.jpg')
console.log('文件大小:', fileInfo.size)
```

### 权限管理

#### checkPermission(permission)
检查权限

```javascript
const hasPermission = await PlatformAdapter.checkPermission('android.permission.CAMERA')
```

#### requestPermission(permission)
请求权限

```javascript
const granted = await PlatformAdapter.requestPermission('android.permission.CAMERA')
```

## 平台差异处理

### 相机功能
- **APP平台**: 支持拍照和相册选择，支持多张图片
- **微信小程序**: 支持拍照和相册选择，限制单张图片
- **H5平台**: 仅支持相册选择

### 存储功能
- **APP平台**: 支持大容量存储和安全存储
- **微信小程序**: 存储容量有限制
- **H5平台**: 使用localStorage，容量最小

### 分享功能
- **APP平台**: 支持多种分享方式（微信、QQ、微博等）
- **微信小程序**: 仅支持微信内分享
- **H5平台**: 使用Web Share API或降级到剪贴板

## 错误处理

适配器提供统一的错误码和错误处理机制：

```javascript
import { ERROR_CODES } from '@/utils/platform'

try {
  await PlatformAdapter.takePhoto()
} catch (error) {
  switch (error.code) {
    case ERROR_CODES.PERMISSION_DENIED:
      // 权限被拒绝
      break
    case ERROR_CODES.FEATURE_NOT_AVAILABLE:
      // 功能不可用
      break
    case ERROR_CODES.FILE_TOO_LARGE:
      // 文件过大
      break
    default:
      // 其他错误
  }
}
```

## 配置说明

每个平台都有对应的配置信息：

```javascript
const config = PlatformAdapter.config
// {
//   supportCamera: true,      // 是否支持相机
//   supportShare: true,       // 是否支持分享
//   supportBiometric: true,   // 是否支持生物识别
//   maxImageSize: 10485760,   // 最大图片大小（字节）
//   supportedImageFormats: ['jpg', 'jpeg', 'png', 'webp']
// }
```

## 最佳实践

### 1. 功能检查
在使用功能前先检查平台是否支持：

```javascript
if (PlatformAdapter.config.supportCamera) {
  // 使用相机功能
} else {
  // 提供替代方案
}
```

### 2. 错误处理
始终使用try-catch处理异步操作：

```javascript
try {
  const result = await PlatformAdapter.chooseImage()
  // 处理成功结果
} catch (error) {
  // 处理错误
  console.error('操作失败:', error)
}
```

### 3. 平台特定逻辑
对于平台特定的功能，使用条件判断：

```javascript
if (isApp()) {
  // APP平台特有功能
} else if (isWeixinMp()) {
  // 微信小程序特有功能
} else {
  // 通用功能
}
```

## 测试

运行单元测试：

```bash
npm test
```

运行手动测试：

```bash
node tests/platform/manual-test.js
```

## 扩展

如需添加新的平台适配功能：

1. 在 `constants.js` 中添加相关常量
2. 在 `PlatformAdapter.js` 中实现适配方法
3. 在 `tests/` 目录下添加对应测试
4. 更新使用示例和文档

## 注意事项

1. 某些功能在特定平台上可能不可用，需要提供降级方案
2. 文件大小限制因平台而异，需要在上传前检查
3. 权限请求仅在APP平台有效，其他平台会自动返回true
4. 安全存储功能目前仅在APP平台实现，其他平台使用普通存储

## 更新日志

- v1.0.0: 初始版本，支持基本的跨平台适配功能