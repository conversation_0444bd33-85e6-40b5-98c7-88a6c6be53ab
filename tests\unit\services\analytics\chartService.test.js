/**
 * 图表服务测试
 * 测试图表生成功能
 */

import chartService from '@/services/analytics/chartService.js'

describe('ChartService', () => {
  // 模拟趋势数据
  const mockTrendData = {
    trend: 'increasing',
    direction: 'increasing',
    slope: 0.1,
    correlation: 0.85,
    changeRate: 23.08,
    summary: '在过去2个月内，该指标呈上升趋势，增长了23.1%',
    dataPoints: [
      { date: '2024-01-01', value: 5.2, isAbnormal: false, timestamp: 1704067200000 },
      { date: '2024-01-15', value: 5.5, isAbnormal: false, timestamp: 1705276800000 },
      { date: '2024-02-01', value: 5.8, isAbnormal: false, timestamp: 1706745600000 },
      { date: '2024-02-15', value: 6.1, isAbnormal: true, timestamp: 1707955200000 },
      { date: '2024-03-01', value: 6.4, isAbnormal: true, timestamp: 1709251200000 }
    ],
    regression: {
      slope: 0.1,
      intercept: 5.0,
      correlation: 0.85,
      points: [
        { x: 0, y: 5.2 },
        { x: 14, y: 5.5 },
        { x: 31, y: 5.8 },
        { x: 45, y: 6.1 },
        { x: 59, y: 6.4 }
      ]
    }
  }

  const mockMultiTrendData = {
    indicators: {
      '血糖': {
        trend: 'increasing',
        dataPoints: [
          { date: '2024-01-01', value: 5.2 },
          { date: '2024-02-01', value: 5.8 },
          { date: '2024-03-01', value: 6.4 }
        ]
      },
      '血压': {
        trend: 'stable',
        dataPoints: [
          { date: '2024-01-01', value: 120 },
          { date: '2024-02-01', value: 122 },
          { date: '2024-03-01', value: 121 }
        ]
      }
    },
    correlations: {
      '血糖_血压': 0.3
    },
    summary: '分析了2个健康指标的趋势变化。其中1个指标呈上升趋势，1个指标保持稳定。'
  }

  describe('generateTrendLineChart', () => {
    test('应该生成正确的趋势折线图配置', () => {
      const options = {
        title: '血糖趋势',
        unit: 'mmol/L',
        showAbnormal: true,
        theme: 'light'
      }
      
      const config = chartService.generateTrendLineChart(mockTrendData, options)
      
      expect(config.type).toBe('line')
      expect(config.title.text).toBe('血糖趋势')
      expect(config.subtitle.text).toBe(mockTrendData.summary)
      expect(config.categories).toHaveLength(5)
      expect(config.series).toHaveLength(1)
      expect(config.series[0].data).toEqual([5.2, 5.5, 5.8, 6.1, 6.4])
      expect(config.series[0].color).toBe('#ff6b6b') // increasing trend color
      expect(config.yAxis.unit).toBe('mmol/L')
    })

    test('应该支持暗色主题', () => {
      const options = { theme: 'dark' }
      const config = chartService.generateTrendLineChart(mockTrendData, options)
      
      expect(config.title.color).toBe('#ffffff')
      expect(config.subtitle.color).toBe('#cccccc')
      expect(config.xAxis.fontColor).toBe('#cccccc')
      expect(config.yAxis.fontColor).toBe('#cccccc')
    })

    test('应该添加参考范围线', () => {
      const options = {
        referenceRange: { min: 3.9, max: 6.1 },
        theme: 'light'
      }
      
      const config = chartService.generateTrendLineChart(mockTrendData, options)
      
      expect(config.extra.markLine).toHaveLength(2)
      expect(config.extra.markLine[0].value).toBe(3.9)
      expect(config.extra.markLine[1].value).toBe(6.1)
    })

    test('应该标记异常点', () => {
      const trendDataWithAbnormal = {
        ...mockTrendData,
        dataPoints: mockTrendData.dataPoints.map((point, index) => ({
          ...point,
          isAbnormal: index >= 3 // 最后两个点异常
        }))
      }
      
      const config = chartService.generateTrendLineChart(trendDataWithAbnormal, { showAbnormal: true })
      
      expect(config.extra.markPoint).toHaveLength(2)
      expect(config.extra.markPoint[0].index).toBe(3)
      expect(config.extra.markPoint[1].index).toBe(4)
    })

    test('应该添加趋势线', () => {
      const config = chartService.generateTrendLineChart(mockTrendData)
      
      // 应该有原始数据线和趋势线
      expect(config.series).toHaveLength(2)
      expect(config.series[1].name).toBe('趋势线')
      expect(config.series[1].lineStyle).toBe('dash')
      expect(config.series[1].pointSize).toBe(0)
    })
  })

  describe('generateMultiIndicatorChart', () => {
    test('应该生成正确的多指标对比图配置', () => {
      const options = {
        title: '多指标趋势对比',
        theme: 'light',
        showLegend: true
      }
      
      const config = chartService.generateMultiIndicatorChart(mockMultiTrendData, options)
      
      expect(config.type).toBe('line')
      expect(config.title.text).toBe('多指标趋势对比')
      expect(config.subtitle.text).toBe(mockMultiTrendData.summary)
      expect(config.series).toHaveLength(2)
      expect(config.series[0].name).toBe('血糖')
      expect(config.series[1].name).toBe('血压')
      expect(config.legend.show).toBe(true)
    })

    test('应该处理不同日期的数据点', () => {
      const multiTrendDataWithDifferentDates = {
        indicators: {
          '血糖': {
            dataPoints: [
              { date: '2024-01-01', value: 5.2 },
              { date: '2024-02-01', value: 5.8 }
            ]
          },
          '血压': {
            dataPoints: [
              { date: '2024-01-15', value: 120 },
              { date: '2024-02-01', value: 122 }
            ]
          }
        },
        summary: '测试摘要'
      }
      
      const config = chartService.generateMultiIndicatorChart(multiTrendDataWithDifferentDates)
      
      // 应该包含所有日期
      expect(config.categories).toContain('01-01')
      expect(config.categories).toContain('01-15')
      expect(config.categories).toContain('02-01')
      
      // 血糖在01-15应该为null
      const bloodSugarSeries = config.series.find(s => s.name === '血糖')
      expect(bloodSugarSeries.data).toContain(null)
    })
  })

  describe('generateDistributionPieChart', () => {
    test('应该生成正确的分布饼图配置', () => {
      const distributionData = {
        '正常': 15,
        '偏高': 3,
        '偏低': 1,
        '异常': 2
      }
      
      const options = {
        title: '血糖分布统计'
      }
      
      const config = chartService.generateDistributionPieChart(distributionData, options)
      
      expect(config.type).toBe('pie')
      expect(config.title.text).toBe('血糖分布统计')
      expect(config.series).toHaveLength(4)
      expect(config.series[0].name).toBe('正常')
      expect(config.series[0].data).toBe(15)
      expect(config.legend.show).toBe(true)
    })
  })

  describe('getTrendColor', () => {
    test('应该返回正确的趋势颜色', () => {
      expect(chartService.getTrendColor('increasing')).toBe('#ff6b6b')
      expect(chartService.getTrendColor('decreasing')).toBe('#4ecdc4')
      expect(chartService.getTrendColor('stable')).toBe('#45b7d1')
      expect(chartService.getTrendColor('fluctuating')).toBe('#f9ca24')
      expect(chartService.getTrendColor('insufficient_data')).toBe('#95a5a6')
      expect(chartService.getTrendColor('unknown')).toBe('#45b7d1') // 默认颜色
    })
  })

  describe('generateColorPalette', () => {
    test('应该生成正确数量的颜色', () => {
      const colors3 = chartService.generateColorPalette(3)
      expect(colors3).toHaveLength(3)
      
      const colors15 = chartService.generateColorPalette(15)
      expect(colors15).toHaveLength(15)
    })

    test('应该为超出基础颜色数量的情况生成HSL颜色', () => {
      const colors = chartService.generateColorPalette(15)
      
      // 检查是否包含HSL格式的颜色
      const hasHslColor = colors.some(color => color.startsWith('hsl('))
      expect(hasHslColor).toBe(true)
    })
  })

  describe('getDistributionColor', () => {
    test('应该返回正确的分布颜色', () => {
      expect(chartService.getDistributionColor('正常')).toBe('#4ecdc4')
      expect(chartService.getDistributionColor('偏高')).toBe('#ff6b6b')
      expect(chartService.getDistributionColor('偏低')).toBe('#feca57')
      expect(chartService.getDistributionColor('异常')).toBe('#ff4757')
      expect(chartService.getDistributionColor('其他')).toBe('#45b7d1') // 默认颜色
    })
  })

  describe('addReferenceLines', () => {
    test('应该正确添加参考范围线', () => {
      const config = {
        extra: {}
      }
      
      const referenceRange = { min: 3.9, max: 6.1 }
      chartService.addReferenceLines(config, referenceRange, 'light')
      
      expect(config.extra.markLine).toHaveLength(2)
      expect(config.extra.markLine[0].type).toBe('horizontal')
      expect(config.extra.markLine[0].value).toBe(3.9)
      expect(config.extra.markLine[1].value).toBe(6.1)
    })

    test('应该处理只有上限或下限的情况', () => {
      const config1 = { extra: {} }
      chartService.addReferenceLines(config1, { min: 3.9 }, 'light')
      expect(config1.extra.markLine).toHaveLength(1)
      
      const config2 = { extra: {} }
      chartService.addReferenceLines(config2, { max: 6.1 }, 'light')
      expect(config2.extra.markLine).toHaveLength(1)
    })
  })

  describe('markAbnormalPoints', () => {
    test('应该正确标记异常点', () => {
      const config = { extra: {} }
      const abnormalPoints = [2, 4]
      
      chartService.markAbnormalPoints(config, abnormalPoints)
      
      expect(config.extra.markPoint).toHaveLength(2)
      expect(config.extra.markPoint[0].index).toBe(2)
      expect(config.extra.markPoint[1].index).toBe(4)
      expect(config.extra.markPoint[0].style.color).toBe('#ff4757')
    })
  })

  describe('addTrendLine', () => {
    test('应该正确添加趋势线', () => {
      const config = { series: [] }
      const regression = {
        points: [
          { x: 0, y: 5.2 },
          { x: 14, y: 5.5 },
          { x: 31, y: 5.8 }
        ],
        slope: 0.1,
        intercept: 5.0
      }
      
      chartService.addTrendLine(config, regression, 'light')
      
      expect(config.series).toHaveLength(1)
      expect(config.series[0].name).toBe('趋势线')
      expect(config.series[0].lineStyle).toBe('dash')
      expect(config.series[0].pointSize).toBe(0)
      expect(config.series[0].data).toHaveLength(3)
    })

    test('应该处理无效的回归数据', () => {
      const config = { series: [] }
      const invalidRegression = { points: [] }
      
      chartService.addTrendLine(config, invalidRegression, 'light')
      
      expect(config.series).toHaveLength(0)
    })
  })

  describe('generateExportConfig', () => {
    test('应该生成正确的导出配置', () => {
      const chartConfig = {
        type: 'line',
        title: { text: '测试图表' }
      }
      
      const exportOptions = {
        format: 'png',
        width: 1200,
        height: 800,
        quality: 0.95
      }
      
      const exportConfig = chartService.generateExportConfig(chartConfig, exportOptions)
      
      expect(exportConfig.width).toBe(1200)
      expect(exportConfig.height).toBe(800)
      expect(exportConfig.pixelRatio).toBe(2)
      expect(exportConfig.export.format).toBe('png')
      expect(exportConfig.export.quality).toBe(0.95)
      expect(exportConfig.export.backgroundColor).toBe('#ffffff')
    })

    test('应该使用默认导出选项', () => {
      const chartConfig = { type: 'line' }
      const exportConfig = chartService.generateExportConfig(chartConfig)
      
      expect(exportConfig.width).toBe(800)
      expect(exportConfig.height).toBe(600)
      expect(exportConfig.export.format).toBe('png')
      expect(exportConfig.export.quality).toBe(0.9)
    })
  })
})