<template>
	<view class="page-container">
		<view class="profile-container">
			<!-- 用户头像和基本信息 -->
			<view class="user-info-section">
				<view class="avatar-container" @click="changeAvatar">
					<image 
						class="avatar" 
						:src="userInfo.avatar || '/static/default-avatar.png'" 
						mode="aspectFill"
					/>
					<view class="avatar-edit-icon">📷</view>
				</view>
				<view class="user-basic-info">
					<text class="user-name">{{ userInfo.nickname || userInfo.phone || '用户' }}</text>
					<text class="user-phone">{{ userInfo.phone }}</text>
					<text v-if="isWechatBound" class="wechat-bound">已绑定微信</text>
				</view>
			</view>
			
			<!-- 个人信息设置 -->
			<view class="settings-section">
				<text class="section-title">个人信息</text>
				
				<view class="setting-item" @click="editNickname">
					<text class="setting-label">昵称</text>
					<view class="setting-value-container">
						<text class="setting-value">{{ userInfo.nickname || '未设置' }}</text>
						<text class="setting-arrow">></text>
					</view>
				</view>
				
				<view class="setting-item" @click="editPhone">
					<text class="setting-label">手机号</text>
					<view class="setting-value-container">
						<text class="setting-value">{{ userInfo.phone }}</text>
						<text class="setting-arrow">></text>
					</view>
				</view>
				
				<view v-if="!isWechatBound" class="setting-item" @click="bindWechat">
					<text class="setting-label">绑定微信</text>
					<view class="setting-value-container">
						<text class="setting-value">未绑定</text>
						<text class="setting-arrow">></text>
					</view>
				</view>
			</view>
			
			<!-- 安全设置 -->
			<view class="settings-section">
				<text class="section-title">安全设置</text>
				
				<view class="setting-item" @click="changePassword">
					<text class="setting-label">修改密码</text>
					<view class="setting-value-container">
						<text class="setting-arrow">></text>
					</view>
				</view>
				
				<view v-if="biometricSupported" class="setting-item">
					<text class="setting-label">{{ biometricTypeName }}登录</text>
					<view class="setting-value-container">
						<switch 
							:checked="biometricEnabled" 
							@change="toggleBiometric"
							color="#007AFF"
						/>
					</view>
				</view>
			</view>
			
			<!-- 应用设置 -->
			<view class="settings-section">
				<text class="section-title">应用设置</text>
				
				<view class="setting-item">
					<text class="setting-label">自动同步</text>
					<view class="setting-value-container">
						<switch 
							:checked="userSettings.autoSync" 
							@change="toggleAutoSync"
							color="#007AFF"
						/>
					</view>
				</view>
				
				<view class="setting-item">
					<text class="setting-label">通知提醒</text>
					<view class="setting-value-container">
						<switch 
							:checked="userSettings.notificationEnabled" 
							@change="toggleNotification"
							color="#007AFF"
						/>
					</view>
				</view>
				
				<view class="setting-item" @click="selectTheme">
					<text class="setting-label">主题</text>
					<view class="setting-value-container">
						<text class="setting-value">{{ themeNames[userSettings.theme] }}</text>
						<text class="setting-arrow">></text>
					</view>
				</view>
				
				<view class="setting-item" @click="selectLanguage">
					<text class="setting-label">语言</text>
					<view class="setting-value-container">
						<text class="setting-value">{{ languageNames[userSettings.language] }}</text>
						<text class="setting-arrow">></text>
					</view>
				</view>
			</view>
			
			<!-- 其他操作 -->
			<view class="settings-section">
				<text class="section-title">其他</text>
				
				<view class="setting-item" @click="showAbout">
					<text class="setting-label">关于应用</text>
					<view class="setting-value-container">
						<text class="setting-arrow">></text>
					</view>
				</view>
				
				<view class="setting-item" @click="showPrivacyPolicy">
					<text class="setting-label">隐私政策</text>
					<view class="setting-value-container">
						<text class="setting-arrow">></text>
					</view>
				</view>
				
				<view class="setting-item" @click="clearCache">
					<text class="setting-label">清除缓存</text>
					<view class="setting-value-container">
						<text class="setting-value">{{ cacheSize }}</text>
						<text class="setting-arrow">></text>
					</view>
				</view>
			</view>
			
			<!-- 退出登录 -->
			<view class="logout-section">
				<button class="logout-btn" @click="handleLogout">退出登录</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { useUserStore } from '../../stores/user.js'
	import biometricService from '../../services/auth/biometricService.js'
	import authService from '../../services/auth/authService.js'
	
	export default {
		name: 'Profile',
		data() {
			return {
				biometricSupported: false,
				biometricEnabled: false,
				biometricTypes: [],
				cacheSize: '0MB',
				themeNames: {
					'light': '浅色',
					'dark': '深色',
					'auto': '跟随系统'
				},
				languageNames: {
					'zh-CN': '简体中文',
					'en-US': 'English'
				}
			}
		},
		computed: {
			userStore() {
				return useUserStore()
			},
			userInfo() {
				return this.userStore.userInfo
			},
			userSettings() {
				return this.userStore.userSettings
			},
			isWechatBound() {
				return this.userStore.isWechatBound
			},
			biometricTypeName() {
				if (this.biometricTypes.includes('fingerprint')) {
					return '指纹'
				} else if (this.biometricTypes.includes('facial')) {
					return '面部识别'
				}
				return '生物识别'
			}
		},
		async onLoad() {
			await this.initPage()
		},
		methods: {
			// 初始化页面
			async initPage() {
				try {
					// 初始化生物识别
					await this.initBiometric()
					
					// 计算缓存大小
					await this.calculateCacheSize()
				} catch (error) {
					console.error('页面初始化失败:', error)
				}
			},
			
			// 初始化生物识别
			async initBiometric() {
				try {
					const initResult = await biometricService.init()
					if (initResult.success) {
						this.biometricSupported = initResult.isSupported
						this.biometricTypes = initResult.types
						this.biometricEnabled = biometricService.isEnabled()
					}
				} catch (error) {
					console.error('生物识别初始化失败:', error)
				}
			},
			
			// 计算缓存大小
			async calculateCacheSize() {
				try {
					// 这里应该实际计算缓存大小
					// 暂时使用模拟数据
					this.cacheSize = '12.5MB'
				} catch (error) {
					console.error('计算缓存大小失败:', error)
				}
			},
			
			// 更换头像
			changeAvatar() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['camera', 'album'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0]
						
						// 这里应该上传头像到服务器
						// 暂时直接更新本地
						this.userStore.setUserInfo({
							avatar: tempFilePath
						})
						
						uni.showToast({
							title: '头像更新成功',
							icon: 'success'
						})
					},
					fail: (err) => {
						console.error('选择头像失败:', err)
					}
				})
			},
			
			// 编辑昵称
			editNickname() {
				uni.showModal({
					title: '修改昵称',
					editable: true,
					placeholderText: '请输入昵称',
					success: (res) => {
						if (res.confirm && res.content) {
							this.userStore.setUserInfo({
								nickname: res.content
							})
							
							uni.showToast({
								title: '昵称修改成功',
								icon: 'success'
							})
						}
					}
				})
			},
			
			// 编辑手机号
			editPhone() {
				uni.navigateTo({
					url: '/pages/profile/change-phone'
				})
			},
			
			// 绑定微信
			async bindWechat() {
				try {
					const result = await authService.loginWithWechat()
					
					if (result.success && result.data.wechatInfo) {
						this.userStore.setWechatInfo(result.data.wechatInfo)
						
						uni.showToast({
							title: '微信绑定成功',
							icon: 'success'
						})
					} else {
						uni.showToast({
							title: result.message || '微信绑定失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('绑定微信失败:', error)
					uni.showToast({
						title: '微信绑定失败',
						icon: 'none'
					})
				}
			},
			
			// 修改密码
			changePassword() {
				uni.navigateTo({
					url: '/pages/profile/change-password'
				})
			},
			
			// 切换生物识别
			async toggleBiometric(e) {
				const enabled = e.detail.value
				
				try {
					if (enabled) {
						const result = await biometricService.enable()
						if (result.success) {
							this.biometricEnabled = true
							this.userStore.updateUserSettings({
								enableBiometric: true
							})
							
							uni.showToast({
								title: result.message,
								icon: 'success'
							})
						} else {
							uni.showToast({
								title: result.message,
								icon: 'none'
							})
						}
					} else {
						const result = await biometricService.disable()
						if (result.success) {
							this.biometricEnabled = false
							this.userStore.updateUserSettings({
								enableBiometric: false
							})
							
							uni.showToast({
								title: result.message,
								icon: 'success'
							})
						}
					}
				} catch (error) {
					console.error('切换生物识别失败:', error)
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},
			
			// 切换自动同步
			toggleAutoSync(e) {
				const enabled = e.detail.value
				this.userStore.updateUserSettings({
					autoSync: enabled
				})
			},
			
			// 切换通知提醒
			toggleNotification(e) {
				const enabled = e.detail.value
				this.userStore.updateUserSettings({
					notificationEnabled: enabled
				})
			},
			
			// 选择主题
			selectTheme() {
				const themes = [
					{ name: '浅色', value: 'light' },
					{ name: '深色', value: 'dark' },
					{ name: '跟随系统', value: 'auto' }
				]
				
				uni.showActionSheet({
					itemList: themes.map(t => t.name),
					success: (res) => {
						const selectedTheme = themes[res.tapIndex]
						this.userStore.updateUserSettings({
							theme: selectedTheme.value
						})
						
						uni.showToast({
							title: `已切换到${selectedTheme.name}主题`,
							icon: 'success'
						})
					}
				})
			},
			
			// 选择语言
			selectLanguage() {
				const languages = [
					{ name: '简体中文', value: 'zh-CN' },
					{ name: 'English', value: 'en-US' }
				]
				
				uni.showActionSheet({
					itemList: languages.map(l => l.name),
					success: (res) => {
						const selectedLanguage = languages[res.tapIndex]
						this.userStore.updateUserSettings({
							language: selectedLanguage.value
						})
						
						uni.showToast({
							title: `语言已切换到${selectedLanguage.name}`,
							icon: 'success'
						})
					}
				})
			},
			
			// 显示关于应用
			showAbout() {
				uni.showModal({
					title: '关于应用',
					content: '健康报告管理 v1.0.0\n\n记录健康，关爱生活',
					showCancel: false
				})
			},
			
			// 显示隐私政策
			showPrivacyPolicy() {
				uni.navigateTo({
					url: '/pages/profile/privacy-policy'
				})
			},
			
			// 清除缓存
			clearCache() {
				uni.showModal({
					title: '清除缓存',
					content: '确定要清除应用缓存吗？',
					success: (res) => {
						if (res.confirm) {
							// 这里应该实际清除缓存
							// 暂时模拟清除
							this.cacheSize = '0MB'
							
							uni.showToast({
								title: '缓存清除成功',
								icon: 'success'
							})
						}
					}
				})
			},
			
			// 退出登录
			handleLogout() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出登录吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								const result = await this.userStore.logout()
								
								if (result.success) {
									uni.showToast({
										title: '已退出登录',
										icon: 'success'
									})
									
									setTimeout(() => {
										uni.reLaunch({
											url: '/pages/auth/login'
										})
									}, 1500)
								}
							} catch (error) {
								console.error('退出登录失败:', error)
								uni.showToast({
									title: '退出登录失败',
									icon: 'none'
								})
							}
						}
					}
				})
			}
		}
	}
</script>

<style scoped>
	.profile-container {
		padding: 20px;
		background-color: #F2F2F7;
		min-height: 100vh;
	}
	
	.user-info-section {
		background-color: #FFFFFF;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		display: flex;
		align-items: center;
		gap: 15px;
	}
	
	.avatar-container {
		position: relative;
	}
	
	.avatar {
		width: 80px;
		height: 80px;
		border-radius: 40px;
		background-color: #E5E5E5;
	}
	
	.avatar-edit-icon {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 24px;
		height: 24px;
		background-color: #007AFF;
		border-radius: 12px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 12px;
		border: 2px solid #FFFFFF;
	}
	
	.user-basic-info {
		flex: 1;
	}
	
	.user-name {
		font-size: 20px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 5px;
	}
	
	.user-phone {
		font-size: 14px;
		color: #8E8E93;
		display: block;
		margin-bottom: 5px;
	}
	
	.wechat-bound {
		font-size: 12px;
		color: #07C160;
		background-color: #E8F5E8;
		padding: 2px 8px;
		border-radius: 10px;
		display: inline-block;
	}
	
	.settings-section {
		background-color: #FFFFFF;
		border-radius: 12px;
		margin-bottom: 20px;
		overflow: hidden;
	}
	
	.section-title {
		font-size: 16px;
		font-weight: 600;
		color: #333333;
		padding: 15px 20px 10px;
		display: block;
	}
	
	.setting-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 15px 20px;
		border-bottom: 1px solid #F2F2F7;
	}
	
	.setting-item:last-child {
		border-bottom: none;
	}
	
	.setting-label {
		font-size: 16px;
		color: #333333;
	}
	
	.setting-value-container {
		display: flex;
		align-items: center;
		gap: 8px;
	}
	
	.setting-value {
		font-size: 14px;
		color: #8E8E93;
	}
	
	.setting-arrow {
		font-size: 16px;
		color: #C7C7CC;
	}
	
	.logout-section {
		margin-top: 20px;
	}
	
	.logout-btn {
		width: 100%;
		height: 50px;
		background-color: #FF3B30;
		color: #FFFFFF;
		border: none;
		border-radius: 12px;
		font-size: 18px;
		font-weight: 600;
	}
</style>