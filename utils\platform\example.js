/**
 * 平台适配层使用示例
 * 展示如何在实际项目中使用PlatformAdapter
 */

import PlatformAdapter, { 
  getCurrentPlatform, 
  isApp, 
  isWeixinMp,
  PLATFORM_TYPES,
  ERROR_CODES 
} from './index.js'

/**
 * 示例1: 拍照功能
 */
export async function takePhotoExample() {
  try {
    console.log('当前平台:', getCurrentPlatform())
    
    // 检查平台是否支持拍照
    if (!PlatformAdapter.config.supportCamera) {
      throw new Error('当前平台不支持相机功能')
    }
    
    // 拍照
    const result = await PlatformAdapter.takePhoto({
      quality: 80,
      sizeType: ['compressed']
    })
    
    console.log('拍照成功:', result)
    return result
    
  } catch (error) {
    console.error('拍照失败:', error)
    
    // 根据错误类型提供不同的处理方案
    if (error.code === ERROR_CODES.PERMISSION_DENIED) {
      uni.showModal({
        title: '权限不足',
        content: '需要相机权限才能拍照，请在设置中开启',
        showCancel: false
      })
    } else if (error.code === ERROR_CODES.FEATURE_NOT_AVAILABLE) {
      // 降级到从相册选择
      return await PlatformAdapter.chooseFromAlbum()
    }
    
    throw error
  }
}

/**
 * 示例2: 跨平台存储
 */
export async function storageExample() {
  try {
    // 保存用户数据
    const userData = {
      id: '123',
      name: '张三',
      avatar: '/path/to/avatar.jpg'
    }
    
    await PlatformAdapter.setStorage('user_info', userData)
    console.log('用户数据保存成功')
    
    // 读取用户数据
    const savedData = await PlatformAdapter.getStorage('user_info')
    console.log('读取的用户数据:', savedData)
    
    // 如果是APP平台，可以使用安全存储保存敏感信息
    if (isApp()) {
      await PlatformAdapter.setStorage('user_token', 'secret_token', 'secure')
      console.log('安全存储保存成功')
    }
    
  } catch (error) {
    console.error('存储操作失败:', error)
  }
}

/**
 * 示例3: 跨平台分享
 */
export async function shareExample() {
  try {
    const shareData = {
      title: '我的健康报告',
      content: '查看我最新的健康检查报告',
      url: 'https://example.com/report/123',
      imageUrl: '/path/to/report-image.jpg'
    }
    
    // 检查平台是否支持分享
    if (!PlatformAdapter.config.supportShare) {
      console.log('当前平台不支持分享功能')
      return
    }
    
    const result = await PlatformAdapter.share(shareData)
    
    if (result.success) {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }
    
  } catch (error) {
    console.error('分享失败:', error)
    uni.showToast({
      title: '分享失败',
      icon: 'error'
    })
  }
}

/**
 * 示例4: 权限检查和请求
 */
export async function permissionExample() {
  try {
    // 检查相机权限
    const hasCameraPermission = await PlatformAdapter.checkPermission('android.permission.CAMERA')
    
    if (!hasCameraPermission) {
      console.log('没有相机权限，正在请求...')
      
      const granted = await PlatformAdapter.requestPermission('android.permission.CAMERA')
      
      if (granted) {
        console.log('相机权限获取成功')
      } else {
        console.log('用户拒绝了相机权限')
        uni.showModal({
          title: '权限提示',
          content: '需要相机权限才能使用拍照功能，请在设置中手动开启',
          showCancel: false
        })
      }
    } else {
      console.log('已有相机权限')
    }
    
  } catch (error) {
    console.error('权限检查失败:', error)
  }
}

/**
 * 示例5: 平台特定功能处理
 */
export async function platformSpecificExample() {
  const platform = getCurrentPlatform()
  
  switch (platform) {
    case PLATFORM_TYPES.APP_PLUS:
      console.log('APP平台特有功能')
      // 可以使用生物识别、推送通知等
      break
      
    case PLATFORM_TYPES.MP_WEIXIN:
      console.log('微信小程序特有功能')
      // 可以使用微信支付、获取用户信息等
      break
      
    case PLATFORM_TYPES.H5:
      console.log('H5平台特有功能')
      // 可以使用Web API、PWA功能等
      break
      
    default:
      console.log('通用功能处理')
  }
}

/**
 * 示例6: 文件操作
 */
export async function fileOperationExample() {
  try {
    // 选择图片
    const imageResult = await PlatformAdapter.chooseImage({
      count: 1,
      sizeType: ['compressed']
    })
    
    if (imageResult.success && imageResult.tempFilePaths.length > 0) {
      const imagePath = imageResult.tempFilePaths[0]
      
      // 获取文件信息
      const fileInfo = await PlatformAdapter.getFileInfo(imagePath)
      console.log('文件信息:', fileInfo)
      
      // 检查文件大小
      const maxSize = PlatformAdapter.config.maxImageSize
      if (fileInfo.size > maxSize) {
        uni.showToast({
          title: '图片太大，请选择较小的图片',
          icon: 'error'
        })
        return
      }
      
      // 如果不是H5平台，可以保存到相册
      if (getCurrentPlatform() !== PLATFORM_TYPES.H5) {
        await PlatformAdapter.saveImageToPhotosAlbum(imagePath)
        uni.showToast({
          title: '图片已保存到相册',
          icon: 'success'
        })
      }
    }
    
  } catch (error) {
    console.error('文件操作失败:', error)
  }
}

/**
 * 综合使用示例
 */
export async function comprehensiveExample() {
  console.log('=== 平台适配层综合使用示例 ===')
  
  // 1. 检查平台信息
  console.log('当前平台:', getCurrentPlatform())
  console.log('平台配置:', PlatformAdapter.config)
  
  // 2. 根据平台能力执行不同操作
  if (PlatformAdapter.config.supportCamera) {
    console.log('支持相机功能')
    // await takePhotoExample()
  }
  
  if (PlatformAdapter.config.supportShare) {
    console.log('支持分享功能')
    // await shareExample()
  }
  
  // 3. 存储操作
  await storageExample()
  
  // 4. 平台特定处理
  await platformSpecificExample()
  
  console.log('=== 示例执行完成 ===')
}