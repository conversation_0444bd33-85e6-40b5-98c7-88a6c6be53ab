<template>
  <uni-popup ref="popup" type="dialog" :mask-click="false">
    <view class="delete-confirm-dialog">
      <!-- 警告图标 -->
      <view class="dialog-icon">
        <uni-icons type="warning-filled" size="48" color="#FF5722"></uni-icons>
      </view>
      
      <!-- 标题 -->
      <text class="dialog-title">{{ title }}</text>
      
      <!-- 内容 -->
      <view class="dialog-content">
        <text class="content-text">{{ content }}</text>
        
        <!-- 详细信息 -->
        <view v-if="details" class="content-details">
          <text class="details-text">{{ details }}</text>
        </view>
        
        <!-- 风险提示 -->
        <view class="risk-warning">
          <uni-icons type="info" size="16" color="#FF9800"></uni-icons>
          <text class="warning-text">此操作无法撤销，请谨慎操作</text>
        </view>
        
        <!-- 确认输入 -->
        <view v-if="requireConfirmText" class="confirm-input-section">
          <text class="confirm-label">
            请输入"{{ confirmText }}"以确认删除：
          </text>
          <input 
            class="confirm-input"
            v-model="userConfirmText"
            :placeholder="`请输入：${confirmText}`"
            @input="onConfirmTextInput"
          />
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <button class="action-btn cancel" @tap="handleCancel">
          <text class="btn-text">取消</text>
        </button>
        <button 
          class="action-btn delete" 
          @tap="handleConfirm"
          :disabled="!canConfirm"
          :class="{ disabled: !canConfirm }"
        >
          <uni-icons v-if="deleting" type="spinner-cycle" size="16" color="#FFFFFF"></uni-icons>
          <text class="btn-text">{{ deleting ? '删除中...' : '确认删除' }}</text>
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'DeleteConfirmDialog',
  props: {
    // 对话框标题
    title: {
      type: String,
      default: '确认删除'
    },
    // 对话框内容
    content: {
      type: String,
      required: true
    },
    // 详细信息
    details: {
      type: String,
      default: ''
    },
    // 是否需要确认文本输入
    requireConfirmText: {
      type: Boolean,
      default: false
    },
    // 确认文本
    confirmText: {
      type: String,
      default: '删除'
    },
    // 是否正在删除
    deleting: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      // 用户输入的确认文本
      userConfirmText: ''
    }
  },
  
  computed: {
    // 是否可以确认
    canConfirm() {
      if (this.requireConfirmText) {
        return this.userConfirmText === this.confirmText
      }
      return true
    }
  },
  
  methods: {
    // 显示对话框
    show() {
      this.userConfirmText = ''
      this.$refs.popup.open()
    },
    
    // 隐藏对话框
    hide() {
      this.$refs.popup.close()
    },
    
    // 确认文本输入
    onConfirmTextInput() {
      // 实时验证输入
    },
    
    // 处理取消
    handleCancel() {
      this.hide()
      this.$emit('cancel')
    },
    
    // 处理确认
    handleConfirm() {
      if (!this.canConfirm) return
      
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
.delete-confirm-dialog {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  width: 600rpx;
  max-width: 90vw;
}

.dialog-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.dialog-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
  display: block;
}

.dialog-content {
  margin-bottom: 40rpx;
}

.content-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  text-align: center;
  display: block;
  margin-bottom: 20rpx;
}

.content-details {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.details-text {
  font-size: 24rpx;
  color: #333333;
  line-height: 1.5;
}

.risk-warning {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  background: #fff3e0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.warning-text {
  font-size: 24rpx;
  color: #E65100;
  flex: 1;
}

.confirm-input-section {
  margin-top: 30rpx;
}

.confirm-label {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.confirm-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  box-sizing: border-box;
  
  &:focus {
    border-color: #FF5722;
  }
  
  &::placeholder {
    color: #999999;
  }
}

.dialog-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  border: none;
  
  &.cancel {
    background: #f8f9fa;
    color: #666666;
    border: 2rpx solid #e9ecef;
  }
  
  &.delete {
    background: #FF5722;
    color: #ffffff;
    
    &.disabled {
      background: #CCCCCC;
      color: #999999;
    }
  }
}

.btn-text {
  font-size: 28rpx;
}
</style>