<template>
	<view class="page-container">
		<view class="change-password-container">
			<view class="header-section">
				<text class="page-title">修改密码</text>
				<text class="page-subtitle">为了您的账户安全，请定期修改密码</text>
			</view>
			
			<view class="form-section">
				<view class="form-item">
					<text class="form-label">当前密码</text>
					<view class="password-input-container">
						<input 
							class="form-input" 
							:type="showCurrentPassword ? 'text' : 'password'" 
							placeholder="请输入当前密码" 
							v-model="formData.currentPassword"
						/>
						<text class="password-toggle" @click="toggleCurrentPassword">
							{{ showCurrentPassword ? '隐藏' : '显示' }}
						</text>
					</view>
					<text v-if="errors.currentPassword" class="error-text">{{ errors.currentPassword }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">新密码</text>
					<view class="password-input-container">
						<input 
							class="form-input" 
							:type="showNewPassword ? 'text' : 'password'" 
							placeholder="请输入新密码（至少8位，包含字母和数字）" 
							v-model="formData.newPassword"
							@blur="validateNewPassword"
						/>
						<text class="password-toggle" @click="toggleNewPassword">
							{{ showNewPassword ? '隐藏' : '显示' }}
						</text>
					</view>
					<text v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</text>
				</view>
				
				<view class="form-item">
					<text class="form-label">确认新密码</text>
					<view class="password-input-container">
						<input 
							class="form-input" 
							:type="showConfirmPassword ? 'text' : 'password'" 
							placeholder="请再次输入新密码" 
							v-model="formData.confirmPassword"
							@blur="validateConfirmPassword"
						/>
						<text class="password-toggle" @click="toggleConfirmPassword">
							{{ showConfirmPassword ? '隐藏' : '显示' }}
						</text>
					</view>
					<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
				</view>
				
				<view class="password-tips">
					<text class="tips-title">密码要求：</text>
					<text class="tips-item">• 至少8位字符</text>
					<text class="tips-item">• 包含字母和数字</text>
					<text class="tips-item">• 建议包含特殊字符</text>
				</view>
				
				<button 
					class="change-btn" 
					:class="{ disabled: !canChange }"
					:disabled="!canChange"
					@click="handleChangePassword"
					:loading="loading"
				>
					{{ loading ? '修改中...' : '修改密码' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { useUserStore } from '../../stores/user.js'
	
	export default {
		name: 'ChangePassword',
		data() {
			return {
				formData: {
					currentPassword: '',
					newPassword: '',
					confirmPassword: ''
				},
				errors: {},
				showCurrentPassword: false,
				showNewPassword: false,
				showConfirmPassword: false,
				loading: false
			}
		},
		computed: {
			canChange() {
				return this.formData.currentPassword && 
					   this.formData.newPassword && 
					   this.formData.confirmPassword && 
					   this.formData.newPassword === this.formData.confirmPassword &&
					   !this.loading
			}
		},
		methods: {
			// 验证新密码
			validateNewPassword() {
				const password = this.formData.newPassword
				if (!password) {
					this.errors.newPassword = '请输入新密码'
				} else if (password.length < 8) {
					this.errors.newPassword = '密码至少8位'
				} else if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(password)) {
					this.errors.newPassword = '密码必须包含字母和数字'
				} else if (password === this.formData.currentPassword) {
					this.errors.newPassword = '新密码不能与当前密码相同'
				} else {
					delete this.errors.newPassword
				}
				this.$forceUpdate()
			},
			
			// 验证确认密码
			validateConfirmPassword() {
				if (!this.formData.confirmPassword) {
					this.errors.confirmPassword = '请确认新密码'
				} else if (this.formData.newPassword !== this.formData.confirmPassword) {
					this.errors.confirmPassword = '两次输入的密码不一致'
				} else {
					delete this.errors.confirmPassword
				}
				this.$forceUpdate()
			},
			
			// 切换当前密码显示
			toggleCurrentPassword() {
				this.showCurrentPassword = !this.showCurrentPassword
			},
			
			// 切换新密码显示
			toggleNewPassword() {
				this.showNewPassword = !this.showNewPassword
			},
			
			// 切换确认密码显示
			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword
			},
			
			// 处理密码修改
			async handleChangePassword() {
				if (!this.canChange) return
				
				// 验证所有字段
				this.validateNewPassword()
				this.validateConfirmPassword()
				
				if (Object.keys(this.errors).length > 0) {
					uni.showToast({
						title: '请检查输入信息',
						icon: 'none'
					})
					return
				}
				
				this.loading = true
				
				try {
					// 这里应该调用修改密码的API
					// 暂时模拟API调用
					await this.mockChangePassword()
					
					uni.showToast({
						title: '密码修改成功',
						icon: 'success'
					})
					
					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} catch (error) {
					console.error('修改密码失败:', error)
					uni.showToast({
						title: error.message || '修改密码失败，请重试',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			
			// 模拟修改密码API
			async mockChangePassword() {
				return new Promise((resolve, reject) => {
					setTimeout(() => {
						// 模拟验证当前密码
						if (this.formData.currentPassword !== 'password123') {
							reject(new Error('当前密码不正确'))
							return
						}
						
						// 模拟成功
						if (Math.random() > 0.1) { // 90%成功率
							resolve()
						} else {
							reject(new Error('网络错误，请重试'))
						}
					}, 1000)
				})
			}
		}
	}
</script>

<style scoped>
	.change-password-container {
		padding: 40px 30px;
		min-height: 100vh;
		background-color: #F2F2F7;
	}
	
	.header-section {
		text-align: center;
		margin-bottom: 40px;
	}
	
	.page-title {
		font-size: 24px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 8px;
	}
	
	.page-subtitle {
		font-size: 14px;
		color: #8E8E93;
		display: block;
	}
	
	.form-section {
		background-color: #FFFFFF;
		border-radius: 12px;
		padding: 20px;
	}
	
	.form-item {
		margin-bottom: 20px;
	}
	
	.form-label {
		display: block;
		font-size: 16px;
		color: #333333;
		margin-bottom: 8px;
		font-weight: 500;
	}
	
	.form-input {
		width: 100%;
		height: 44px;
		padding: 0 15px;
		border: 1px solid #E5E5E5;
		border-radius: 8px;
		font-size: 16px;
		background-color: #FFFFFF;
		box-sizing: border-box;
	}
	
	.form-input:focus {
		border-color: #007AFF;
	}
	
	.password-input-container {
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.password-toggle {
		position: absolute;
		right: 15px;
		color: #007AFF;
		font-size: 14px;
		cursor: pointer;
	}
	
	.error-text {
		display: block;
		color: #FF3B30;
		font-size: 12px;
		margin-top: 5px;
	}
	
	.password-tips {
		background-color: #F2F2F7;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 30px;
	}
	
	.tips-title {
		font-size: 14px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 8px;
	}
	
	.tips-item {
		font-size: 12px;
		color: #8E8E93;
		display: block;
		margin-bottom: 4px;
		line-height: 1.4;
	}
	
	.change-btn {
		width: 100%;
		height: 50px;
		background-color: #007AFF;
		color: #FFFFFF;
		border: none;
		border-radius: 25px;
		font-size: 18px;
		font-weight: 600;
	}
	
	.change-btn.disabled {
		background-color: #C7C7CC;
		color: #8E8E93;
	}
</style>