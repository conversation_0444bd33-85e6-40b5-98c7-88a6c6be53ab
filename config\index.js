/**
 * 应用配置文件
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
}

// 当前环境
const currentEnv = process.env.NODE_ENV || ENV.DEVELOPMENT

// 基础配置
const baseConfig = {
  // 应用信息
  app: {
    name: '健康报告管理',
    version: '1.0.0',
    description: '个人健康检查报告管理应用'
  },
  
  // 存储配置
  storage: {
    prefix: 'heath_report_',
    encryptionKey: 'heath_report_2024_key',
    maxSize: 50 * 1024 * 1024 // 50MB
  },
  
  // 同步配置
  sync: {
    defaultInterval: 30, // 分钟
    maxRetries: 3,
    timeout: 30000 // 30秒
  },
  
  // OCR配置
  ocr: {
    maxImageSize: 5 * 1024 * 1024, // 5MB
    supportedFormats: ['jpg', 'jpeg', 'png'],
    quality: 0.8,
    timeout: 15000 // 15秒
  },
  
  // 图片配置
  image: {
    maxSize: 10 * 1024 * 1024, // 10MB
    maxWidth: 1920,
    maxHeight: 1920,
    quality: 0.8,
    formats: ['jpg', 'jpeg', 'png', 'webp']
  },
  
  // 分页配置
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100
  },
  
  // 缓存配置
  cache: {
    maxAge: 24 * 60 * 60 * 1000, // 24小时
    maxSize: 100 // 最大缓存项数
  }
}

// 开发环境配置
const developmentConfig = {
  ...baseConfig,
  api: {
    baseURL: 'https://dev-api.heath-report.com',
    timeout: 10000,
    enableMock: true
  },
  log: {
    level: 'debug',
    enableConsole: true,
    enableStorage: true
  },
  debug: {
    enabled: true,
    showNetworkLog: true,
    showStateLog: true
  }
}

// 生产环境配置
const productionConfig = {
  ...baseConfig,
  api: {
    baseURL: 'https://api.heath-report.com',
    timeout: 15000,
    enableMock: false
  },
  log: {
    level: 'error',
    enableConsole: false,
    enableStorage: true
  },
  debug: {
    enabled: false,
    showNetworkLog: false,
    showStateLog: false
  }
}

// 测试环境配置
const testConfig = {
  ...baseConfig,
  api: {
    baseURL: 'https://test-api.heath-report.com',
    timeout: 10000,
    enableMock: true
  },
  log: {
    level: 'info',
    enableConsole: true,
    enableStorage: true
  },
  debug: {
    enabled: true,
    showNetworkLog: true,
    showStateLog: false
  }
}

// 根据环境选择配置
const configs = {
  [ENV.DEVELOPMENT]: developmentConfig,
  [ENV.PRODUCTION]: productionConfig,
  [ENV.TEST]: testConfig
}

const config = configs[currentEnv] || developmentConfig

// 平台特定配置
const platformConfig = {
  // APP端配置
  'app-plus': {
    permissions: {
      camera: true,
      storage: true,
      network: true,
      location: false
    },
    features: {
      biometric: true,
      push: true,
      background: true
    }
  },
  
  // 微信小程序配置
  'mp-weixin': {
    permissions: {
      camera: true,
      storage: true,
      network: true,
      location: false
    },
    features: {
      biometric: false,
      push: false,
      background: false
    },
    limits: {
      imageSize: 2 * 1024 * 1024, // 2MB
      storageSize: 10 * 1024 * 1024 // 10MB
    }
  },
  
  // H5配置
  'h5': {
    permissions: {
      camera: true,
      storage: true,
      network: true,
      location: false
    },
    features: {
      biometric: false,
      push: false,
      background: false
    }
  }
}

// 获取当前平台配置
function getPlatformConfig() {
  // #ifdef APP-PLUS
  return platformConfig['app-plus']
  // #endif
  
  // #ifdef MP-WEIXIN
  return platformConfig['mp-weixin']
  // #endif
  
  // #ifdef H5
  return platformConfig['h5']
  // #endif
  
  return platformConfig['app-plus'] // 默认配置
}

// 合并平台配置
const finalConfig = {
  ...config,
  platform: getPlatformConfig(),
  env: currentEnv,
  isDevelopment: currentEnv === ENV.DEVELOPMENT,
  isProduction: currentEnv === ENV.PRODUCTION,
  isTest: currentEnv === ENV.TEST
}

export default finalConfig