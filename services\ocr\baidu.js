/**
 * 百度OCR服务
 * 实现百度文字识别API的调用
 */

const BaseOCRService = require('./base.js');

class BaiduOCRService extends BaseOCRService {
  constructor(config = {}) {
    super(config);
    this.baseUrl = 'https://aip.baidubce.com';
    this.tokenUrl = `${this.baseUrl}/oauth/2.0/token`;
    this.ocrUrl = `${this.baseUrl}/rest/2.0/ocr/v1/general_basic`;
    this.accessToken = null;
    this.tokenExpireTime = 0;
  }

  /**
   * 获取访问令牌
   * @returns {Promise<string>} 访问令牌
   */
  async getAccessToken() {
    // 检查token是否还有效
    if (this.accessToken && Date.now() < this.tokenExpireTime) {
      return this.accessToken;
    }

    try {
      const response = await this.request(this.tokenUrl, {
        grant_type: 'client_credentials',
        client_id: this.apiKey,
        client_secret: this.secretKey
      });

      if (response.access_token) {
        this.accessToken = response.access_token;
        // token有效期通常是30天，这里设置为29天
        this.tokenExpireTime = Date.now() + (29 * 24 * 60 * 60 * 1000);
        return this.accessToken;
      } else {
        throw new Error(`获取访问令牌失败: ${response.error_description || '未知错误'}`);
      }
    } catch (error) {
      throw new Error(`获取百度OCR访问令牌失败: ${error.message}`);
    }
  }

  /**
   * 识别图片中的文字
   * @param {string} imagePath - 图片路径
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognize(imagePath, options = {}) {
    if (!this.validateConfig()) {
      throw new Error('百度OCR配置无效，请检查API Key和Secret Key');
    }

    try {
      return await this.retry(async () => {
        // 获取访问令牌
        const accessToken = await this.getAccessToken();
        
        // 将图片转换为Base64
        const imageBase64 = await this.imageToBase64(imagePath);
        
        // 构建请求参数
        const requestData = {
          image: imageBase64,
          language_type: options.language || 'CHN_ENG', // 中英文混合
          detect_direction: options.detectDirection || 'true', // 检测图像朝向
          detect_language: options.detectLanguage || 'true', // 检测语言
          probability: options.probability || 'false' // 是否返回识别结果中每一行的置信度
        };

        // 发送OCR请求
        const response = await this.request(
          `${this.ocrUrl}?access_token=${accessToken}`,
          requestData
        );

        return this.parseResponse(response);
      });
    } catch (error) {
      console.error('百度OCR识别失败:', error);
      return this.formatError(error);
    }
  }

  /**
   * 解析百度OCR响应
   * @param {Object} response - API响应
   * @returns {Object} 解析后的结果
   */
  parseResponse(response) {
    if (response.error_code) {
      const errorMessage = this.getErrorMessage(response.error_code);
      throw new Error(`百度OCR错误 ${response.error_code}: ${errorMessage}`);
    }

    if (!response.words_result || !Array.isArray(response.words_result)) {
      throw new Error('百度OCR返回数据格式异常');
    }

    // 提取文字内容
    const textLines = response.words_result.map(item => ({
      text: item.words || '',
      confidence: item.probability ? parseFloat(item.probability.average) : null,
      location: item.location || null
    }));

    // 合并所有文字
    const fullText = textLines.map(line => line.text).join('\n');

    return {
      success: true,
      data: {
        fullText: fullText,
        lines: textLines,
        wordsCount: response.words_result_num || 0,
        direction: response.direction || 0,
        language: response.language || 'unknown'
      },
      timestamp: new Date().toISOString(),
      provider: 'baidu'
    };
  }

  /**
   * 获取错误信息
   * @param {number} errorCode - 错误码
   * @returns {string} 错误信息
   */
  getErrorMessage(errorCode) {
    const errorMessages = {
      1: 'Unknown error',
      2: 'Service temporarily unavailable',
      3: 'Unsupported openapi method',
      4: 'Open api request limit reached',
      6: 'No permission to access data',
      14: 'IAM Certification failed',
      17: 'Open api daily request limit reached',
      18: 'Open api qps request limit reached',
      19: 'Open api total request limit reached',
      100: 'Invalid parameter',
      110: 'Access token invalid or no longer valid',
      111: 'Access token expired',
      216015: 'Module closed',
      216100: 'Invalid image',
      216101: 'Image not found',
      216102: 'Image size error',
      216103: 'Image pixel size error',
      216110: 'Image download timeout',
      216200: 'Image recognition error',
      216201: 'Image recognition timeout',
      216202: 'Image recognition failed',
      282000: 'Internal error',
      282003: 'Request parameter missing',
      282005: 'Image format error',
      282006: 'Image size error',
      282007: 'Image pixel size error'
    };

    return errorMessages[errorCode] || `未知错误码: ${errorCode}`;
  }

  /**
   * 高精度文字识别
   * @param {string} imagePath - 图片路径
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeAccurate(imagePath, options = {}) {
    const originalUrl = this.ocrUrl;
    this.ocrUrl = `${this.baseUrl}/rest/2.0/ocr/v1/accurate_basic`;
    
    try {
      return await this.recognize(imagePath, options);
    } finally {
      this.ocrUrl = originalUrl;
    }
  }

  /**
   * 表格文字识别
   * @param {string} imagePath - 图片路径
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeTable(imagePath, options = {}) {
    const originalUrl = this.ocrUrl;
    this.ocrUrl = `${this.baseUrl}/rest/2.0/ocr/v1/table`;
    
    try {
      const result = await this.recognize(imagePath, options);
      
      if (result.success && result.data) {
        // 解析表格结构
        result.data.tables = this.parseTableStructure(result.data.lines);
      }
      
      return result;
    } finally {
      this.ocrUrl = originalUrl;
    }
  }

  /**
   * 解析表格结构
   * @param {Array} lines - 文字行
   * @returns {Array} 表格数据
   */
  parseTableStructure(lines) {
    // 简单的表格解析逻辑
    // 实际项目中可能需要更复杂的表格识别算法
    const tables = [];
    let currentTable = [];
    
    for (const line of lines) {
      const text = line.text.trim();
      
      // 检测是否为表格行（包含多个数值或特定分隔符）
      if (this.isTableRow(text)) {
        const cells = this.parseTableRow(text);
        currentTable.push(cells);
      } else if (currentTable.length > 0) {
        // 表格结束
        tables.push(currentTable);
        currentTable = [];
      }
    }
    
    if (currentTable.length > 0) {
      tables.push(currentTable);
    }
    
    return tables;
  }

  /**
   * 判断是否为表格行
   * @param {string} text - 文字内容
   * @returns {boolean} 是否为表格行
   */
  isTableRow(text) {
    // 检测包含数字、单位、分隔符等特征
    const patterns = [
      /\d+\.?\d*\s*[a-zA-Z%]+/, // 数字+单位
      /\d+\.?\d*\s*[-~]\s*\d+\.?\d*/, // 数字范围
      /[\s\t]{2,}/, // 多个空格或制表符
      /[|｜\t]/ // 分隔符
    ];
    
    return patterns.some(pattern => pattern.test(text));
  }

  /**
   * 解析表格行
   * @param {string} text - 表格行文字
   * @returns {Array} 单元格数组
   */
  parseTableRow(text) {
    // 使用多种分隔符分割
    const separators = /[\s\t|｜]{2,}/;
    return text.split(separators)
      .map(cell => cell.trim())
      .filter(cell => cell.length > 0);
  }
}

module.exports = BaiduOCRService;