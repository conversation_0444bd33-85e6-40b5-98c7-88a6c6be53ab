/**
 * 基础Repository类
 * 提供数据访问层的基础实现
 */

const { IRepository } = require('../interfaces/IRepository.js')
const { Constants } = require('../../types/index.js')

class BaseRepository extends IRepository {
  constructor(storageManager, modelClass) {
    super()
    this.storageManager = storageManager
    this.modelClass = modelClass
    this.tableName = this.getTableName()
  }
  
  /**
   * 获取表名
   * 子类可以重写此方法来自定义表名
   * @returns {String} 表名
   */
  getTableName() {
    return this.modelClass.name.toLowerCase() + 's'
  }
  
  /**
   * 创建记录
   * @param {Object} data - 要创建的数据
   * @returns {Promise<Object>} 创建的记录
   */
  async create(data) {
    try {
      // 创建模型实例
      const model = new this.modelClass(data)
      
      // 验证数据
      const validation = model.validate()
      if (!validation.isValid) {
        throw new Error(`数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
      }
      
      // 保存到存储
      const savedData = await this.storageManager.insert(this.tableName, model.toJSON())
      
      return this.modelClass.fromJSON(savedData)
    } catch (error) {
      throw this.handleError(error, 'create')
    }
  }
  
  /**
   * 根据ID查找记录
   * @param {String} id - 记录ID
   * @returns {Promise<Object|null>} 找到的记录或null
   */
  async findById(id) {
    try {
      const data = await this.storageManager.findById(this.tableName, id)
      return data ? this.modelClass.fromJSON(data) : null
    } catch (error) {
      throw this.handleError(error, 'findById')
    }
  }
  
  /**
   * 查找所有记录
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 记录列表
   */
  async findAll(options = {}) {
    try {
      const dataList = await this.storageManager.findAll(this.tableName, options)
      return dataList.map(data => this.modelClass.fromJSON(data))
    } catch (error) {
      throw this.handleError(error, 'findAll')
    }
  }
  
  /**
   * 根据条件查找记录
   * @param {Object} conditions - 查询条件
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 匹配的记录列表
   */
  async findBy(conditions, options = {}) {
    try {
      const dataList = await this.storageManager.findBy(this.tableName, conditions, options)
      return dataList.map(data => this.modelClass.fromJSON(data))
    } catch (error) {
      throw this.handleError(error, 'findBy')
    }
  }
  
  /**
   * 更新记录
   * @param {String} id - 记录ID
   * @param {Object} data - 要更新的数据
   * @returns {Promise<Object>} 更新后的记录
   */
  async update(id, data) {
    try {
      // 先查找现有记录
      const existingData = await this.storageManager.findById(this.tableName, id)
      if (!existingData) {
        throw new Error(`记录不存在: ${id}`)
      }
      
      // 创建模型实例并更新
      const model = this.modelClass.fromJSON(existingData)
      model.update(data)
      
      // 验证更新后的数据
      const validation = model.validate()
      if (!validation.isValid) {
        throw new Error(`数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
      }
      
      // 保存更新
      const updatedData = await this.storageManager.update(this.tableName, id, model.toJSON())
      
      return this.modelClass.fromJSON(updatedData)
    } catch (error) {
      throw this.handleError(error, 'update')
    }
  }
  
  /**
   * 删除记录
   * @param {String} id - 记录ID
   * @returns {Promise<Boolean>} 删除是否成功
   */
  async delete(id) {
    try {
      return await this.storageManager.delete(this.tableName, id)
    } catch (error) {
      throw this.handleError(error, 'delete')
    }
  }
  
  /**
   * 统计记录数量
   * @param {Object} conditions - 统计条件
   * @returns {Promise<Number>} 记录数量
   */
  async count(conditions = {}) {
    try {
      return await this.storageManager.count(this.tableName, conditions)
    } catch (error) {
      throw this.handleError(error, 'count')
    }
  }
  
  /**
   * 批量操作
   * @param {Array} operations - 操作列表
   * @returns {Promise<Array>} 操作结果
   */
  async batch(operations) {
    try {
      const results = []
      
      // 开始事务
      await this.storageManager.beginTransaction()
      
      try {
        for (const operation of operations) {
          let result
          
          switch (operation.type) {
            case 'create':
              result = await this.create(operation.data)
              break
            case 'update':
              result = await this.update(operation.id, operation.data)
              break
            case 'delete':
              result = await this.delete(operation.id)
              break
            default:
              throw new Error(`不支持的操作类型: ${operation.type}`)
          }
          
          results.push(result)
        }
        
        // 提交事务
        await this.storageManager.commitTransaction()
        
        return results
      } catch (error) {
        // 回滚事务
        await this.storageManager.rollbackTransaction()
        throw error
      }
    } catch (error) {
      throw this.handleError(error, 'batch')
    }
  }
  
  /**
   * 处理错误
   * @param {Error} error - 原始错误
   * @param {String} operation - 操作名称
   * @returns {Error} 处理后的错误
   */
  handleError(error, operation) {
    const errorMessage = `${this.tableName} ${operation} 操作失败: ${error.message}`
    
    // 根据错误类型创建特定的错误
    if (error.name === 'ValidationError') {
      const validationError = new Error(errorMessage)
      validationError.type = Constants.ERROR_TYPES.VALIDATION_ERROR
      validationError.originalError = error
      return validationError
    }
    
    if (error.name === 'StorageError') {
      const storageError = new Error(errorMessage)
      storageError.type = Constants.ERROR_TYPES.STORAGE_ERROR
      storageError.originalError = error
      return storageError
    }
    
    // 默认错误
    const unknownError = new Error(errorMessage)
    unknownError.type = Constants.ERROR_TYPES.UNKNOWN_ERROR
    unknownError.originalError = error
    return unknownError
  }
  
  /**
   * 清理过期数据
   * @param {Number} daysToKeep - 保留天数
   * @returns {Promise<Number>} 清理的记录数量
   */
  async cleanupExpiredData(daysToKeep = 30) {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
      
      const conditions = {
        createdAt: { $lt: cutoffDate }
      }
      
      const expiredRecords = await this.findBy(conditions)
      let deletedCount = 0
      
      for (const record of expiredRecords) {
        await this.delete(record.id)
        deletedCount++
      }
      
      return deletedCount
    } catch (error) {
      throw this.handleError(error, 'cleanupExpiredData')
    }
  }
  
  /**
   * 导出数据
   * @param {Object} conditions - 导出条件
   * @param {String} format - 导出格式
   * @returns {Promise<String>} 导出的数据
   */
  async exportData(conditions = {}, format = 'json') {
    try {
      const records = await this.findBy(conditions)
      
      switch (format.toLowerCase()) {
        case 'json':
          return JSON.stringify(records.map(r => r.toJSON()), null, 2)
        case 'csv':
          return this.convertToCSV(records)
        default:
          throw new Error(`不支持的导出格式: ${format}`)
      }
    } catch (error) {
      throw this.handleError(error, 'exportData')
    }
  }
  
  /**
   * 转换为CSV格式
   * @param {Array} records - 记录列表
   * @returns {String} CSV字符串
   */
  convertToCSV(records) {
    if (records.length === 0) return ''
    
    const headers = Object.keys(records[0].toJSON())
    const csvRows = [headers.join(',')]
    
    for (const record of records) {
      const values = headers.map(header => {
        const value = record[header]
        return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
      })
      csvRows.push(values.join(','))
    }
    
    return csvRows.join('\n')
  }
}

module.exports = { BaseRepository }