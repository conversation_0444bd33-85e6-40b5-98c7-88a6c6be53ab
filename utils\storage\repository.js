/**
 * 基础CRUD操作封装类
 * 提供统一的数据访问接口
 */

import { getDatabase } from './database.js';
import { getEncryption } from './encryption.js';

/**
 * 基础仓储类
 */
class BaseRepository {
  constructor(tableName) {
    this.tableName = tableName;
    this.db = getDatabase();
    this.encryption = getEncryption();
  }

  /**
   * 确保数据库已初始化
   */
  async ensureInitialized() {
    if (!this.db.isInitialized) {
      await this.db.initialize();
    }
  }

  /**
   * 创建记录
   * @param {Object} data 
   * @returns {Promise<Object>}
   */
  async create(data) {
    await this.ensureInitialized();
    return await this.db.insert(this.tableName, data);
  }

  /**
   * 根据ID查找记录
   * @param {number} id 
   * @returns {Promise<Object|null>}
   */
  async findById(id) {
    await this.ensureInitialized();
    const results = this.db.query(this.tableName, { id });
    return results.length > 0 ? results[0] : null;
  }

  /**
   * 查找所有记录
   * @param {Object} conditions 查询条件
   * @param {Object} options 查询选项
   * @returns {Promise<Array>}
   */
  async findAll(conditions = {}, options = {}) {
    await this.ensureInitialized();
    return this.db.query(this.tableName, conditions, options);
  }

  /**
   * 查找单条记录
   * @param {Object} conditions 
   * @returns {Promise<Object|null>}
   */
  async findOne(conditions) {
    await this.ensureInitialized();
    const results = this.db.query(this.tableName, conditions, { limit: 1 });
    return results.length > 0 ? results[0] : null;
  }

  /**
   * 更新记录
   * @param {number} id 
   * @param {Object} updateData 
   * @returns {Promise<Object|null>}
   */
  async update(id, updateData) {
    await this.ensureInitialized();
    const updatedCount = await this.db.update(this.tableName, { id }, updateData);
    return updatedCount > 0 ? await this.findById(id) : null;
  }

  /**
   * 批量更新
   * @param {Object} conditions 
   * @param {Object} updateData 
   * @returns {Promise<number>}
   */
  async updateMany(conditions, updateData) {
    await this.ensureInitialized();
    return await this.db.update(this.tableName, conditions, updateData);
  }

  /**
   * 删除记录
   * @param {number} id 
   * @returns {Promise<boolean>}
   */
  async delete(id) {
    await this.ensureInitialized();
    const deletedCount = await this.db.delete(this.tableName, { id });
    return deletedCount > 0;
  }

  /**
   * 批量删除
   * @param {Object} conditions 
   * @returns {Promise<number>}
   */
  async deleteMany(conditions) {
    await this.ensureInitialized();
    return await this.db.delete(this.tableName, conditions);
  }

  /**
   * 统计记录数
   * @param {Object} conditions 
   * @returns {Promise<number>}
   */
  async count(conditions = {}) {
    await this.ensureInitialized();
    const results = this.db.query(this.tableName, conditions);
    return results.length;
  }

  /**
   * 检查记录是否存在
   * @param {Object} conditions 
   * @returns {Promise<boolean>}
   */
  async exists(conditions) {
    const count = await this.count(conditions);
    return count > 0;
  }

  /**
   * 分页查询
   * @param {number} page 页码（从1开始）
   * @param {number} pageSize 每页大小
   * @param {Object} conditions 查询条件
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async paginate(page = 1, pageSize = 10, conditions = {}, options = {}) {
    await this.ensureInitialized();
    
    const offset = (page - 1) * pageSize;
    const paginationOptions = {
      ...options,
      limit: pageSize,
      offset
    };
    
    const data = this.db.query(this.tableName, conditions, paginationOptions);
    const total = await this.count(conditions);
    const totalPages = Math.ceil(total / pageSize);
    
    return {
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }
}

/**
 * 用户仓储类
 */
class UserRepository extends BaseRepository {
  constructor() {
    super('users');
  }

  /**
   * 根据用户名查找用户
   * @param {string} username 
   * @returns {Promise<Object|null>}
   */
  async findByUsername(username) {
    return await this.findOne({ username });
  }

  /**
   * 根据手机号查找用户
   * @param {string} phone 
   * @returns {Promise<Object|null>}
   */
  async findByPhone(phone) {
    return await this.findOne({ phone });
  }

  /**
   * 根据邮箱查找用户
   * @param {string} email 
   * @returns {Promise<Object|null>}
   */
  async findByEmail(email) {
    return await this.findOne({ email });
  }

  /**
   * 创建用户（包含密码加密）
   * @param {Object} userData 
   * @returns {Promise<Object>}
   */
  async createUser(userData) {
    const salt = this.encryption.generateSalt();
    const passwordHash = await this.encryption.hashPassword(userData.password, salt);
    
    const user = await this.create({
      ...userData,
      password_hash: passwordHash,
      salt,
      password: undefined // 移除明文密码
    });
    
    // 返回时不包含敏感信息
    const { password_hash, salt: userSalt, ...safeUser } = user;
    return safeUser;
  }

  /**
   * 验证用户密码
   * @param {string} username 
   * @param {string} password 
   * @returns {Promise<Object|null>}
   */
  async validatePassword(username, password) {
    const user = await this.findByUsername(username);
    if (!user) {
      return null;
    }

    const hashedPassword = await this.encryption.hashPassword(password, user.salt);
    if (hashedPassword === user.password_hash) {
      // 更新最后登录时间
      await this.update(user.id, { last_login_at: new Date().toISOString() });
      
      // 返回安全的用户信息
      const { password_hash, salt, ...safeUser } = user;
      return safeUser;
    }

    return null;
  }

  /**
   * 更新用户密码
   * @param {number} userId 
   * @param {string} newPassword 
   * @returns {Promise<boolean>}
   */
  async updatePassword(userId, newPassword) {
    const salt = this.encryption.generateSalt();
    const passwordHash = await this.encryption.hashPassword(newPassword, salt);
    
    const result = await this.update(userId, {
      password_hash: passwordHash,
      salt
    });
    
    return result !== null;
  }
}

/**
 * 健康报告仓储类
 */
class HealthReportRepository extends BaseRepository {
  constructor() {
    super('health_reports');
  }

  /**
   * 根据用户ID查找报告
   * @param {number} userId 
   * @param {Object} options 
   * @returns {Promise<Array>}
   */
  async findByUserId(userId, options = {}) {
    const defaultOptions = {
      orderBy: 'report_date DESC',
      ...options
    };
    return await this.findAll({ user_id: userId }, defaultOptions);
  }

  /**
   * 根据日期范围查找报告
   * @param {number} userId 
   * @param {string} startDate 
   * @param {string} endDate 
   * @returns {Promise<Array>}
   */
  async findByDateRange(userId, startDate, endDate) {
    await this.ensureInitialized();
    return this.db.query(this.tableName, {
      user_id: userId,
      report_date: {
        $gte: startDate,
        $lte: endDate
      }
    }, { orderBy: 'report_date DESC' });
  }

  /**
   * 根据报告类型查找
   * @param {number} userId 
   * @param {string} reportType 
   * @returns {Promise<Array>}
   */
  async findByType(userId, reportType) {
    return await this.findAll({
      user_id: userId,
      report_type: reportType
    }, { orderBy: 'report_date DESC' });
  }

  /**
   * 创建加密报告
   * @param {Object} reportData 
   * @param {boolean} encrypt 是否加密敏感数据
   * @returns {Promise<Object>}
   */
  async createReport(reportData, encrypt = false) {
    let processedData = { ...reportData };
    
    if (encrypt && reportData.ocr_text) {
      const encryptionKey = this.encryption.generateKey();
      processedData.ocr_text = await this.encryption.encrypt(reportData.ocr_text, encryptionKey);
      processedData.is_encrypted = 1;
      // 在实际应用中，加密密钥应该安全存储
      processedData.encryption_key = encryptionKey;
    }
    
    return await this.create(processedData);
  }

  /**
   * 获取解密后的报告
   * @param {number} reportId 
   * @returns {Promise<Object|null>}
   */
  async getDecryptedReport(reportId) {
    const report = await this.findById(reportId);
    if (!report) {
      return null;
    }

    if (report.is_encrypted && report.ocr_text && report.encryption_key) {
      try {
        report.ocr_text = await this.encryption.decrypt(report.ocr_text, report.encryption_key);
      } catch (error) {
        console.error('解密报告失败:', error);
        report.ocr_text = '[解密失败]';
      }
    }

    // 移除加密密钥
    delete report.encryption_key;
    return report;
  }
}

/**
 * 健康指标仓储类
 */
class HealthIndicatorRepository extends BaseRepository {
  constructor() {
    super('health_indicators');
  }

  /**
   * 根据报告ID查找指标
   * @param {number} reportId 
   * @returns {Promise<Array>}
   */
  async findByReportId(reportId) {
    return await this.findAll({ report_id: reportId }, { orderBy: 'category ASC, indicator_name ASC' });
  }

  /**
   * 根据指标名称查找历史数据
   * @param {number} userId 
   * @param {string} indicatorName 
   * @returns {Promise<Array>}
   */
  async findHistoryByName(userId, indicatorName) {
    await this.ensureInitialized();
    
    // 需要关联查询health_reports表
    const reports = this.db.query('health_reports', { user_id: userId });
    const reportIds = reports.map(r => r.id);
    
    if (reportIds.length === 0) {
      return [];
    }

    const indicators = this.db.query(this.tableName, {
      report_id: { $in: reportIds },
      indicator_name: indicatorName
    });

    // 关联报告日期信息
    return indicators.map(indicator => {
      const report = reports.find(r => r.id === indicator.report_id);
      return {
        ...indicator,
        report_date: report ? report.report_date : null,
        hospital_name: report ? report.hospital_name : null
      };
    }).sort((a, b) => new Date(b.report_date) - new Date(a.report_date));
  }

  /**
   * 查找异常指标
   * @param {number} reportId 
   * @returns {Promise<Array>}
   */
  async findAbnormalIndicators(reportId) {
    return await this.findAll({
      report_id: reportId,
      is_abnormal: 1
    }, { orderBy: 'abnormal_level DESC' });
  }

  /**
   * 批量创建指标
   * @param {Array} indicators 
   * @returns {Promise<Array>}
   */
  async createBatch(indicators) {
    const results = [];
    for (const indicator of indicators) {
      const result = await this.create(indicator);
      results.push(result);
    }
    return results;
  }
}

/**
 * 同步记录仓储类
 */
class SyncRecordRepository extends BaseRepository {
  constructor() {
    super('sync_records');
  }

  /**
   * 创建同步记录
   * @param {number} userId 
   * @param {string} tableName 
   * @param {number} recordId 
   * @param {string} operationType 
   * @returns {Promise<Object>}
   */
  async createSyncRecord(userId, tableName, recordId, operationType) {
    return await this.create({
      user_id: userId,
      table_name: tableName,
      record_id: recordId,
      operation_type: operationType,
      sync_status: 0, // 待同步
      sync_attempts: 0
    });
  }

  /**
   * 获取待同步记录
   * @param {number} userId 
   * @returns {Promise<Array>}
   */
  async getPendingSyncRecords(userId) {
    return await this.findAll({
      user_id: userId,
      sync_status: 0
    }, { orderBy: 'created_at ASC' });
  }

  /**
   * 更新同步状态
   * @param {number} recordId 
   * @param {number} status 
   * @param {string} errorMessage 
   * @returns {Promise<Object|null>}
   */
  async updateSyncStatus(recordId, status, errorMessage = null) {
    const updateData = {
      sync_status: status,
      last_sync_at: new Date().toISOString()
    };

    if (status === 2) { // 同步失败
      updateData.error_message = errorMessage;
      // 增加重试次数
      const record = await this.findById(recordId);
      if (record) {
        updateData.sync_attempts = (record.sync_attempts || 0) + 1;
      }
    }

    return await this.update(recordId, updateData);
  }
}

// 导出仓储实例
export const userRepository = new UserRepository();
export const healthReportRepository = new HealthReportRepository();
export const healthIndicatorRepository = new HealthIndicatorRepository();
export const syncRecordRepository = new SyncRecordRepository();

export {
  BaseRepository,
  UserRepository,
  HealthReportRepository,
  HealthIndicatorRepository,
  SyncRecordRepository
};