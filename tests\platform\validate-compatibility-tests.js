/**
 * 跨平台兼容性测试验证脚本
 * 验证所有测试文件的语法和基本功能
 */

const fs = require('fs')
const path = require('path')

class CompatibilityTestValidator {
  constructor() {
    this.testFiles = [
      'cross-platform-compatibility.test.js',
      'data-sync-consistency.test.js', 
      'platform-degradation.test.js',
      'performance-optimization.test.js',
      'user-experience-consistency.test.js'
    ]
    
    this.validationResults = {
      totalFiles: 0,
      validFiles: 0,
      invalidFiles: 0,
      issues: []
    }
  }

  /**
   * 验证所有测试文件
   */
  async validateAllTests() {
    console.log('🔍 开始验证跨平台兼容性测试文件...\n')
    
    this.validationResults.totalFiles = this.testFiles.length

    for (const testFile of this.testFiles) {
      console.log(`📋 验证测试文件: ${testFile}`)
      
      try {
        const result = await this.validateTestFile(testFile)
        
        if (result.valid) {
          this.validationResults.validFiles++
          console.log(`✅ ${testFile} 验证通过`)
        } else {
          this.validationResults.invalidFiles++
          console.log(`❌ ${testFile} 验证失败`)
          this.validationResults.issues.push(...result.issues)
        }
        
      } catch (error) {
        console.error(`💥 ${testFile} 验证出错:`, error.message)
        this.validationResults.invalidFiles++
        this.validationResults.issues.push({
          file: testFile,
          type: 'error',
          message: error.message
        })
      }
      
      console.log('') // 空行分隔
    }

    this.printValidationSummary()
    return this.validationResults
  }

  /**
   * 验证单个测试文件
   * @param {string} testFile 测试文件名
   * @returns {Promise<Object>} 验证结果
   */
  async validateTestFile(testFile) {
    const testPath = path.join(__dirname, testFile)
    const result = {
      file: testFile,
      valid: true,
      issues: []
    }

    // 检查文件是否存在
    if (!fs.existsSync(testPath)) {
      result.valid = false
      result.issues.push({
        file: testFile,
        type: 'missing',
        message: '文件不存在'
      })
      return result
    }

    // 读取文件内容
    const content = fs.readFileSync(testPath, 'utf8')

    // 基本语法检查
    const syntaxIssues = this.checkSyntax(testFile, content)
    if (syntaxIssues.length > 0) {
      result.valid = false
      result.issues.push(...syntaxIssues)
    }

    // 测试结构检查
    const structureIssues = this.checkTestStructure(testFile, content)
    if (structureIssues.length > 0) {
      result.issues.push(...structureIssues)
    }

    // 导入检查
    const importIssues = this.checkImports(testFile, content)
    if (importIssues.length > 0) {
      result.issues.push(...importIssues)
    }

    // 平台覆盖检查
    const platformIssues = this.checkPlatformCoverage(testFile, content)
    if (platformIssues.length > 0) {
      result.issues.push(...platformIssues)
    }

    return result
  }

  /**
   * 检查基本语法
   * @param {string} testFile 文件名
   * @param {string} content 文件内容
   * @returns {Array} 问题列表
   */
  checkSyntax(testFile, content) {
    const issues = []

    // 检查基本的JavaScript语法错误
    try {
      // 简单的语法检查
      if (!content.includes('describe(') && !content.includes('test(')) {
        issues.push({
          file: testFile,
          type: 'syntax',
          message: '缺少测试用例定义'
        })
      }

      // 检查括号匹配
      const openBraces = (content.match(/\{/g) || []).length
      const closeBraces = (content.match(/\}/g) || []).length
      if (openBraces !== closeBraces) {
        issues.push({
          file: testFile,
          type: 'syntax',
          message: '括号不匹配'
        })
      }

      // 检查引号匹配
      const singleQuotes = (content.match(/'/g) || []).length
      const doubleQuotes = (content.match(/"/g) || []).length
      if (singleQuotes % 2 !== 0) {
        issues.push({
          file: testFile,
          type: 'syntax',
          message: '单引号不匹配'
        })
      }
      if (doubleQuotes % 2 !== 0) {
        issues.push({
          file: testFile,
          type: 'syntax',
          message: '双引号不匹配'
        })
      }

    } catch (error) {
      issues.push({
        file: testFile,
        type: 'syntax',
        message: `语法错误: ${error.message}`
      })
    }

    return issues
  }

  /**
   * 检查测试结构
   * @param {string} testFile 文件名
   * @param {string} content 文件内容
   * @returns {Array} 问题列表
   */
  checkTestStructure(testFile, content) {
    const issues = []

    // 检查是否有describe块
    const describeBlocks = content.match(/describe\s*\(/g) || []
    if (describeBlocks.length === 0) {
      issues.push({
        file: testFile,
        type: 'structure',
        message: '缺少describe测试块'
      })
    }

    // 检查是否有test或it块
    const testBlocks = content.match(/(test|it)\s*\(/g) || []
    if (testBlocks.length === 0) {
      issues.push({
        file: testFile,
        type: 'structure',
        message: '缺少test测试用例'
      })
    }

    // 检查beforeEach/afterEach
    const hasBeforeEach = content.includes('beforeEach(')
    const hasAfterEach = content.includes('afterEach(')
    
    if (!hasBeforeEach && testBlocks.length > 3) {
      issues.push({
        file: testFile,
        type: 'structure',
        message: '建议添加beforeEach进行测试初始化'
      })
    }

    return issues
  }

  /**
   * 检查导入语句
   * @param {string} testFile 文件名
   * @param {string} content 文件内容
   * @returns {Array} 问题列表
   */
  checkImports(testFile, content) {
    const issues = []

    // 检查导入语句
    const importStatements = content.match(/import\s+.*from\s+['"][^'"]+['"]/g) || []
    
    if (importStatements.length === 0) {
      issues.push({
        file: testFile,
        type: 'import',
        message: '缺少导入语句'
      })
    }

    // 检查是否导入了必要的模块
    const requiredImports = {
      'cross-platform-compatibility.test.js': ['PlatformAdapterClass', 'PLATFORM_TYPES'],
      'data-sync-consistency.test.js': ['syncService', 'conflictResolver'],
      'platform-degradation.test.js': ['PlatformAdapterClass', 'PLATFORM_TYPES'],
      'performance-optimization.test.js': ['PlatformAdapterClass'],
      'user-experience-consistency.test.js': ['PlatformAdapterClass']
    }

    const required = requiredImports[testFile] || []
    for (const module of required) {
      if (!content.includes(module)) {
        issues.push({
          file: testFile,
          type: 'import',
          message: `缺少必要的导入: ${module}`
        })
      }
    }

    return issues
  }

  /**
   * 检查平台覆盖情况
   * @param {string} testFile 文件名
   * @param {string} content 文件内容
   * @returns {Array} 问题列表
   */
  checkPlatformCoverage(testFile, content) {
    const issues = []

    const platforms = ['APP_PLUS', 'MP_WEIXIN', 'H5']
    const missingPlatforms = []

    for (const platform of platforms) {
      if (!content.includes(platform)) {
        missingPlatforms.push(platform)
      }
    }

    if (missingPlatforms.length > 0) {
      issues.push({
        file: testFile,
        type: 'coverage',
        message: `缺少平台测试覆盖: ${missingPlatforms.join(', ')}`
      })
    }

    return issues
  }

  /**
   * 打印验证总结
   */
  printValidationSummary() {
    console.log('📈 验证总结:')
    console.log('='.repeat(50))
    console.log(`总测试文件: ${this.validationResults.totalFiles}`)
    console.log(`有效文件: ${this.validationResults.validFiles}`)
    console.log(`无效文件: ${this.validationResults.invalidFiles}`)
    console.log(`验证通过率: ${(this.validationResults.validFiles / this.validationResults.totalFiles * 100).toFixed(2)}%`)

    if (this.validationResults.issues.length > 0) {
      console.log('\n⚠️  发现的问题:')
      
      const groupedIssues = this.groupIssuesByType()
      
      Object.entries(groupedIssues).forEach(([type, issues]) => {
        console.log(`\n${type.toUpperCase()} 问题 (${issues.length}个):`)
        issues.forEach(issue => {
          console.log(`  - ${issue.file}: ${issue.message}`)
        })
      })
    }

    console.log('\n✨ 验证完成!')
  }

  /**
   * 按类型分组问题
   * @returns {Object} 分组后的问题
   */
  groupIssuesByType() {
    const grouped = {}
    
    this.validationResults.issues.forEach(issue => {
      if (!grouped[issue.type]) {
        grouped[issue.type] = []
      }
      grouped[issue.type].push(issue)
    })

    return grouped
  }

  /**
   * 生成验证报告
   * @returns {string} 报告内容
   */
  generateValidationReport() {
    const report = `# 跨平台兼容性测试验证报告

## 验证概述
- 总测试文件: ${this.validationResults.totalFiles}
- 有效文件: ${this.validationResults.validFiles}
- 无效文件: ${this.validationResults.invalidFiles}
- 验证通过率: ${(this.validationResults.validFiles / this.validationResults.totalFiles * 100).toFixed(2)}%

## 测试文件状态

${this.testFiles.map(file => {
  const fileIssues = this.validationResults.issues.filter(issue => issue.file === file)
  const status = fileIssues.length === 0 ? '✅ 通过' : '❌ 有问题'
  return `- ${file}: ${status}`
}).join('\n')}

## 发现的问题

${this.validationResults.issues.length === 0 ? '无问题发现' : 
  Object.entries(this.groupIssuesByType()).map(([type, issues]) => 
    `### ${type.toUpperCase()} 问题\n${issues.map(issue => 
      `- **${issue.file}**: ${issue.message}`
    ).join('\n')}`
  ).join('\n\n')
}

## 建议

1. 修复所有语法错误
2. 完善测试结构和覆盖率
3. 确保所有必要的导入都存在
4. 验证平台覆盖的完整性

---
*报告生成时间: ${new Date().toLocaleString('zh-CN')}*
`

    return report
  }

  /**
   * 保存验证报告
   * @param {string} outputPath 输出路径
   */
  async saveValidationReport(outputPath = null) {
    const defaultPath = outputPath || path.join(__dirname, 'validation-report.md')
    const report = this.generateValidationReport()

    try {
      fs.writeFileSync(defaultPath, report, 'utf8')
      console.log(`📊 验证报告已保存到: ${defaultPath}`)
      return defaultPath
    } catch (error) {
      console.error('保存验证报告失败:', error)
      throw error
    }
  }
}

// 主执行函数
async function main() {
  const validator = new CompatibilityTestValidator()
  
  try {
    const results = await validator.validateAllTests()
    await validator.saveValidationReport()
    
    // 根据验证结果设置退出码
    process.exit(results.invalidFiles > 0 ? 1 : 0)
    
  } catch (error) {
    console.error('💥 验证执行失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main()
}

module.exports = CompatibilityTestValidator