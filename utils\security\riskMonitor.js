/**
 * 数据泄露风险监测和通知系统
 */

import securityManager from './encryption.js'
import loginSecurityManager from './loginSecurity.js'

class RiskMonitorManager {
  constructor() {
    this.riskLevels = {
      LOW: 'low',
      MEDIUM: 'medium',
      HIGH: 'high',
      CRITICAL: 'critical'
    }
    
    this.monitoringEnabled = true
    this.checkInterval = 60 * 60 * 1000 // 1小时检查一次
    this.lastCheckTime = 0
  }

  /**
   * 启动风险监控
   */
  startMonitoring() {
    if (!this.monitoringEnabled) return
    
    console.log('启动数据泄露风险监控')
    
    // 立即执行一次检查
    this.performRiskCheck()
    
    // 设置定期检查
    this.monitoringTimer = setInterval(() => {
      this.performRiskCheck()
    }, this.checkInterval)
    
    // 监听应用状态变化
    this.setupAppStateListeners()
  }

  /**
   * 停止风险监控
   */
  stopMonitoring() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer)
      this.monitoringTimer = null
    }
    console.log('数据泄露风险监控已停止')
  }

  /**
   * 执行风险检查
   */
  async performRiskCheck() {
    try {
      console.log('执行数据泄露风险检查...')
      this.lastCheckTime = Date.now()
      
      const risks = []
      
      // 检查数据完整性
      const integrityRisk = await this.checkDataIntegrity()
      if (integrityRisk) risks.push(integrityRisk)
      
      // 检查异常访问
      const accessRisk = await this.checkAbnormalAccess()
      if (accessRisk) risks.push(accessRisk)
      
      // 检查设备安全
      const deviceRisk = await this.checkDeviceSecurity()
      if (deviceRisk) risks.push(deviceRisk)
      
      // 检查网络安全
      const networkRisk = await this.checkNetworkSecurity()
      if (networkRisk) risks.push(networkRisk)
      
      // 检查应用权限
      const permissionRisk = await this.checkAppPermissions()
      if (permissionRisk) risks.push(permissionRisk)
      
      // 检查数据存储安全
      const storageRisk = await this.checkStorageSecurity()
      if (storageRisk) risks.push(storageRisk)
      
      // 处理检测到的风险
      if (risks.length > 0) {
        await this.handleDetectedRisks(risks)
      }
      
      // 记录检查结果
      this.recordRiskCheckResult(risks)
      
      console.log(`风险检查完成，发现 ${risks.length} 个风险`)
      
    } catch (error) {
      console.error('风险检查失败:', error)
      this.recordRiskCheckError(error)
    }
  }

  /**
   * 检查数据完整性
   */
  async checkDataIntegrity() {
    try {
      // 检查关键数据是否被篡改
      const criticalData = [
        'user_info',
        'health_reports',
        'app_settings'
      ]
      
      for (const dataKey of criticalData) {
        const data = uni.getStorageSync(dataKey)
        if (data) {
          // 检查数据结构是否完整
          if (this.isDataCorrupted(data)) {
            return {
              type: 'data_integrity',
              level: this.riskLevels.HIGH,
              description: `检测到数据 ${dataKey} 可能已被篡改`,
              affectedData: dataKey,
              timestamp: Date.now()
            }
          }
        }
      }
      
      return null
    } catch (error) {
      console.error('数据完整性检查失败:', error)
      return {
        type: 'data_integrity',
        level: this.riskLevels.MEDIUM,
        description: '数据完整性检查异常',
        error: error.message,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 检查异常访问
   */
  async checkAbnormalAccess() {
    try {
      const currentUser = uni.getStorageSync('current_user')
      if (!currentUser) return null
      
      // 获取最近的登录记录
      const loginAttempts = loginSecurityManager.getLoginAttempts(currentUser.phone)
      const recentAttempts = loginAttempts.filter(attempt => 
        Date.now() - attempt.timestamp < 24 * 60 * 60 * 1000 // 最近24小时
      )
      
      // 检查是否有异常登录模式
      if (recentAttempts.length > 10) {
        return {
          type: 'abnormal_access',
          level: this.riskLevels.HIGH,
          description: '检测到异常高频访问',
          details: {
            attemptCount: recentAttempts.length,
            timeWindow: '24小时'
          },
          timestamp: Date.now()
        }
      }
      
      // 检查多设备同时访问
      const deviceFingerprints = new Set(recentAttempts.map(a => a.deviceInfo.fingerprint))
      if (deviceFingerprints.size > 3) {
        return {
          type: 'abnormal_access',
          level: this.riskLevels.MEDIUM,
          description: '检测到多设备同时访问',
          details: {
            deviceCount: deviceFingerprints.size
          },
          timestamp: Date.now()
        }
      }
      
      return null
    } catch (error) {
      console.error('异常访问检查失败:', error)
      return null
    }
  }

  /**
   * 检查设备安全
   */
  async checkDeviceSecurity() {
    try {
      const systemInfo = uni.getSystemInfoSync()
      const risks = []
      
      // 检查设备是否已越狱/root
      if (this.isDeviceCompromised(systemInfo)) {
        risks.push({
          type: 'device_security',
          level: this.riskLevels.CRITICAL,
          description: '检测到设备可能已被越狱或root',
          timestamp: Date.now()
        })
      }
      
      // 检查系统版本是否过旧
      if (this.isSystemOutdated(systemInfo)) {
        risks.push({
          type: 'device_security',
          level: this.riskLevels.MEDIUM,
          description: '设备系统版本过旧，存在安全风险',
          details: {
            currentVersion: systemInfo.system,
            platform: systemInfo.platform
          },
          timestamp: Date.now()
        })
      }
      
      return risks.length > 0 ? risks[0] : null
    } catch (error) {
      console.error('设备安全检查失败:', error)
      return null
    }
  }

  /**
   * 检查网络安全
   */
  async checkNetworkSecurity() {
    try {
      // 检查网络连接类型
      const networkInfo = await uni.getNetworkType()
      
      // 检查是否使用不安全的网络
      if (networkInfo.networkType === 'wifi') {
        // 这里应该检查WiFi网络的安全性
        // 由于uni-app限制，这里只是模拟检查
        const isUnsafeWifi = Math.random() < 0.1 // 10%概率检测到不安全WiFi
        
        if (isUnsafeWifi) {
          return {
            type: 'network_security',
            level: this.riskLevels.MEDIUM,
            description: '检测到可能不安全的WiFi网络',
            recommendation: '建议使用移动数据或安全的WiFi网络',
            timestamp: Date.now()
          }
        }
      }
      
      return null
    } catch (error) {
      console.error('网络安全检查失败:', error)
      return null
    }
  }

  /**
   * 检查应用权限
   */
  async checkAppPermissions() {
    try {
      // 检查是否有异常的权限请求
      // 这里应该检查应用权限的使用情况
      
      // 模拟权限检查
      const hasExcessivePermissions = false // 实际应该检查权限使用
      
      if (hasExcessivePermissions) {
        return {
          type: 'app_permissions',
          level: this.riskLevels.MEDIUM,
          description: '检测到应用权限使用异常',
          timestamp: Date.now()
        }
      }
      
      return null
    } catch (error) {
      console.error('应用权限检查失败:', error)
      return null
    }
  }

  /**
   * 检查存储安全
   */
  async checkStorageSecurity() {
    try {
      // 检查是否有未加密的敏感数据
      const storageInfo = uni.getStorageInfoSync()
      const allKeys = storageInfo.keys
      
      const sensitiveKeys = ['user_info', 'health_reports', 'login_token']
      const unencryptedSensitiveData = []
      
      for (const key of allKeys) {
        if (sensitiveKeys.some(sensitive => key.includes(sensitive))) {
          if (!key.startsWith('secure_')) {
            unencryptedSensitiveData.push(key)
          }
        }
      }
      
      if (unencryptedSensitiveData.length > 0) {
        return {
          type: 'storage_security',
          level: this.riskLevels.HIGH,
          description: '检测到未加密的敏感数据',
          details: {
            affectedKeys: unencryptedSensitiveData
          },
          timestamp: Date.now()
        }
      }
      
      return null
    } catch (error) {
      console.error('存储安全检查失败:', error)
      return null
    }
  }

  /**
   * 处理检测到的风险
   */
  async handleDetectedRisks(risks) {
    try {
      // 按风险级别排序
      risks.sort((a, b) => {
        const levelOrder = {
          [this.riskLevels.CRITICAL]: 4,
          [this.riskLevels.HIGH]: 3,
          [this.riskLevels.MEDIUM]: 2,
          [this.riskLevels.LOW]: 1
        }
        return levelOrder[b.level] - levelOrder[a.level]
      })
      
      // 处理最高级别的风险
      const criticalRisks = risks.filter(r => r.level === this.riskLevels.CRITICAL)
      const highRisks = risks.filter(r => r.level === this.riskLevels.HIGH)
      
      // 发送风险通知
      if (criticalRisks.length > 0) {
        await this.sendCriticalRiskAlert(criticalRisks)
      } else if (highRisks.length > 0) {
        await this.sendHighRiskAlert(highRisks)
      } else {
        await this.sendGeneralRiskAlert(risks)
      }
      
      // 自动采取保护措施
      await this.takeProtectiveMeasures(risks)
      
    } catch (error) {
      console.error('处理风险失败:', error)
    }
  }

  /**
   * 发送严重风险警报
   */
  async sendCriticalRiskAlert(risks) {
    const riskDescriptions = risks.map(r => r.description).join('、')
    
    const alert = {
      type: 'critical_risk',
      title: '严重安全风险警报',
      message: `检测到严重安全风险：${riskDescriptions}。建议立即采取保护措施。`,
      risks,
      timestamp: Date.now()
    }
    
    // 显示紧急通知
    uni.showModal({
      title: '严重安全风险',
      content: alert.message,
      showCancel: false,
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm) {
          // 跳转到安全中心
          uni.navigateTo({
            url: '/pages/security/center'
          })
        }
      }
    })
    
    // 记录警报
    this.recordSecurityAlert(alert)
  }

  /**
   * 发送高风险警报
   */
  async sendHighRiskAlert(risks) {
    const riskDescriptions = risks.map(r => r.description).join('、')
    
    const alert = {
      type: 'high_risk',
      title: '安全风险提醒',
      message: `检测到安全风险：${riskDescriptions}。建议及时处理。`,
      risks,
      timestamp: Date.now()
    }
    
    // 显示提醒通知
    uni.showToast({
      title: '检测到安全风险',
      icon: 'none',
      duration: 3000
    })
    
    // 记录警报
    this.recordSecurityAlert(alert)
  }

  /**
   * 发送一般风险警报
   */
  async sendGeneralRiskAlert(risks) {
    const alert = {
      type: 'general_risk',
      title: '安全提醒',
      message: `检测到 ${risks.length} 个安全风险，建议查看安全中心。`,
      risks,
      timestamp: Date.now()
    }
    
    // 记录警报（不显示通知，避免打扰用户）
    this.recordSecurityAlert(alert)
  }

  /**
   * 采取保护措施
   */
  async takeProtectiveMeasures(risks) {
    try {
      for (const risk of risks) {
        switch (risk.type) {
          case 'data_integrity':
            // 备份数据并尝试修复
            await this.backupAndRepairData(risk.affectedData)
            break
            
          case 'abnormal_access':
            // 临时锁定账户
            if (risk.level === this.riskLevels.CRITICAL) {
              await this.temporaryAccountLock()
            }
            break
            
          case 'storage_security':
            // 加密未加密的敏感数据
            await this.encryptSensitiveData(risk.details.affectedKeys)
            break
            
          case 'device_security':
            // 限制功能使用
            if (risk.level === this.riskLevels.CRITICAL) {
              await this.restrictAppFunctionality()
            }
            break
        }
      }
    } catch (error) {
      console.error('采取保护措施失败:', error)
    }
  }

  /**
   * 记录安全警报
   */
  recordSecurityAlert(alert) {
    try {
      let alerts = uni.getStorageSync('security_risk_alerts') || []
      alerts.unshift(alert)
      
      // 只保留最近100条警报
      if (alerts.length > 100) {
        alerts = alerts.slice(0, 100)
      }
      
      uni.setStorageSync('security_risk_alerts', alerts)
    } catch (error) {
      console.error('记录安全警报失败:', error)
    }
  }

  /**
   * 记录风险检查结果
   */
  recordRiskCheckResult(risks) {
    try {
      const result = {
        timestamp: Date.now(),
        riskCount: risks.length,
        risks: risks.map(r => ({
          type: r.type,
          level: r.level,
          description: r.description
        }))
      }
      
      let checkHistory = uni.getStorageSync('risk_check_history') || []
      checkHistory.unshift(result)
      
      // 只保留最近50次检查记录
      if (checkHistory.length > 50) {
        checkHistory = checkHistory.slice(0, 50)
      }
      
      uni.setStorageSync('risk_check_history', checkHistory)
    } catch (error) {
      console.error('记录风险检查结果失败:', error)
    }
  }

  /**
   * 记录风险检查错误
   */
  recordRiskCheckError(error) {
    try {
      const errorRecord = {
        timestamp: Date.now(),
        error: error.message,
        stack: error.stack
      }
      
      let errorHistory = uni.getStorageSync('risk_check_errors') || []
      errorHistory.unshift(errorRecord)
      
      // 只保留最近20条错误记录
      if (errorHistory.length > 20) {
        errorHistory = errorHistory.slice(0, 20)
      }
      
      uni.setStorageSync('risk_check_errors', errorHistory)
    } catch (err) {
      console.error('记录风险检查错误失败:', err)
    }
  }

  /**
   * 设置应用状态监听
   */
  setupAppStateListeners() {
    // 监听应用进入后台
    uni.onAppHide(() => {
      this.performRiskCheck()
    })
    
    // 监听应用进入前台
    uni.onAppShow(() => {
      // 检查是否需要执行风险检查
      if (Date.now() - this.lastCheckTime > this.checkInterval) {
        this.performRiskCheck()
      }
    })
  }

  /**
   * 辅助方法：检查数据是否损坏
   */
  isDataCorrupted(data) {
    try {
      // 简单的数据完整性检查
      if (typeof data !== 'object' || data === null) {
        return false
      }
      
      // 检查必要字段
      if (data.timestamp && typeof data.timestamp !== 'number') {
        return true
      }
      
      return false
    } catch (error) {
      return true
    }
  }

  /**
   * 辅助方法：检查设备是否被破解
   */
  isDeviceCompromised(systemInfo) {
    // 这里应该实现实际的设备安全检查
    // 由于uni-app限制，这里只是模拟
    return false
  }

  /**
   * 辅助方法：检查系统是否过旧
   */
  isSystemOutdated(systemInfo) {
    // 检查iOS和Android的最低安全版本
    if (systemInfo.platform === 'ios') {
      const version = parseFloat(systemInfo.system.replace('iOS ', ''))
      return version < 12.0 // iOS 12以下认为过旧
    } else if (systemInfo.platform === 'android') {
      const version = parseInt(systemInfo.system.replace('Android ', ''))
      return version < 8 // Android 8以下认为过旧
    }
    
    return false
  }

  /**
   * 辅助方法：备份和修复数据
   */
  async backupAndRepairData(dataKey) {
    try {
      const data = uni.getStorageSync(dataKey)
      if (data) {
        // 创建备份
        uni.setStorageSync(`backup_${dataKey}_${Date.now()}`, data)
        console.log(`数据 ${dataKey} 已备份`)
      }
    } catch (error) {
      console.error('备份数据失败:', error)
    }
  }

  /**
   * 辅助方法：临时锁定账户
   */
  async temporaryAccountLock() {
    try {
      const currentUser = uni.getStorageSync('current_user')
      if (currentUser) {
        loginSecurityManager.lockAccount(currentUser.phone, '检测到安全风险')
      }
    } catch (error) {
      console.error('临时锁定账户失败:', error)
    }
  }

  /**
   * 辅助方法：加密敏感数据
   */
  async encryptSensitiveData(keys) {
    try {
      for (const key of keys) {
        const data = uni.getStorageSync(key)
        if (data) {
          // 加密数据并重新存储
          securityManager.setSecureStorage(key, data)
          // 删除原始未加密数据
          uni.removeStorageSync(key)
          console.log(`数据 ${key} 已加密`)
        }
      }
    } catch (error) {
      console.error('加密敏感数据失败:', error)
    }
  }

  /**
   * 辅助方法：限制应用功能
   */
  async restrictAppFunctionality() {
    try {
      // 设置安全模式标记
      uni.setStorageSync('security_mode', {
        enabled: true,
        reason: '检测到设备安全风险',
        timestamp: Date.now()
      })
      
      console.log('已启用安全模式，部分功能将受限')
    } catch (error) {
      console.error('限制应用功能失败:', error)
    }
  }

  /**
   * 获取风险监控状态
   */
  getMonitoringStatus() {
    return {
      enabled: this.monitoringEnabled,
      lastCheckTime: this.lastCheckTime,
      checkInterval: this.checkInterval,
      isRunning: !!this.monitoringTimer
    }
  }

  /**
   * 获取风险报告
   */
  getRiskReport() {
    try {
      const alerts = uni.getStorageSync('security_risk_alerts') || []
      const checkHistory = uni.getStorageSync('risk_check_history') || []
      const errors = uni.getStorageSync('risk_check_errors') || []
      
      return {
        summary: {
          totalAlerts: alerts.length,
          criticalAlerts: alerts.filter(a => a.type === 'critical_risk').length,
          highRiskAlerts: alerts.filter(a => a.type === 'high_risk').length,
          lastCheckTime: this.lastCheckTime,
          totalChecks: checkHistory.length,
          errorCount: errors.length
        },
        recentAlerts: alerts.slice(0, 10),
        recentChecks: checkHistory.slice(0, 10),
        recentErrors: errors.slice(0, 5)
      }
    } catch (error) {
      console.error('获取风险报告失败:', error)
      return null
    }
  }
}

// 创建全局风险监控管理器实例
const riskMonitorManager = new RiskMonitorManager()

export default riskMonitorManager
export { RiskMonitorManager }