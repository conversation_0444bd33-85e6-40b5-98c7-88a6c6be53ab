/**
 * 网络错误处理器
 * 处理网络请求错误、重试机制和离线模式
 */
class NetworkErrorHandler {
  constructor() {
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
      retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', 'CONNECTION_FAILED']
    }
    
    this.offlineMode = false
    this.offlineQueue = []
    this.networkStatus = true
    
    this.initNetworkMonitoring()
  }

  /**
   * 初始化网络监控
   */
  initNetworkMonitoring() {
    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      this.networkStatus = res.isConnected
      
      if (res.isConnected && this.offlineMode) {
        this.handleNetworkReconnect()
      } else if (!res.isConnected) {
        this.handleNetworkDisconnect()
      }
    })

    // 获取初始网络状态
    uni.getNetworkType({
      success: (res) => {
        this.networkStatus = res.networkType !== 'none'
        if (!this.networkStatus) {
          this.handleNetworkDisconnect()
        }
      }
    })
  }

  /**
   * 处理网络断开
   */
  handleNetworkDisconnect() {
    this.offlineMode = true
    
    uni.showToast({
      title: '网络连接已断开',
      icon: 'none',
      duration: 3000
    })

    // 触发离线模式事件
    uni.$emit('networkOffline')
  }

  /**
   * 处理网络重连
   */
  async handleNetworkReconnect() {
    this.offlineMode = false
    
    uni.showToast({
      title: '网络连接已恢复',
      icon: 'success',
      duration: 2000
    })

    // 处理离线队列
    await this.processOfflineQueue()
    
    // 触发在线模式事件
    uni.$emit('networkOnline')
  }

  /**
   * 网络请求包装器
   */
  async request(options) {
    // 检查网络状态
    if (this.offlineMode) {
      return this.handleOfflineRequest(options)
    }

    let lastError = null
    let retryCount = 0

    while (retryCount <= this.retryConfig.maxRetries) {
      try {
        const response = await this.makeRequest(options)
        
        // 请求成功，重置重试计数
        if (retryCount > 0) {
          console.log(`请求重试成功，重试次数: ${retryCount}`)
        }
        
        return response
      } catch (error) {
        lastError = error
        
        // 检查是否需要重试
        if (!this.shouldRetry(error, retryCount)) {
          break
        }
        
        retryCount++
        
        // 计算延迟时间
        const delay = this.calculateRetryDelay(retryCount)
        
        console.log(`请求失败，${delay}ms后进行第${retryCount}次重试:`, error.message)
        
        // 等待重试
        await this.sleep(delay)
      }
    }

    // 所有重试都失败了
    throw this.createNetworkError(lastError, retryCount)
  }

  /**
   * 执行实际请求
   */
  async makeRequest(options) {
    return new Promise((resolve, reject) => {
      const requestOptions = {
        ...options,
        success: (response) => {
          if (response.statusCode >= 200 && response.statusCode < 300) {
            resolve(response)
          } else {
            reject(this.createHttpError(response))
          }
        },
        fail: (error) => {
          reject(this.createRequestError(error))
        }
      }

      uni.request(requestOptions)
    })
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error, retryCount) {
    // 超过最大重试次数
    if (retryCount >= this.retryConfig.maxRetries) {
      return false
    }

    // 检查错误类型
    if (this.retryConfig.retryableErrors.some(type => 
      error.message.includes(type) || error.code === type)) {
      return true
    }

    // 检查HTTP状态码
    if (error.statusCode && 
      this.retryConfig.retryableStatusCodes.includes(error.statusCode)) {
      return true
    }

    return false
  }

  /**
   * 计算重试延迟
   */
  calculateRetryDelay(retryCount) {
    return this.retryConfig.retryDelay * 
      Math.pow(this.retryConfig.backoffMultiplier, retryCount - 1)
  }

  /**
   * 处理离线请求
   */
  handleOfflineRequest(options) {
    return new Promise((resolve, reject) => {
      // 检查是否是可以离线处理的请求
      if (this.canHandleOffline(options)) {
        // 从缓存获取数据
        const cachedData = this.getCachedData(options)
        if (cachedData) {
          resolve(cachedData)
          return
        }
      }

      // 添加到离线队列
      this.offlineQueue.push({
        options,
        resolve,
        reject,
        timestamp: Date.now()
      })

      // 提示用户
      uni.showToast({
        title: '网络不可用，请求已加入队列',
        icon: 'none',
        duration: 2000
      })
    })
  }

  /**
   * 检查是否可以离线处理
   */
  canHandleOffline(options) {
    // GET请求可能有缓存
    return options.method === 'GET' || !options.method
  }

  /**
   * 获取缓存数据
   */
  getCachedData(options) {
    try {
      const cacheKey = this.generateCacheKey(options)
      const cached = uni.getStorageSync(`cache_${cacheKey}`)
      
      if (cached && cached.expiry > Date.now()) {
        return {
          data: cached.data,
          statusCode: 200,
          fromCache: true
        }
      }
    } catch (error) {
      console.error('获取缓存数据失败:', error)
    }
    
    return null
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(options) {
    const key = `${options.method || 'GET'}_${options.url}`
    if (options.data) {
      return `${key}_${JSON.stringify(options.data)}`
    }
    return key
  }

  /**
   * 处理离线队列
   */
  async processOfflineQueue() {
    if (this.offlineQueue.length === 0) {
      return
    }

    console.log(`开始处理离线队列，共${this.offlineQueue.length}个请求`)

    const queue = [...this.offlineQueue]
    this.offlineQueue = []

    for (const item of queue) {
      try {
        const response = await this.request(item.options)
        item.resolve(response)
      } catch (error) {
        // 检查请求是否过期（超过5分钟）
        if (Date.now() - item.timestamp > 5 * 60 * 1000) {
          item.reject(new Error('请求已过期'))
        } else {
          // 重新加入队列
          this.offlineQueue.push(item)
        }
      }
    }

    if (this.offlineQueue.length > 0) {
      console.log(`还有${this.offlineQueue.length}个请求未处理完成`)
    }
  }

  /**
   * 创建网络错误
   */
  createNetworkError(originalError, retryCount) {
    const error = new Error(`网络请求失败，已重试${retryCount}次`)
    error.name = 'NetworkError'
    error.code = 'NETWORK_ERROR'
    error.originalError = originalError
    error.retryCount = retryCount
    return error
  }

  /**
   * 创建HTTP错误
   */
  createHttpError(response) {
    const error = new Error(`HTTP错误: ${response.statusCode}`)
    error.name = 'HttpError'
    error.code = 'HTTP_ERROR'
    error.statusCode = response.statusCode
    error.response = response
    return error
  }

  /**
   * 创建请求错误
   */
  createRequestError(error) {
    const networkError = new Error(error.errMsg || '请求失败')
    networkError.name = 'RequestError'
    networkError.code = 'REQUEST_ERROR'
    networkError.originalError = error
    return networkError
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取网络状态
   */
  getNetworkStatus() {
    return {
      online: this.networkStatus,
      offlineMode: this.offlineMode,
      queueLength: this.offlineQueue.length
    }
  }

  /**
   * 清空离线队列
   */
  clearOfflineQueue() {
    this.offlineQueue.forEach(item => {
      item.reject(new Error('离线队列已清空'))
    })
    this.offlineQueue = []
  }

  /**
   * 设置重试配置
   */
  setRetryConfig(config) {
    this.retryConfig = { ...this.retryConfig, ...config }
  }
}

module.exports = NetworkErrorHandler