import { defineStore } from 'pinia'
import tokenService from '../services/auth/tokenService.js'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户信息
    userInfo: {
      id: '',
      phone: '',
      nickname: '',
      avatar: '',
      gender: '',
      birthday: null,
      height: null,
      weight: null,
      createdAt: null,
      updatedAt: null
    },
    
    // 用户设置
    userSettings: {
      biometricEnabled: false,
      autoSync: true,
      notificationEnabled: true,
      theme: 'light',
      language: 'zh-CN'
    },
    
    // 认证状态
    auth: {
      isLoggedIn: false,
      token: '',
      refreshToken: '',
      tokenExpiry: null,
      lastLoginTime: null,
      loginMethod: '' // password, code, biometric, wechat
    },
    
    // 微信小程序相关
    wechat: {
      openid: '',
      unionid: '',
      sessionKey: ''
    }
  }),
  
  getters: {
    // 是否已登录
    isAuthenticated: (state) => state.auth.isLoggedIn && state.auth.token && tokenService.isTokenValid(),
    
    // 是否需要刷新token
    needRefreshToken: (state) => {
      if (!state.auth.tokenExpiry) return false
      return Date.now() > state.auth.tokenExpiry - 5 * 60 * 1000 // 提前5分钟刷新
    },
    
    // 用户显示名称
    displayName: (state) => state.userInfo.nickname || state.userInfo.phone || '用户',
    
    // 是否绑定微信
    isWechatBound: (state) => !!state.wechat.openid,
    
    // 获取用户头像
    userAvatar: (state) => state.userInfo.avatar || '/static/default-avatar.png',
    
    // 获取登录方式显示名称
    loginMethodName: (state) => {
      const methods = {
        password: '密码登录',
        code: '验证码登录',
        biometric: '生物识别登录',
        wechat: '微信登录'
      }
      return methods[state.auth.loginMethod] || '未知'
    }
  },
  
  actions: {
    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = { ...this.userInfo, ...userInfo }
      // 持久化存储
      uni.setStorageSync('user_info', this.userInfo)
    },
    
    // 设置认证信息
    setAuth(authData) {
      this.auth = { ...this.auth, ...authData }
      
      // 如果有token信息，使用tokenService管理
      if (authData.token && authData.refreshToken && authData.tokenExpiry) {
        tokenService.saveTokens({
          token: authData.token,
          refreshToken: authData.refreshToken,
          tokenExpiry: authData.tokenExpiry
        })
      }
    },
    
    // 设置微信信息
    setWechatInfo(wechatData) {
      this.wechat = { ...this.wechat, ...wechatData }
      uni.setStorageSync('wechat_info', this.wechat)
    },
    
    // 更新用户设置
    updateUserSettings(settings) {
      this.userSettings = { ...this.userSettings, ...settings }
      uni.setStorageSync('user_settings', this.userSettings)
    },
    
    // 登录
    async login(loginData, loginMethod = 'password') {
      try {
        this.setAuth({
          isLoggedIn: true,
          token: loginData.token,
          refreshToken: loginData.refreshToken,
          tokenExpiry: loginData.tokenExpiry,
          lastLoginTime: Date.now(),
          loginMethod: loginMethod
        })
        
        this.setUserInfo(loginData.userInfo)
        
        // 如果有微信信息，保存它
        if (loginData.wechatInfo) {
          this.setWechatInfo(loginData.wechatInfo)
        }
        
        return { success: true }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, error: error.message }
      }
    },
    
    // 登出
    async logout() {
      try {
        // 清除状态
        this.auth = {
          isLoggedIn: false,
          token: '',
          refreshToken: '',
          tokenExpiry: null,
          lastLoginTime: null,
          loginMethod: ''
        }
        
        this.userInfo = {
          id: '',
          phone: '',
          nickname: '',
          avatar: '',
          gender: '',
          birthday: null,
          height: null,
          weight: null,
          createdAt: null,
          updatedAt: null
        }
        
        this.wechat = {
          openid: '',
          unionid: '',
          sessionKey: ''
        }
        
        // 使用tokenService清除token
        tokenService.clearTokens()
        
        // 清除其他本地存储
        uni.removeStorageSync('user_info')
        uni.removeStorageSync('user_settings')
        uni.removeStorageSync('wechat_info')
        
        return { success: true }
      } catch (error) {
        console.error('登出失败:', error)
        return { success: false, error: error.message }
      }
    },
    
    // 刷新token
    async refreshAuthToken() {
      try {
        const result = await tokenService.refreshToken()
        
        if (result.success) {
          // 更新状态中的token信息
          this.setAuth({
            token: result.token,
            tokenExpiry: tokenService.getTokenExpiry()
          })
          
          return { success: true }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('刷新token失败:', error)
        // token刷新失败，需要重新登录
        await this.logout()
        return { success: false, error: error.message }
      }
    },
    
    // 初始化用户状态
    async initUser() {
      try {
        // 从本地存储恢复用户信息
        const savedUserInfo = uni.getStorageSync('user_info')
        const savedUserSettings = uni.getStorageSync('user_settings')
        const savedWechatInfo = uni.getStorageSync('wechat_info')
        
        if (savedUserInfo) {
          this.setUserInfo(savedUserInfo)
        }
        
        if (savedUserSettings) {
          this.updateUserSettings(savedUserSettings)
        }
        
        if (savedWechatInfo) {
          this.setWechatInfo(savedWechatInfo)
        }
        
        // 检查token状态
        const token = tokenService.getToken()
        const refreshToken = tokenService.getRefreshToken()
        const tokenExpiry = tokenService.getTokenExpiry()
        
        if (token && refreshToken) {
          this.setAuth({
            isLoggedIn: true,
            token: token,
            refreshToken: refreshToken,
            tokenExpiry: tokenExpiry
          })
          
          // 检查token是否需要刷新
          if (this.needRefreshToken) {
            await this.refreshAuthToken()
          }
        }
        
      } catch (error) {
        console.error('用户状态初始化失败:', error)
        // 初始化失败时清除可能损坏的数据
        await this.logout()
      }
    },
    
    // 更新用户资料
    async updateProfile(profileData) {
      try {
        // 这里应该调用API更新用户资料
        // const result = await userService.updateProfile(profileData)
        
        // 模拟更新成功
        this.setUserInfo({
          ...this.userInfo,
          ...profileData,
          updatedAt: new Date().toISOString()
        })
        
        return { success: true }
      } catch (error) {
        console.error('更新用户资料失败:', error)
        return { success: false, error: error.message }
      }
    },
    
    // 启用生物识别
    async enableBiometric() {
      try {
        this.updateUserSettings({ biometricEnabled: true })
        return { success: true }
      } catch (error) {
        console.error('启用生物识别失败:', error)
        return { success: false, error: error.message }
      }
    },
    
    // 禁用生物识别
    async disableBiometric() {
      try {
        this.updateUserSettings({ biometricEnabled: false })
        return { success: true }
      } catch (error) {
        console.error('禁用生物识别失败:', error)
        return { success: false, error: error.message }
      }
    }
  }
})