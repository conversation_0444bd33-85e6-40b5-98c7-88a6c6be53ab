/**
 * OCR服务基类
 * 定义OCR服务的通用接口和方法
 */

class BaseOCRService {
  constructor(config = {}) {
    this.config = config;
    this.apiKey = config.apiKey || '';
    this.secretKey = config.secretKey || '';
    this.timeout = config.timeout || 30000; // 30秒超时
    this.retryCount = config.retryCount || 3;
  }

  /**
   * 识别图片中的文字
   * @param {string} imagePath - 图片路径
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognize(imagePath, options = {}) {
    throw new Error('recognize方法需要在子类中实现');
  }

  /**
   * 获取访问令牌
   * @returns {Promise<string>} 访问令牌
   */
  async getAccessToken() {
    throw new Error('getAccessToken方法需要在子类中实现');
  }

  /**
   * 将图片转换为Base64
   * @param {string} imagePath - 图片路径
   * @returns {Promise<string>} Base64字符串
   */
  async imageToBase64(imagePath) {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        
        const base64 = canvas.toDataURL('image/jpeg', 0.8);
        const base64Data = base64.split(',')[1];
        resolve(base64Data);
      };
      
      img.onerror = reject;
      img.src = imagePath;
      // #endif
      
      // #ifdef APP-PLUS
      plus.io.resolveLocalFileSystemURL(imagePath, (entry) => {
        entry.file((file) => {
          const reader = new plus.io.FileReader();
          reader.onloadend = (e) => {
            const base64 = e.target.result;
            const base64Data = base64.split(',')[1];
            resolve(base64Data);
          };
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
      }, reject);
      // #endif
      
      // #ifdef MP
      uni.getFileSystemManager().readFile({
        filePath: imagePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data);
        },
        fail: reject
      });
      // #endif
    });
  }

  /**
   * 发送HTTP请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} headers - 请求头
   * @returns {Promise<Object>} 响应结果
   */
  async request(url, data, headers = {}) {
    return new Promise((resolve, reject) => {
      uni.request({
        url: url,
        method: 'POST',
        data: data,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded',
          ...headers
        },
        timeout: this.timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
          }
        },
        fail: (error) => {
          reject(new Error(`请求失败: ${error.errMsg}`));
        }
      });
    });
  }

  /**
   * 重试机制
   * @param {Function} fn - 要重试的函数
   * @param {number} maxRetries - 最大重试次数
   * @returns {Promise} 执行结果
   */
  async retry(fn, maxRetries = this.retryCount) {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i < maxRetries) {
          // 指数退避策略
          const delay = Math.min(1000 * Math.pow(2, i), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
          console.warn(`OCR识别重试 ${i + 1}/${maxRetries}:`, error.message);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 验证配置
   * @returns {boolean} 配置是否有效
   */
  validateConfig() {
    return !!(this.apiKey && this.secretKey);
  }

  /**
   * 格式化错误信息
   * @param {Error} error - 错误对象
   * @returns {Object} 格式化的错误信息
   */
  formatError(error) {
    return {
      success: false,
      error: {
        code: error.code || 'UNKNOWN_ERROR',
        message: error.message || '未知错误',
        details: error.details || null
      },
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = BaseOCRService;