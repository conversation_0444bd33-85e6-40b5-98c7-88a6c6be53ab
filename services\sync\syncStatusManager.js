/**
 * 同步状态管理和进度显示
 * 管理同步过程的状态和进度信息
 */

import { useSyncStore } from '../../stores/sync.js';

class SyncStatusManager {
  constructor() {
    this.syncStore = null;
    this.progressCallbacks = new Set();
    this.statusCallbacks = new Set();
    this.currentProgress = {
      phase: '', // 'preparing', 'uploading', 'downloading', 'resolving', 'completed'
      current: 0,
      total: 0,
      percentage: 0,
      message: '',
      details: {}
    };
  }

  /**
   * 初始化状态管理器
   */
  initialize() {
    this.syncStore = useSyncStore();
  }

  /**
   * 开始同步进度跟踪
   * @param {Object} options 同步选项
   */
  startProgress(options = {}) {
    if (!this.syncStore) {
      this.initialize();
    }

    this.currentProgress = {
      phase: 'preparing',
      current: 0,
      total: 0,
      percentage: 0,
      message: '准备同步...',
      startTime: Date.now(),
      details: {
        userId: options.userId,
        syncType: options.syncType || 'full',
        background: options.background || false
      }
    };

    this.notifyProgressUpdate();
    this.updateSyncStatus({ isRunning: true });
  }

  /**
   * 更新同步进度
   * @param {Object} progress 进度信息
   */
  updateProgress(progress) {
    const {
      phase,
      current,
      total,
      message,
      details = {}
    } = progress;

    if (phase) this.currentProgress.phase = phase;
    if (current !== undefined) this.currentProgress.current = current;
    if (total !== undefined) this.currentProgress.total = total;
    if (message) this.currentProgress.message = message;

    // 计算百分比
    if (this.currentProgress.total > 0) {
      this.currentProgress.percentage = Math.round(
        (this.currentProgress.current / this.currentProgress.total) * 100
      );
    }

    // 更新详细信息
    this.currentProgress.details = {
      ...this.currentProgress.details,
      ...details
    };

    this.notifyProgressUpdate();
  }

  /**
   * 设置同步阶段
   * @param {string} phase 阶段名称
   * @param {string} message 阶段消息
   * @param {number} total 总数
   */
  setPhase(phase, message, total = 0) {
    this.updateProgress({
      phase,
      message,
      current: 0,
      total,
      details: {
        phaseStartTime: Date.now()
      }
    });
  }

  /**
   * 增加进度
   * @param {number} increment 增量
   * @param {string} message 消息
   */
  incrementProgress(increment = 1, message = '') {
    this.updateProgress({
      current: this.currentProgress.current + increment,
      message: message || this.currentProgress.message
    });
  }

  /**
   * 完成同步进度
   * @param {Object} result 同步结果
   */
  completeProgress(result) {
    const duration = Date.now() - this.currentProgress.startTime;
    
    this.currentProgress = {
      ...this.currentProgress,
      phase: 'completed',
      current: this.currentProgress.total,
      percentage: 100,
      message: result.success ? '同步完成' : '同步失败',
      endTime: Date.now(),
      duration,
      result
    };

    this.notifyProgressUpdate();
    this.updateSyncStatus({ 
      isRunning: false,
      lastSyncTime: Date.now()
    });

    // 清理进度信息（延迟清理，让UI有时间显示完成状态）
    setTimeout(() => {
      this.resetProgress();
    }, 3000);
  }

  /**
   * 重置进度信息
   */
  resetProgress() {
    this.currentProgress = {
      phase: '',
      current: 0,
      total: 0,
      percentage: 0,
      message: '',
      details: {}
    };
    this.notifyProgressUpdate();
  }

  /**
   * 获取当前进度
   * @returns {Object}
   */
  getCurrentProgress() {
    return { ...this.currentProgress };
  }

  /**
   * 更新同步状态
   * @param {Object} status 状态信息
   */
  updateSyncStatus(status) {
    if (!this.syncStore) {
      this.initialize();
    }

    this.syncStore.updateSyncStatus(status);
    this.notifyStatusUpdate();
  }

  /**
   * 获取同步状态
   * @returns {Object}
   */
  getSyncStatus() {
    if (!this.syncStore) {
      this.initialize();
    }

    return {
      ...this.syncStore.syncStatus,
      progress: this.currentProgress
    };
  }

  /**
   * 获取同步统计信息
   * @returns {Object}
   */
  getSyncStatistics() {
    if (!this.syncStore) {
      this.initialize();
    }

    const stats = this.syncStore.statistics;
    const recentHistory = this.syncStore.recentSyncHistory;

    return {
      ...stats,
      successRate: this.syncStore.syncSuccessRate,
      recentSyncs: recentHistory.slice(0, 5),
      averageDuration: this.calculateAverageDuration(recentHistory),
      lastSyncResult: recentHistory[0] || null
    };
  }

  /**
   * 计算平均同步时长
   * @param {Array} history 同步历史
   * @returns {number}
   */
  calculateAverageDuration(history) {
    const durationsWithDuration = history
      .filter(record => record.details && record.details.duration)
      .map(record => record.details.duration);

    if (durationsWithDuration.length === 0) {
      return 0;
    }

    const totalDuration = durationsWithDuration.reduce((sum, duration) => sum + duration, 0);
    return Math.round(totalDuration / durationsWithDuration.length);
  }

  /**
   * 获取同步健康状态
   * @returns {Object}
   */
  getSyncHealth() {
    if (!this.syncStore) {
      this.initialize();
    }

    const stats = this.syncStore.statistics;
    const recentHistory = this.syncStore.recentSyncHistory.slice(0, 10);
    
    // 计算健康指标
    const successRate = parseFloat(this.syncStore.syncSuccessRate);
    const recentFailures = recentHistory.filter(r => r.status === 'failed').length;
    const hasRecentConflicts = this.syncStore.conflicts.some(c => 
      !c.resolved && (Date.now() - c.timestamp) < 24 * 60 * 60 * 1000
    );

    let healthStatus = 'good';
    let healthMessage = '同步状态良好';
    const issues = [];

    // 评估健康状态
    if (successRate < 80) {
      healthStatus = 'poor';
      healthMessage = '同步成功率较低';
      issues.push('同步成功率低于80%');
    } else if (successRate < 95) {
      healthStatus = 'fair';
      healthMessage = '同步状态一般';
      issues.push('同步成功率低于95%');
    }

    if (recentFailures >= 3) {
      healthStatus = 'poor';
      healthMessage = '最近同步失败较多';
      issues.push('最近10次同步中有3次或更多失败');
    }

    if (hasRecentConflicts) {
      if (healthStatus === 'good') {
        healthStatus = 'fair';
      }
      issues.push('存在未解决的数据冲突');
    }

    const lastSyncTime = this.syncStore.syncStatus.lastSyncTime;
    if (lastSyncTime && (Date.now() - lastSyncTime) > 24 * 60 * 60 * 1000) {
      if (healthStatus === 'good') {
        healthStatus = 'fair';
      }
      issues.push('超过24小时未同步');
    }

    return {
      status: healthStatus,
      message: healthMessage,
      issues,
      metrics: {
        successRate,
        recentFailures,
        conflictCount: this.syncStore.conflicts.filter(c => !c.resolved).length,
        lastSyncTime
      }
    };
  }

  /**
   * 注册进度更新回调
   * @param {Function} callback 回调函数
   */
  onProgressUpdate(callback) {
    this.progressCallbacks.add(callback);
    
    // 返回取消注册的函数
    return () => {
      this.progressCallbacks.delete(callback);
    };
  }

  /**
   * 注册状态更新回调
   * @param {Function} callback 回调函数
   */
  onStatusUpdate(callback) {
    this.statusCallbacks.add(callback);
    
    // 返回取消注册的函数
    return () => {
      this.statusCallbacks.delete(callback);
    };
  }

  /**
   * 通知进度更新
   */
  notifyProgressUpdate() {
    const progress = this.getCurrentProgress();
    this.progressCallbacks.forEach(callback => {
      try {
        callback(progress);
      } catch (error) {
        console.error('进度回调执行失败:', error);
      }
    });
  }

  /**
   * 通知状态更新
   */
  notifyStatusUpdate() {
    const status = this.getSyncStatus();
    this.statusCallbacks.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('状态回调执行失败:', error);
      }
    });
  }

  /**
   * 格式化同步时间
   * @param {number} timestamp 时间戳
   * @returns {string}
   */
  formatSyncTime(timestamp) {
    if (!timestamp) return '从未同步';

    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    
    return new Date(timestamp).toLocaleDateString();
  }

  /**
   * 格式化同步持续时间
   * @param {number} duration 持续时间（毫秒）
   * @returns {string}
   */
  formatDuration(duration) {
    if (!duration) return '未知';

    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);

    if (seconds < 60) return `${seconds}秒`;
    return `${minutes}分${seconds % 60}秒`;
  }

  /**
   * 获取进度显示文本
   * @returns {string}
   */
  getProgressText() {
    const { phase, current, total, percentage, message } = this.currentProgress;

    if (!phase) return '';

    const phaseNames = {
      'preparing': '准备中',
      'uploading': '上传中',
      'downloading': '下载中',
      'resolving': '解决冲突',
      'completed': '已完成'
    };

    const phaseName = phaseNames[phase] || phase;
    
    if (total > 0) {
      return `${phaseName} ${current}/${total} (${percentage}%) - ${message}`;
    } else {
      return `${phaseName} - ${message}`;
    }
  }

  /**
   * 清理状态管理器
   */
  cleanup() {
    this.progressCallbacks.clear();
    this.statusCallbacks.clear();
    this.resetProgress();
  }
}

// 导出单例实例
export const syncStatusManager = new SyncStatusManager();
export default syncStatusManager;