import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 应用基础信息
    appInfo: {
      version: '1.0.0',
      name: '健康报告管理'
    },
    
    // 系统设置
    settings: {
      theme: 'light', // light, dark, auto
      language: 'zh-CN',
      fontSize: 'medium', // small, medium, large
      enableNotification: true,
      autoSync: true,
      enableAnimation: true,
      enableVibration: true,
      enableEyeProtection: false,
      hasCompletedGuide: false
    },
    
    // 网络状态
    networkStatus: {
      isOnline: true,
      networkType: 'wifi'
    },
    
    // 加载状态
    loading: {
      global: false,
      sync: false,
      upload: false
    },
    
    // 错误信息
    error: {
      message: '',
      code: null,
      timestamp: null
    }
  }),
  
  getters: {
    // 是否为暗色主题
    isDarkTheme: (state) => {
      if (state.settings.theme === 'auto') {
        // 根据系统时间判断
        const hour = new Date().getHours()
        return hour < 6 || hour > 18
      }
      return state.settings.theme === 'dark'
    },
    
    // 是否有网络连接
    isNetworkAvailable: (state) => state.networkStatus.isOnline,
    
    // 是否正在加载
    isLoading: (state) => Object.values(state.loading).some(loading => loading)
  },
  
  actions: {
    // 更新应用设置
    updateSettings(newSettings) {
      this.settings = { ...this.settings, ...newSettings }
      // 持久化存储设置
      uni.setStorageSync('app_settings', this.settings)
    },
    
    // 更新网络状态
    updateNetworkStatus(status) {
      this.networkStatus = { ...this.networkStatus, ...status }
    },
    
    // 设置加载状态
    setLoading(type, status) {
      this.loading[type] = status
    },
    
    // 设置错误信息
    setError(error) {
      this.error = {
        message: error.message || '未知错误',
        code: error.code || null,
        timestamp: Date.now()
      }
    },
    
    // 清除错误信息
    clearError() {
      this.error = {
        message: '',
        code: null,
        timestamp: null
      }
    },
    
    // 初始化应用状态
    async initApp() {
      try {
        // 从本地存储恢复设置
        const savedSettings = uni.getStorageSync('app_settings')
        if (savedSettings) {
          this.settings = { ...this.settings, ...savedSettings }
        }
        
        // 检查网络状态
        const networkInfo = await uni.getNetworkType()
        this.updateNetworkStatus({
          isOnline: networkInfo.networkType !== 'none',
          networkType: networkInfo.networkType
        })
        
        // 监听网络状态变化
        uni.onNetworkStatusChange((res) => {
          this.updateNetworkStatus({
            isOnline: res.isConnected,
            networkType: res.networkType
          })
        })
        
      } catch (error) {
        console.error('应用初始化失败:', error)
        this.setError(error)
      }
    }
  }
})