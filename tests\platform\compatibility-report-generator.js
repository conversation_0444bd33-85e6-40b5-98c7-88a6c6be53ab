/**
 * 跨平台兼容性测试报告生成器
 * 生成详细的兼容性测试报告
 */

import fs from 'fs'
import path from 'path'
import { PLATFORM_TYPES } from '../../utils/platform/constants.js'

class CompatibilityReportGenerator {
  constructor() {
    this.testResults = {
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        skippedTests: 0,
        platforms: Object.values(PLATFORM_TYPES),
        testStartTime: null,
        testEndTime: null,
        duration: 0
      },
      platformResults: {},
      featureMatrix: {},
      performanceMetrics: {},
      issues: [],
      recommendations: []
    }

    this.initializePlatformResults()
  }

  /**
   * 初始化平台测试结果结构
   */
  initializePlatformResults() {
    Object.values(PLATFORM_TYPES).forEach(platform => {
      this.testResults.platformResults[platform] = {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        skippedTests: 0,
        features: {
          camera: { supported: false, tested: false, issues: [] },
          storage: { supported: false, tested: false, issues: [] },
          sharing: { supported: false, tested: false, issues: [] },
          fileSystem: { supported: false, tested: false, issues: [] },
          network: { supported: false, tested: false, issues: [] },
          permissions: { supported: false, tested: false, issues: [] },
          ui: { supported: false, tested: false, issues: [] },
          performance: { supported: false, tested: false, issues: [] }
        },
        limitations: [],
        workarounds: []
      }
    })
  }

  /**
   * 开始测试记录
   */
  startTesting() {
    this.testResults.summary.testStartTime = new Date()
    console.log('开始跨平台兼容性测试...')
  }

  /**
   * 结束测试记录
   */
  endTesting() {
    this.testResults.summary.testEndTime = new Date()
    this.testResults.summary.duration = 
      this.testResults.summary.testEndTime - this.testResults.summary.testStartTime
    
    this.calculateSummary()
    console.log('跨平台兼容性测试完成')
  }

  /**
   * 记录测试结果
   * @param {string} platform 平台类型
   * @param {string} feature 功能名称
   * @param {Object} result 测试结果
   */
  recordTestResult(platform, feature, result) {
    const platformResult = this.testResults.platformResults[platform]
    
    if (!platformResult) {
      console.warn(`未知平台: ${platform}`)
      return
    }

    platformResult.totalTests++
    this.testResults.summary.totalTests++

    if (result.passed) {
      platformResult.passedTests++
      this.testResults.summary.passedTests++
      platformResult.features[feature].supported = true
    } else if (result.skipped) {
      platformResult.skippedTests++
      this.testResults.summary.skippedTests++
    } else {
      platformResult.failedTests++
      this.testResults.summary.failedTests++
      
      if (result.error) {
        platformResult.features[feature].issues.push(result.error)
        this.testResults.issues.push({
          platform,
          feature,
          error: result.error,
          severity: result.severity || 'medium'
        })
      }
    }

    platformResult.features[feature].tested = true

    // 记录性能指标
    if (result.performance) {
      this.recordPerformanceMetric(platform, feature, result.performance)
    }

    // 记录限制和解决方案
    if (result.limitations) {
      platformResult.limitations.push(...result.limitations)
    }

    if (result.workarounds) {
      platformResult.workarounds.push(...result.workarounds)
    }
  }

  /**
   * 记录性能指标
   * @param {string} platform 平台类型
   * @param {string} feature 功能名称
   * @param {Object} metrics 性能指标
   */
  recordPerformanceMetric(platform, feature, metrics) {
    if (!this.testResults.performanceMetrics[platform]) {
      this.testResults.performanceMetrics[platform] = {}
    }

    this.testResults.performanceMetrics[platform][feature] = {
      ...metrics,
      timestamp: new Date()
    }
  }

  /**
   * 计算汇总统计
   */
  calculateSummary() {
    // 计算通过率
    this.testResults.summary.passRate = 
      (this.testResults.summary.passedTests / this.testResults.summary.totalTests * 100).toFixed(2)

    // 生成功能兼容性矩阵
    this.generateFeatureMatrix()

    // 生成建议
    this.generateRecommendations()
  }

  /**
   * 生成功能兼容性矩阵
   */
  generateFeatureMatrix() {
    const features = ['camera', 'storage', 'sharing', 'fileSystem', 'network', 'permissions', 'ui', 'performance']
    
    features.forEach(feature => {
      this.testResults.featureMatrix[feature] = {}
      
      Object.values(PLATFORM_TYPES).forEach(platform => {
        const platformResult = this.testResults.platformResults[platform]
        const featureResult = platformResult.features[feature]
        
        this.testResults.featureMatrix[feature][platform] = {
          supported: featureResult.supported,
          tested: featureResult.tested,
          issueCount: featureResult.issues.length,
          status: this.getFeatureStatus(featureResult)
        }
      })
    })
  }

  /**
   * 获取功能状态
   * @param {Object} featureResult 功能测试结果
   * @returns {string} 状态
   */
  getFeatureStatus(featureResult) {
    if (!featureResult.tested) return 'untested'
    if (featureResult.supported && featureResult.issues.length === 0) return 'full'
    if (featureResult.supported && featureResult.issues.length > 0) return 'partial'
    return 'unsupported'
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = []

    // 分析高频问题
    const issueFrequency = {}
    this.testResults.issues.forEach(issue => {
      const key = `${issue.feature}_${issue.error}`
      issueFrequency[key] = (issueFrequency[key] || 0) + 1
    })

    // 生成针对性建议
    Object.entries(issueFrequency).forEach(([key, frequency]) => {
      if (frequency >= 2) {
        const [feature, error] = key.split('_')
        recommendations.push({
          type: 'bug_fix',
          priority: 'high',
          description: `${feature}功能在多个平台出现"${error}"问题，建议优先修复`,
          affectedPlatforms: this.testResults.issues
            .filter(issue => issue.feature === feature && issue.error === error)
            .map(issue => issue.platform)
        })
      }
    })

    // 性能优化建议
    Object.entries(this.testResults.performanceMetrics).forEach(([platform, metrics]) => {
      Object.entries(metrics).forEach(([feature, metric]) => {
        if (metric.duration && metric.duration > 3000) {
          recommendations.push({
            type: 'performance',
            priority: 'medium',
            description: `${platform}平台的${feature}功能响应时间过长(${metric.duration}ms)，建议优化`,
            suggestion: '考虑使用缓存、异步处理或分片加载等优化策略'
          })
        }
      })
    })

    // 兼容性建议
    Object.entries(this.testResults.featureMatrix).forEach(([feature, platforms]) => {
      const unsupportedPlatforms = Object.entries(platforms)
        .filter(([platform, status]) => status.status === 'unsupported')
        .map(([platform]) => platform)

      if (unsupportedPlatforms.length > 0) {
        recommendations.push({
          type: 'compatibility',
          priority: 'medium',
          description: `${feature}功能在${unsupportedPlatforms.join(', ')}平台不支持`,
          suggestion: '考虑实现降级方案或替代功能'
        })
      }
    })

    this.testResults.recommendations = recommendations
  }

  /**
   * 生成HTML报告
   * @returns {string} HTML内容
   */
  generateHTMLReport() {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨平台兼容性测试报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h2 { color: #666; border-bottom: 2px solid #007AFF; padding-bottom: 10px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #007AFF, #5AC8FA); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; font-size: 14px; opacity: 0.9; }
        .summary-card .value { font-size: 32px; font-weight: bold; margin: 0; }
        .matrix-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .matrix-table th, .matrix-table td { border: 1px solid #ddd; padding: 12px; text-align: center; }
        .matrix-table th { background: #f8f9fa; font-weight: bold; }
        .status-full { background: #28a745; color: white; }
        .status-partial { background: #ffc107; color: #333; }
        .status-unsupported { background: #dc3545; color: white; }
        .status-untested { background: #6c757d; color: white; }
        .issue-list { list-style: none; padding: 0; }
        .issue-item { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .issue-high { border-left: 5px solid #dc3545; }
        .issue-medium { border-left: 5px solid #ffc107; }
        .issue-low { border-left: 5px solid #28a745; }
        .recommendation { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .performance-chart { margin: 20px 0; }
        .chart-bar { height: 20px; background: #007AFF; margin: 5px 0; border-radius: 3px; position: relative; }
        .chart-label { position: absolute; left: 10px; top: 50%; transform: translateY(-50%); color: white; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>健康报告管理系统 - 跨平台兼容性测试报告</h1>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <p class="value">${this.testResults.summary.totalTests}</p>
            </div>
            <div class="summary-card">
                <h3>通过测试</h3>
                <p class="value">${this.testResults.summary.passedTests}</p>
            </div>
            <div class="summary-card">
                <h3>失败测试</h3>
                <p class="value">${this.testResults.summary.failedTests}</p>
            </div>
            <div class="summary-card">
                <h3>通过率</h3>
                <p class="value">${this.testResults.summary.passRate}%</p>
            </div>
        </div>

        <h2>功能兼容性矩阵</h2>
        <table class="matrix-table">
            <thead>
                <tr>
                    <th>功能</th>
                    ${Object.values(PLATFORM_TYPES).map(platform => `<th>${platform}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${Object.entries(this.testResults.featureMatrix).map(([feature, platforms]) => `
                    <tr>
                        <td><strong>${this.getFeatureName(feature)}</strong></td>
                        ${Object.values(PLATFORM_TYPES).map(platform => {
                          const status = platforms[platform]?.status || 'untested'
                          return `<td class="status-${status}">${this.getStatusText(status)}</td>`
                        }).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <h2>平台详细结果</h2>
        ${Object.entries(this.testResults.platformResults).map(([platform, result]) => `
            <h3>${platform} 平台</h3>
            <p>测试通过率: ${(result.passedTests / result.totalTests * 100).toFixed(2)}% (${result.passedTests}/${result.totalTests})</p>
            
            ${result.limitations.length > 0 ? `
                <h4>平台限制</h4>
                <ul>
                    ${result.limitations.map(limitation => `<li>${limitation}</li>`).join('')}
                </ul>
            ` : ''}
            
            ${result.workarounds.length > 0 ? `
                <h4>解决方案</h4>
                <ul>
                    ${result.workarounds.map(workaround => `<li>${workaround}</li>`).join('')}
                </ul>
            ` : ''}
        `).join('')}

        <h2>性能指标</h2>
        <div class="performance-chart">
            ${Object.entries(this.testResults.performanceMetrics).map(([platform, metrics]) => `
                <h4>${platform} 平台性能</h4>
                ${Object.entries(metrics).map(([feature, metric]) => `
                    <div>
                        <strong>${this.getFeatureName(feature)}</strong>: ${metric.duration || 'N/A'}ms
                        ${metric.duration ? `<div class="chart-bar" style="width: ${Math.min(metric.duration / 50, 100)}%">
                            <span class="chart-label">${metric.duration}ms</span>
                        </div>` : ''}
                    </div>
                `).join('')}
            `).join('')}
        </div>

        <h2>发现的问题</h2>
        <ul class="issue-list">
            ${this.testResults.issues.map(issue => `
                <li class="issue-item issue-${issue.severity}">
                    <strong>${issue.platform} - ${this.getFeatureName(issue.feature)}</strong><br>
                    ${issue.error}
                </li>
            `).join('')}
        </ul>

        <h2>优化建议</h2>
        ${this.testResults.recommendations.map(rec => `
            <div class="recommendation">
                <strong>[${rec.type.toUpperCase()}] ${rec.description}</strong><br>
                ${rec.suggestion ? `建议: ${rec.suggestion}` : ''}
                ${rec.affectedPlatforms ? `<br>影响平台: ${rec.affectedPlatforms.join(', ')}` : ''}
            </div>
        `).join('')}

        <h2>测试信息</h2>
        <p>测试开始时间: ${this.testResults.summary.testStartTime?.toLocaleString('zh-CN')}</p>
        <p>测试结束时间: ${this.testResults.summary.testEndTime?.toLocaleString('zh-CN')}</p>
        <p>测试持续时间: ${Math.round(this.testResults.summary.duration / 1000)}秒</p>
        <p>报告生成时间: ${new Date().toLocaleString('zh-CN')}</p>
    </div>
</body>
</html>
    `

    return html
  }

  /**
   * 获取功能中文名称
   * @param {string} feature 功能英文名
   * @returns {string} 中文名称
   */
  getFeatureName(feature) {
    const names = {
      camera: '相机功能',
      storage: '存储功能',
      sharing: '分享功能',
      fileSystem: '文件系统',
      network: '网络请求',
      permissions: '权限管理',
      ui: '用户界面',
      performance: '性能表现'
    }
    return names[feature] || feature
  }

  /**
   * 获取状态文本
   * @param {string} status 状态
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const texts = {
      full: '完全支持',
      partial: '部分支持',
      unsupported: '不支持',
      untested: '未测试'
    }
    return texts[status] || status
  }

  /**
   * 生成JSON报告
   * @returns {string} JSON内容
   */
  generateJSONReport() {
    return JSON.stringify(this.testResults, null, 2)
  }

  /**
   * 保存报告到文件
   * @param {string} format 报告格式 ('html' | 'json')
   * @param {string} outputPath 输出路径
   */
  async saveReport(format = 'html', outputPath = null) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const defaultPath = outputPath || `./compatibility-report-${timestamp}.${format}`

    let content
    if (format === 'html') {
      content = this.generateHTMLReport()
    } else if (format === 'json') {
      content = this.generateJSONReport()
    } else {
      throw new Error(`不支持的报告格式: ${format}`)
    }

    try {
      await fs.promises.writeFile(defaultPath, content, 'utf8')
      console.log(`报告已保存到: ${defaultPath}`)
      return defaultPath
    } catch (error) {
      console.error('保存报告失败:', error)
      throw error
    }
  }

  /**
   * 运行模拟测试并生成报告
   */
  async runMockTests() {
    this.startTesting()

    // 模拟各平台测试结果
    const platforms = Object.values(PLATFORM_TYPES)
    const features = ['camera', 'storage', 'sharing', 'fileSystem', 'network', 'permissions', 'ui', 'performance']

    for (const platform of platforms) {
      for (const feature of features) {
        // 模拟测试结果
        const mockResult = this.generateMockTestResult(platform, feature)
        this.recordTestResult(platform, feature, mockResult)
        
        // 模拟测试延迟
        await new Promise(resolve => setTimeout(resolve, 10))
      }
    }

    this.endTesting()

    // 生成并保存报告
    const htmlPath = await this.saveReport('html')
    const jsonPath = await this.saveReport('json')

    return { htmlPath, jsonPath }
  }

  /**
   * 生成模拟测试结果
   * @param {string} platform 平台
   * @param {string} feature 功能
   * @returns {Object} 模拟结果
   */
  generateMockTestResult(platform, feature) {
    // 基于平台和功能的真实限制生成模拟结果
    const platformLimitations = {
      [PLATFORM_TYPES.H5]: {
        camera: { supported: false, error: 'H5平台不支持直接相机访问' },
        fileSystem: { supported: false, error: '浏览器安全限制' },
        permissions: { supported: false, error: '浏览器权限模型不同' }
      },
      [PLATFORM_TYPES.MP_WEIXIN]: {
        sharing: { supported: true, limitations: ['仅支持微信内分享'] },
        storage: { supported: true, limitations: ['存储大小限制10MB'] }
      },
      [PLATFORM_TYPES.APP_PLUS]: {
        // APP平台通常支持所有功能
      }
    }

    const limitation = platformLimitations[platform]?.[feature]
    
    if (limitation && limitation.supported === false) {
      return {
        passed: false,
        error: limitation.error,
        severity: 'high',
        performance: { duration: Math.random() * 1000 + 500 }
      }
    }

    // 随机生成一些问题来模拟真实测试
    const hasIssue = Math.random() < 0.2 // 20%概率有问题
    
    if (hasIssue) {
      return {
        passed: false,
        error: `${feature}功能在${platform}平台出现兼容性问题`,
        severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
        performance: { duration: Math.random() * 2000 + 1000 }
      }
    }

    return {
      passed: true,
      performance: { duration: Math.random() * 1000 + 200 },
      limitations: limitation?.limitations || [],
      workarounds: limitation?.workarounds || []
    }
  }
}

export default CompatibilityReportGenerator

// 如果直接运行此文件，执行模拟测试
if (import.meta.url === `file://${process.argv[1]}`) {
  const generator = new CompatibilityReportGenerator()
  generator.runMockTests().then(({ htmlPath, jsonPath }) => {
    console.log('测试报告生成完成:')
    console.log('HTML报告:', htmlPath)
    console.log('JSON报告:', jsonPath)
  }).catch(console.error)
}