/**
 * 错误日志记录器
 * 负责错误日志的收集、存储和上报
 */
class ErrorLogger {
  constructor() {
    this.logQueue = []
    this.isUploading = false
    this.maxLocalLogs = 500
    this.uploadBatchSize = 50
  }

  /**
   * 记录错误日志
   */
  log(errorInfo) {
    const logEntry = {
      ...errorInfo,
      id: this.generateLogId(),
      timestamp: Date.now(),
      deviceInfo: this.getDeviceInfo(),
      appVersion: this.getAppVersion(),
      uploaded: false
    }

    // 添加到队列
    this.logQueue.push(logEntry)
    
    // 保存到本地存储
    this.saveToLocal(logEntry)
    
    // 尝试上报
    this.scheduleUpload()
  }

  /**
   * 生成日志ID
   */
  generateLogId() {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    try {
      const systemInfo = uni.getSystemInfoSync()
      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        pixelRatio: systemInfo.pixelRatio,
        language: systemInfo.language,
        wifiEnabled: systemInfo.wifiEnabled,
        locationEnabled: systemInfo.locationEnabled,
        bluetoothEnabled: systemInfo.bluetoothEnabled,
        cameraAuthorized: systemInfo.cameraAuthorized,
        locationAuthorized: systemInfo.locationAuthorized,
        microphoneAuthorized: systemInfo.microphoneAuthorized,
        notificationAuthorized: systemInfo.notificationAuthorized
      }
    } catch (error) {
      return {
        platform: 'unknown',
        error: error.message
      }
    }
  }

  /**
   * 获取应用版本
   */
  getAppVersion() {
    try {
      // 从package.json或manifest.json获取版本信息
      return process.env.VUE_APP_VERSION || '1.0.0'
    } catch (error) {
      return '1.0.0'
    }
  }

  /**
   * 保存到本地存储
   */
  saveToLocal(logEntry) {
    try {
      let logs = uni.getStorageSync('error_logs') || []
      logs.unshift(logEntry)
      
      // 限制本地日志数量
      if (logs.length > this.maxLocalLogs) {
        logs = logs.slice(0, this.maxLocalLogs)
      }
      
      uni.setStorageSync('error_logs', logs)
    } catch (error) {
      console.error('保存错误日志失败:', error)
    }
  }

  /**
   * 获取本地日志
   */
  getLocalLogs(filter = {}) {
    try {
      let logs = uni.getStorageSync('error_logs') || []
      
      // 应用过滤条件
      if (filter.level) {
        logs = logs.filter(log => log.level === filter.level)
      }
      if (filter.context) {
        logs = logs.filter(log => log.context === filter.context)
      }
      if (filter.startTime) {
        logs = logs.filter(log => log.timestamp >= filter.startTime)
      }
      if (filter.endTime) {
        logs = logs.filter(log => log.timestamp <= filter.endTime)
      }
      
      return logs
    } catch (error) {
      console.error('获取本地日志失败:', error)
      return []
    }
  }

  /**
   * 清除本地日志
   */
  clearLocalLogs() {
    try {
      uni.removeStorageSync('error_logs')
      this.logQueue = []
    } catch (error) {
      console.error('清除本地日志失败:', error)
    }
  }

  /**
   * 调度上传
   */
  scheduleUpload() {
    // 延迟上传，避免频繁请求
    setTimeout(() => {
      this.uploadLogs()
    }, 5000)
  }

  /**
   * 上传日志到服务器
   */
  async uploadLogs() {
    if (this.isUploading) {
      return
    }

    try {
      this.isUploading = true
      
      // 获取未上传的日志
      const unuploadedLogs = this.getLocalLogs().filter(log => !log.uploaded)
      
      if (unuploadedLogs.length === 0) {
        return
      }

      // 分批上传
      const batches = this.chunkArray(unuploadedLogs, this.uploadBatchSize)
      
      for (const batch of batches) {
        await this.uploadBatch(batch)
      }
    } catch (error) {
      console.error('上传日志失败:', error)
    } finally {
      this.isUploading = false
    }
  }

  /**
   * 上传单批日志
   */
  async uploadBatch(logs) {
    try {
      // 这里实现实际的上传逻辑
      const response = await uni.request({
        url: 'https://api.example.com/logs/error',
        method: 'POST',
        data: {
          logs: logs,
          timestamp: Date.now()
        },
        header: {
          'Content-Type': 'application/json'
        }
      })

      if (response.statusCode === 200) {
        // 标记为已上传
        this.markAsUploaded(logs.map(log => log.id))
      }
    } catch (error) {
      console.error('上传日志批次失败:', error)
      throw error
    }
  }

  /**
   * 标记日志为已上传
   */
  markAsUploaded(logIds) {
    try {
      let logs = uni.getStorageSync('error_logs') || []
      logs = logs.map(log => {
        if (logIds.includes(log.id)) {
          return { ...log, uploaded: true }
        }
        return log
      })
      uni.setStorageSync('error_logs', logs)
    } catch (error) {
      console.error('标记日志上传状态失败:', error)
    }
  }

  /**
   * 数组分块
   */
  chunkArray(array, chunkSize) {
    const chunks = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  /**
   * 获取日志统计信息
   */
  getLogStats() {
    try {
      const logs = this.getLocalLogs()
      const stats = {
        total: logs.length,
        uploaded: logs.filter(log => log.uploaded).length,
        pending: logs.filter(log => !log.uploaded).length,
        byLevel: {},
        byContext: {},
        recent24h: 0,
        recent7d: 0
      }

      const now = Date.now()
      const day = 24 * 60 * 60 * 1000
      const week = 7 * day

      logs.forEach(log => {
        // 按级别统计
        stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1
        
        // 按上下文统计
        stats.byContext[log.context] = (stats.byContext[log.context] || 0) + 1
        
        // 时间范围统计
        if (now - log.timestamp < day) {
          stats.recent24h++
        }
        if (now - log.timestamp < week) {
          stats.recent7d++
        }
      })

      return stats
    } catch (error) {
      console.error('获取日志统计失败:', error)
      return {
        total: 0,
        uploaded: 0,
        pending: 0,
        byLevel: {},
        byContext: {},
        recent24h: 0,
        recent7d: 0
      }
    }
  }
}

module.exports = ErrorLogger