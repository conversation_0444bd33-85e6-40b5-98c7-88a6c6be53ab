/**
 * 数据同步服务主入口文件
 * 统一导出所有同步相关功能
 */

import { syncService } from './syncService.js';
import { cloudApiService } from './cloudApi.js';
import { conflictResolver } from './conflictResolver.js';
import { syncStatusManager } from './syncStatusManager.js';
import { offlineManager } from './offlineManager.js';

/**
 * 初始化同步服务
 * @returns {Promise<void>}
 */
export async function initializeSyncServices() {
  try {
    console.log('开始初始化同步服务...');
    
    // 按顺序初始化各个服务
    await syncService.initialize();
    await offlineManager.initialize();
    syncStatusManager.initialize();
    conflictResolver.initialize();
    
    console.log('同步服务初始化完成');
  } catch (error) {
    console.error('同步服务初始化失败:', error);
    throw error;
  }
}

/**
 * 开始数据同步
 * @param {Object} options 同步选项
 * @returns {Promise<Object>}
 */
export async function startSync(options = {}) {
  return await syncService.startSync(options);
}

/**
 * 停止数据同步
 */
export function stopSync() {
  syncService.stopSync();
}

/**
 * 获取同步状态
 * @returns {Object}
 */
export function getSyncStatus() {
  return syncStatusManager.getSyncStatus();
}

/**
 * 获取同步统计信息
 * @returns {Object}
 */
export function getSyncStatistics() {
  return syncStatusManager.getSyncStatistics();
}

/**
 * 获取网络状态
 * @returns {Object}
 */
export function getNetworkStatus() {
  return offlineManager.getNetworkStatus();
}

/**
 * 解决数据冲突
 * @param {Object} conflict 冲突信息
 * @param {string} strategy 解决策略
 * @returns {Promise<Object>}
 */
export async function resolveConflict(conflict, strategy) {
  return await conflictResolver.resolveConflict(conflict, strategy);
}

/**
 * 添加数据到同步队列
 * @param {string} tableName 表名
 * @param {number} recordId 记录ID
 * @param {string} operationType 操作类型
 * @param {number} userId 用户ID
 */
export async function addToSyncQueue(tableName, recordId, operationType, userId) {
  await syncService.addToSyncQueue(tableName, recordId, operationType, userId);
}

/**
 * 设置认证令牌
 * @param {string} token 访问令牌
 * @param {string} refresh 刷新令牌
 */
export function setAuthTokens(token, refresh) {
  cloudApiService.setAuthTokens(token, refresh);
}

/**
 * 监听同步进度更新
 * @param {Function} callback 回调函数
 * @returns {Function} 取消监听的函数
 */
export function onProgressUpdate(callback) {
  return syncStatusManager.onProgressUpdate(callback);
}

/**
 * 监听同步状态更新
 * @param {Function} callback 回调函数
 * @returns {Function} 取消监听的函数
 */
export function onStatusUpdate(callback) {
  return syncStatusManager.onStatusUpdate(callback);
}

/**
 * 监听网络状态变化
 * @param {Function} callback 回调函数
 * @returns {Function} 取消监听的函数
 */
export function onNetworkStatusChange(callback) {
  return offlineManager.onNetworkStatusChange(callback);
}

/**
 * 清理同步服务
 */
export function cleanup() {
  syncService.stopSync();
  offlineManager.cleanup();
  syncStatusManager.cleanup();
}

// 导出各个服务实例
export {
  syncService,
  cloudApiService,
  conflictResolver,
  syncStatusManager,
  offlineManager
};

// 默认导出主要功能
export default {
  initialize: initializeSyncServices,
  startSync,
  stopSync,
  getSyncStatus,
  getSyncStatistics,
  getNetworkStatus,
  resolveConflict,
  addToSyncQueue,
  setAuthTokens,
  onProgressUpdate,
  onStatusUpdate,
  onNetworkStatusChange,
  cleanup
};