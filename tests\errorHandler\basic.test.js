/**
 * 错误处理系统基础测试
 */

// Mock uni-app API
global.uni = {
  showToast: jest.fn(),
  showModal: jest.fn(),
  getStorageSync: jest.fn(() => []),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  getSystemInfoSync: jest.fn(() => ({
    platform: 'android',
    system: 'Android 10',
    version: '10',
    model: 'Test Device',
    brand: 'Test Brand'
  })),
  onNetworkStatusChange: jest.fn(),
  getNetworkType: jest.fn(),
  request: jest.fn(),
  reLaunch: jest.fn(),
  $emit: jest.fn()
}

// Mock process.env
process.env.VUE_APP_VERSION = '1.0.0'

describe('错误处理系统基础功能', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('ErrorLogger', () => {
    const ErrorLogger = require('../../utils/errorHandler/ErrorLogger.js')
    
    it('应该能够创建ErrorLogger实例', () => {
      const logger = new ErrorLogger()
      expect(logger).toBeDefined()
      expect(logger.logQueue).toEqual([])
    })

    it('应该能够记录错误日志', () => {
      const logger = new ErrorLogger()
      const errorInfo = {
        message: '测试错误',
        level: 'error',
        context: 'test'
      }
      
      logger.log(errorInfo)
      
      expect(uni.setStorageSync).toHaveBeenCalled()
      expect(logger.logQueue.length).toBe(1)
    })

    it('应该能够获取设备信息', () => {
      const logger = new ErrorLogger()
      const deviceInfo = logger.getDeviceInfo()
      
      expect(deviceInfo).toHaveProperty('platform')
      expect(deviceInfo).toHaveProperty('system')
      expect(deviceInfo.platform).toBe('android')
    })

    it('应该能够生成日志统计', () => {
      const logger = new ErrorLogger()
      const stats = logger.getLogStats()
      
      expect(stats).toHaveProperty('total')
      expect(stats).toHaveProperty('uploaded')
      expect(stats).toHaveProperty('pending')
      expect(stats).toHaveProperty('byLevel')
      expect(stats).toHaveProperty('byContext')
    })
  })

  describe('NetworkErrorHandler', () => {
    const NetworkErrorHandler = require('../../utils/errorHandler/NetworkErrorHandler.js')
    
    it('应该能够创建NetworkErrorHandler实例', () => {
      const handler = new NetworkErrorHandler()
      expect(handler).toBeDefined()
      expect(handler.retryConfig).toBeDefined()
      expect(handler.offlineQueue).toEqual([])
    })

    it('应该能够计算重试延迟', () => {
      const handler = new NetworkErrorHandler()
      
      const delay1 = handler.calculateRetryDelay(1)
      const delay2 = handler.calculateRetryDelay(2)
      const delay3 = handler.calculateRetryDelay(3)
      
      expect(delay1).toBe(1000)
      expect(delay2).toBe(2000)
      expect(delay3).toBe(4000)
    })

    it('应该能够判断是否需要重试', () => {
      const handler = new NetworkErrorHandler()
      
      const networkError = new Error('NETWORK_ERROR')
      networkError.code = 'NETWORK_ERROR'
      
      const httpError = new Error('HTTP Error')
      httpError.statusCode = 500
      
      const normalError = new Error('Normal Error')
      
      expect(handler.shouldRetry(networkError, 1)).toBe(true)
      expect(handler.shouldRetry(httpError, 1)).toBe(true)
      expect(handler.shouldRetry(normalError, 1)).toBe(false)
      expect(handler.shouldRetry(networkError, 5)).toBe(false) // 超过最大重试次数
    })

    it('应该能够获取网络状态', () => {
      const handler = new NetworkErrorHandler()
      const status = handler.getNetworkStatus()
      
      expect(status).toHaveProperty('online')
      expect(status).toHaveProperty('offlineMode')
      expect(status).toHaveProperty('queueLength')
    })
  })

  describe('OCRErrorHandler', () => {
    const OCRErrorHandler = require('../../utils/errorHandler/OCRErrorHandler.js')
    
    it('应该能够创建OCRErrorHandler实例', () => {
      const handler = new OCRErrorHandler()
      expect(handler).toBeDefined()
      expect(handler.fallbackStrategies).toBeDefined()
      expect(handler.ocrServices).toBeDefined()
    })

    it('应该能够设置服务可用性', () => {
      const handler = new OCRErrorHandler()
      
      handler.setServiceAvailability('baidu', false)
      const status = handler.getServiceStatus()
      const baiduService = status.find(s => s.name === 'baidu')
      
      expect(baiduService.available).toBe(false)
    })

    it('应该能够获取服务状态', () => {
      const handler = new OCRErrorHandler()
      const status = handler.getServiceStatus()
      
      expect(Array.isArray(status)).toBe(true)
      expect(status.length).toBeGreaterThan(0)
      expect(status[0]).toHaveProperty('name')
      expect(status[0]).toHaveProperty('priority')
      expect(status[0]).toHaveProperty('available')
    })
  })

  describe('ErrorMonitor', () => {
    const ErrorMonitor = require('../../utils/errorHandler/ErrorMonitor.js')
    
    it('应该能够创建ErrorMonitor实例', () => {
      const monitor = new ErrorMonitor()
      expect(monitor).toBeDefined()
      expect(monitor.recentErrors).toEqual([])
      expect(monitor.alertCallbacks).toEqual([])
    })

    it('应该能够记录错误', () => {
      const monitor = new ErrorMonitor()
      const errorInfo = {
        message: '测试错误',
        level: 'error',
        context: 'test'
      }
      
      monitor.recordError(errorInfo)
      
      expect(monitor.recentErrors.length).toBe(1)
      expect(monitor.errorStats.total).toBe(1)
    })

    it('应该能够注册和移除告警回调', () => {
      const monitor = new ErrorMonitor()
      const callback = jest.fn()
      
      monitor.onAlert(callback)
      expect(monitor.alertCallbacks.length).toBe(1)
      
      monitor.offAlert(callback)
      expect(monitor.alertCallbacks.length).toBe(0)
    })

    it('应该能够生成错误报告', () => {
      const monitor = new ErrorMonitor()
      
      // 添加一些测试错误
      monitor.recordError({ message: '错误1', level: 'error', context: 'test' })
      monitor.recordError({ message: '错误2', level: 'warning', context: 'network' })
      
      const report = monitor.generateErrorReport()
      
      expect(report).toHaveProperty('timestamp')
      expect(report).toHaveProperty('stats')
      expect(report).toHaveProperty('trends')
      expect(report).toHaveProperty('topErrors')
      expect(report).toHaveProperty('recommendations')
    })

    it('应该能够设置告警阈值', () => {
      const monitor = new ErrorMonitor()
      const newThresholds = {
        errorRate: 0.2,
        criticalErrors: 10
      }
      
      monitor.setAlertThresholds(newThresholds)
      
      expect(monitor.alertThresholds.errorRate).toBe(0.2)
      expect(monitor.alertThresholds.criticalErrors).toBe(10)
    })
  })

  describe('GlobalErrorHandler集成', () => {
    const GlobalErrorHandler = require('../../utils/errorHandler.js')
    
    it('应该能够创建GlobalErrorHandler实例', () => {
      const handler = new GlobalErrorHandler()
      expect(handler).toBeDefined()
      expect(handler.logger).toBeDefined()
      expect(handler.networkHandler).toBeDefined()
      expect(handler.ocrHandler).toBeDefined()
      expect(handler.monitor).toBeDefined()
    })

    it('应该能够格式化错误信息', () => {
      const handler = new GlobalErrorHandler()
      const error = new Error('测试错误')
      const context = { context: 'test' }
      
      const formatted = handler.formatError(error, context)
      
      expect(formatted).toHaveProperty('id')
      expect(formatted).toHaveProperty('timestamp')
      expect(formatted).toHaveProperty('message')
      expect(formatted).toHaveProperty('context')
      expect(formatted).toHaveProperty('level')
      expect(formatted.message).toBe('测试错误')
      expect(formatted.context).toBe('test')
    })

    it('应该能够获取用户友好的错误消息', () => {
      const handler = new GlobalErrorHandler()
      
      const networkError = { message: 'NetworkError', level: 'warning' }
      const ocrError = { context: 'OCRError', level: 'error' }
      const generalError = { message: '一般错误', level: 'error' }
      
      expect(handler.getUserFriendlyMessage(networkError)).toBe('网络连接异常，请检查网络设置')
      expect(handler.getUserFriendlyMessage(ocrError)).toBe('OCR识别失败，请重新拍摄清晰的图片')
      expect(handler.getUserFriendlyMessage(generalError)).toBe('操作失败，请稍后重试')
    })

    it('应该能够获取错误级别', () => {
      const handler = new GlobalErrorHandler()
      
      const networkError = new Error('NetworkError')
      networkError.name = 'NetworkError'
      
      const validationError = new Error('ValidationError')
      validationError.name = 'ValidationError'
      
      const generalError = new Error('General error')
      
      expect(handler.getErrorLevel(networkError)).toBe('warning')
      expect(handler.getErrorLevel(validationError)).toBe('info')
      expect(handler.getErrorLevel(generalError)).toBe('error')
    })

    it('应该能够获取各种状态和统计信息', () => {
      const handler = new GlobalErrorHandler()
      
      // 测试获取错误日志
      const logs = handler.getErrorLogs()
      expect(Array.isArray(logs)).toBe(true)
      
      // 测试获取错误统计
      const stats = handler.getErrorStats()
      expect(stats).toHaveProperty('total')
      
      // 测试获取网络状态
      const networkStatus = handler.getNetworkStatus()
      expect(networkStatus).toHaveProperty('online')
      
      // 测试获取监控报告
      const report = handler.getMonitoringReport()
      expect(report).toHaveProperty('timestamp')
      
      // 测试获取告警历史
      const alerts = handler.getAlertHistory()
      expect(Array.isArray(alerts)).toBe(true)
    })
  })
})