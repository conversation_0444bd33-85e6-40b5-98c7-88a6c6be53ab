/**
 * 同步服务集成测试
 * 测试云端同步服务的各项功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { syncService } from '../../services/sync/syncService.js';
import { cloudApiService } from '../../services/sync/cloudApi.js';
import { conflictResolver } from '../../services/sync/conflictResolver.js';
import { syncStatusManager } from '../../services/sync/syncStatusManager.js';
import { offlineManager } from '../../services/sync/offlineManager.js';
import { useSyncStore } from '../../stores/sync.js';
import { storage } from '../../utils/storage/index.js';

// Mock uni-app API
global.uni = {
  request: vi.fn(),
  uploadFile: vi.fn(),
  downloadFile: vi.fn(),
  saveFile: vi.fn(),
  getNetworkType: vi.fn(),
  onNetworkStatusChange: vi.fn(),
  getStorageSync: vi.fn(),
  setStorageSync: vi.fn(),
  showToast: vi.fn(),
  showModal: vi.fn()
};

describe('同步服务集成测试', () => {
  let mockUserId;
  let mockSyncStore;

  beforeEach(async () => {
    // 重置所有mock
    vi.clearAllMocks();
    
    // 设置测试用户ID
    mockUserId = 1;
    
    // Mock同步存储
    mockSyncStore = {
      syncStatus: {
        isEnabled: true,
        isRunning: false,
        lastSyncTime: null,
        nextSyncTime: null
      },
      syncConfig: {
        autoSync: true,
        syncInterval: 30,
        wifiOnly: false,
        syncOnAppStart: true
      },
      pendingSync: {
        upload: [],
        download: [],
        delete: []
      },
      conflicts: [],
      syncHistory: [],
      statistics: {
        totalSynced: 0,
        successCount: 0,
        failureCount: 0,
        conflictCount: 0
      },
      updateSyncStatus: vi.fn(),
      addSyncRecord: vi.fn(),
      addConflict: vi.fn(),
      initSync: vi.fn()
    };
    
    // Mock存储服务
    vi.mocked(storage.sync.getPendingSyncRecords).mockResolvedValue([]);
    vi.mocked(storage.users.findById).mockResolvedValue({
      id: mockUserId,
      username: 'testuser',
      email: '<EMAIL>',
      updated_at: new Date().toISOString()
    });
    
    // Mock网络状态
    vi.mocked(uni.getNetworkType).mockImplementation((options) => {
      options.success({ networkType: 'wifi' });
    });
    
    // 初始化服务
    await syncService.initialize();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('云端API服务测试', () => {
    it('应该能够成功认证用户', async () => {
      // Mock认证响应
      vi.mocked(uni.request).mockResolvedValue({
        statusCode: 200,
        data: {
          access_token: 'mock_access_token',
          refresh_token: 'mock_refresh_token',
          user: { id: mockUserId, username: 'testuser' }
        }
      });

      const result = await cloudApiService.authenticate('testuser', 'password');

      expect(result.access_token).toBe('mock_access_token');
      expect(cloudApiService.authToken).toBe('mock_access_token');
    });

    it('应该能够上传用户数据', async () => {
      // 设置认证令牌
      cloudApiService.setAuthTokens('mock_token');

      // Mock上传响应
      vi.mocked(uni.request).mockResolvedValue({
        statusCode: 200,
        data: { success: true, id: mockUserId }
      });

      const userData = {
        id: mockUserId,
        username: 'testuser',
        email: '<EMAIL>'
      };

      const result = await cloudApiService.uploadUserData(userData);

      expect(result.success).toBe(true);
      expect(uni.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: expect.stringContaining('/sync/user'),
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock_token'
          })
        })
      );
    });

    it('应该能够下载用户数据', async () => {
      // 设置认证令牌
      cloudApiService.setAuthTokens('mock_token');

      // Mock下载响应
      vi.mocked(uni.request).mockResolvedValue({
        statusCode: 200,
        data: {
          data: {
            id: mockUserId,
            username: 'testuser',
            email: '<EMAIL>',
            updated_at: new Date().toISOString()
          }
        }
      });

      const result = await cloudApiService.downloadUserData(mockUserId);

      expect(result.data.email).toBe('<EMAIL>');
    });

    it('应该能够处理网络错误', async () => {
      // Mock网络错误
      vi.mocked(uni.request).mockRejectedValue(new Error('Network error'));

      await expect(
        cloudApiService.authenticate('testuser', 'password')
      ).rejects.toThrow('Network error');
    });
  });

  describe('同步服务测试', () => {
    it('应该能够开始完整同步', async () => {
      // Mock网络连接检查
      vi.spyOn(cloudApiService, 'checkNetworkConnection').mockResolvedValue(true);

      // Mock待同步记录
      vi.mocked(storage.sync.getPendingSyncRecords).mockResolvedValue([
        {
          id: 1,
          table_name: 'health_reports',
          record_id: 1,
          operation_type: 'INSERT',
          user_id: mockUserId
        }
      ]);

      // Mock云端API调用
      vi.spyOn(cloudApiService, 'uploadHealthReports').mockResolvedValue({ success: true });
      vi.spyOn(cloudApiService, 'downloadHealthReports').mockResolvedValue({ data: [] });

      const result = await syncService.startSync({
        userId: mockUserId,
        syncType: 'full'
      });

      expect(result.success).toBe(true);
      expect(result.uploaded).toBeGreaterThanOrEqual(0);
      expect(result.downloaded).toBeGreaterThanOrEqual(0);
    });

    it('应该能够处理上传失败', async () => {
      // Mock网络连接检查
      vi.spyOn(cloudApiService, 'checkNetworkConnection').mockResolvedValue(true);

      // Mock待同步记录
      vi.mocked(storage.sync.getPendingSyncRecords).mockResolvedValue([
        {
          id: 1,
          table_name: 'health_reports',
          record_id: 1,
          operation_type: 'INSERT',
          user_id: mockUserId
        }
      ]);

      // Mock上传失败
      vi.spyOn(cloudApiService, 'uploadHealthReports').mockRejectedValue(
        new Error('Upload failed')
      );

      const result = await syncService.startSync({
        userId: mockUserId,
        syncType: 'upload'
      });

      expect(result.success).toBe(true); // 部分成功
      expect(result.errors).toContain('健康报告上传失败: Upload failed');
    });

    it('应该能够检测网络不可用', async () => {
      // Mock网络连接检查失败
      vi.spyOn(cloudApiService, 'checkNetworkConnection').mockResolvedValue(false);

      const result = await syncService.startSync({
        userId: mockUserId
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('网络连接不可用');
    });

    it('应该能够防止重复同步', async () => {
      // 设置同步正在运行
      syncService.isRunning = true;

      const result = await syncService.startSync({
        userId: mockUserId
      });

      expect(result.success).toBe(false);
      expect(result.message).toBe('同步正在进行中');
    });
  });

  describe('冲突解决测试', () => {
    it('应该能够检测数据冲突', () => {
      const localData = {
        id: 1,
        report_title: '血液检查报告',
        report_date: '2024-01-01',
        updated_at: '2024-01-01T10:00:00Z'
      };

      const remoteData = {
        id: 1,
        report_title: '血液检查报告（修订版）',
        report_date: '2024-01-01',
        updated_at: '2024-01-01T09:00:00Z'
      };

      const conflict = conflictResolver.detectConflict(localData, remoteData, 'health_report');

      expect(conflict).not.toBeNull();
      expect(conflict.conflictFields).toHaveLength(1);
      expect(conflict.conflictFields[0].field).toBe('report_title');
    });

    it('应该能够自动解决低严重程度冲突', async () => {
      const conflict = {
        id: 'test_conflict',
        type: 'health_report',
        severity: 'low',
        localData: {
          id: 1,
          notes: '本地备注',
          updated_at: '2024-01-01T10:00:00Z'
        },
        remoteData: {
          id: 1,
          notes: '远程备注',
          updated_at: '2024-01-01T09:00:00Z'
        },
        conflictFields: [
          {
            field: 'notes',
            localValue: '本地备注',
            remoteValue: '远程备注',
            severity: 'low'
          }
        ]
      };

      // Mock数据库更新
      vi.mocked(storage.reports.update).mockResolvedValue({});

      const result = await conflictResolver.resolveConflict(conflict, 'auto');

      expect(result.success).toBe(true);
      expect(result.strategy).toBe('local_wins'); // 本地数据更新时间更晚
    });

    it('应该能够合并数据解决冲突', async () => {
      const conflict = {
        id: 'test_conflict',
        type: 'health_report',
        severity: 'medium',
        localData: {
          id: 1,
          report_title: '血液检查',
          notes: '本地备注',
          updated_at: '2024-01-01T10:00:00Z'
        },
        remoteData: {
          id: 1,
          report_title: '血液检查报告',
          notes: '',
          updated_at: '2024-01-01T09:00:00Z'
        },
        conflictFields: [
          {
            field: 'report_title',
            localValue: '血液检查',
            remoteValue: '血液检查报告',
            severity: 'medium'
          },
          {
            field: 'notes',
            localValue: '本地备注',
            remoteValue: '',
            severity: 'low'
          }
        ]
      };

      // Mock数据库更新
      vi.mocked(storage.reports.update).mockResolvedValue({});

      const result = await conflictResolver.resolveConflict(conflict, 'merge');

      expect(result.success).toBe(true);
      expect(result.strategy).toBe('merge');
    });
  });

  describe('同步状态管理测试', () => {
    it('应该能够跟踪同步进度', () => {
      syncStatusManager.startProgress({
        userId: mockUserId,
        syncType: 'full'
      });

      expect(syncStatusManager.currentProgress.phase).toBe('preparing');
      expect(syncStatusManager.currentProgress.details.userId).toBe(mockUserId);

      syncStatusManager.setPhase('uploading', '上传数据中...', 10);
      expect(syncStatusManager.currentProgress.phase).toBe('uploading');
      expect(syncStatusManager.currentProgress.total).toBe(10);

      syncStatusManager.incrementProgress(3, '已上传3条记录');
      expect(syncStatusManager.currentProgress.current).toBe(3);
      expect(syncStatusManager.currentProgress.percentage).toBe(30);
    });

    it('应该能够完成同步进度', () => {
      syncStatusManager.startProgress({ userId: mockUserId });
      syncStatusManager.setPhase('uploading', '上传中', 5);
      syncStatusManager.updateProgress({ current: 5 });

      const result = { success: true, uploaded: 5, downloaded: 3 };
      syncStatusManager.completeProgress(result);

      expect(syncStatusManager.currentProgress.phase).toBe('completed');
      expect(syncStatusManager.currentProgress.percentage).toBe(100);
      expect(syncStatusManager.currentProgress.result).toEqual(result);
    });

    it('应该能够计算同步健康状态', () => {
      // Mock同步存储数据
      mockSyncStore.statistics = {
        successCount: 8,
        failureCount: 2,
        totalSynced: 10
      };
      mockSyncStore.syncSuccessRate = '80';
      mockSyncStore.conflicts = [];
      mockSyncStore.recentSyncHistory = [
        { status: 'success', timestamp: Date.now() },
        { status: 'failed', timestamp: Date.now() - 1000 }
      ];

      const health = syncStatusManager.getSyncHealth();

      expect(health.status).toBe('fair'); // 成功率80%，状态一般
      expect(health.issues).toContain('同步成功率低于95%');
    });
  });

  describe('离线管理测试', () => {
    it('应该能够检测网络状态变化', async () => {
      let networkCallback;
      vi.mocked(uni.onNetworkStatusChange).mockImplementation((callback) => {
        networkCallback = callback;
      });

      await offlineManager.initialize();

      // 模拟网络断开
      networkCallback({ isConnected: false });
      expect(offlineManager.isOnline).toBe(false);

      // 模拟网络恢复
      networkCallback({ isConnected: true });
      expect(offlineManager.isOnline).toBe(true);
    });

    it('应该能够添加数据到离线队列', async () => {
      const operation = {
        type: 'create_report',
        data: { id: 1, title: '测试报告' },
        userId: mockUserId
      };

      // Mock本地存储
      vi.mocked(uni.setStorageSync).mockImplementation(() => {});

      await offlineManager.addToOfflineQueue(operation);

      expect(offlineManager.offlineQueue).toHaveLength(1);
      expect(offlineManager.offlineQueue[0].type).toBe('create_report');
    });

    it('应该能够处理离线队列', async () => {
      // 添加离线项目
      offlineManager.offlineQueue = [
        {
          id: '1',
          type: 'create_report',
          data: { id: 1, title: '测试报告' },
          userId: mockUserId,
          timestamp: Date.now()
        }
      ];

      // Mock同步记录创建
      vi.mocked(storage.sync.createSyncRecord).mockResolvedValue({});

      const result = await offlineManager.processOfflineQueue();

      expect(result.processed).toBe(1);
      expect(result.failed).toBe(0);
      expect(offlineManager.offlineQueue).toHaveLength(0);
    });

    it('应该能够处理离线项目失败', async () => {
      // 添加离线项目
      offlineManager.offlineQueue = [
        {
          id: '1',
          type: 'unknown_type',
          data: { id: 1 },
          userId: mockUserId,
          timestamp: Date.now()
        }
      ];

      const result = await offlineManager.processOfflineQueue();

      expect(result.processed).toBe(0);
      expect(result.failed).toBe(1);
      expect(result.failedItems).toHaveLength(1);
    });
  });

  describe('端到端同步测试', () => {
    it('应该能够完成完整的同步流程', async () => {
      // 设置测试数据
      const testReport = {
        id: 1,
        user_id: mockUserId,
        report_title: '血液检查报告',
        report_date: '2024-01-01',
        updated_at: new Date().toISOString()
      };

      // Mock各种服务调用
      vi.spyOn(cloudApiService, 'checkNetworkConnection').mockResolvedValue(true);
      vi.mocked(storage.sync.getPendingSyncRecords).mockResolvedValue([
        {
          id: 1,
          table_name: 'health_reports',
          record_id: 1,
          operation_type: 'INSERT',
          user_id: mockUserId
        }
      ]);
      vi.mocked(storage.reports.findById).mockResolvedValue(testReport);
      vi.spyOn(cloudApiService, 'uploadHealthReports').mockResolvedValue({ success: true });
      vi.spyOn(cloudApiService, 'downloadHealthReports').mockResolvedValue({
        data: [
          {
            ...testReport,
            report_title: '血液检查报告（更新版）',
            updated_at: new Date(Date.now() + 1000).toISOString()
          }
        ]
      });
      vi.mocked(storage.sync.updateSyncStatus).mockResolvedValue({});

      // 执行同步
      const result = await syncService.startSync({
        userId: mockUserId,
        syncType: 'full'
      });

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.uploaded).toBe(1);
      expect(result.downloaded).toBe(1);
      expect(result.conflicts).toBe(0);
    });

    it('应该能够处理同步过程中的冲突', async () => {
      // 设置冲突数据
      const localReport = {
        id: 1,
        user_id: mockUserId,
        report_title: '本地标题',
        updated_at: '2024-01-01T10:00:00Z'
      };

      const remoteReport = {
        id: 1,
        user_id: mockUserId,
        report_title: '远程标题',
        updated_at: '2024-01-01T09:00:00Z'
      };

      // Mock服务调用
      vi.spyOn(cloudApiService, 'checkNetworkConnection').mockResolvedValue(true);
      vi.mocked(storage.sync.getPendingSyncRecords).mockResolvedValue([]);
      vi.spyOn(cloudApiService, 'downloadHealthReports').mockResolvedValue({
        data: [remoteReport]
      });
      vi.mocked(storage.reports.findById).mockResolvedValue(localReport);

      // 执行同步
      const result = await syncService.startSync({
        userId: mockUserId,
        syncType: 'download'
      });

      // 验证冲突被检测到
      expect(result.conflicts).toBe(1);
    });
  });

  describe('性能测试', () => {
    it('应该能够处理大量数据同步', async () => {
      const startTime = Date.now();
      
      // 创建大量测试数据
      const largeDataSet = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        table_name: 'health_reports',
        record_id: i + 1,
        operation_type: 'INSERT',
        user_id: mockUserId
      }));

      // Mock服务调用
      vi.spyOn(cloudApiService, 'checkNetworkConnection').mockResolvedValue(true);
      vi.mocked(storage.sync.getPendingSyncRecords).mockResolvedValue(largeDataSet);
      vi.spyOn(cloudApiService, 'uploadHealthReports').mockResolvedValue({ success: true });
      vi.mocked(storage.reports.findById).mockImplementation((id) => 
        Promise.resolve({
          id,
          user_id: mockUserId,
          report_title: `报告${id}`,
          updated_at: new Date().toISOString()
        })
      );

      const result = await syncService.startSync({
        userId: mockUserId,
        syncType: 'upload'
      });

      const duration = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(5000); // 应该在5秒内完成
    });
  });

  describe('错误处理测试', () => {
    it('应该能够处理认证失败', async () => {
      // Mock认证失败
      vi.mocked(uni.request).mockResolvedValue({
        statusCode: 401,
        data: { message: 'Unauthorized' }
      });

      await expect(
        cloudApiService.authenticate('invalid', 'credentials')
      ).rejects.toThrow('HTTP 401: Unauthorized');
    });

    it('应该能够处理网络超时', async () => {
      // Mock网络超时
      vi.mocked(uni.request).mockImplementation(() => 
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 100);
        })
      );

      await expect(
        cloudApiService.authenticate('user', 'pass')
      ).rejects.toThrow('Request timeout');
    });

    it('应该能够处理数据库错误', async () => {
      // Mock数据库错误
      vi.mocked(storage.sync.getPendingSyncRecords).mockRejectedValue(
        new Error('Database connection failed')
      );

      const result = await syncService.startSync({
        userId: mockUserId
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database connection failed');
    });
  });
});

describe('同步服务压力测试', () => {
  it('应该能够处理并发同步请求', async () => {
    const concurrentRequests = 5;
    const promises = [];

    // Mock网络连接检查
    vi.spyOn(cloudApiService, 'checkNetworkConnection').mockResolvedValue(true);
    vi.mocked(storage.sync.getPendingSyncRecords).mockResolvedValue([]);

    for (let i = 0; i < concurrentRequests; i++) {
      promises.push(
        syncService.startSync({
          userId: i + 1,
          background: true
        })
      );
    }

    const results = await Promise.allSettled(promises);
    
    // 只有一个请求应该成功（防止并发同步）
    const successfulResults = results.filter(r => 
      r.status === 'fulfilled' && r.value.success
    );
    
    expect(successfulResults.length).toBeLessThanOrEqual(1);
  });
});