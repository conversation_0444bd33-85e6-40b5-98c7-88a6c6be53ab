/**
 * 报告编辑和删除功能集成测试
 * 测试编辑表单、数据验证、保存和删除功能
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ReportEditor from '@/components/business/ReportEditor.vue'
import DeleteConfirmDialog from '@/components/common/DeleteConfirmDialog.vue'
import { useReportStore } from '@/stores/report.js'
import { Report, ReportItem } from '@/models/Report.js'
import { Constants } from '@/types/index.js'

// 模拟uni-app API
global.uni = {
  showToast: jest.fn(),
  showModal: jest.fn()
}

describe('报告编辑和删除功能测试', () => {
  let pinia
  let reportStore

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    reportStore = useReportStore()
    
    // 重置模拟函数
    jest.clearAllMocks()
  })

  /**
   * 创建测试报告数据
   */
  function createTestReport() {
    const items = [
      new ReportItem({
        id: 'item_1',
        name: '白细胞计数',
        value: '8.5',
        unit: '×10^9/L',
        referenceRange: '3.5-9.5',
        isAbnormal: false,
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
        notes: '正常范围内'
      }),
      new ReportItem({
        id: 'item_2',
        name: '红细胞计数',
        value: '3.2',
        unit: '×10^12/L',
        referenceRange: '4.3-5.8',
        isAbnormal: true,
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
        notes: '偏低，需要关注'
      })
    ]

    return new Report({
      id: 'test_report_1',
      userId: 'test_user',
      title: '血常规检查报告',
      hospital: '北京协和医院',
      doctor: '张医生',
      department: '内科',
      checkDate: new Date('2024-01-15'),
      reportDate: new Date('2024-01-15'),
      category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
      items,
      notes: '患者需要注意休息，定期复查。',
      tags: ['复查', '血常规']
    })
  }

  describe('ReportEditor 组件测试', () => {
    let wrapper

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount()
      }
    })

    it('应该正确渲染编辑表单', () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.report-editor').exists()).toBe(true)
      expect(wrapper.find('.editor-form').exists()).toBe(true)
    })

    it('应该正确初始化表单数据', () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      const formData = wrapper.vm.formData
      expect(formData.title).toBe(testReport.title)
      expect(formData.hospital).toBe(testReport.hospital)
      expect(formData.doctor).toBe(testReport.doctor)
      expect(formData.items.length).toBe(testReport.items.length)
      expect(formData.notes).toBe(testReport.notes)
      expect(formData.tags).toEqual(testReport.tags)
    })

    it('应该能够编辑基本信息', async () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 修改医院名称
      const hospitalInput = wrapper.findAll('.form-input')[1] // 第二个输入框是医院
      await hospitalInput.setValue('上海华山医院')
      
      expect(wrapper.vm.formData.hospital).toBe('上海华山医院')

      // 修改医生姓名
      const doctorInput = wrapper.findAll('.form-input')[2] // 第三个输入框是医生
      await doctorInput.setValue('李医生')
      
      expect(wrapper.vm.formData.doctor).toBe('李医生')
    })

    it('应该能够添加新的检查项目', async () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      const initialItemCount = wrapper.vm.formData.items.length

      // 点击添加项目按钮
      const addItemBtn = wrapper.find('.add-item-btn')
      await addItemBtn.trigger('tap')

      expect(wrapper.vm.formData.items.length).toBe(initialItemCount + 1)
      
      // 验证新项目的默认值
      const newItem = wrapper.vm.formData.items[wrapper.vm.formData.items.length - 1]
      expect(newItem.name).toBe('')
      expect(newItem.value).toBe('')
      expect(newItem.isAbnormal).toBe(false)
    })

    it('应该能够删除检查项目', async () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      const initialItemCount = wrapper.vm.formData.items.length

      // 点击删除第一个项目
      const removeBtn = wrapper.find('.remove-item-btn')
      await removeBtn.trigger('tap')

      expect(wrapper.vm.formData.items.length).toBe(initialItemCount - 1)
    })

    it('应该能够编辑检查项目信息', async () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 修改第一个项目的名称
      const firstItem = wrapper.vm.formData.items[0]
      firstItem.name = '血小板计数'
      firstItem.value = '250'
      firstItem.unit = '×10^9/L'

      await wrapper.vm.$nextTick()

      expect(wrapper.vm.formData.items[0].name).toBe('血小板计数')
      expect(wrapper.vm.formData.items[0].value).toBe('250')
      expect(wrapper.vm.formData.items[0].unit).toBe('×10^9/L')
    })

    it('应该能够管理标签', async () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      const initialTagCount = wrapper.vm.formData.tags.length

      // 添加新标签
      wrapper.vm.newTag = '重要'
      await wrapper.vm.addTag()

      expect(wrapper.vm.formData.tags.length).toBe(initialTagCount + 1)
      expect(wrapper.vm.formData.tags).toContain('重要')
      expect(wrapper.vm.newTag).toBe('')

      // 删除标签
      await wrapper.vm.removeTag(0)
      expect(wrapper.vm.formData.tags.length).toBe(initialTagCount)
    })

    it('应该能够添加推荐标签', async () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 添加推荐标签
      await wrapper.vm.addRecommendedTag('异常')

      expect(wrapper.vm.formData.tags).toContain('异常')
    })

    it('应该正确验证表单数据', async () => {
      wrapper = mount(ReportEditor, {
        props: {
          report: null,
          isEdit: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 测试空表单验证
      const isValid = wrapper.vm.validateForm()
      expect(isValid).toBe(false)
      expect(wrapper.vm.errors.hospital).toBeTruthy()
      expect(wrapper.vm.errors.checkDate).toBeTruthy()
      expect(wrapper.vm.errors.reportDate).toBeTruthy()

      // 填写必填字段
      wrapper.vm.formData.hospital = '测试医院'
      wrapper.vm.formData.checkDate = '2024-01-15'
      wrapper.vm.formData.reportDate = '2024-01-15'

      const isValidAfterFill = wrapper.vm.validateForm()
      expect(isValidAfterFill).toBe(true)
      expect(Object.keys(wrapper.vm.errors).length).toBe(0)
    })

    it('应该验证日期逻辑', async () => {
      wrapper = mount(ReportEditor, {
        props: {
          report: null,
          isEdit: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 设置报告日期早于检查日期
      wrapper.vm.formData.hospital = '测试医院'
      wrapper.vm.formData.checkDate = '2024-01-15'
      wrapper.vm.formData.reportDate = '2024-01-10'

      const isValid = wrapper.vm.validateForm()
      expect(isValid).toBe(false)
      expect(wrapper.vm.errors.reportDate).toContain('报告日期不能早于检查日期')
    })

    it('应该验证检查项目必填字段', async () => {
      wrapper = mount(ReportEditor, {
        props: {
          report: null,
          isEdit: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 添加空的检查项目
      wrapper.vm.formData.hospital = '测试医院'
      wrapper.vm.formData.checkDate = '2024-01-15'
      wrapper.vm.formData.reportDate = '2024-01-15'
      wrapper.vm.formData.items = [
        { name: '', value: '', unit: '', referenceRange: '', isAbnormal: false }
      ]

      const isValid = wrapper.vm.validateForm()
      expect(isValid).toBe(false)
      expect(wrapper.vm.errors['items.0.name']).toBeTruthy()
      expect(wrapper.vm.errors['items.0.value']).toBeTruthy()
    })

    it('应该能够保存报告', async () => {
      const testReport = createTestReport()
      const saveHandler = jest.fn()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      wrapper.vm.$emit = saveHandler

      // 修改一些数据
      wrapper.vm.formData.hospital = '修改后的医院'

      // 保存报告
      await wrapper.vm.handleSave()

      expect(saveHandler).toHaveBeenCalledWith('save', expect.objectContaining({
        hospital: '修改后的医院'
      }))
    })

    it('应该能够取消编辑', async () => {
      const testReport = createTestReport()
      const cancelHandler = jest.fn()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      wrapper.vm.$emit = cancelHandler

      // 没有修改时直接取消
      await wrapper.vm.handleCancel()
      expect(cancelHandler).toHaveBeenCalledWith('cancel')
    })

    it('应该检测未保存的更改', () => {
      const testReport = createTestReport()
      
      wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 初始状态没有更改
      expect(wrapper.vm.hasUnsavedChanges()).toBe(false)

      // 修改数据后有更改
      wrapper.vm.formData.hospital = '修改后的医院'
      expect(wrapper.vm.hasUnsavedChanges()).toBe(true)
    })

    it('应该能够自动判断异常状态', async () => {
      wrapper = mount(ReportEditor, {
        props: {
          report: null,
          isEdit: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      const item = {
        name: '血糖',
        value: '8.5',
        unit: 'mmol/L',
        referenceRange: '3.9-6.1',
        isAbnormal: false
      }

      // 更新异常状态
      wrapper.vm.updateItemAbnormalStatus(item, 0)

      // 8.5 > 6.1，应该被标记为异常
      expect(item.isAbnormal).toBe(true)
    })

    it('应该正确解析参考范围', () => {
      wrapper = mount(ReportEditor, {
        props: {
          report: null,
          isEdit: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 测试范围格式
      expect(wrapper.vm.parseReferenceRange('3.5-5.5')).toEqual({ min: 3.5, max: 5.5 })
      expect(wrapper.vm.parseReferenceRange('3.5~5.5')).toEqual({ min: 3.5, max: 5.5 })
      expect(wrapper.vm.parseReferenceRange('<5.5')).toEqual({ min: null, max: 5.5 })
      expect(wrapper.vm.parseReferenceRange('>3.5')).toEqual({ min: 3.5, max: null })
      expect(wrapper.vm.parseReferenceRange('≤5.5')).toEqual({ min: null, max: 5.5 })
      expect(wrapper.vm.parseReferenceRange('≥3.5')).toEqual({ min: 3.5, max: null })
    })
  })

  describe('DeleteConfirmDialog 组件测试', () => {
    let wrapper

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount()
      }
    })

    it('应该正确渲染删除确认对话框', () => {
      wrapper = mount(DeleteConfirmDialog, {
        props: {
          content: '确定要删除这份报告吗？'
        },
        global: {
          stubs: {
            'uni-icons': true,
            'uni-popup': {
              template: '<div class="popup-mock"><slot /></div>',
              methods: {
                open: jest.fn(),
                close: jest.fn()
              }
            }
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.delete-confirm-dialog').exists()).toBe(true)
      expect(wrapper.find('.content-text').text()).toBe('确定要删除这份报告吗？')
    })

    it('应该显示风险警告', () => {
      wrapper = mount(DeleteConfirmDialog, {
        props: {
          content: '确定要删除这份报告吗？'
        },
        global: {
          stubs: {
            'uni-icons': true,
            'uni-popup': {
              template: '<div class="popup-mock"><slot /></div>',
              methods: {
                open: jest.fn(),
                close: jest.fn()
              }
            }
          }
        }
      })

      const riskWarning = wrapper.find('.risk-warning')
      expect(riskWarning.exists()).toBe(true)
      expect(riskWarning.find('.warning-text').text()).toBe('此操作无法撤销，请谨慎操作')
    })

    it('应该支持确认文本输入', async () => {
      wrapper = mount(DeleteConfirmDialog, {
        props: {
          content: '确定要删除这份报告吗？',
          requireConfirmText: true,
          confirmText: '删除'
        },
        global: {
          stubs: {
            'uni-icons': true,
            'uni-popup': {
              template: '<div class="popup-mock"><slot /></div>',
              methods: {
                open: jest.fn(),
                close: jest.fn()
              }
            }
          }
        }
      })

      expect(wrapper.find('.confirm-input-section').exists()).toBe(true)
      expect(wrapper.vm.canConfirm).toBe(false)

      // 输入正确的确认文本
      const confirmInput = wrapper.find('.confirm-input')
      await confirmInput.setValue('删除')

      expect(wrapper.vm.userConfirmText).toBe('删除')
      expect(wrapper.vm.canConfirm).toBe(true)
    })

    it('应该能够取消删除', async () => {
      const cancelHandler = jest.fn()
      
      wrapper = mount(DeleteConfirmDialog, {
        props: {
          content: '确定要删除这份报告吗？'
        },
        global: {
          stubs: {
            'uni-icons': true,
            'uni-popup': {
              template: '<div class="popup-mock"><slot /></div>',
              methods: {
                open: jest.fn(),
                close: jest.fn()
              }
            }
          }
        }
      })

      wrapper.vm.$emit = cancelHandler

      // 点击取消按钮
      const cancelBtn = wrapper.find('.action-btn.cancel')
      await cancelBtn.trigger('tap')

      expect(cancelHandler).toHaveBeenCalledWith('cancel')
    })

    it('应该能够确认删除', async () => {
      const confirmHandler = jest.fn()
      
      wrapper = mount(DeleteConfirmDialog, {
        props: {
          content: '确定要删除这份报告吗？'
        },
        global: {
          stubs: {
            'uni-icons': true,
            'uni-popup': {
              template: '<div class="popup-mock"><slot /></div>',
              methods: {
                open: jest.fn(),
                close: jest.fn()
              }
            }
          }
        }
      })

      wrapper.vm.$emit = confirmHandler

      // 点击确认按钮
      const confirmBtn = wrapper.find('.action-btn.delete')
      await confirmBtn.trigger('tap')

      expect(confirmHandler).toHaveBeenCalledWith('confirm')
    })

    it('应该在删除中时禁用确认按钮', () => {
      wrapper = mount(DeleteConfirmDialog, {
        props: {
          content: '确定要删除这份报告吗？',
          deleting: true
        },
        global: {
          stubs: {
            'uni-icons': true,
            'uni-popup': {
              template: '<div class="popup-mock"><slot /></div>',
              methods: {
                open: jest.fn(),
                close: jest.fn()
              }
            }
          }
        }
      })

      const confirmBtn = wrapper.find('.action-btn.delete')
      expect(confirmBtn.classes()).toContain('disabled')
      expect(confirmBtn.find('.btn-text').text()).toBe('删除中...')
    })
  })

  describe('编辑和删除集成测试', () => {
    it('应该能够完整的编辑流程', async () => {
      const testReport = createTestReport()
      reportStore.setReports([testReport])

      // 模拟编辑页面
      const wrapper = mount(ReportEditor, {
        props: {
          report: testReport,
          isEdit: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 修改报告信息
      wrapper.vm.formData.hospital = '修改后的医院'
      wrapper.vm.formData.doctor = '修改后的医生'
      wrapper.vm.formData.notes = '修改后的备注'

      // 添加新的检查项目
      wrapper.vm.addNewItem()
      const newItem = wrapper.vm.formData.items[wrapper.vm.formData.items.length - 1]
      newItem.name = '新增项目'
      newItem.value = '100'
      newItem.unit = 'mg/dL'

      // 验证修改
      expect(wrapper.vm.formData.hospital).toBe('修改后的医院')
      expect(wrapper.vm.formData.items.length).toBe(testReport.items.length + 1)
      expect(wrapper.vm.hasUnsavedChanges()).toBe(true)

      // 保存修改
      const saveHandler = jest.fn()
      wrapper.vm.$emit = saveHandler
      await wrapper.vm.handleSave()

      expect(saveHandler).toHaveBeenCalledWith('save', expect.objectContaining({
        hospital: '修改后的医院',
        doctor: '修改后的医生',
        notes: '修改后的备注'
      }))
    })

    it('应该能够处理复杂的表单验证场景', async () => {
      const wrapper = mount(ReportEditor, {
        props: {
          report: null,
          isEdit: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'uni-icons': true,
            'uni-popup': true,
            'uni-popup-dialog': true,
            'picker': true,
            'checkbox': true,
            'textarea': true
          }
        }
      })

      // 添加多个检查项目，部分有错误
      wrapper.vm.formData.hospital = '测试医院'
      wrapper.vm.formData.checkDate = '2024-01-15'
      wrapper.vm.formData.reportDate = '2024-01-15'
      
      wrapper.vm.formData.items = [
        { name: '项目1', value: '100', unit: 'mg/dL', referenceRange: '80-120', isAbnormal: false },
        { name: '', value: '200', unit: 'mg/dL', referenceRange: '80-120', isAbnormal: false }, // 名称为空
        { name: '项目3', value: '', unit: 'mg/dL', referenceRange: '80-120', isAbnormal: false } // 值为空
      ]

      const isValid = wrapper.vm.validateForm()
      expect(isValid).toBe(false)
      expect(wrapper.vm.errors['items.1.name']).toBeTruthy()
      expect(wrapper.vm.errors['items.2.value']).toBeTruthy()
    })
  })
})