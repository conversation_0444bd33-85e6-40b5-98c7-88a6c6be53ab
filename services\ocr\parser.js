/**
 * OCR结果解析器
 * 专门用于解析体检报告中的结构化数据
 */

class OCRParser {
  constructor() {
    // 常见的检查项目模式
    this.itemPatterns = [
      // 血常规
      /白细胞计数|白细胞|WBC/i,
      /红细胞计数|红细胞|RBC/i,
      /血红蛋白|血红蛋白浓度|HGB|Hb/i,
      /红细胞压积|血细胞比容|HCT/i,
      /血小板计数|血小板|PLT/i,
      /中性粒细胞|中性粒细胞百分比|NEUT/i,
      /淋巴细胞|淋巴细胞百分比|LYMPH/i,
      
      // 生化检查
      /总胆固醇|胆固醇|TC|CHOL/i,
      /甘油三酯|三酰甘油|TG/i,
      /高密度脂蛋白|HDL/i,
      /低密度脂蛋白|LDL/i,
      /血糖|葡萄糖|GLU/i,
      /尿酸|UA/i,
      /肌酐|CREA/i,
      /尿素氮|BUN/i,
      /总蛋白|TP/i,
      /白蛋白|ALB/i,
      /谷丙转氨酶|ALT/i,
      /谷草转氨酶|AST/i,
      
      // 尿常规
      /尿蛋白|蛋白质|PRO/i,
      /尿糖|葡萄糖|GLU/i,
      /白细胞|WBC/i,
      /红细胞|RBC/i,
      /尿比重|SG/i
    ];

    // 数值模式
    this.valuePatterns = [
      /(\d+\.?\d*)\s*([a-zA-Z\/μμmolLgdl%]+)/g, // 数字+单位
      /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/g, // 数字范围
      /(\d+\.?\d*)/g // 纯数字
    ];

    // 参考值模式
    this.referencePatterns = [
      /参考值[：:]\s*([^\n\r]+)/i,
      /正常值[：:]\s*([^\n\r]+)/i,
      /参考范围[：:]\s*([^\n\r]+)/i,
      /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/g
    ];

    // 医院信息模式
    this.hospitalPatterns = [
      /医院[：:]\s*([^\n\r]+)/i,
      /检验科[：:]\s*([^\n\r]+)/i,
      /送检科室[：:]\s*([^\n\r]+)/i
    ];

    // 医生信息模式
    this.doctorPatterns = [
      /医师[：:]?\s*([^\n\r]+)/i,
      /医生[：:]?\s*([^\n\r]+)/i,
      /检验医师[：:]?\s*([^\n\r]+)/i,
      /报告医师[：:]?\s*([^\n\r]+)/i
    ];

    // 时间模式
    this.datePatterns = [
      /(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日]?/g,
      /(\d{4})\-(\d{2})\-(\d{2})/g,
      /(\d{2})\/(\d{2})\/(\d{4})/g
    ];
  }

  /**
   * 解析OCR识别结果
   * @param {Object} ocrResult - OCR识别结果
   * @returns {Object} 解析后的结构化数据
   */
  parse(ocrResult) {
    if (!ocrResult.success || !ocrResult.data) {
      return {
        success: false,
        error: 'OCR识别结果无效',
        data: null
      };
    }

    try {
      const fullText = ocrResult.data.fullText || '';
      const lines = ocrResult.data.lines || [];

      const parsedData = {
        // 基本信息
        hospital: this.extractHospital(fullText),
        doctor: this.extractDoctor(fullText),
        checkDate: this.extractDate(fullText),
        reportDate: this.extractDate(fullText, 'report'),
        
        // 检查项目
        items: this.extractItems(lines, fullText),
        
        // 原始数据
        originalText: fullText,
        confidence: this.calculateConfidence(lines),
        
        // 解析统计
        stats: {
          totalLines: lines.length,
          itemsFound: 0,
          valuesFound: 0,
          referencesFound: 0
        }
      };

      // 更新统计信息
      parsedData.stats.itemsFound = parsedData.items.length;
      parsedData.stats.valuesFound = parsedData.items.filter(item => item.value).length;
      parsedData.stats.referencesFound = parsedData.items.filter(item => item.referenceRange).length;

      return {
        success: true,
        data: parsedData,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('OCR结果解析失败:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 提取医院信息
   * @param {string} text - 文本内容
   * @returns {string} 医院名称
   */
  extractHospital(text) {
    // 首先尝试匹配明确的医院标识模式
    for (const pattern of this.hospitalPatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    // 尝试从文本开头提取医院名称
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    for (let i = 0; i < Math.min(5, lines.length); i++) {
      const line = lines[i];
      
      // 检查是否包含医院相关关键词
      if (line.includes('医院') || line.includes('医疗') || line.includes('诊所') || 
          line.includes('中心') || line.includes('科室')) {
        return line;
      }
    }

    return '';
  }

  /**
   * 提取医生信息
   * @param {string} text - 文本内容
   * @returns {string} 医生姓名
   */
  extractDoctor(text) {
    for (const pattern of this.doctorPatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    return '';
  }

  /**
   * 提取日期信息
   * @param {string} text - 文本内容
   * @param {string} type - 日期类型 ('check' | 'report')
   * @returns {string} 日期字符串
   */
  extractDate(text, type = 'check') {
    const dates = [];
    
    for (const pattern of this.datePatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const year = match[1] || match[3];
        const month = match[2];
        const day = match[3] || match[1];
        
        if (year && month && day) {
          const dateStr = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          dates.push(dateStr);
        }
      }
    }

    if (dates.length === 0) return '';
    
    // 如果有多个日期，检查日期通常在报告日期之前
    if (type === 'check' && dates.length > 1) {
      return dates[0]; // 返回较早的日期
    } else if (type === 'report' && dates.length > 1) {
      return dates[dates.length - 1]; // 返回较晚的日期
    }
    
    return dates[0];
  }

  /**
   * 提取检查项目
   * @param {Array} lines - 文本行数组
   * @param {string} fullText - 完整文本
   * @returns {Array} 检查项目数组
   */
  extractItems(lines, fullText) {
    const items = [];
    const processedLines = new Set();

    for (let i = 0; i < lines.length; i++) {
      if (processedLines.has(i)) continue;

      const line = lines[i];
      const text = line.text || '';
      
      // 检查是否包含检查项目
      const itemMatch = this.findItemMatch(text);
      if (itemMatch) {
        const item = this.parseItemLine(text, itemMatch);
        
        // 如果当前行没有找到完整信息，尝试查看下一行
        if (!item.value || !item.referenceRange) {
          const nextLineInfo = this.checkNextLines(lines, i + 1, 3);
          if (nextLineInfo.value && !item.value) {
            item.value = nextLineInfo.value;
            item.unit = nextLineInfo.unit;
          }
          if (nextLineInfo.referenceRange && !item.referenceRange) {
            item.referenceRange = nextLineInfo.referenceRange;
          }
          
          // 标记已处理的行
          for (let j = i + 1; j <= i + nextLineInfo.linesUsed; j++) {
            processedLines.add(j);
          }
        }

        if (item.name) {
          // 判断是否异常
          item.isAbnormal = this.isAbnormalValue(item.value, item.referenceRange);
          items.push(item);
        }
        
        processedLines.add(i);
      }
    }

    return items;
  }

  /**
   * 查找项目匹配
   * @param {string} text - 文本内容
   * @returns {Object|null} 匹配结果
   */
  findItemMatch(text) {
    for (const pattern of this.itemPatterns) {
      const match = text.match(pattern);
      if (match) {
        return {
          pattern: pattern,
          match: match[0],
          index: match.index
        };
      }
    }
    return null;
  }

  /**
   * 解析项目行
   * @param {string} text - 文本内容
   * @param {Object} itemMatch - 项目匹配结果
   * @returns {Object} 解析后的项目信息
   */
  parseItemLine(text, itemMatch) {
    const item = {
      name: itemMatch.match,
      value: '',
      unit: '',
      referenceRange: '',
      isAbnormal: false,
      originalText: text
    };

    // 提取数值和单位
    const valueInfo = this.extractValue(text);
    if (valueInfo) {
      item.value = valueInfo.value;
      item.unit = valueInfo.unit;
    }

    // 提取参考范围
    const referenceRange = this.extractReferenceRange(text);
    if (referenceRange) {
      item.referenceRange = referenceRange;
    }

    return item;
  }

  /**
   * 检查后续行
   * @param {Array} lines - 文本行数组
   * @param {number} startIndex - 开始索引
   * @param {number} maxLines - 最大检查行数
   * @returns {Object} 提取的信息
   */
  checkNextLines(lines, startIndex, maxLines = 3) {
    const result = {
      value: '',
      unit: '',
      referenceRange: '',
      linesUsed: 0
    };

    for (let i = startIndex; i < Math.min(startIndex + maxLines, lines.length); i++) {
      const text = lines[i].text || '';
      
      // 如果遇到新的检查项目，停止查找
      if (this.findItemMatch(text)) {
        break;
      }

      // 提取数值
      if (!result.value) {
        const valueInfo = this.extractValue(text);
        if (valueInfo) {
          result.value = valueInfo.value;
          result.unit = valueInfo.unit;
          result.linesUsed = i - startIndex + 1;
        }
      }

      // 提取参考范围
      if (!result.referenceRange) {
        const referenceRange = this.extractReferenceRange(text);
        if (referenceRange) {
          result.referenceRange = referenceRange;
          result.linesUsed = Math.max(result.linesUsed, i - startIndex + 1);
        }
      }
    }

    return result;
  }

  /**
   * 提取数值和单位
   * @param {string} text - 文本内容
   * @returns {Object|null} 数值信息
   */
  extractValue(text) {
    // 尝试匹配数字+复杂单位的模式（包括科学计数法）
    const complexUnitMatch = text.match(/(\d+\.?\d*)\s+(\d+\^?\d*[a-zA-Z\/μμmolLgdl%]+)/);
    if (complexUnitMatch) {
      return {
        value: complexUnitMatch[1],
        unit: complexUnitMatch[2]
      };
    }

    // 尝试匹配数字+简单单位的模式
    const valueUnitMatch = text.match(/(\d+\.?\d*)\s*([a-zA-Z\/μμmolLgdl%]+)/);
    if (valueUnitMatch) {
      return {
        value: valueUnitMatch[1],
        unit: valueUnitMatch[2]
      };
    }

    // 尝试匹配纯数字
    const numberMatch = text.match(/(\d+\.?\d*)/);
    if (numberMatch) {
      return {
        value: numberMatch[1],
        unit: ''
      };
    }

    return null;
  }

  /**
   * 提取参考范围
   * @param {string} text - 文本内容
   * @returns {string} 参考范围
   */
  extractReferenceRange(text) {
    // 尝试匹配参考值标识
    for (const pattern of this.referencePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    // 尝试匹配数字范围
    const rangeMatch = text.match(/(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/);
    if (rangeMatch) {
      return `${rangeMatch[1]}-${rangeMatch[2]}`;
    }

    return '';
  }

  /**
   * 判断数值是否异常
   * @param {string} value - 检查值
   * @param {string} referenceRange - 参考范围
   * @returns {boolean} 是否异常
   */
  isAbnormalValue(value, referenceRange) {
    if (!value || !referenceRange) return false;

    const numValue = parseFloat(value);
    if (isNaN(numValue)) return false;

    // 解析参考范围
    const rangeMatch = referenceRange.match(/(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/);
    if (rangeMatch) {
      const minValue = parseFloat(rangeMatch[1]);
      const maxValue = parseFloat(rangeMatch[2]);
      
      if (!isNaN(minValue) && !isNaN(maxValue)) {
        return numValue < minValue || numValue > maxValue;
      }
    }

    return false;
  }

  /**
   * 计算识别置信度
   * @param {Array} lines - 文本行数组
   * @returns {number} 置信度 (0-1)
   */
  calculateConfidence(lines) {
    if (!lines || lines.length === 0) return 0;

    const confidences = lines
      .map(line => line.confidence)
      .filter(conf => conf !== null && conf !== undefined);

    if (confidences.length === 0) return 0.8; // 默认置信度

    const avgConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
    return Math.max(0, Math.min(1, avgConfidence));
  }

  /**
   * 验证解析结果
   * @param {Object} parsedData - 解析后的数据
   * @returns {Object} 验证结果
   */
  validateResult(parsedData) {
    const validation = {
      isValid: true,
      issues: [],
      score: 0
    };

    // 检查基本信息完整性
    if (!parsedData.hospital) {
      validation.issues.push('缺少医院信息');
    } else {
      validation.score += 20;
    }

    if (!parsedData.checkDate) {
      validation.issues.push('缺少检查日期');
    } else {
      validation.score += 20;
    }

    // 检查项目数量
    if (parsedData.items.length === 0) {
      validation.issues.push('未识别到检查项目');
      validation.isValid = false;
    } else {
      validation.score += Math.min(40, parsedData.items.length * 5);
    }

    // 检查数值完整性
    const itemsWithValues = parsedData.items.filter(item => item.value);
    if (itemsWithValues.length < parsedData.items.length * 0.5) {
      validation.issues.push('超过50%的项目缺少数值');
    } else {
      validation.score += 20;
    }

    validation.isValid = validation.issues.length === 0 && validation.score >= 60;
    return validation;
  }
}

module.exports = OCRParser;