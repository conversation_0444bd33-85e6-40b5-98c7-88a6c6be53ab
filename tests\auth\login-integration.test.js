/**
 * 登录功能集成测试
 */

const { describe, it, expect, beforeEach, afterEach, jest } = require('@jest/globals')
const authService = require('../../services/auth/authService.js').default
const sessionService = require('../../services/auth/sessionService.js').default
const tokenService = require('../../services/auth/tokenService.js').default
const { useUserStore } = require('../../stores/user.js')
const { createPinia, setActivePinia } = require('pinia')

// Mock uni API
global.uni = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  reLaunch: jest.fn(),
  navigateTo: jest.fn(),
  $emit: jest.fn(),
  $on: jest.fn(),
  onAppShow: jest.fn(),
  onAppHide: jest.fn(),
  getSystemInfo: jest.fn((options) => {
    options.success({
      platform: 'devtools',
      system: 'Windows 10',
      model: 'PC',
      brand: 'PC',
      screenWidth: 1920,
      screenHeight: 1080,
      version: '1.0.0'
    })
  })
}

describe('登录功能集成测试', () => {
  let userStore

  beforeEach(() => {
    setActivePinia(createPinia())
    userStore = useUserStore()
    jest.clearAllMocks()
    uni.getStorageSync.mockReturnValue(null)
  })

  afterEach(() => {
    jest.clearAllTimers()
  })

  describe('密码登录流程', () => {
    it('应该完成完整的密码登录流程', async () => {
      const loginData = {
        phone: '13800138000',
        password: 'Test123456'
      }

      // Mock authService.loginWithPassword
      const mockLoginResult = {
        success: true,
        data: {
          token: 'test-token-123',
          refreshToken: 'refresh-token-123',
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000,
          userInfo: {
            id: 'user-123',
            phone: '13800138000',
            nickname: '测试用户',
            avatar: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            settings: {
              biometricEnabled: false,
              autoSync: true,
              notificationEnabled: true,
              theme: 'light'
            }
          }
        }
      }

      jest.spyOn(authService, 'loginWithPassword').mockResolvedValue(mockLoginResult)

      // 执行登录
      const result = await authService.loginWithPassword(loginData)
      expect(result.success).toBe(true)

      // 保存到用户store
      await userStore.login(result.data, 'password')

      // 创建会话
      await sessionService.createSession(result.data)

      // 验证用户状态
      expect(userStore.auth.isLoggedIn).toBe(true)
      expect(userStore.auth.loginMethod).toBe('password')
      expect(userStore.userInfo.phone).toBe('13800138000')

      // 验证token保存
      expect(uni.setStorageSync).toHaveBeenCalledWith('auth_token', 'test-token-123')
      expect(uni.setStorageSync).toHaveBeenCalledWith('refresh_token', 'refresh-token-123')

      // 验证会话信息保存
      expect(uni.setStorageSync).toHaveBeenCalledWith('session_info', expect.objectContaining({
        userId: 'user-123',
        version: '1.0'
      }))
    })

    it('应该正确处理登录失败', async () => {
      const loginData = {
        phone: '13800138000',
        password: 'wrongpassword'
      }

      const mockLoginResult = {
        success: false,
        message: '密码错误'
      }

      jest.spyOn(authService, 'loginWithPassword').mockResolvedValue(mockLoginResult)

      const result = await authService.loginWithPassword(loginData)

      expect(result.success).toBe(false)
      expect(result.message).toBe('密码错误')

      // 验证用户状态未改变
      expect(userStore.auth.isLoggedIn).toBe(false)
    })
  })

  describe('验证码登录流程', () => {
    it('应该完成完整的验证码登录流程', async () => {
      // 1. 发送验证码
      const sendCodeResult = {
        success: true,
        message: '验证码已发送',
        data: {
          codeId: 'code_123',
          expiry: Date.now() + 5 * 60 * 1000
        }
      }

      jest.spyOn(authService, 'sendVerificationCode').mockResolvedValue(sendCodeResult)

      const codeResult = await authService.sendVerificationCode('13800138000', 'login')
      expect(codeResult.success).toBe(true)

      // 2. 验证码登录
      const loginData = {
        phone: '13800138000',
        code: '123456'
      }

      const mockLoginResult = {
        success: true,
        data: {
          token: 'test-token-456',
          refreshToken: 'refresh-token-456',
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000,
          userInfo: {
            id: 'user-456',
            phone: '13800138000',
            nickname: '验证码用户'
          }
        }
      }

      jest.spyOn(authService, 'loginWithCode').mockResolvedValue(mockLoginResult)

      const result = await authService.loginWithCode(loginData)
      expect(result.success).toBe(true)

      // 保存到用户store
      await userStore.login(result.data, 'code')

      // 验证登录方式
      expect(userStore.auth.loginMethod).toBe('code')
    })

    it('应该正确处理验证码错误', async () => {
      const loginData = {
        phone: '13800138000',
        code: '000000'
      }

      const mockLoginResult = {
        success: false,
        message: '验证码错误'
      }

      jest.spyOn(authService, 'loginWithCode').mockResolvedValue(mockLoginResult)

      const result = await authService.loginWithCode(loginData)

      expect(result.success).toBe(false)
      expect(result.message).toBe('验证码错误')
    })
  })

  describe('生物识别登录流程', () => {
    it('应该完成生物识别登录流程', async () => {
      // 先设置已有的用户信息（模拟之前已登录过）
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'user_info') {
          return {
            id: 'user-bio',
            phone: '13800138000',
            nickname: '生物识别用户'
          }
        }
        if (key === 'auth_token') {
          return 'saved-token'
        }
        return null
      })

      const mockBiometricResult = {
        success: true,
        message: '生物识别登录成功',
        data: {
          token: 'saved-token',
          userInfo: {
            id: 'user-bio',
            phone: '13800138000',
            nickname: '生物识别用户'
          }
        }
      }

      jest.spyOn(authService, 'loginWithBiometric').mockResolvedValue(mockBiometricResult)

      const result = await authService.loginWithBiometric()
      expect(result.success).toBe(true)

      // 保存到用户store
      await userStore.login(result.data, 'biometric')

      expect(userStore.auth.loginMethod).toBe('biometric')
    })

    it('应该正确处理生物识别失败', async () => {
      const mockBiometricResult = {
        success: false,
        message: '生物识别验证失败'
      }

      jest.spyOn(authService, 'loginWithBiometric').mockResolvedValue(mockBiometricResult)

      const result = await authService.loginWithBiometric()

      expect(result.success).toBe(false)
      expect(result.message).toBe('生物识别验证失败')
    })
  })

  describe('微信登录流程', () => {
    it('应该完成微信登录流程', async () => {
      const mockWechatResult = {
        success: true,
        message: '微信登录成功',
        data: {
          token: 'wechat-token',
          refreshToken: 'wechat-refresh-token',
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000,
          userInfo: {
            id: 'user-wechat',
            phone: '',
            nickname: '微信用户'
          },
          wechatInfo: {
            openid: 'wx-openid-123',
            unionid: 'wx-unionid-123'
          }
        }
      }

      jest.spyOn(authService, 'loginWithWechat').mockResolvedValue(mockWechatResult)

      const result = await authService.loginWithWechat()
      expect(result.success).toBe(true)

      // 保存到用户store
      await userStore.login(result.data, 'wechat')

      expect(userStore.auth.loginMethod).toBe('wechat')
      expect(userStore.wechat.openid).toBe('wx-openid-123')
    })
  })

  describe('会话恢复流程', () => {
    it('应该正确恢复有效会话', async () => {
      // 模拟本地存储的用户信息
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'user_info') {
          return {
            id: 'user-restore',
            phone: '13800138000',
            nickname: '恢复用户'
          }
        }
        if (key === 'auth_token') {
          return 'valid-token'
        }
        if (key === 'refresh_token') {
          return 'valid-refresh-token'
        }
        if (key === 'token_expiry') {
          return Date.now() + 60 * 60 * 1000 // 1小时后过期
        }
        if (key === 'session_info') {
          return {
            userId: 'user-restore',
            loginTime: Date.now() - 30 * 60 * 1000, // 30分钟前登录
            lastActiveTime: Date.now() - 5 * 60 * 1000 // 5分钟前活跃
          }
        }
        return null
      })

      const result = await sessionService.restoreSession()

      expect(result.success).toBe(true)
      expect(userStore.auth.isLoggedIn).toBe(true)
      expect(userStore.userInfo.id).toBe('user-restore')
    })

    it('应该正确处理过期会话', async () => {
      // 模拟过期的token
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'user_info') {
          return { id: 'user-expired' }
        }
        if (key === 'auth_token') {
          return 'expired-token'
        }
        if (key === 'token_expiry') {
          return Date.now() - 60 * 60 * 1000 // 1小时前过期
        }
        return null
      })

      // Mock刷新token失败
      jest.spyOn(tokenService, 'refreshToken').mockResolvedValue({
        success: false,
        message: 'Refresh token过期'
      })

      const result = await sessionService.restoreSession()

      expect(result.success).toBe(false)
      expect(userStore.auth.isLoggedIn).toBe(false)
    })
  })

  describe('登出流程', () => {
    it('应该完成完整的登出流程', async () => {
      // 先设置登录状态
      userStore.setAuth({
        isLoggedIn: true,
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        loginMethod: 'password'
      })

      userStore.setUserInfo({
        id: 'user-logout',
        phone: '13800138000'
      })

      // 执行登出
      const logoutResult = await userStore.logout()
      expect(logoutResult.success).toBe(true)

      // 销毁会话
      const destroyResult = await sessionService.destroySession()
      expect(destroyResult.success).toBe(true)

      // 验证状态清除
      expect(userStore.auth.isLoggedIn).toBe(false)
      expect(userStore.auth.token).toBe('')
      expect(userStore.userInfo.id).toBe('')

      // 验证存储清除
      expect(uni.removeStorageSync).toHaveBeenCalledWith('auth_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('refresh_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('session_info')
    })
  })

  describe('Token自动刷新', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('应该在token即将过期时自动刷新', async () => {
      // 设置即将过期的token
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'auth_token') return 'expiring-token'
        if (key === 'refresh_token') return 'valid-refresh-token'
        if (key === 'token_expiry') return Date.now() + 5 * 60 * 1000 // 5分钟后过期
        return null
      })

      // Mock刷新成功
      const mockRefreshResult = {
        success: true,
        data: {
          token: 'new-token',
          refreshToken: 'new-refresh-token',
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000
        }
      }

      jest.spyOn(authService, 'refreshToken').mockResolvedValue(mockRefreshResult)

      // 触发自动刷新
      const refreshResult = await tokenService.refreshToken()

      expect(refreshResult.success).toBe(true)
      expect(uni.setStorageSync).toHaveBeenCalledWith('auth_token', 'new-token')
    })

    it('应该在刷新失败时跳转到登录页', async () => {
      // Mock刷新失败
      jest.spyOn(authService, 'refreshToken').mockResolvedValue({
        success: false,
        message: 'Refresh token无效'
      })

      const refreshResult = await tokenService.refreshToken()

      expect(refreshResult.success).toBe(false)
      expect(uni.showModal).toHaveBeenCalledWith(expect.objectContaining({
        title: '登录已过期'
      }))
    })
  })
})