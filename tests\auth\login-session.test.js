/**
 * 登录和会话管理测试
 */

const { describe, it, expect, beforeEach, afterEach, jest } = require('@jest/globals')
const tokenService = require('../../services/auth/tokenService.js').default
const sessionService = require('../../services/auth/sessionService.js').default
const { useUserStore } = require('../../stores/user.js')
const { createPinia, setActivePinia } = require('pinia')

// Mock uni API
global.uni = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  showModal: jest.fn(),
  reLaunch: jest.fn(),
  $emit: jest.fn(),
  $on: jest.fn(),
  onAppShow: jest.fn(),
  onAppHide: jest.fn(),
  getSystemInfo: jest.fn((options) => {
    options.success({
      platform: 'devtools',
      system: 'Windows 10',
      model: 'PC',
      brand: 'PC',
      screenWidth: 1920,
      screenHeight: 1080,
      version: '1.0.0'
    })
  })
}

describe('登录和会话管理', () => {
  let userStore

  beforeEach(() => {
    // 创建新的Pinia实例
    setActivePinia(createPinia())
    userStore = useUserStore()
    
    // 清除所有mock调用
    jest.clearAllMocks()
    
    // 重置存储mock
    uni.getStorageSync.mockReturnValue(null)
  })

  afterEach(() => {
    // 清理定时器
    jest.clearAllTimers()
  })

  describe('Token服务', () => {
    it('应该正确保存和获取token', () => {
      const tokenData = {
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        tokenExpiry: Date.now() + 2 * 60 * 60 * 1000
      }

      tokenService.saveTokens(tokenData)

      expect(uni.setStorageSync).toHaveBeenCalledWith('auth_token', tokenData.token)
      expect(uni.setStorageSync).toHaveBeenCalledWith('refresh_token', tokenData.refreshToken)
      expect(uni.setStorageSync).toHaveBeenCalledWith('token_expiry', tokenData.tokenExpiry)
    })

    it('应该正确检查token有效性', () => {
      // 设置有效token
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'auth_token') return 'valid-token'
        if (key === 'token_expiry') return Date.now() + 60 * 60 * 1000 // 1小时后过期
        return null
      })

      expect(tokenService.isTokenValid()).toBe(true)

      // 设置过期token
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'auth_token') return 'expired-token'
        if (key === 'token_expiry') return Date.now() - 60 * 60 * 1000 // 1小时前过期
        return null
      })

      expect(tokenService.isTokenValid()).toBe(false)
    })

    it('应该正确检查是否需要刷新token', () => {
      // 设置即将过期的token
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'token_expiry') return Date.now() + 5 * 60 * 1000 // 5分钟后过期
        return null
      })

      expect(tokenService.needsRefresh()).toBe(true)

      // 设置还有很久才过期的token
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'token_expiry') return Date.now() + 60 * 60 * 1000 // 1小时后过期
        return null
      })

      expect(tokenService.needsRefresh()).toBe(false)
    })

    it('应该正确清除token信息', () => {
      tokenService.clearTokens()

      expect(uni.removeStorageSync).toHaveBeenCalledWith('auth_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('refresh_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('token_expiry')
    })

    it('应该正确生成认证头', () => {
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'auth_token') return 'test-token'
        return null
      })

      const headers = tokenService.getAuthHeaders()

      expect(headers).toEqual({
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      })
    })
  })

  describe('会话服务', () => {
    it('应该正确创建会话', async () => {
      const loginData = {
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        tokenExpiry: Date.now() + 2 * 60 * 60 * 1000,
        userInfo: {
          id: 'user-123',
          phone: '13800138000',
          nickname: '测试用户'
        }
      }

      const result = await sessionService.createSession(loginData)

      expect(result.success).toBe(true)
      expect(uni.setStorageSync).toHaveBeenCalledWith('session_info', expect.objectContaining({
        userId: 'user-123',
        version: '1.0'
      }))
    })

    it('应该正确销毁会话', async () => {
      const result = await sessionService.destroySession()

      expect(result.success).toBe(true)
      expect(uni.removeStorageSync).toHaveBeenCalledWith('session_info')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('last_login_phone')
    })

    it('应该正确检查会话有效性', async () => {
      // 设置有效会话
      userStore.setAuth({
        isLoggedIn: true,
        token: 'valid-token',
        tokenExpiry: Date.now() + 60 * 60 * 1000
      })

      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'auth_token') return 'valid-token'
        if (key === 'token_expiry') return Date.now() + 60 * 60 * 1000
        return null
      })

      const result = await sessionService.checkSession()

      expect(result.valid).toBe(true)
    })

    it('应该正确处理会话过期', () => {
      const sessionInfo = {
        lastActiveTime: Date.now() - 8 * 24 * 60 * 60 * 1000 // 8天前
      }

      uni.getStorageSync.mockReturnValue(sessionInfo)

      expect(sessionService.isSessionExpired()).toBe(true)
    })

    it('应该正确更新最后活跃时间', () => {
      const sessionInfo = {
        userId: 'user-123',
        lastActiveTime: Date.now() - 60 * 60 * 1000 // 1小时前
      }

      uni.getStorageSync.mockReturnValue(sessionInfo)

      sessionService.updateLastActiveTime()

      expect(uni.setStorageSync).toHaveBeenCalledWith('session_info', expect.objectContaining({
        userId: 'user-123',
        lastActiveTime: expect.any(Number)
      }))
    })
  })

  describe('用户Store集成', () => {
    it('应该正确处理登录', async () => {
      const loginData = {
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        tokenExpiry: Date.now() + 2 * 60 * 60 * 1000,
        userInfo: {
          id: 'user-123',
          phone: '13800138000',
          nickname: '测试用户'
        }
      }

      const result = await userStore.login(loginData, 'password')

      expect(result.success).toBe(true)
      expect(userStore.auth.isLoggedIn).toBe(true)
      expect(userStore.auth.loginMethod).toBe('password')
      expect(userStore.userInfo.id).toBe('user-123')
    })

    it('应该正确处理登出', async () => {
      // 先设置登录状态
      userStore.setAuth({
        isLoggedIn: true,
        token: 'test-token',
        loginMethod: 'password'
      })

      const result = await userStore.logout()

      expect(result.success).toBe(true)
      expect(userStore.auth.isLoggedIn).toBe(false)
      expect(userStore.auth.token).toBe('')
      expect(userStore.auth.loginMethod).toBe('')
    })

    it('应该正确检查认证状态', () => {
      // 未登录状态
      expect(userStore.isAuthenticated).toBe(false)

      // 设置登录状态但token无效
      userStore.setAuth({
        isLoggedIn: true,
        token: 'invalid-token'
      })

      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'auth_token') return 'invalid-token'
        if (key === 'token_expiry') return Date.now() - 60 * 60 * 1000 // 过期
        return null
      })

      expect(userStore.isAuthenticated).toBe(false)

      // 设置有效的登录状态
      uni.getStorageSync.mockImplementation((key) => {
        if (key === 'auth_token') return 'valid-token'
        if (key === 'token_expiry') return Date.now() + 60 * 60 * 1000 // 有效
        return null
      })

      expect(userStore.isAuthenticated).toBe(true)
    })

    it('应该正确处理用户设置', () => {
      const settings = {
        biometricEnabled: true,
        theme: 'dark',
        notificationEnabled: false
      }

      userStore.updateUserSettings(settings)

      expect(userStore.userSettings.biometricEnabled).toBe(true)
      expect(userStore.userSettings.theme).toBe('dark')
      expect(userStore.userSettings.notificationEnabled).toBe(false)
      expect(uni.setStorageSync).toHaveBeenCalledWith('user_settings', expect.objectContaining(settings))
    })

    it('应该正确获取用户显示名称', () => {
      // 有昵称时
      userStore.setUserInfo({ nickname: '测试用户', phone: '13800138000' })
      expect(userStore.displayName).toBe('测试用户')

      // 没有昵称时使用手机号
      userStore.setUserInfo({ nickname: '', phone: '13800138000' })
      expect(userStore.displayName).toBe('13800138000')

      // 都没有时使用默认值
      userStore.setUserInfo({ nickname: '', phone: '' })
      expect(userStore.displayName).toBe('用户')
    })
  })

  describe('自动刷新机制', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('应该在token即将过期时自动刷新', async () => {
      const mockRefreshToken = jest.spyOn(tokenService, 'refreshToken').mockResolvedValue({
        success: true,
        token: 'new-token'
      })

      // 设置即将过期的token
      const expiry = Date.now() + 5 * 60 * 1000 // 5分钟后过期
      tokenService.scheduleTokenRefresh(expiry)

      // 快进到刷新时间
      jest.advanceTimersByTime(5 * 60 * 1000)

      // 等待异步操作完成
      await jest.runAllTimersAsync()

      expect(mockRefreshToken).toHaveBeenCalled()
    })

    it('应该在刷新失败时清除token', async () => {
      const mockRefreshToken = jest.spyOn(tokenService, 'refreshToken').mockResolvedValue({
        success: false,
        message: '刷新失败'
      })

      const mockClearTokens = jest.spyOn(tokenService, 'clearTokens')

      await tokenService.refreshToken()

      expect(mockRefreshToken).toHaveBeenCalled()
      expect(mockClearTokens).toHaveBeenCalled()
      expect(uni.showModal).toHaveBeenCalledWith(expect.objectContaining({
        title: '登录已过期'
      }))
    })
  })
})