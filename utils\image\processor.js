/**
 * 图片处理工具类
 * 提供图片压缩、质量检查、格式转换等功能
 */

class ImageProcessor {
  constructor() {
    this.maxFileSize = 5 * 1024 * 1024; // 5MB
    this.minWidth = 300;
    this.minHeight = 300;
    this.maxWidth = 2048;
    this.maxHeight = 2048;
    this.defaultQuality = 0.8;
  }

  /**
   * 压缩图片
   * @param {string} imagePath - 图片路径
   * @param {number} quality - 压缩质量 (0-1)
   * @returns {Promise<string>} 压缩后的图片路径
   */
  async compressImage(imagePath, quality = this.defaultQuality) {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS || H5
      uni.compressImage({
        src: imagePath,
        quality: quality,
        success: (res) => {
          console.log('图片压缩成功:', res.tempFilePath);
          resolve(res.tempFilePath);
        },
        fail: (error) => {
          console.error('图片压缩失败:', error);
          // 压缩失败时返回原图片
          resolve(imagePath);
        }
      });
      // #endif
      
      // #ifdef MP-WEIXIN || MP-ALIPAY
      uni.compressImage({
        src: imagePath,
        quality: quality,
        success: (res) => {
          resolve(res.tempFilePath);
        },
        fail: (error) => {
          resolve(imagePath);
        }
      });
      // #endif
    });
  }

  /**
   * 检查图片质量
   * @param {string} imagePath - 图片路径
   * @returns {Promise<Object>} 质量检查结果
   */
  async validateImageQuality(imagePath) {
    return new Promise((resolve) => {
      // 检查路径有效性
      if (!imagePath || typeof imagePath !== 'string' || imagePath.trim() === '') {
        resolve({
          passed: false,
          issues: ['图片路径无效'],
          width: 0,
          height: 0,
          size: 0
        });
        return;
      }

      uni.getImageInfo({
        src: imagePath,
        success: (res) => {
          const result = {
            passed: true,
            issues: [],
            width: res.width,
            height: res.height,
            size: res.size || 0
          };

          // 检查分辨率
          if (res.width < this.minWidth || res.height < this.minHeight) {
            result.passed = false;
            result.issues.push('图片分辨率过低，建议重新拍摄更清晰的照片');
          }

          // 检查文件大小
          if (res.size && res.size > this.maxFileSize) {
            result.passed = false;
            result.issues.push('图片文件过大，将自动压缩');
          }

          // 检查宽高比
          const aspectRatio = res.width / res.height;
          if (aspectRatio < 0.5 || aspectRatio > 2) {
            result.issues.push('图片宽高比异常，可能影响识别效果');
          }

          resolve(result);
        },
        fail: (error) => {
          resolve({
            passed: false,
            issues: ['无法获取图片信息，请检查图片是否有效'],
            error: error
          });
        }
      });
    });
  }

  /**
   * 图片预处理（提高OCR识别率）
   * @param {string} imagePath - 图片路径
   * @returns {Promise<string>} 处理后的图片路径
   */
  async preprocessForOCR(imagePath) {
    try {
      // 首先检查图片质量
      const qualityCheck = await this.validateImageQuality(imagePath);
      
      let processedPath = imagePath;
      
      // 如果需要压缩
      if (qualityCheck.size > this.maxFileSize) {
        processedPath = await this.compressImage(processedPath, 0.7);
      }
      
      // 如果分辨率过高，进行适当缩放
      if (qualityCheck.width > this.maxWidth || qualityCheck.height > this.maxHeight) {
        processedPath = await this.resizeImage(processedPath);
      }
      
      return processedPath;
    } catch (error) {
      console.error('图片预处理失败:', error);
      return imagePath;
    }
  }

  /**
   * 调整图片尺寸
   * @param {string} imagePath - 图片路径
   * @returns {Promise<string>} 调整后的图片路径
   */
  async resizeImage(imagePath) {
    return new Promise((resolve) => {
      uni.getImageInfo({
        src: imagePath,
        success: (res) => {
          const { width, height } = res;
          let newWidth = width;
          let newHeight = height;
          
          // 计算新尺寸，保持宽高比
          if (width > this.maxWidth) {
            newWidth = this.maxWidth;
            newHeight = (height * this.maxWidth) / width;
          }
          
          if (newHeight > this.maxHeight) {
            newHeight = this.maxHeight;
            newWidth = (newWidth * this.maxHeight) / newHeight;
          }
          
          // 使用canvas进行缩放
          this.canvasResize(imagePath, newWidth, newHeight)
            .then(resolve)
            .catch(() => resolve(imagePath));
        },
        fail: () => resolve(imagePath)
      });
    });
  }

  /**
   * 使用Canvas调整图片尺寸
   * @param {string} imagePath - 图片路径
   * @param {number} width - 目标宽度
   * @param {number} height - 目标高度
   * @returns {Promise<string>} 调整后的图片路径
   */
  async canvasResize(imagePath, width, height) {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = width;
        canvas.height = height;
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          const url = URL.createObjectURL(blob);
          resolve(url);
        }, 'image/jpeg', 0.8);
      };
      
      img.onerror = reject;
      img.src = imagePath;
      // #endif
      
      // #ifdef APP-PLUS || MP
      // 在小程序和APP中，直接返回原图片
      // 实际项目中可以使用原生插件进行图片处理
      resolve(imagePath);
      // #endif
    });
  }

  /**
   * 生成图片缩略图
   * @param {string} imagePath - 图片路径
   * @param {number} size - 缩略图尺寸
   * @returns {Promise<string>} 缩略图路径
   */
  async generateThumbnail(imagePath, size = 150) {
    try {
      const compressed = await this.compressImage(imagePath, 0.6);
      return await this.canvasResize(compressed, size, size);
    } catch (error) {
      console.error('生成缩略图失败:', error);
      return imagePath;
    }
  }
}

module.exports = new ImageProcessor();