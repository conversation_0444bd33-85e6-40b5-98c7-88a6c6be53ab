/**
 * 基础模型类
 * 提供数据模型的基础功能
 */

const { ValidationRules } = require('../../types/index.js')

class BaseModel {
  constructor(data = {}) {
    this.id = data.id || this.generateId()
    this.createdAt = data.createdAt || new Date()
    this.updatedAt = data.updatedAt || new Date()
    
    // 初始化其他属性
    this.initializeProperties(data)
  }
  
  /**
   * 初始化属性
   * 子类应该重写此方法来初始化特定属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    // 子类实现
  }
  
  /**
   * 生成唯一ID
   * @returns {String} 唯一ID
   */
  generateId() {
    return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  /**
   * 验证模型数据
   * @returns {Object} 验证结果 {isValid: Boolean, errors: Array}
   */
  validate() {
    const errors = []
    const validationRules = this.getValidationRules()
    
    for (const [field, rule] of Object.entries(validationRules)) {
      const value = this[field]
      const fieldErrors = this.validateField(field, value, rule)
      if (fieldErrors.length > 0) {
        errors.push(...fieldErrors)
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 验证单个字段
   * @param {String} field - 字段名
   * @param {*} value - 字段值
   * @param {Object} rule - 验证规则
   * @returns {Array} 错误列表
   */
  validateField(field, value, rule) {
    const errors = []
    
    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field,
        message: rule.requiredMessage || `${field}是必填项`
      })
      return errors
    }
    
    // 如果值为空且不是必填，跳过其他验证
    if (value === undefined || value === null || value === '') {
      return errors
    }
    
    // 类型验证
    if (rule.type && typeof value !== rule.type) {
      errors.push({
        field,
        message: rule.typeMessage || `${field}类型不正确`
      })
    }
    
    // 长度验证
    if (rule.minLength && value.length < rule.minLength) {
      errors.push({
        field,
        message: rule.minLengthMessage || `${field}长度不能少于${rule.minLength}位`
      })
    }
    
    if (rule.maxLength && value.length > rule.maxLength) {
      errors.push({
        field,
        message: rule.maxLengthMessage || `${field}长度不能超过${rule.maxLength}位`
      })
    }
    
    // 正则验证
    if (rule.pattern && !rule.pattern.test(value)) {
      errors.push({
        field,
        message: rule.message || `${field}格式不正确`
      })
    }
    
    // 自定义验证
    if (rule.validator && typeof rule.validator === 'function') {
      const customResult = rule.validator(value)
      if (customResult !== true) {
        errors.push({
          field,
          message: customResult || `${field}验证失败`
        })
      }
    }
    
    return errors
  }
  
  /**
   * 获取验证规则
   * 子类应该重写此方法来定义验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {}
  }
  
  /**
   * 转换为JSON对象
   * @returns {Object} JSON对象
   */
  toJSON() {
    const json = {}
    const properties = this.getSerializableProperties()
    
    for (const prop of properties) {
      if (this.hasOwnProperty(prop)) {
        json[prop] = this[prop]
      }
    }
    
    return json
  }
  
  /**
   * 获取可序列化的属性列表
   * 子类可以重写此方法来控制序列化的属性
   * @returns {Array} 属性名列表
   */
  getSerializableProperties() {
    return Object.keys(this)
  }
  
  /**
   * 从JSON对象创建模型实例
   * @param {Object} json - JSON对象
   * @returns {BaseModel} 模型实例
   */
  static fromJSON(json) {
    return new this(json)
  }
  
  /**
   * 更新模型数据
   * @param {Object} data - 更新数据
   */
  update(data) {
    for (const [key, value] of Object.entries(data)) {
      if (this.hasOwnProperty(key) && key !== 'id' && key !== 'createdAt') {
        this[key] = value
      }
    }
    this.updatedAt = new Date()
  }
  
  /**
   * 克隆模型
   * @returns {BaseModel} 克隆的模型实例
   */
  clone() {
    const json = this.toJSON()
    delete json.id // 克隆时生成新ID
    return this.constructor.fromJSON(json)
  }
  
  /**
   * 比较两个模型是否相等
   * @param {BaseModel} other - 另一个模型
   * @returns {Boolean} 是否相等
   */
  equals(other) {
    if (!other || this.constructor !== other.constructor) {
      return false
    }
    
    return this.id === other.id
  }
  
  /**
   * 获取模型的哈希值
   * @returns {String} 哈希值
   */
  getHash() {
    const json = this.toJSON()
    return this.hashCode(JSON.stringify(json))
  }
  
  /**
   * 计算字符串的哈希值
   * @param {String} str - 字符串
   * @returns {String} 哈希值
   */
  hashCode(str) {
    let hash = 0
    if (str.length === 0) return hash.toString()
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }
}

module.exports = { BaseModel }