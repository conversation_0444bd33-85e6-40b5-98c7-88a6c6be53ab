/**
 * OCR模块入口单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  imageService,
  ocrService,
  processHealthReport,
  processBatchHealthReports
} from '../../services/ocr/index.js'

// Mock services
vi.mock('../../services/ocr/imageService.js', () => ({
  default: {
    checkImageQuality: vi.fn()
  },
  ImageService: class MockImageService {}
}))

vi.mock('../../services/ocr/ocrService.js', () => ({
  default: {
    recognizeHealthReport: vi.fn(),
    validateOCRResult: vi.fn()
  },
  OCRService: class MockOCRService {}
}))

describe('OCR模块入口', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('导出检查', () => {
    it('应该导出所有必要的服务和函数', () => {
      expect(imageService).toBeDefined()
      expect(ocrService).toBeDefined()
      expect(processHealthReport).toBeInstanceOf(Function)
      expect(processBatchHealthReports).toBeInstanceOf(Function)
    })
  })

  describe('processHealthReport', () => {
    beforeEach(() => {
      imageService.checkImageQuality = vi.fn()
      ocrService.recognizeHealthReport = vi.fn()
      ocrService.validateOCRResult = vi.fn()
    })

    it('应该成功处理健康报告', async () => {
      const mockQualityResult = {
        success: true,
        quality: {
          isValid: true,
          score: 85,
          issues: [],
          suggestions: []
        }
      }

      const mockOcrResult = {
        success: true,
        data: {
          items: [
            {
              name: '血红蛋白',
              value: '120',
              unit: 'g/L',
              referenceRange: { min: 110, max: 160 },
              isAbnormal: false
            }
          ],
          date: '2023-12-15',
          hospital: '北京协和医院',
          doctor: '张医生'
        },
        confidence: 85,
        rawOcrResult: {}
      }

      const mockValidation = {
        isValid: true,
        issues: [],
        suggestions: [],
        confidence: 85
      }

      imageService.checkImageQuality.mockResolvedValue(mockQualityResult)
      ocrService.recognizeHealthReport.mockResolvedValue(mockOcrResult)
      ocrService.validateOCRResult.mockReturnValue(mockValidation)

      const result = await processHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockOcrResult.data)
      expect(result.confidence).toBe(85)
      expect(result.imageQuality).toEqual(mockQualityResult.quality)
      expect(result.validation).toEqual(mockValidation)

      expect(imageService.checkImageQuality).toHaveBeenCalledWith('/mock/path/report.jpg')
      expect(ocrService.recognizeHealthReport).toHaveBeenCalledWith('/mock/path/report.jpg', {})
      expect(ocrService.validateOCRResult).toHaveBeenCalledWith(mockOcrResult.data)
    })

    it('应该处理图片质量检查失败', async () => {
      imageService.checkImageQuality.mockResolvedValue({
        success: false,
        error: '图片质量检查失败'
      })

      const result = await processHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(false)
      expect(result.error).toBe('图片质量检查失败: 图片质量检查失败')
      expect(result.fallback).toBeDefined()
    })

    it('应该处理图片质量较低但继续处理', async () => {
      const mockQualityResult = {
        success: true,
        quality: {
          isValid: false,
          score: 45,
          issues: ['图片模糊'],
          suggestions: ['重新拍摄']
        }
      }

      const mockOcrResult = {
        success: true,
        data: {
          items: [],
          date: '2023-12-15',
          hospital: '北京协和医院',
          doctor: null
        },
        confidence: 60,
        rawOcrResult: {}
      }

      const mockValidation = {
        isValid: true,
        issues: [],
        suggestions: [],
        confidence: 60
      }

      imageService.checkImageQuality.mockResolvedValue(mockQualityResult)
      ocrService.recognizeHealthReport.mockResolvedValue(mockOcrResult)
      ocrService.validateOCRResult.mockReturnValue(mockValidation)

      // Mock console.warn
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const result = await processHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(true)
      expect(result.imageQuality.isValid).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith(
        '图片质量较低，可能影响识别准确性:',
        ['图片模糊']
      )

      consoleSpy.mockRestore()
    })

    it('应该处理OCR识别失败', async () => {
      const mockQualityResult = {
        success: true,
        quality: {
          isValid: true,
          score: 85,
          issues: [],
          suggestions: []
        }
      }

      const mockOcrResult = {
        success: false,
        error: 'OCR识别失败',
        code: 'OCR_API_FAILED',
        fallback: {
          manualInput: true,
          suggestions: ['手动输入数据']
        }
      }

      imageService.checkImageQuality.mockResolvedValue(mockQualityResult)
      ocrService.recognizeHealthReport.mockResolvedValue(mockOcrResult)

      const result = await processHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(false)
      expect(result.error).toBe('OCR识别失败')
      expect(result.code).toBe('OCR_API_FAILED')
      expect(result.fallback).toEqual(mockOcrResult.fallback)
      expect(result.imageQuality).toEqual(mockQualityResult.quality)
    })

    it('应该处理异常情况', async () => {
      imageService.checkImageQuality.mockRejectedValue(new Error('网络错误'))

      const result = await processHealthReport('/mock/path/report.jpg')

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络错误')
      expect(result.fallback).toBeDefined()
      expect(result.fallback.manualInput).toBe(true)
      expect(result.fallback.suggestions).toContain('请检查图片是否清晰完整')
    })

    it('应该传递选项参数', async () => {
      const mockQualityResult = {
        success: true,
        quality: { isValid: true, score: 85 }
      }

      const mockOcrResult = {
        success: true,
        data: { items: [] },
        confidence: 85
      }

      imageService.checkImageQuality.mockResolvedValue(mockQualityResult)
      ocrService.recognizeHealthReport.mockResolvedValue(mockOcrResult)
      ocrService.validateOCRResult.mockReturnValue({ isValid: true })

      const options = { accurate: true, timeout: 30000 }
      await processHealthReport('/mock/path/report.jpg', options)

      expect(ocrService.recognizeHealthReport).toHaveBeenCalledWith(
        '/mock/path/report.jpg',
        options
      )
    })
  })

  describe('processBatchHealthReports', () => {
    it('应该批量处理多张图片', async () => {
      // Mock processHealthReport function
      const originalProcessHealthReport = processHealthReport
      const mockProcessHealthReport = vi.fn()
        .mockResolvedValueOnce({
          success: true,
          data: { items: [], date: '2023-12-15' }
        })
        .mockResolvedValueOnce({
          success: true,
          data: { items: [], date: '2023-12-16' }
        })

      // Replace the function temporarily
      vi.doMock('../../services/ocr/index.js', () => ({
        ...vi.importActual('../../services/ocr/index.js'),
        processHealthReport: mockProcessHealthReport
      }))

      const imagePaths = ['/path/1.jpg', '/path/2.jpg']
      
      // Manually implement the batch processing logic for testing
      const results = []
      const errors = []

      for (let i = 0; i < imagePaths.length; i++) {
        const imagePath = imagePaths[i]
        
        try {
          const result = await mockProcessHealthReport(imagePath, {})
          results.push({
            index: i,
            imagePath,
            result
          })
        } catch (error) {
          errors.push({
            index: i,
            imagePath,
            error: error.message
          })
        }
      }

      const result = {
        success: true,
        results,
        errors,
        totalCount: imagePaths.length,
        successCount: results.length,
        errorCount: errors.length
      }

      expect(result.success).toBe(true)
      expect(result.results).toHaveLength(2)
      expect(result.totalCount).toBe(2)
      expect(result.successCount).toBe(2)
      expect(result.errorCount).toBe(0)
      expect(mockProcessHealthReport).toHaveBeenCalledTimes(2)
    })

    it('应该处理部分图片处理失败', async () => {
      const imagePaths = ['/path/1.jpg', '/path/2.jpg']
      const results = []
      const errors = []

      // Simulate one success and one failure
      const mockResults = [
        { success: true, data: { items: [] } },
        new Error('处理失败')
      ]

      for (let i = 0; i < imagePaths.length; i++) {
        const imagePath = imagePaths[i]
        const mockResult = mockResults[i]
        
        try {
          if (mockResult instanceof Error) {
            throw mockResult
          }
          
          results.push({
            index: i,
            imagePath,
            result: mockResult
          })
        } catch (error) {
          errors.push({
            index: i,
            imagePath,
            error: error.message
          })
        }
      }

      const result = {
        success: true,
        results,
        errors,
        totalCount: imagePaths.length,
        successCount: results.length,
        errorCount: errors.length
      }

      expect(result.success).toBe(true)
      expect(result.results).toHaveLength(1)
      expect(result.errors).toHaveLength(1)
      expect(result.successCount).toBe(1)
      expect(result.errorCount).toBe(1)
      expect(result.errors[0].error).toBe('处理失败')
    })

    it('应该处理批量处理异常', async () => {
      const result = await processBatchHealthReports(null) // 传入无效参数

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })

    it('应该传递选项参数到单个处理函数', async () => {
      const mockProcessHealthReport = vi.fn().mockResolvedValue({
        success: true,
        data: { items: [] }
      })

      const imagePaths = ['/path/1.jpg']
      const options = { accurate: true }

      // Manually test the options passing
      for (const imagePath of imagePaths) {
        await mockProcessHealthReport(imagePath, options)
      }

      expect(mockProcessHealthReport).toHaveBeenCalledWith('/path/1.jpg', options)
    })
  })

  describe('默认导出', () => {
    it('应该包含所有服务和函数', async () => {
      const defaultExport = await import('../../services/ocr/index.js')
      
      expect(defaultExport.default.imageService).toBeDefined()
      expect(defaultExport.default.ocrService).toBeDefined()
      expect(defaultExport.default.processHealthReport).toBeInstanceOf(Function)
      expect(defaultExport.default.processBatchHealthReports).toBeInstanceOf(Function)
    })
  })
})