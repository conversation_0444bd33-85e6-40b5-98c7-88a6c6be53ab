/**
 * 数据加密服务
 * 提供AES-256加密算法的数据加密和解密功能
 */

const { Constants } = require('../../types/index.js')
const { logger } = require('../logger/Logger.js')

// 模拟CryptoJS的功能（在实际项目中应该使用真正的CryptoJS）
const CryptoJS = {
  AES: {
    encrypt: (data, key, options) => {
      // 简单的加密模拟，实际应使用真正的AES加密
      const encrypted = Buffer.from(data + '|' + key).toString('base64')
      return { toString: () => encrypted }
    },
    decrypt: (encryptedData, key) => {
      // 简单的解密模拟，实际应使用真正的AES解密
      try {
        const decrypted = Buffer.from(encryptedData, 'base64').toString()
        const parts = decrypted.split('|')
        if (parts.length !== 2 || parts[1] !== key) {
          throw new Error('Invalid key or corrupted data')
        }
        return { toString: (encoding) => parts[0] }
      } catch (error) {
        throw new Error('Decryption failed')
      }
    }
  },
  mode: {
    CBC: 'CBC'
  },
  pad: {
    Pkcs7: 'Pkcs7'
  },
  enc: {
    Utf8: 'Utf8'
  },
  lib: {
    WordArray: {
      random: (size) => ({
        toString: () => Math.random().toString(36).substring(2, 2 + size)
      })
    }
  },
  SHA256: (data) => ({
    toString: () => Buffer.from(data).toString('base64')
  })
}

class EncryptionService {
  constructor(options = {}) {
    this.options = {
      algorithm: 'AES-256-CBC',
      keySize: 256,
      ivSize: 128,
      saltSize: 128,
      iterations: 10000,
      ...options
    }
    
    // 主密钥（在实际项目中应该从安全的地方获取）
    this.masterKey = options.masterKey || this.generateMasterKey()
    
    // 敏感字段列表
    this.sensitiveFields = [
      'password',
      'passwordHash',
      'salt',
      'verificationToken',
      'phone', // 根据需求可能需要加密手机号
      'notes', // 报告备注可能包含敏感信息
      'originalImage' // 图片路径可能需要加密
    ]
  }
  
  /**
   * 生成主密钥
   * @returns {String} 主密钥
   */
  generateMasterKey() {
    return CryptoJS.lib.WordArray.random(this.options.keySize / 8).toString()
  }
  
  /**
   * 生成随机密钥
   * @returns {String} 随机密钥
   */
  generateKey() {
    return CryptoJS.lib.WordArray.random(this.options.keySize / 8).toString()
  }
  
  /**
   * 生成随机IV
   * @returns {String} 随机IV
   */
  generateIV() {
    return CryptoJS.lib.WordArray.random(this.options.ivSize / 8).toString()
  }
  
  /**
   * 加密数据
   * @param {String} data - 要加密的数据
   * @param {String} key - 加密密钥（可选，默认使用主密钥）
   * @returns {String} 加密后的数据
   */
  encrypt(data, key = null) {
    try {
      if (!data || typeof data !== 'string') {
        throw new Error('数据必须是非空字符串')
      }
      
      const encryptionKey = key || this.masterKey
      
      const encrypted = CryptoJS.AES.encrypt(
        data,
        encryptionKey,
        {
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        }
      )
      
      const result = encrypted.toString()
      
      logger.debug('数据加密成功', { 
        dataLength: data.length,
        encryptedLength: result.length 
      })
      
      return result
    } catch (error) {
      logger.error('数据加密失败', { error: error.message })
      throw new Error(`加密失败: ${error.message}`)
    }
  }
  
  /**
   * 解密数据
   * @param {String} encryptedData - 加密的数据
   * @param {String} key - 解密密钥（可选，默认使用主密钥）
   * @returns {String} 解密后的数据
   */
  decrypt(encryptedData, key = null) {
    try {
      if (!encryptedData || typeof encryptedData !== 'string') {
        throw new Error('加密数据必须是非空字符串')
      }
      
      const decryptionKey = key || this.masterKey
      
      const decrypted = CryptoJS.AES.decrypt(encryptedData, decryptionKey)
      const result = decrypted.toString(CryptoJS.enc.Utf8)
      
      if (!result) {
        throw new Error('解密失败，可能是密钥错误或数据损坏')
      }
      
      logger.debug('数据解密成功', { 
        encryptedLength: encryptedData.length,
        decryptedLength: result.length 
      })
      
      return result
    } catch (error) {
      logger.error('数据解密失败', { error: error.message })
      throw new Error(`解密失败: ${error.message}`)
    }
  }
  
  /**
   * 加密对象中的敏感字段
   * @param {Object} obj - 要加密的对象
   * @param {Array} fields - 要加密的字段列表（可选）
   * @returns {Object} 加密后的对象
   */
  encryptObject(obj, fields = null) {
    try {
      if (!obj || typeof obj !== 'object') {
        throw new Error('输入必须是对象')
      }
      
      const fieldsToEncrypt = fields || this.sensitiveFields
      const encryptedObj = { ...obj }
      
      for (const field of fieldsToEncrypt) {
        if (encryptedObj.hasOwnProperty(field) && encryptedObj[field] !== null && encryptedObj[field] !== undefined) {
          const value = encryptedObj[field]
          
          if (typeof value === 'string' && value.length > 0) {
            encryptedObj[field] = this.encrypt(value)
            encryptedObj[`${field}_encrypted`] = true
            encryptedObj[`${field}_type`] = 'string'
          } else if (typeof value === 'number') {
            // 对于数字类型，转换为字符串再加密
            encryptedObj[field] = this.encrypt(value.toString())
            encryptedObj[`${field}_encrypted`] = true
            encryptedObj[`${field}_type`] = 'number'
          } else if (typeof value === 'object') {
            // 对于对象类型，先序列化再加密
            encryptedObj[field] = this.encrypt(JSON.stringify(value))
            encryptedObj[`${field}_encrypted`] = true
            encryptedObj[`${field}_type`] = 'object'
          }
        }
      }
      
      logger.debug('对象加密完成', { 
        fieldsEncrypted: fieldsToEncrypt.filter(f => encryptedObj[`${f}_encrypted`])
      })
      
      return encryptedObj
    } catch (error) {
      logger.error('对象加密失败', { error: error.message })
      throw new Error(`对象加密失败: ${error.message}`)
    }
  }
  
  /**
   * 解密对象中的敏感字段
   * @param {Object} obj - 要解密的对象
   * @param {Array} fields - 要解密的字段列表（可选）
   * @returns {Object} 解密后的对象
   */
  decryptObject(obj, fields = null) {
    try {
      if (!obj || typeof obj !== 'object') {
        throw new Error('输入必须是对象')
      }
      
      const fieldsToDecrypt = fields || this.sensitiveFields
      const decryptedObj = { ...obj }
      
      for (const field of fieldsToDecrypt) {
        if (decryptedObj.hasOwnProperty(field) && decryptedObj[`${field}_encrypted`]) {
          try {
            const encryptedValue = decryptedObj[field]
            const decryptedValue = this.decrypt(encryptedValue)
            const originalType = decryptedObj[`${field}_type`]
            
            // 根据原始类型恢复数据
            if (originalType === 'number') {
              decryptedObj[field] = Number(decryptedValue)
            } else if (originalType === 'object') {
              try {
                decryptedObj[field] = JSON.parse(decryptedValue)
              } catch {
                decryptedObj[field] = decryptedValue
              }
            } else {
              // 默认为字符串类型
              decryptedObj[field] = decryptedValue
            }
            
            // 删除加密标记和类型标记
            delete decryptedObj[`${field}_encrypted`]
            delete decryptedObj[`${field}_type`]
          } catch (error) {
            logger.warn(`字段 ${field} 解密失败`, { error: error.message })
            // 保持原值，不抛出错误
          }
        }
      }
      
      logger.debug('对象解密完成')
      
      return decryptedObj
    } catch (error) {
      logger.error('对象解密失败', { error: error.message })
      throw new Error(`对象解密失败: ${error.message}`)
    }
  }
  
  /**
   * 生成数据哈希
   * @param {String} data - 要哈希的数据
   * @returns {String} 哈希值
   */
  hash(data) {
    try {
      if (!data || typeof data !== 'string') {
        throw new Error('数据必须是非空字符串')
      }
      
      const hash = CryptoJS.SHA256(data).toString()
      
      logger.debug('数据哈希生成成功', { 
        dataLength: data.length,
        hashLength: hash.length 
      })
      
      return hash
    } catch (error) {
      logger.error('数据哈希生成失败', { error: error.message })
      throw new Error(`哈希生成失败: ${error.message}`)
    }
  }
  
  /**
   * 验证数据完整性
   * @param {String} data - 原始数据
   * @param {String} hash - 预期的哈希值
   * @returns {Boolean} 是否匹配
   */
  verifyHash(data, hash) {
    try {
      const computedHash = this.hash(data)
      const isValid = computedHash === hash
      
      logger.debug('数据完整性验证', { isValid })
      
      return isValid
    } catch (error) {
      logger.error('数据完整性验证失败', { error: error.message })
      return false
    }
  }
  
  /**
   * 加密文件内容
   * @param {String} content - 文件内容
   * @param {String} filename - 文件名（用于生成密钥）
   * @returns {Object} 加密结果
   */
  encryptFile(content, filename) {
    try {
      if (!content || typeof content !== 'string') {
        throw new Error('文件内容必须是非空字符串')
      }
      
      // 为文件生成唯一密钥
      const fileKey = this.hash(filename + this.masterKey).substring(0, 32)
      const encryptedContent = this.encrypt(content, fileKey)
      const contentHash = this.hash(content)
      
      const result = {
        encryptedContent,
        contentHash,
        filename,
        encryptedAt: new Date().toISOString()
      }
      
      logger.debug('文件加密成功', { 
        filename,
        originalSize: content.length,
        encryptedSize: encryptedContent.length 
      })
      
      return result
    } catch (error) {
      logger.error('文件加密失败', { filename, error: error.message })
      throw new Error(`文件加密失败: ${error.message}`)
    }
  }
  
  /**
   * 解密文件内容
   * @param {Object} encryptedFile - 加密的文件对象
   * @returns {String} 解密后的文件内容
   */
  decryptFile(encryptedFile) {
    try {
      if (!encryptedFile || !encryptedFile.encryptedContent || !encryptedFile.filename) {
        throw new Error('无效的加密文件对象')
      }
      
      // 重新生成文件密钥
      const fileKey = this.hash(encryptedFile.filename + this.masterKey).substring(0, 32)
      const decryptedContent = this.decrypt(encryptedFile.encryptedContent, fileKey)
      
      // 验证文件完整性
      if (encryptedFile.contentHash) {
        const isValid = this.verifyHash(decryptedContent, encryptedFile.contentHash)
        if (!isValid) {
          throw new Error('文件完整性验证失败')
        }
      }
      
      logger.debug('文件解密成功', { 
        filename: encryptedFile.filename,
        decryptedSize: decryptedContent.length 
      })
      
      return decryptedContent
    } catch (error) {
      logger.error('文件解密失败', { 
        filename: encryptedFile?.filename,
        error: error.message 
      })
      throw new Error(`文件解密失败: ${error.message}`)
    }
  }
  
  /**
   * 批量加密数据
   * @param {Array} dataList - 数据列表
   * @param {Function} encryptFn - 加密函数
   * @returns {Array} 加密后的数据列表
   */
  async batchEncrypt(dataList, encryptFn = null) {
    try {
      if (!Array.isArray(dataList)) {
        throw new Error('数据列表必须是数组')
      }
      
      const encryptFunction = encryptFn || ((data) => this.encryptObject(data))
      const results = []
      
      for (const data of dataList) {
        try {
          const encrypted = encryptFunction(data)
          results.push(encrypted)
        } catch (error) {
          logger.warn('批量加密中的单项失败', { error: error.message })
          results.push(data) // 保持原数据
        }
      }
      
      logger.debug('批量加密完成', { 
        total: dataList.length,
        successful: results.length 
      })
      
      return results
    } catch (error) {
      logger.error('批量加密失败', { error: error.message })
      throw new Error(`批量加密失败: ${error.message}`)
    }
  }
  
  /**
   * 批量解密数据
   * @param {Array} encryptedDataList - 加密数据列表
   * @param {Function} decryptFn - 解密函数
   * @returns {Array} 解密后的数据列表
   */
  async batchDecrypt(encryptedDataList, decryptFn = null) {
    try {
      if (!Array.isArray(encryptedDataList)) {
        throw new Error('加密数据列表必须是数组')
      }
      
      const decryptFunction = decryptFn || ((data) => this.decryptObject(data))
      const results = []
      
      for (const encryptedData of encryptedDataList) {
        try {
          const decrypted = decryptFunction(encryptedData)
          results.push(decrypted)
        } catch (error) {
          logger.warn('批量解密中的单项失败', { error: error.message })
          results.push(encryptedData) // 保持原数据
        }
      }
      
      logger.debug('批量解密完成', { 
        total: encryptedDataList.length,
        successful: results.length 
      })
      
      return results
    } catch (error) {
      logger.error('批量解密失败', { error: error.message })
      throw new Error(`批量解密失败: ${error.message}`)
    }
  }
  
  /**
   * 获取加密服务信息
   * @returns {Object} 服务信息
   */
  getServiceInfo() {
    return {
      algorithm: this.options.algorithm,
      keySize: this.options.keySize,
      ivSize: this.options.ivSize,
      saltSize: this.options.saltSize,
      iterations: this.options.iterations,
      sensitiveFields: this.sensitiveFields,
      hasMasterKey: !!this.masterKey
    }
  }
  
  /**
   * 更新敏感字段列表
   * @param {Array} fields - 新的敏感字段列表
   */
  updateSensitiveFields(fields) {
    if (!Array.isArray(fields)) {
      throw new Error('敏感字段列表必须是数组')
    }
    
    this.sensitiveFields = [...fields]
    
    logger.info('敏感字段列表已更新', { 
      fields: this.sensitiveFields 
    })
  }
  
  /**
   * 添加敏感字段
   * @param {String|Array} fields - 要添加的字段
   */
  addSensitiveFields(fields) {
    const fieldsToAdd = Array.isArray(fields) ? fields : [fields]
    
    for (const field of fieldsToAdd) {
      if (!this.sensitiveFields.includes(field)) {
        this.sensitiveFields.push(field)
      }
    }
    
    logger.info('敏感字段已添加', { 
      added: fieldsToAdd,
      current: this.sensitiveFields 
    })
  }
  
  /**
   * 移除敏感字段
   * @param {String|Array} fields - 要移除的字段
   */
  removeSensitiveFields(fields) {
    const fieldsToRemove = Array.isArray(fields) ? fields : [fields]
    
    this.sensitiveFields = this.sensitiveFields.filter(
      field => !fieldsToRemove.includes(field)
    )
    
    logger.info('敏感字段已移除', { 
      removed: fieldsToRemove,
      current: this.sensitiveFields 
    })
  }
}

// 创建默认实例
const encryptionService = new EncryptionService()

module.exports = { EncryptionService, encryptionService }