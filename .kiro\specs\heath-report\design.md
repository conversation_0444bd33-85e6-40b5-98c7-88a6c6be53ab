# 个人健康检查报告管理APP设计文档

## 概述

本设计文档基于需求文档，为健康检查报告管理APP提供详细的技术架构和实现方案。应用采用uni-app框架开发，支持多平台部署（H5、小程序、APP），使用Vue 3 + Pinia状态管理，实现用户账户管理、报告数据录入、数据分析可视化等核心功能。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    C --> D[本地存储]
    C --> E[云端服务]
    
    A --> A1[页面组件]
    A --> A2[通用组件]
    A --> A3[布局组件]
    
    B --> B1[状态管理 Pinia]
    B --> B2[业务服务]
    B --> B3[工具函数]
    
    C --> C1[本地数据库]
    C --> C2[文件存储]
    C --> C3[网络请求]
    
    D --> D1[SQLite/IndexedDB]
    D --> D2[本地文件系统]
    
    E --> E1[用户认证服务]
    E --> E2[数据同步服务]
    E --> E3[OCR识别服务]
```

### 技术栈

- **前端框架**: uni-app (Vue 3)
- **状态管理**: Pinia
- **UI组件**: 自定义组件库
- **本地存储**: SQLite (APP) / IndexedDB (H5) / 小程序存储API
- **图表库**: uCharts 或 ECharts
- **加密**: CryptoJS (AES-256)
- **网络请求**: uni.request 封装
- **OCR服务**: 百度OCR API / 腾讯OCR API

## 组件和接口设计

### 核心组件结构

```
components/
├── business/           # 业务组件
│   ├── ReportCard/     # 报告卡片组件
│   ├── ChartView/      # 图表展示组件
│   ├── CameraUpload/   # 拍照上传组件
│   ├── OCRResult/      # OCR结果展示组件
│   └── HealthTrend/    # 健康趋势组件
├── common/             # 通用组件
│   ├── Button/         # 按钮组件
│   ├── Input/          # 输入框组件
│   ├── Modal/          # 弹窗组件
│   ├── Loading/        # 加载组件
│   └── Empty/          # 空状态组件
└── layout/             # 布局组件
    ├── Header/         # 头部组件
    ├── TabBar/         # 底部导航
    └── Container/      # 容器组件
```

### 状态管理设计

```javascript
// stores/
├── app.js          # 应用全局状态
├── user.js         # 用户状态管理
├── report.js       # 报告数据管理
└── sync.js         # 数据同步管理

// 用户状态示例
const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    isLoggedIn: false,
    token: '',
    biometricEnabled: false
  }),
  actions: {
    async login(credentials),
    async register(userInfo),
    async logout(),
    async resetPassword(phone),
    async enableBiometric()
  }
})
```

### 服务层设计

```javascript
// services/
├── auth/           # 认证服务
│   ├── login.js    # 登录服务
│   ├── register.js # 注册服务
│   └── biometric.js # 生物识别
├── ocr/            # OCR识别服务
│   ├── baidu.js    # 百度OCR
│   ├── tencent.js  # 腾讯OCR
│   └── parser.js   # 结果解析
├── report/         # 报告服务
│   ├── crud.js     # 增删改查
│   ├── export.js   # 数据导出
│   └── analysis.js # 数据分析
├── analytics/      # 分析服务
│   ├── trend.js    # 趋势分析
│   ├── chart.js    # 图表生成
│   └── report.js   # 报告生成
├── security/       # 安全服务
│   ├── encrypt.js  # 数据加密
│   ├── auth.js     # 权限验证
│   └── monitor.js  # 安全监控
└── sync/           # 同步服务
    ├── upload.js   # 数据上传
    ├── download.js # 数据下载
    └── conflict.js # 冲突解决
```

## 数据模型

### 用户模型 (User)

```javascript
{
  id: String,           // 用户ID
  phone: String,        // 手机号
  nickname: String,     // 昵称
  avatar: String,       // 头像URL
  gender: String,       // 性别 (male/female/other)
  birthday: Date,       // 生日
  height: Number,       // 身高(cm)
  weight: Number,       // 体重(kg)
  createdAt: Date,      // 创建时间
  updatedAt: Date,      // 更新时间
  settings: {           // 用户设置
    biometricEnabled: Boolean,
    autoSync: Boolean,
    notificationEnabled: Boolean,
    theme: String       // light/dark/auto
  }
}
```

### 报告模型 (Report)

```javascript
{
  id: String,           // 报告ID
  userId: String,       // 用户ID
  title: String,        // 报告标题
  hospital: String,     // 医院名称
  doctor: String,       // 医生姓名
  checkDate: Date,      // 检查日期
  reportDate: Date,     // 报告日期
  originalImage: String, // 原始图片路径
  items: [              // 检查项目列表
    {
      id: String,       // 项目ID
      name: String,     // 项目名称
      value: String,    // 检查结果
      unit: String,     // 单位
      referenceRange: String, // 参考范围
      isAbnormal: Boolean,    // 是否异常
      category: String  // 分类 (血常规/生化/免疫等)
    }
  ],
  tags: [String],       // 标签
  notes: String,        // 备注
  createdAt: Date,      // 创建时间
  updatedAt: Date,      // 更新时间
  syncStatus: String    // 同步状态 (local/synced/pending)
}
```

### 分析结果模型 (Analysis)

```javascript
{
  id: String,           // 分析ID
  userId: String,       // 用户ID
  type: String,         // 分析类型 (trend/comparison/summary)
  timeRange: {          // 时间范围
    start: Date,
    end: Date
  },
  items: [String],      // 分析项目
  results: {            // 分析结果
    trends: [            // 趋势数据
      {
        item: String,    // 项目名称
        data: [          // 数据点
          {
            date: Date,
            value: Number,
            isAbnormal: Boolean
          }
        ],
        trend: String    // 趋势方向 (up/down/stable)
      }
    ],
    summary: String,     // 分析摘要
    recommendations: [String] // 建议
  },
  createdAt: Date       // 创建时间
}
```

## 错误处理

### 错误分类和处理策略

```javascript
// utils/errorHandler.js
const ErrorTypes = {
  NETWORK_ERROR: 'network_error',      // 网络错误
  AUTH_ERROR: 'auth_error',            // 认证错误
  VALIDATION_ERROR: 'validation_error', // 验证错误
  OCR_ERROR: 'ocr_error',              // OCR识别错误
  STORAGE_ERROR: 'storage_error',      // 存储错误
  PERMISSION_ERROR: 'permission_error', // 权限错误
  UNKNOWN_ERROR: 'unknown_error'       // 未知错误
}

const errorHandler = {
  // 全局错误处理
  handleError(error, context) {
    const errorInfo = this.parseError(error)
    this.logError(errorInfo, context)
    this.showUserFriendlyMessage(errorInfo)
    this.reportError(errorInfo)
  },
  
  // 网络错误重试机制
  async retryRequest(requestFn, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        if (i === maxRetries - 1) throw error
        await this.delay(1000 * Math.pow(2, i)) // 指数退避
      }
    }
  }
}
```

### OCR错误处理

```javascript
// services/ocr/errorHandler.js
const ocrErrorHandler = {
  // OCR识别失败处理
  handleOCRFailure(error, imageData) {
    const fallbackStrategies = [
      () => this.tryAlternativeOCRService(imageData),
      () => this.suggestImageImprovement(),
      () => this.enableManualInput()
    ]
    
    return this.executeStrategies(fallbackStrategies)
  },
  
  // 图片质量检查
  validateImageQuality(imageData) {
    const checks = [
      this.checkResolution(imageData),
      this.checkBrightness(imageData),
      this.checkBlur(imageData)
    ]
    
    return checks.every(check => check.passed)
  }
}
```

## 测试策略

### 测试层级

1. **单元测试**
   - 工具函数测试
   - 数据模型验证
   - 状态管理逻辑
   - 服务层方法

2. **组件测试**
   - 组件渲染测试
   - 用户交互测试
   - 属性传递测试
   - 事件触发测试

3. **集成测试**
   - API接口测试
   - 数据流测试
   - 页面跳转测试
   - 状态同步测试

4. **端到端测试**
   - 用户注册登录流程
   - 报告录入完整流程
   - 数据分析展示流程
   - 跨平台兼容性测试

### 测试工具配置

```javascript
// jest.config.js
module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  testEnvironment: 'jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

### 性能测试

```javascript
// tests/performance/
├── load-time.test.js      # 页面加载时间测试
├── memory-usage.test.js   # 内存使用测试
├── image-processing.test.js # 图片处理性能测试
└── data-sync.test.js      # 数据同步性能测试
```

## 安全设计

### 数据加密

```javascript
// utils/security/encrypt.js
import CryptoJS from 'crypto-js'

const encryptionService = {
  // AES-256加密
  encrypt(data, key) {
    const encrypted = CryptoJS.AES.encrypt(
      JSON.stringify(data), 
      key, 
      {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    )
    return encrypted.toString()
  },
  
  // 解密
  decrypt(encryptedData, key) {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, key)
    return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8))
  },
  
  // 生成密钥
  generateKey() {
    return CryptoJS.lib.WordArray.random(256/8).toString()
  }
}
```

### 生物识别认证

```javascript
// services/auth/biometric.js
const biometricAuth = {
  // 检查生物识别支持
  async checkSupport() {
    // #ifdef APP-PLUS
    return await plus.fingerprint.isSupport()
    // #endif
    
    // #ifdef H5
    return 'credentials' in navigator
    // #endif
    
    return false
  },
  
  // 启用生物识别
  async enable() {
    const isSupported = await this.checkSupport()
    if (!isSupported) {
      throw new Error('设备不支持生物识别')
    }
    
    return await this.authenticate()
  },
  
  // 生物识别认证
  async authenticate() {
    // #ifdef APP-PLUS
    return new Promise((resolve, reject) => {
      plus.fingerprint.authenticate(
        () => resolve(true),
        (error) => reject(error),
        { message: '请验证指纹' }
      )
    })
    // #endif
  }
}
```

### 安全监控

```javascript
// utils/security/monitor.js
const securityMonitor = {
  // 异常登录检测
  detectAbnormalLogin(loginInfo) {
    const checks = [
      this.checkLoginFrequency(loginInfo),
      this.checkDeviceFingerprint(loginInfo),
      this.checkLocationChange(loginInfo)
    ]
    
    return checks.some(check => check.isAbnormal)
  },
  
  // 数据完整性检查
  verifyDataIntegrity(data) {
    const hash = CryptoJS.SHA256(JSON.stringify(data)).toString()
    return hash === data.checksum
  }
}
```

## 性能优化

### 图片处理优化

```javascript
// utils/image/processor.js
const imageProcessor = {
  // 图片压缩
  async compressImage(imagePath, quality = 0.8) {
    return new Promise((resolve) => {
      uni.compressImage({
        src: imagePath,
        quality,
        success: (res) => resolve(res.tempFilePath),
        fail: () => resolve(imagePath)
      })
    })
  },
  
  // 图片预处理（提高OCR识别率）
  async preprocessForOCR(imagePath) {
    // 调整亮度、对比度、锐化等
    const canvas = uni.createCanvasContext('imageCanvas')
    // 图像处理逻辑
    return processedImagePath
  }
}
```

### 数据缓存策略

```javascript
// utils/cache/manager.js
const cacheManager = {
  // LRU缓存实现
  cache: new Map(),
  maxSize: 100,
  
  set(key, value, ttl = 3600000) { // 默认1小时
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, {
      value,
      expiry: Date.now() + ttl
    })
  },
  
  get(key) {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }
    
    return item.value
  }
}
```

### 懒加载和虚拟滚动

```javascript
// components/common/VirtualList.vue
// 虚拟滚动组件，用于大量报告数据的展示
export default {
  name: 'VirtualList',
  props: {
    items: Array,
    itemHeight: Number,
    containerHeight: Number
  },
  computed: {
    visibleItems() {
      const start = Math.floor(this.scrollTop / this.itemHeight)
      const end = Math.min(
        start + Math.ceil(this.containerHeight / this.itemHeight) + 1,
        this.items.length
      )
      return this.items.slice(start, end)
    }
  }
}
```

## 部署和配置

### 多平台构建配置

```javascript
// 平台特定配置
const platformConfig = {
  'h5': {
    router: {
      mode: 'hash'
    },
    optimization: {
      treeShaking: {
        enable: true
      }
    }
  },
  'mp-weixin': {
    setting: {
      urlCheck: false,
      minified: true
    },
    requiredBackgroundModes: ['audio']
  },
  'app-plus': {
    modules: {
      Camera: {},
      Fingerprint: {},
      SQLite: {}
    },
    distribute: {
      android: {
        permissions: [
          'CAMERA',
          'WRITE_EXTERNAL_STORAGE',
          'USE_FINGERPRINT'
        ]
      },
      ios: {
        capabilities: [
          'com.apple.developer.camera',
          'com.apple.developer.biometric'
        ]
      }
    }
  }
}
```

### 环境配置

```javascript
// config/env.js
const config = {
  development: {
    apiBaseUrl: 'http://localhost:3000/api',
    ocrService: 'mock',
    enableDebug: true
  },
  production: {
    apiBaseUrl: 'https://api.healthreport.com',
    ocrService: 'baidu',
    enableDebug: false
  }
}
```

这个设计文档提供了完整的技术架构、组件设计、数据模型、错误处理、测试策略、安全设计和性能优化方案，为后续的开发实现提供了详细的指导。