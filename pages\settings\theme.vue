<template>
	<view class="page-container">
		<view class="settings-container">
			<!-- 主题设置 -->
			<view class="settings-section">
				<view class="section-title">
					<text>主题设置</text>
				</view>
				
				<view class="theme-options">
					<view 
						class="theme-option" 
						:class="{ 'active': currentTheme === 'light' }"
						@tap="selectTheme('light')"
					>
						<view class="theme-preview light-theme">
							<view class="preview-header"></view>
							<view class="preview-content">
								<view class="preview-card"></view>
								<view class="preview-card"></view>
							</view>
						</view>
						<text class="theme-name">浅色主题</text>
						<view class="theme-check" v-if="currentTheme === 'light'">
							<text class="check-icon">✓</text>
						</view>
					</view>
					
					<view 
						class="theme-option" 
						:class="{ 'active': currentTheme === 'dark' }"
						@tap="selectTheme('dark')"
					>
						<view class="theme-preview dark-theme">
							<view class="preview-header"></view>
							<view class="preview-content">
								<view class="preview-card"></view>
								<view class="preview-card"></view>
							</view>
						</view>
						<text class="theme-name">深色主题</text>
						<view class="theme-check" v-if="currentTheme === 'dark'">
							<text class="check-icon">✓</text>
						</view>
					</view>
					
					<view 
						class="theme-option" 
						:class="{ 'active': currentTheme === 'auto' }"
						@tap="selectTheme('auto')"
					>
						<view class="theme-preview auto-theme">
							<view class="preview-left light-theme">
								<view class="preview-header"></view>
								<view class="preview-content">
									<view class="preview-card"></view>
								</view>
							</view>
							<view class="preview-right dark-theme">
								<view class="preview-header"></view>
								<view class="preview-content">
									<view class="preview-card"></view>
								</view>
							</view>
						</view>
						<text class="theme-name">跟随系统</text>
						<view class="theme-check" v-if="currentTheme === 'auto'">
							<text class="check-icon">✓</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 字体大小设置 -->
			<view class="settings-section">
				<view class="section-title">
					<text>字体大小</text>
				</view>
				
				<view class="font-size-options">
					<view 
						class="font-option"
						:class="{ 'active': currentFontSize === 'small' }"
						@tap="selectFontSize('small')"
					>
						<text class="font-preview small-font">Aa</text>
						<text class="font-name">小</text>
					</view>
					
					<view 
						class="font-option"
						:class="{ 'active': currentFontSize === 'medium' }"
						@tap="selectFontSize('medium')"
					>
						<text class="font-preview medium-font">Aa</text>
						<text class="font-name">中</text>
					</view>
					
					<view 
						class="font-option"
						:class="{ 'active': currentFontSize === 'large' }"
						@tap="selectFontSize('large')"
					>
						<text class="font-preview large-font">Aa</text>
						<text class="font-name">大</text>
					</view>
				</view>
			</view>
			
			<!-- 语言设置 */
			<view class="settings-section">
				<view class="section-title">
					<text>语言设置</text>
				</view>
				
				<view class="language-options">
					<view 
						class="language-option"
						:class="{ 'active': currentLanguage === 'zh-CN' }"
						@tap="selectLanguage('zh-CN')"
					>
						<text class="language-name">简体中文</text>
						<view class="language-check" v-if="currentLanguage === 'zh-CN'">
							<text class="check-icon">✓</text>
						</view>
					</view>
					
					<view 
						class="language-option"
						:class="{ 'active': currentLanguage === 'zh-TW' }"
						@tap="selectLanguage('zh-TW')"
					>
						<text class="language-name">繁體中文</text>
						<view class="language-check" v-if="currentLanguage === 'zh-TW'">
							<text class="check-icon">✓</text>
						</view>
					</view>
					
					<view 
						class="language-option"
						:class="{ 'active': currentLanguage === 'en-US' }"
						@tap="selectLanguage('en-US')"
					>
						<text class="language-name">English</text>
						<view class="language-check" v-if="currentLanguage === 'en-US'">
							<text class="check-icon">✓</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 其他显示设置 -->
			<view class="settings-section">
				<view class="section-title">
					<text>显示设置</text>
				</view>
				
				<view class="display-options">
					<view class="option-item">
						<view class="option-info">
							<text class="option-name">动画效果</text>
							<text class="option-desc">开启页面切换动画</text>
						</view>
						<switch 
							:checked="enableAnimation" 
							@change="toggleAnimation"
							color="#007AFF"
						/>
					</view>
					
					<view class="option-item">
						<view class="option-info">
							<text class="option-name">震动反馈</text>
							<text class="option-desc">操作时提供震动反馈</text>
						</view>
						<switch 
							:checked="enableVibration" 
							@change="toggleVibration"
							color="#007AFF"
						/>
					</view>
					
					<view class="option-item">
						<view class="option-info">
							<text class="option-name">护眼模式</text>
							<text class="option-desc">降低蓝光，保护视力</text>
						</view>
						<switch 
							:checked="enableEyeProtection" 
							@change="toggleEyeProtection"
							color="#007AFF"
						/>
					</view>
				</view>
			</view>
			
			<!-- 重置按钮 -->
			<view class="reset-section">
				<button class="btn btn-secondary btn-block" @tap="resetToDefault">
					恢复默认设置
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { useAppStore } from '../../stores/app.js'
	
	export default {
		name: 'ThemeSettings',
		data() {
			return {
				
			}
		},
		computed: {
			currentTheme() {
				const appStore = useAppStore()
				return appStore.settings.theme
			},
			
			currentFontSize() {
				const appStore = useAppStore()
				return appStore.settings.fontSize || 'medium'
			},
			
			currentLanguage() {
				const appStore = useAppStore()
				return appStore.settings.language
			},
			
			enableAnimation() {
				const appStore = useAppStore()
				return appStore.settings.enableAnimation !== false
			},
			
			enableVibration() {
				const appStore = useAppStore()
				return appStore.settings.enableVibration !== false
			},
			
			enableEyeProtection() {
				const appStore = useAppStore()
				return appStore.settings.enableEyeProtection === true
			}
		},
		onLoad() {
			console.log('主题设置页面加载')
		},
		methods: {
			// 选择主题
			selectTheme(theme) {
				const appStore = useAppStore()
				appStore.updateSettings({ theme })
				
				// 应用主题
				this.applyTheme(theme)
				
				// 震动反馈
				if (this.enableVibration) {
					uni.vibrateShort()
				}
				
				uni.showToast({
					title: '主题已切换',
					icon: 'success',
					duration: 1500
				})
			},
			
			// 选择字体大小
			selectFontSize(fontSize) {
				const appStore = useAppStore()
				appStore.updateSettings({ fontSize })
				
				// 应用字体大小
				this.applyFontSize(fontSize)
				
				if (this.enableVibration) {
					uni.vibrateShort()
				}
				
				uni.showToast({
					title: '字体大小已调整',
					icon: 'success',
					duration: 1500
				})
			},
			
			// 选择语言
			selectLanguage(language) {
				const appStore = useAppStore()
				appStore.updateSettings({ language })
				
				if (this.enableVibration) {
					uni.vibrateShort()
				}
				
				uni.showModal({
					title: '语言设置',
					content: '语言切换需要重启应用才能生效，是否立即重启？',
					success: (res) => {
						if (res.confirm) {
							// 重启应用
							uni.reLaunch({
								url: '/pages/index/index'
							})
						}
					}
				})
			},
			
			// 切换动画效果
			toggleAnimation(e) {
				const appStore = useAppStore()
				appStore.updateSettings({ enableAnimation: e.detail.value })
			},
			
			// 切换震动反馈
			toggleVibration(e) {
				const appStore = useAppStore()
				appStore.updateSettings({ enableVibration: e.detail.value })
			},
			
			// 切换护眼模式
			toggleEyeProtection(e) {
				const appStore = useAppStore()
				appStore.updateSettings({ enableEyeProtection: e.detail.value })
				
				// 应用护眼模式
				this.applyEyeProtection(e.detail.value)
			},
			
			// 应用主题
			applyTheme(theme) {
				let actualTheme = theme
				
				// 如果是自动模式，根据时间判断
				if (theme === 'auto') {
					const hour = new Date().getHours()
					actualTheme = (hour < 6 || hour > 18) ? 'dark' : 'light'
				}
				
				// 设置页面样式
				const pages = getCurrentPages()
				const currentPage = pages[pages.length - 1]
				
				if (currentPage) {
					currentPage.$el.setAttribute('data-theme', actualTheme)
				}
			},
			
			// 应用字体大小
			applyFontSize(fontSize) {
				const fontSizeMap = {
					small: '12px',
					medium: '14px',
					large: '16px'
				}
				
				// 设置根字体大小
				const pages = getCurrentPages()
				const currentPage = pages[pages.length - 1]
				
				if (currentPage) {
					currentPage.$el.style.fontSize = fontSizeMap[fontSize]
				}
			},
			
			// 应用护眼模式
			applyEyeProtection(enabled) {
				const pages = getCurrentPages()
				const currentPage = pages[pages.length - 1]
				
				if (currentPage) {
					if (enabled) {
						currentPage.$el.style.filter = 'sepia(10%) saturate(90%) hue-rotate(15deg)'
					} else {
						currentPage.$el.style.filter = 'none'
					}
				}
			},
			
			// 恢复默认设置
			resetToDefault() {
				uni.showModal({
					title: '恢复默认设置',
					content: '确定要恢复所有显示设置为默认值吗？',
					success: (res) => {
						if (res.confirm) {
							const appStore = useAppStore()
							appStore.updateSettings({
								theme: 'light',
								fontSize: 'medium',
								language: 'zh-CN',
								enableAnimation: true,
								enableVibration: true,
								enableEyeProtection: false
							})
							
							// 重新应用设置
							this.applyTheme('light')
							this.applyFontSize('medium')
							this.applyEyeProtection(false)
							
							uni.showToast({
								title: '已恢复默认设置',
								icon: 'success'
							})
						}
					}
				})
			}
		}
	}
</script>

<style scoped>
	.settings-container {
		padding: 15px;
	}
	
	.settings-section {
		margin-bottom: 30px;
	}
	
	.section-title {
		font-size: 16px;
		font-weight: 600;
		color: #333333;
		margin-bottom: 15px;
	}
	
	/* 主题选项 */
	.theme-options {
		display: flex;
		flex-direction: column;
		gap: 15px;
	}
	
	.theme-option {
		display: flex;
		align-items: center;
		padding: 15px;
		background-color: #FFFFFF;
		border-radius: 12px;
		border: 2px solid transparent;
		transition: all 0.3s ease;
	}
	
	.theme-option.active {
		border-color: #007AFF;
		background-color: rgba(0, 122, 255, 0.05);
	}
	
	.theme-preview {
		width: 60px;
		height: 40px;
		border-radius: 6px;
		overflow: hidden;
		margin-right: 15px;
		border: 1px solid #E5E5EA;
	}
	
	.light-theme {
		background-color: #FFFFFF;
	}
	
	.dark-theme {
		background-color: #1C1C1E;
	}
	
	.auto-theme {
		display: flex;
	}
	
	.preview-left,
	.preview-right {
		flex: 1;
	}
	
	.preview-header {
		height: 12px;
		background-color: #F2F2F7;
		margin-bottom: 4px;
	}
	
	.dark-theme .preview-header {
		background-color: #2C2C2E;
	}
	
	.preview-content {
		padding: 4px;
	}
	
	.preview-card {
		height: 6px;
		background-color: #E5E5EA;
		border-radius: 2px;
		margin-bottom: 2px;
	}
	
	.dark-theme .preview-card {
		background-color: #3A3A3C;
	}
	
	.theme-name {
		flex: 1;
		font-size: 14px;
		color: #333333;
	}
	
	.theme-check {
		width: 20px;
		height: 20px;
		border-radius: 10px;
		background-color: #007AFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.check-icon {
		color: #FFFFFF;
		font-size: 12px;
		font-weight: bold;
	}
	
	/* 字体大小选项 */
	.font-size-options {
		display: flex;
		justify-content: space-around;
		background-color: #FFFFFF;
		border-radius: 12px;
		padding: 20px;
	}
	
	.font-option {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 15px;
		border-radius: 8px;
		transition: all 0.3s ease;
	}
	
	.font-option.active {
		background-color: rgba(0, 122, 255, 0.1);
	}
	
	.font-preview {
		font-weight: 600;
		color: #333333;
		margin-bottom: 8px;
	}
	
	.small-font {
		font-size: 18px;
	}
	
	.medium-font {
		font-size: 24px;
	}
	
	.large-font {
		font-size: 30px;
	}
	
	.font-name {
		font-size: 12px;
		color: #8E8E93;
	}
	
	.font-option.active .font-name {
		color: #007AFF;
	}
	
	/* 语言选项 */
	.language-options {
		background-color: #FFFFFF;
		border-radius: 12px;
		overflow: hidden;
	}
	
	.language-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 15px;
		border-bottom: 1px solid #F2F2F7;
		transition: background-color 0.3s ease;
	}
	
	.language-option:last-child {
		border-bottom: none;
	}
	
	.language-option.active {
		background-color: rgba(0, 122, 255, 0.05);
	}
	
	.language-name {
		font-size: 14px;
		color: #333333;
	}
	
	.language-check {
		width: 20px;
		height: 20px;
		border-radius: 10px;
		background-color: #007AFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 显示选项 */
	.display-options {
		background-color: #FFFFFF;
		border-radius: 12px;
		overflow: hidden;
	}
	
	.option-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 15px;
		border-bottom: 1px solid #F2F2F7;
	}
	
	.option-item:last-child {
		border-bottom: none;
	}
	
	.option-info {
		flex: 1;
	}
	
	.option-name {
		font-size: 14px;
		color: #333333;
		margin-bottom: 4px;
	}
	
	.option-desc {
		font-size: 12px;
		color: #8E8E93;
	}
	
	/* 重置区域 */
	.reset-section {
		margin-top: 30px;
		padding-top: 20px;
		border-top: 1px solid #F2F2F7;
	}
</style>