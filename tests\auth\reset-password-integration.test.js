/**
 * 密码重置功能集成测试
 * 测试从发送验证码到密码重置完成的完整流程
 */

// Mock uni API before importing authService
global.uni = {
  getStorageSync: jest.fn(() => null),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  clearStorageSync: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  navigateBack: jest.fn(),
  request: jest.fn()
}

const authService = require('../../services/auth/authService.js')

describe('密码重置集成测试', () => {
  // 测试用户数据
  const testUser = {
    phone: '13800138000',
    oldPassword: 'OldPass123',
    newPassword: 'NewPass123',
    code: '123456'
  }

  beforeEach(() => {
    // 清理本地存储
    uni.clearStorageSync()
    
    // Mock uni API
    global.uni = {
      getStorageSync: jest.fn(() => null),
      setStorageSync: jest.fn(),
      removeStorageSync: jest.fn(),
      clearStorageSync: jest.fn(),
      showToast: jest.fn(),
      showModal: jest.fn(),
      navigateBack: jest.fn()
    }
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('完整密码重置流程', () => {
    test('应该完成完整的密码重置流程', async () => {
      // 步骤1: 发送验证码
      const sendCodeResult = await authService.sendVerificationCode(testUser.phone, 'reset')
      expect(sendCodeResult.success).toBe(true)
      expect(sendCodeResult.message).toBe('验证码已发送')
      
      // 验证发送时间被记录
      expect(uni.setStorageSync).toHaveBeenCalledWith(
        `last_send_code_${testUser.phone}`,
        expect.any(Number)
      )
      
      // 步骤2: 重置密码
      const resetResult = await authService.resetPassword({
        phone: testUser.phone,
        code: testUser.code,
        newPassword: testUser.newPassword
      })
      
      expect(resetResult.success).toBe(true)
      expect(resetResult.message).toBe('密码重置成功')
      
      // 验证认证数据被清除
      expect(uni.removeStorageSync).toHaveBeenCalledWith('auth_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('refresh_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('token_expiry')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('user_info')
    })

    test('应该处理验证码过期场景', async () => {
      // 发送验证码
      await authService.sendVerificationCode(testUser.phone, 'reset')
      
      // 模拟验证码过期（使用错误的验证码）
      const resetResult = await authService.resetPassword({
        phone: testUser.phone,
        code: '000000', // 错误的验证码
        newPassword: testUser.newPassword
      })
      
      expect(resetResult.success).toBe(false)
      expect(resetResult.message).toContain('验证码错误')
    })

    test('应该处理未注册用户场景', async () => {
      const unregisteredPhone = '18888888888'
      
      // 尝试为未注册用户重置密码
      const resetResult = await authService.resetPassword({
        phone: unregisteredPhone,
        code: testUser.code,
        newPassword: testUser.newPassword
      })
      
      expect(resetResult.success).toBe(false)
      expect(resetResult.message).toBe('该手机号未注册，请先注册')
    })
  })

  describe('安全性集成测试', () => {
    test('应该防止验证码发送频率攻击', async () => {
      // 清除之前的发送记录
      uni.getStorageSync.mockReturnValue(null)
      
      // 第一次发送验证码
      const firstResult = await authService.sendVerificationCode(testUser.phone, 'reset')
      expect(firstResult.success).toBe(true)
      
      // 模拟上次发送时间为30秒前
      uni.getStorageSync.mockReturnValue(Date.now() - 30000)
      
      // 第二次发送验证码（应该被拒绝）
      const secondResult = await authService.sendVerificationCode(testUser.phone, 'reset')
      expect(secondResult.success).toBe(false)
      expect(secondResult.message).toContain('验证码发送过于频繁')
    })

    test('应该防止密码重置频率攻击', async () => {
      // 第一次重置尝试
      const firstResult = await authService.resetPassword({
        phone: testUser.phone,
        code: '000000', // 错误验证码
        newPassword: testUser.newPassword
      })
      expect(firstResult.success).toBe(false)
      
      // 模拟上次重置尝试为30秒前
      uni.getStorageSync.mockReturnValue(Date.now() - 30000)
      
      // 第二次重置尝试（应该被拒绝）
      const secondResult = await authService.resetPassword({
        phone: testUser.phone,
        code: testUser.code,
        newPassword: testUser.newPassword
      })
      expect(secondResult.success).toBe(false)
      expect(secondResult.message).toContain('重置密码过于频繁')
    })

    test('应该记录安全事件', async () => {
      // 模拟多次失败的重置尝试
      for (let i = 0; i < 3; i++) {
        await authService.resetPassword({
          phone: testUser.phone,
          code: '000000', // 错误验证码
          newPassword: testUser.newPassword
        })
        // 添加小延迟以避免频率限制
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      // 验证安全事件被记录
      expect(uni.setStorageSync).toHaveBeenCalledWith(
        'security_logs',
        expect.arrayContaining([
          expect.objectContaining({
            type: 'password_reset_failed',
            data: expect.objectContaining({
              phone: testUser.phone,
              error: expect.any(String),
              timestamp: expect.any(Number)
            })
          })
        ])
      )
    }, 10000) // 增加超时时间
  })

  describe('错误恢复测试', () => {
    test('应该从网络错误中恢复', async () => {
      // 模拟网络错误恢复场景
      const originalRequest = authService.request
      const originalCheckPhoneExists = authService.checkPhoneExists
      let callCount = 0
      
      // Mock checkPhoneExists to always return true
      authService.checkPhoneExists = jest.fn().mockResolvedValue(true)
      
      authService.request = jest.fn().mockImplementation((url, options) => {
        callCount++
        if (callCount === 1) {
          return Promise.reject(new Error('网络连接失败'))
        } else {
          return originalRequest.call(authService, url, options)
        }
      })
      
      // 第一次尝试（失败）
      const firstResult = await authService.resetPassword({
        phone: testUser.phone,
        code: testUser.code,
        newPassword: testUser.newPassword
      })
      expect(firstResult.success).toBe(false)
      
      // 等待一段时间以避免频率限制
      await new Promise(resolve => setTimeout(resolve, 1100))
      
      // 第二次尝试（成功）
      const secondResult = await authService.resetPassword({
        phone: testUser.phone,
        code: testUser.code,
        newPassword: testUser.newPassword
      })
      expect(secondResult.success).toBe(true)
      
      // 恢复原方法
      authService.request = originalRequest
      authService.checkPhoneExists = originalCheckPhoneExists
    })

    test('应该处理存储错误', async () => {
      // 保存原始方法
      const originalSetStorageSync = uni.setStorageSync
      
      // 模拟存储错误，但不影响核心功能
      uni.setStorageSync.mockImplementation((key) => {
        if (key.startsWith('last_send_code_')) {
          throw new Error('存储空间不足')
        }
        return originalSetStorageSync(key)
      })
      
      // 发送验证码应该仍然成功，即使存储失败
      const result = await authService.sendVerificationCode(testUser.phone, 'reset')
      expect(result.success).toBe(true)
      
      // 恢复原始方法
      uni.setStorageSync = originalSetStorageSync
    })
  })

  describe('数据一致性测试', () => {
    test('应该确保重置成功后清除所有相关数据', async () => {
      // 清除之前的mock调用记录
      jest.clearAllMocks()
      
      // 模拟已有的认证数据
      uni.getStorageSync.mockImplementation((key) => {
        const mockData = {
          'auth_token': 'old_token',
          'refresh_token': 'old_refresh_token',
          'token_expiry': Date.now() + 3600000,
          'user_info': { id: 'user123', phone: testUser.phone }
        }
        return mockData[key] || null
      })
      
      // 重置密码
      const result = await authService.resetPassword({
        phone: testUser.phone,
        code: testUser.code,
        newPassword: testUser.newPassword
      })
      
      expect(result.success).toBe(true)
      
      // 验证所有认证相关数据都被清除
      expect(uni.removeStorageSync).toHaveBeenCalledWith('auth_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('refresh_token')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('token_expiry')
      expect(uni.removeStorageSync).toHaveBeenCalledWith('user_info')
    })

    test('应该确保重置失败时不清除认证数据', async () => {
      // 重置密码失败
      const result = await authService.resetPassword({
        phone: testUser.phone,
        code: '000000', // 错误验证码
        newPassword: testUser.newPassword
      })
      
      expect(result.success).toBe(false)
      
      // 验证认证数据没有被清除
      expect(uni.removeStorageSync).not.toHaveBeenCalledWith('auth_token')
      expect(uni.removeStorageSync).not.toHaveBeenCalledWith('refresh_token')
      expect(uni.removeStorageSync).not.toHaveBeenCalledWith('token_expiry')
      expect(uni.removeStorageSync).not.toHaveBeenCalledWith('user_info')
    })
  })

  describe('并发处理测试', () => {
    test('应该正确处理并发的验证码发送请求', async () => {
      // 由于当前实现是基于时间戳检查，并发请求可能都会成功
      // 这个测试更多是验证系统不会崩溃
      const promises = Array(3).fill().map(() => 
        authService.sendVerificationCode(testUser.phone, 'reset')
      )
      
      const results = await Promise.all(promises)
      
      // 至少有一个请求应该成功
      const successCount = results.filter(r => r.success).length
      expect(successCount).toBeGreaterThanOrEqual(1)
      
      // 所有请求都应该有响应
      expect(results.length).toBe(3)
      results.forEach(result => {
        expect(result).toHaveProperty('success')
        expect(result).toHaveProperty('message')
      })
    })

    test('应该正确处理并发的密码重置请求', async () => {
      // 由于当前实现是基于时间戳检查，并发请求可能都会失败或成功
      // 这个测试更多是验证系统不会崩溃
      const promises = Array(3).fill().map(() => 
        authService.resetPassword({
          phone: testUser.phone,
          code: testUser.code,
          newPassword: testUser.newPassword
        })
      )
      
      const results = await Promise.all(promises)
      
      // 所有请求都应该有响应
      expect(results.length).toBe(3)
      results.forEach(result => {
        expect(result).toHaveProperty('success')
        expect(result).toHaveProperty('message')
      })
    })
  })

  describe('性能测试', () => {
    test('验证码发送应该在合理时间内完成', async () => {
      const startTime = Date.now()
      
      const result = await authService.sendVerificationCode(testUser.phone, 'reset')
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      expect(result.success).toBe(true)
      expect(duration).toBeLessThan(3000) // 应该在3秒内完成
    })

    test('密码重置应该在合理时间内完成', async () => {
      // 等待一段时间以避免频率限制
      await new Promise(resolve => setTimeout(resolve, 1100))
      
      const startTime = Date.now()
      
      const result = await authService.resetPassword({
        phone: testUser.phone,
        code: testUser.code,
        newPassword: testUser.newPassword
      })
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      expect(result.success).toBe(true)
      expect(duration).toBeLessThan(3000) // 应该在3秒内完成
    })
  })
})