/**
 * 报告数据验证器
 * 提供报告数据的完整性验证功能
 */

class ReportValidator {
  constructor() {
    // 验证规则配置
    this.rules = {
      hospital: {
        required: true,
        minLength: 2,
        maxLength: 100,
        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-()（）]+$/
      },
      doctor: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/
      },
      checkDate: {
        required: true,
        type: 'date',
        maxDate: new Date()
      },
      title: {
        required: false,
        maxLength: 200
      },
      notes: {
        required: false,
        maxLength: 500
      },
      items: {
        required: true,
        minItems: 1,
        maxItems: 50
      }
    };

    // 检查项目验证规则
    this.itemRules = {
      name: {
        required: true,
        minLength: 2,
        maxLength: 100
      },
      value: {
        required: true,
        type: 'number'
      },
      unit: {
        required: false,
        maxLength: 20
      },
      referenceRange: {
        required: false,
        maxLength: 50
      },
      category: {
        required: false,
        enum: [
          '血常规',
          '生化检查',
          '免疫检查',
          '尿常规',
          '肝功能',
          '肾功能',
          '血脂',
          '血糖',
          '其他'
        ]
      }
    };
  }

  /**
   * 验证完整报告数据
   * @param {Object} reportData - 报告数据
   * @returns {Object} 验证结果
   */
  validateReport(reportData) {
    const result = {
      isValid: true,
      errors: {},
      warnings: [],
      score: 0,
      details: {
        basicInfo: { isValid: true, errors: {} },
        items: { isValid: true, errors: {}, validCount: 0 },
        completeness: { score: 0, maxScore: 100 }
      }
    };

    try {
      // 验证基本信息
      this.validateBasicInfo(reportData, result);
      
      // 验证检查项目
      this.validateItems(reportData.items || [], result);
      
      // 计算完整性得分
      this.calculateCompletenessScore(reportData, result);
      
      // 检查数据一致性
      this.checkDataConsistency(reportData, result);
      
      // 最终验证结果
      result.isValid = result.details.basicInfo.isValid && 
                      result.details.items.isValid &&
                      Object.keys(result.errors).length === 0;

    } catch (error) {
      console.error('报告验证过程出错:', error);
      result.isValid = false;
      result.errors.system = '验证过程出现系统错误';
    }

    return result;
  }

  /**
   * 验证基本信息
   * @param {Object} reportData - 报告数据
   * @param {Object} result - 验证结果对象
   */
  validateBasicInfo(reportData, result) {
    const basicFields = ['hospital', 'doctor', 'checkDate', 'title', 'notes'];
    
    basicFields.forEach(field => {
      const value = reportData[field];
      const rule = this.rules[field];
      const fieldErrors = this.validateField(value, rule, field);
      
      if (fieldErrors.length > 0) {
        result.details.basicInfo.errors[field] = fieldErrors;
        result.errors[field] = fieldErrors[0]; // 只显示第一个错误
        
        if (rule.required) {
          result.details.basicInfo.isValid = false;
        }
      }
    });

    // 特殊验证：检查日期不能晚于今天
    if (reportData.checkDate) {
      const checkDate = new Date(reportData.checkDate);
      const today = new Date();
      today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
      
      if (checkDate > today) {
        const error = '检查日期不能晚于今天';
        result.details.basicInfo.errors.checkDate = [error];
        result.errors.checkDate = error;
        result.details.basicInfo.isValid = false;
      }
    }
  }

  /**
   * 验证检查项目
   * @param {Array} items - 检查项目数组
   * @param {Object} result - 验证结果对象
   */
  validateItems(items, result) {
    if (!Array.isArray(items)) {
      result.errors.items = '检查项目必须是数组格式';
      result.details.items.isValid = false;
      return;
    }

    // 验证项目数量
    if (items.length === 0) {
      result.errors.items = '至少需要一个检查项目';
      result.details.items.isValid = false;
      return;
    }

    if (items.length > this.rules.items.maxItems) {
      result.errors.items = `检查项目数量不能超过${this.rules.items.maxItems}个`;
      result.details.items.isValid = false;
      return;
    }

    // 验证每个项目
    const itemErrors = {};
    let validItemCount = 0;
    const itemNames = new Set();

    items.forEach((item, index) => {
      const itemResult = this.validateItem(item, index);
      
      if (!itemResult.isValid) {
        itemErrors[index] = itemResult.errors;
        result.details.items.isValid = false;
      } else {
        validItemCount++;
      }

      // 检查项目名称重复
      if (item.name) {
        if (itemNames.has(item.name)) {
          if (!itemErrors[index]) itemErrors[index] = {};
          itemErrors[index].name = '项目名称不能重复';
          result.details.items.isValid = false;
        } else {
          itemNames.add(item.name);
        }
      }

      // 检查异常值标记的准确性
      if (item.value && item.referenceRange) {
        const calculatedAbnormal = this.isValueAbnormal(item.value, item.referenceRange);
        if (calculatedAbnormal !== item.isAbnormal) {
          result.warnings.push(`项目"${item.name}"的异常标记可能不准确`);
        }
      }
    });

    if (Object.keys(itemErrors).length > 0) {
      result.details.items.errors = itemErrors;
    }

    result.details.items.validCount = validItemCount;
  }

  /**
   * 验证单个检查项目
   * @param {Object} item - 检查项目
   * @param {number} index - 项目索引
   * @returns {Object} 验证结果
   */
  validateItem(item, index) {
    const result = {
      isValid: true,
      errors: {}
    };

    Object.keys(this.itemRules).forEach(field => {
      const value = item[field];
      const rule = this.itemRules[field];
      const fieldErrors = this.validateField(value, rule, field);
      
      if (fieldErrors.length > 0) {
        result.errors[field] = fieldErrors[0];
        if (rule.required) {
          result.isValid = false;
        }
      }
    });

    // 特殊验证：数值格式
    if (item.value !== undefined && item.value !== null && item.value !== '') {
      const numValue = parseFloat(item.value);
      if (isNaN(numValue)) {
        result.errors.value = '检查结果必须是有效数字';
        result.isValid = false;
      } else if (numValue < 0) {
        result.warnings = result.warnings || [];
        result.warnings.push('检查结果为负数，请确认是否正确');
      }
    }

    return result;
  }

  /**
   * 验证单个字段
   * @param {any} value - 字段值
   * @param {Object} rule - 验证规则
   * @param {string} fieldName - 字段名称
   * @returns {Array} 错误信息数组
   */
  validateField(value, rule, fieldName) {
    const errors = [];

    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push(`${this.getFieldDisplayName(fieldName)}是必填项`);
      return errors;
    }

    // 如果值为空且非必填，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return errors;
    }

    // 类型验证
    if (rule.type) {
      switch (rule.type) {
        case 'date':
          if (!this.isValidDate(value)) {
            errors.push(`${this.getFieldDisplayName(fieldName)}格式不正确`);
          }
          break;
        case 'number':
          if (isNaN(parseFloat(value))) {
            errors.push(`${this.getFieldDisplayName(fieldName)}必须是数字`);
          }
          break;
      }
    }

    // 长度验证
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors.push(`${this.getFieldDisplayName(fieldName)}至少需要${rule.minLength}个字符`);
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push(`${this.getFieldDisplayName(fieldName)}不能超过${rule.maxLength}个字符`);
      }
    }

    // 模式验证
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      errors.push(`${this.getFieldDisplayName(fieldName)}格式不正确`);
    }

    // 枚举验证
    if (rule.enum && !rule.enum.includes(value)) {
      errors.push(`${this.getFieldDisplayName(fieldName)}必须是有效选项`);
    }

    // 日期范围验证
    if (rule.maxDate && this.isValidDate(value)) {
      const dateValue = new Date(value);
      if (dateValue > rule.maxDate) {
        errors.push(`${this.getFieldDisplayName(fieldName)}不能晚于${rule.maxDate.toLocaleDateString()}`);
      }
    }

    return errors;
  }

  /**
   * 计算完整性得分
   * @param {Object} reportData - 报告数据
   * @param {Object} result - 验证结果对象
   */
  calculateCompletenessScore(reportData, result) {
    let score = 0;
    const maxScore = 100;

    // 基本信息得分 (40分)
    const basicFields = ['hospital', 'doctor', 'checkDate'];
    const optionalFields = ['title', 'notes'];
    
    basicFields.forEach(field => {
      if (reportData[field] && reportData[field].trim() !== '') {
        score += 10;
      }
    });

    optionalFields.forEach(field => {
      if (reportData[field] && reportData[field].trim() !== '') {
        score += 5;
      }
    });

    // 检查项目得分 (60分)
    const items = reportData.items || [];
    if (items.length > 0) {
      score += 20; // 有项目基础分

      const itemCompleteness = items.reduce((sum, item) => {
        let itemScore = 0;
        if (item.name) itemScore += 2;
        if (item.value) itemScore += 2;
        if (item.unit) itemScore += 1;
        if (item.referenceRange) itemScore += 1;
        if (item.category) itemScore += 1;
        return sum + Math.min(itemScore, 7);
      }, 0);

      // 项目完整性得分，最多40分
      score += Math.min(40, Math.round((itemCompleteness / (items.length * 7)) * 40));
    }

    result.details.completeness.score = Math.min(score, maxScore);
    result.details.completeness.maxScore = maxScore;
    result.score = result.details.completeness.score;
  }

  /**
   * 检查数据一致性
   * @param {Object} reportData - 报告数据
   * @param {Object} result - 验证结果对象
   */
  checkDataConsistency(reportData, result) {
    const items = reportData.items || [];

    // 检查异常值标记一致性
    items.forEach((item, index) => {
      if (item.value && item.referenceRange) {
        const calculatedAbnormal = this.isValueAbnormal(item.value, item.referenceRange);
        if (calculatedAbnormal !== item.isAbnormal) {
          result.warnings.push(`项目${index + 1}"${item.name}"的异常标记与计算结果不一致`);
        }
      }
    });

    // 检查日期逻辑性
    if (reportData.checkDate && reportData.reportDate) {
      const checkDate = new Date(reportData.checkDate);
      const reportDate = new Date(reportData.reportDate);
      
      if (reportDate < checkDate) {
        result.warnings.push('报告日期早于检查日期，请确认是否正确');
      }
    }

    // 检查项目分类一致性
    const categoryStats = {};
    items.forEach(item => {
      if (item.category) {
        categoryStats[item.category] = (categoryStats[item.category] || 0) + 1;
      }
    });

    // 如果某个分类只有一个项目，可能存在分类错误
    Object.entries(categoryStats).forEach(([category, count]) => {
      if (count === 1 && items.length > 3) {
        result.warnings.push(`分类"${category}"只有一个项目，请确认分类是否正确`);
      }
    });
  }

  /**
   * 判断数值是否异常
   * @param {string|number} value - 检查值
   * @param {string} referenceRange - 参考范围
   * @returns {boolean} 是否异常
   */
  isValueAbnormal(value, referenceRange) {
    if (!value || !referenceRange) return false;

    const numValue = parseFloat(value);
    if (isNaN(numValue)) return false;

    // 解析参考范围
    const rangeMatch = referenceRange.match(/(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/);
    if (rangeMatch) {
      const minValue = parseFloat(rangeMatch[1]);
      const maxValue = parseFloat(rangeMatch[2]);
      
      if (!isNaN(minValue) && !isNaN(maxValue)) {
        return numValue < minValue || numValue > maxValue;
      }
    }

    return false;
  }

  /**
   * 验证日期格式
   * @param {string} dateStr - 日期字符串
   * @returns {boolean} 是否有效日期
   */
  isValidDate(dateStr) {
    if (!dateStr) return false;
    const date = new Date(dateStr);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * 获取字段显示名称
   * @param {string} fieldName - 字段名称
   * @returns {string} 显示名称
   */
  getFieldDisplayName(fieldName) {
    const displayNames = {
      hospital: '医院名称',
      doctor: '医生姓名',
      checkDate: '检查日期',
      title: '报告标题',
      notes: '备注',
      name: '项目名称',
      value: '检查结果',
      unit: '单位',
      referenceRange: '参考范围',
      category: '分类'
    };

    return displayNames[fieldName] || fieldName;
  }

  /**
   * 快速验证（仅检查必填项）
   * @param {Object} reportData - 报告数据
   * @returns {Object} 简化的验证结果
   */
  quickValidate(reportData) {
    const errors = [];

    if (!reportData.hospital || reportData.hospital.trim() === '') {
      errors.push('医院名称是必填项');
    }

    if (!reportData.doctor || reportData.doctor.trim() === '') {
      errors.push('医生姓名是必填项');
    }

    if (!reportData.checkDate) {
      errors.push('检查日期是必填项');
    }

    if (!reportData.items || reportData.items.length === 0) {
      errors.push('至少需要一个检查项目');
    } else {
      reportData.items.forEach((item, index) => {
        if (!item.name || item.name.trim() === '') {
          errors.push(`第${index + 1}个项目缺少名称`);
        }
        if (!item.value || item.value.toString().trim() === '') {
          errors.push(`第${index + 1}个项目缺少检查结果`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}

module.exports = ReportValidator;