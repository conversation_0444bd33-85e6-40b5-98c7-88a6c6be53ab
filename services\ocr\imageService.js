/**
 * 图片处理服务
 * 提供图片选择、拍照、压缩、格式转换等功能
 */

import platformAdapter from '../../utils/platform/PlatformAdapter.js'
import { ERROR_CODES } from '../../utils/platform/constants.js'

class ImageService {
  constructor() {
    this.maxImageSize = 2 * 1024 * 1024 // 2MB
    this.maxWidth = 1920
    this.maxHeight = 1080
    this.quality = 0.8
    this.supportedFormats = ['jpg', 'jpeg', 'png', 'webp']
  }

  /**
   * 选择图片（拍照或从相册选择）
   * @param {Object} options 选择选项
   * @returns {Promise<Object>} 处理后的图片信息
   */
  async selectImage(options = {}) {
    try {
      const defaultOptions = {
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album']
      }

      const mergedOptions = { ...defaultOptions, ...options }
      
      // 检查相机权限（如果需要拍照）
      if (mergedOptions.sourceType.includes('camera')) {
        const hasPermission = await this._checkCameraPermission()
        if (!hasPermission) {
          throw new Error('相机权限未授权')
        }
      }

      // 选择图片
      const result = await platformAdapter.chooseImage(mergedOptions)
      
      if (!result.success || !result.tempFilePaths || result.tempFilePaths.length === 0) {
        throw new Error('图片选择失败')
      }

      // 处理选中的图片
      const processedImages = []
      for (const filePath of result.tempFilePaths) {
        const processedImage = await this._processImage(filePath)
        processedImages.push(processedImage)
      }

      return {
        success: true,
        images: processedImages
      }
    } catch (error) {
      console.error('选择图片失败:', error)
      return {
        success: false,
        error: error.message,
        code: ERROR_CODES.IMAGE_SELECT_FAILED
      }
    }
  }

  /**
   * 拍照
   * @param {Object} options 拍照选项
   * @returns {Promise<Object>} 处理后的图片信息
   */
  async takePhoto(options = {}) {
    try {
      const result = await platformAdapter.takePhoto(options)
      
      if (!result.success || !result.tempFilePaths || result.tempFilePaths.length === 0) {
        throw new Error('拍照失败')
      }

      const processedImage = await this._processImage(result.tempFilePaths[0])
      
      return {
        success: true,
        image: processedImage
      }
    } catch (error) {
      console.error('拍照失败:', error)
      return {
        success: false,
        error: error.message,
        code: ERROR_CODES.CAMERA_FAILED
      }
    }
  }

  /**
   * 从相册选择图片
   * @param {Object} options 选择选项
   * @returns {Promise<Object>} 处理后的图片信息
   */
  async selectFromAlbum(options = {}) {
    try {
      const result = await platformAdapter.chooseFromAlbum(options)
      
      if (!result.success || !result.tempFilePaths || result.tempFilePaths.length === 0) {
        throw new Error('从相册选择图片失败')
      }

      const processedImages = []
      for (const filePath of result.tempFilePaths) {
        const processedImage = await this._processImage(filePath)
        processedImages.push(processedImage)
      }

      return {
        success: true,
        images: processedImages
      }
    } catch (error) {
      console.error('从相册选择图片失败:', error)
      return {
        success: false,
        error: error.message,
        code: ERROR_CODES.ALBUM_SELECT_FAILED
      }
    }
  }

  /**
   * 压缩图片
   * @param {string} filePath 图片路径
   * @param {Object} options 压缩选项
   * @returns {Promise<Object>} 压缩后的图片信息
   */
  async compressImage(filePath, options = {}) {
    try {
      const compressOptions = {
        src: filePath,
        quality: options.quality || this.quality,
        width: options.width || this.maxWidth,
        height: options.height || this.maxHeight,
        ...options
      }

      return new Promise((resolve, reject) => {
        uni.compressImage({
          ...compressOptions,
          success: (res) => {
            resolve({
              success: true,
              tempFilePath: res.tempFilePath,
              size: res.size || 0
            })
          },
          fail: (error) => {
            reject({
              success: false,
              error: error.errMsg || '图片压缩失败',
              code: ERROR_CODES.IMAGE_COMPRESS_FAILED
            })
          }
        })
      })
    } catch (error) {
      console.error('压缩图片失败:', error)
      return {
        success: false,
        error: error.message,
        code: ERROR_CODES.IMAGE_COMPRESS_FAILED
      }
    }
  }

  /**
   * 检查图片质量
   * @param {string} filePath 图片路径
   * @returns {Promise<Object>} 质量检查结果
   */
  async checkImageQuality(filePath) {
    try {
      // 获取图片信息
      const imageInfo = await this._getImageInfo(filePath)
      
      const quality = {
        isValid: true,
        score: 100,
        issues: [],
        suggestions: []
      }

      // 检查文件大小
      if (imageInfo.size > this.maxImageSize) {
        quality.issues.push('文件过大')
        quality.suggestions.push('建议压缩图片或重新拍摄')
        quality.score -= 20
      }

      // 检查图片尺寸
      if (imageInfo.width < 300 || imageInfo.height < 300) {
        quality.issues.push('图片分辨率过低')
        quality.suggestions.push('建议使用更高分辨率的图片')
        quality.score -= 30
      }

      // 检查图片格式
      const format = this._getImageFormat(filePath)
      if (!this.supportedFormats.includes(format.toLowerCase())) {
        quality.issues.push('不支持的图片格式')
        quality.suggestions.push('请使用JPG、PNG或WebP格式的图片')
        quality.score -= 40
      }

      // 模糊检测（简单实现）
      const blurScore = await this._detectBlur(filePath)
      if (blurScore < 0.5) {
        quality.issues.push('图片可能模糊')
        quality.suggestions.push('建议重新拍摄更清晰的图片')
        quality.score -= 25
      }

      quality.isValid = quality.score >= 60
      quality.score = Math.max(0, quality.score)

      return {
        success: true,
        quality
      }
    } catch (error) {
      console.error('检查图片质量失败:', error)
      return {
        success: false,
        error: error.message,
        code: ERROR_CODES.IMAGE_QUALITY_CHECK_FAILED
      }
    }
  }

  /**
   * 私有方法：处理图片
   * @private
   */
  async _processImage(filePath) {
    try {
      // 获取原始图片信息
      const originalInfo = await this._getImageInfo(filePath)
      
      // 检查图片质量
      const qualityResult = await this.checkImageQuality(filePath)
      
      let processedPath = filePath
      let processedSize = originalInfo.size

      // 如果图片过大或质量不佳，进行压缩
      if (originalInfo.size > this.maxImageSize || 
          originalInfo.width > this.maxWidth || 
          originalInfo.height > this.maxHeight) {
        
        const compressResult = await this.compressImage(filePath)
        if (compressResult.success) {
          processedPath = compressResult.tempFilePath
          processedSize = compressResult.size
        }
      }

      return {
        originalPath: filePath,
        processedPath: processedPath,
        originalSize: originalInfo.size,
        processedSize: processedSize,
        width: originalInfo.width,
        height: originalInfo.height,
        format: this._getImageFormat(filePath),
        quality: qualityResult.success ? qualityResult.quality : null,
        timestamp: Date.now()
      }
    } catch (error) {
      console.error('处理图片失败:', error)
      throw error
    }
  }

  /**
   * 私有方法：获取图片信息
   * @private
   */
  async _getImageInfo(filePath) {
    return new Promise((resolve, reject) => {
      uni.getImageInfo({
        src: filePath,
        success: (res) => {
          resolve({
            width: res.width,
            height: res.height,
            path: res.path,
            size: res.size || 0,
            type: res.type || ''
          })
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '获取图片信息失败'))
        }
      })
    })
  }

  /**
   * 私有方法：获取图片格式
   * @private
   */
  _getImageFormat(filePath) {
    const extension = filePath.split('.').pop()
    return extension ? extension.toLowerCase() : 'unknown'
  }

  /**
   * 私有方法：检查相机权限
   * @private
   */
  async _checkCameraPermission() {
    try {
      const hasPermission = await platformAdapter.checkPermission('android.permission.CAMERA')
      if (!hasPermission) {
        const granted = await platformAdapter.requestPermission('android.permission.CAMERA')
        return granted
      }
      return true
    } catch (error) {
      console.error('检查相机权限失败:', error)
      return false
    }
  }

  /**
   * 私有方法：模糊检测（简单实现）
   * @private
   */
  async _detectBlur(filePath) {
    // 这里是一个简化的模糊检测实现
    // 实际项目中可以使用更复杂的算法或第三方库
    try {
      const imageInfo = await this._getImageInfo(filePath)
      
      // 基于图片尺寸的简单评分
      const minDimension = Math.min(imageInfo.width, imageInfo.height)
      if (minDimension < 300) return 0.3
      if (minDimension < 600) return 0.6
      return 0.8
    } catch (error) {
      console.error('模糊检测失败:', error)
      return 0.5 // 默认中等分数
    }
  }
}

// 创建单例实例
const imageService = new ImageService()

export default imageService
export { ImageService }