// 交互式图表组件测试
describe('InteractiveChart 组件测试', () => {
  // Mock uni-app API
  global.uni = {
    createCanvasContext: jest.fn(() => ({
      clearRect: jest.fn(),
      setFillStyle: jest.fn(),
      fillRect: jest.fn(),
      beginPath: jest.fn(),
      setStrokeStyle: jest.fn(),
      setLineWidth: jest.fn(),
      moveTo: jest.fn(),
      lineTo: jest.fn(),
      stroke: jest.fn(),
      arc: jest.fn(),
      fill: jest.fn(),
      setFontSize: jest.fn(),
      fillText: jest.fn(),
      draw: jest.fn(),
      save: jest.fn(),
      restore: jest.fn(),
      translate: jest.fn(),
      scale: jest.fn()
    })),
    createSelectorQuery: jest.fn(() => ({
      in: jest.fn(() => ({
        select: jest.fn(() => ({
          fields: jest.fn(() => ({
            exec: jest.fn()
          }))
        }))
      }))
    })),
    getSystemInfoSync: jest.fn(() => ({
      pixelRatio: 2
    }))
  }

  // 模拟交互式图表组件的核心方法
  const interactiveChartMethods = {
    // 初始状态
    scale: 1,
    offsetX: 0,
    offsetY: 0,
    enableZoom: false,
    enablePan: false,
    
    // 测试数据
    chartData: [
      { date: '2024-01-01', value: 120 },
      { date: '2024-02-01', value: 135 },
      { date: '2024-03-01', value: 125 }
    ],
    
    normalRange: { min: 90, max: 140 },
    
    dataPoints: [
      { x: 100, y: 100, data: { date: '2024-01-01', value: 120 }, isAbnormal: false },
      { x: 200, y: 80, data: { date: '2024-02-01', value: 135 }, isAbnormal: false },
      { x: 300, y: 90, data: { date: '2024-03-01', value: 125 }, isAbnormal: false }
    ],

    // 交互控制方法
    toggleZoom() {
      this.enableZoom = !this.enableZoom
      if (this.enableZoom) {
        this.enablePan = false
      }
      return this.enableZoom
    },

    togglePan() {
      this.enablePan = !this.enablePan
      if (this.enablePan) {
        this.enableZoom = false
      }
      return this.enablePan
    },

    resetView() {
      this.scale = 1
      this.offsetX = 0
      this.offsetY = 0
      return { scale: this.scale, offsetX: this.offsetX, offsetY: this.offsetY }
    },

    zoomIn() {
      this.scale = Math.min(this.scale * 1.2, 3)
      return this.scale
    },

    zoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.5)
      return this.scale
    },

    // 触摸事件处理
    handlePan(deltaX, deltaY) {
      if (!this.enablePan) return { offsetX: this.offsetX, offsetY: this.offsetY }
      
      this.offsetX += deltaX
      this.offsetY += deltaY
      
      return { offsetX: this.offsetX, offsetY: this.offsetY }
    },

    handleZoom(scaleChange) {
      if (!this.enableZoom) return this.scale
      
      this.scale *= scaleChange
      this.scale = Math.max(0.5, Math.min(3, this.scale))
      
      return this.scale
    },

    // 数据点查找
    findNearestDataPoint(x, y, threshold = 20) {
      for (const point of this.dataPoints) {
        const distance = Math.sqrt(
          Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2)
        )
        
        if (distance <= threshold) {
          return point
        }
      }
      
      return null
    },

    // 提示框数据生成
    generateTooltipData(point, x, y, width = 350) {
      return {
        show: true,
        x: Math.min(x, width - 120),
        y: Math.max(y - 60, 10),
        date: this.formatDate(point.data.date),
        value: `${point.data.value}`,
        status: point.isAbnormal ? 'abnormal' : 'normal',
        statusText: point.isAbnormal ? '异常' : '正常'
      }
    },

    // 数据点位置缓存
    cacheDataPoints(chartData, width = 350, height = 250, scale = 1, offsetX = 0, offsetY = 0) {
      const padding = [40, 40, 40, 40]
      const chartWidth = width - padding[1] - padding[3]
      const chartHeight = height - padding[0] - padding[2]
      
      const values = chartData.map(item => item.value)
      const minValue = Math.min(...values)
      const maxValue = Math.max(...values)
      const valueRange = maxValue - minValue || 1
      
      return chartData.map((item, index) => {
        const x = padding[3] + (index / (chartData.length - 1)) * chartWidth
        const y = padding[0] + chartHeight - ((item.value - minValue) / valueRange) * chartHeight
        
        return {
          x: x * scale + offsetX,
          y: y * scale + offsetY,
          data: item,
          isAbnormal: this.isAbnormalValue(item.value)
        }
      })
    },

    // 异常值判断
    isAbnormalValue(value) {
      if (this.normalRange.min === null || this.normalRange.max === null) {
        return false
      }
      return value < this.normalRange.min || value > this.normalRange.max
    },

    // 日期格式化
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  }

  describe('交互控制功能', () => {
    beforeEach(() => {
      // 重置状态
      interactiveChartMethods.scale = 1
      interactiveChartMethods.offsetX = 0
      interactiveChartMethods.offsetY = 0
      interactiveChartMethods.enableZoom = false
      interactiveChartMethods.enablePan = false
    })

    it('应该正确切换缩放模式', () => {
      expect(interactiveChartMethods.enableZoom).toBe(false)
      
      const result = interactiveChartMethods.toggleZoom()
      
      expect(result).toBe(true)
      expect(interactiveChartMethods.enableZoom).toBe(true)
      expect(interactiveChartMethods.enablePan).toBe(false) // 应该关闭拖拽模式
    })

    it('应该正确切换拖拽模式', () => {
      expect(interactiveChartMethods.enablePan).toBe(false)
      
      const result = interactiveChartMethods.togglePan()
      
      expect(result).toBe(true)
      expect(interactiveChartMethods.enablePan).toBe(true)
      expect(interactiveChartMethods.enableZoom).toBe(false) // 应该关闭缩放模式
    })

    it('应该正确重置视图', () => {
      // 设置一些变换
      interactiveChartMethods.scale = 2
      interactiveChartMethods.offsetX = 50
      interactiveChartMethods.offsetY = 30
      
      const result = interactiveChartMethods.resetView()
      
      expect(result.scale).toBe(1)
      expect(result.offsetX).toBe(0)
      expect(result.offsetY).toBe(0)
    })

    it('应该正确处理放大操作', () => {
      interactiveChartMethods.scale = 1
      
      const newScale = interactiveChartMethods.zoomIn()
      
      expect(newScale).toBe(1.2)
      expect(interactiveChartMethods.scale).toBe(1.2)
    })

    it('应该正确处理缩小操作', () => {
      interactiveChartMethods.scale = 1.2
      
      const newScale = interactiveChartMethods.zoomOut()
      
      expect(newScale).toBe(1)
      expect(interactiveChartMethods.scale).toBe(1)
    })

    it('应该限制缩放范围', () => {
      // 测试最大缩放
      interactiveChartMethods.scale = 2.8
      interactiveChartMethods.zoomIn()
      expect(interactiveChartMethods.scale).toBe(3) // 不应超过3
      
      // 测试最小缩放
      interactiveChartMethods.scale = 0.6
      interactiveChartMethods.zoomOut()
      expect(interactiveChartMethods.scale).toBe(0.5) // 不应小于0.5
    })
  })

  describe('触摸交互处理', () => {
    beforeEach(() => {
      interactiveChartMethods.scale = 1
      interactiveChartMethods.offsetX = 0
      interactiveChartMethods.offsetY = 0
      interactiveChartMethods.enableZoom = false
      interactiveChartMethods.enablePan = false
    })

    it('应该在启用拖拽模式时处理平移', () => {
      interactiveChartMethods.enablePan = true
      
      const result = interactiveChartMethods.handlePan(20, 15)
      
      expect(result.offsetX).toBe(20)
      expect(result.offsetY).toBe(15)
    })

    it('应该在未启用拖拽模式时忽略平移', () => {
      interactiveChartMethods.enablePan = false
      
      const result = interactiveChartMethods.handlePan(20, 15)
      
      expect(result.offsetX).toBe(0)
      expect(result.offsetY).toBe(0)
    })

    it('应该在启用缩放模式时处理缩放', () => {
      interactiveChartMethods.enableZoom = true
      interactiveChartMethods.scale = 1
      
      const newScale = interactiveChartMethods.handleZoom(1.5)
      
      expect(newScale).toBe(1.5)
    })

    it('应该在未启用缩放模式时忽略缩放', () => {
      interactiveChartMethods.enableZoom = false
      interactiveChartMethods.scale = 1
      
      const newScale = interactiveChartMethods.handleZoom(1.5)
      
      expect(newScale).toBe(1)
    })
  })

  describe('数据点查找功能', () => {
    it('应该找到附近的数据点', () => {
      const point = interactiveChartMethods.findNearestDataPoint(105, 95) // 接近第一个点
      
      expect(point).toBeDefined()
      expect(point.data.value).toBe(120)
    })

    it('应该在没有附近数据点时返回null', () => {
      const point = interactiveChartMethods.findNearestDataPoint(500, 500) // 远离所有点
      
      expect(point).toBeNull()
    })

    it('应该使用自定义阈值', () => {
      const point = interactiveChartMethods.findNearestDataPoint(130, 130, 50) // 使用更大的阈值
      
      expect(point).toBeDefined()
      expect(point.data.value).toBe(120)
    })

    it('应该在阈值范围外返回null', () => {
      const point = interactiveChartMethods.findNearestDataPoint(130, 130, 10) // 使用较小的阈值
      
      expect(point).toBeNull()
    })
  })

  describe('提示框数据生成', () => {
    it('应该生成正确的提示框数据', () => {
      const point = interactiveChartMethods.dataPoints[0]
      const tooltip = interactiveChartMethods.generateTooltipData(point, 150, 100)
      
      expect(tooltip.show).toBe(true)
      expect(tooltip.x).toBe(150)
      expect(tooltip.y).toBe(40) // Math.max(100 - 60, 10)
      expect(tooltip.date).toBe('1月1日')
      expect(tooltip.value).toBe('120')
      expect(tooltip.status).toBe('normal')
      expect(tooltip.statusText).toBe('正常')
    })

    it('应该防止提示框超出边界', () => {
      const point = interactiveChartMethods.dataPoints[0]
      const tooltip = interactiveChartMethods.generateTooltipData(point, 300, 5, 350)
      
      expect(tooltip.x).toBe(230) // Math.min(300, 350 - 120)
      expect(tooltip.y).toBe(10) // Math.max(5 - 60, 10)
    })

    it('应该正确标识异常值', () => {
      // 创建一个异常值数据点
      const abnormalPoint = {
        x: 100,
        y: 100,
        data: { date: '2024-01-01', value: 160 }, // 超出正常范围
        isAbnormal: true
      }
      
      const tooltip = interactiveChartMethods.generateTooltipData(abnormalPoint, 150, 100)
      
      expect(tooltip.status).toBe('abnormal')
      expect(tooltip.statusText).toBe('异常')
    })
  })

  describe('数据点位置缓存', () => {
    it('应该正确计算数据点位置', () => {
      const cachedPoints = interactiveChartMethods.cacheDataPoints(
        interactiveChartMethods.chartData
      )
      
      expect(cachedPoints.length).toBe(3)
      cachedPoints.forEach(point => {
        expect(point.x).toBeGreaterThan(0)
        expect(point.y).toBeGreaterThan(0)
        expect(point.data).toBeDefined()
        expect(typeof point.isAbnormal).toBe('boolean')
      })
    })

    it('应该应用缩放和偏移', () => {
      const cachedPoints = interactiveChartMethods.cacheDataPoints(
        interactiveChartMethods.chartData,
        350, 250, 2, 10, 5 // scale=2, offsetX=10, offsetY=5
      )
      
      // 验证缩放和偏移被正确应用
      expect(cachedPoints[0].x).toBeGreaterThan(10) // 应该包含偏移
      expect(cachedPoints[0].y).toBeGreaterThan(5)
    })

    it('应该正确标识异常值', () => {
      const testData = [
        { date: '2024-01-01', value: 80 },  // 异常低值
        { date: '2024-02-01', value: 120 }, // 正常值
        { date: '2024-03-01', value: 160 }  // 异常高值
      ]
      
      const cachedPoints = interactiveChartMethods.cacheDataPoints(testData)
      
      expect(cachedPoints[0].isAbnormal).toBe(true)  // 80 < 90
      expect(cachedPoints[1].isAbnormal).toBe(false) // 90 <= 120 <= 140
      expect(cachedPoints[2].isAbnormal).toBe(true)  // 160 > 140
    })
  })

  describe('异常值判断', () => {
    it('应该正确判断正常值', () => {
      expect(interactiveChartMethods.isAbnormalValue(120)).toBe(false)
      expect(interactiveChartMethods.isAbnormalValue(90)).toBe(false)  // 边界值
      expect(interactiveChartMethods.isAbnormalValue(140)).toBe(false) // 边界值
    })

    it('应该正确判断异常值', () => {
      expect(interactiveChartMethods.isAbnormalValue(80)).toBe(true)  // 偏低
      expect(interactiveChartMethods.isAbnormalValue(150)).toBe(true) // 偏高
    })

    it('应该在没有正常范围时返回false', () => {
      const originalRange = interactiveChartMethods.normalRange
      interactiveChartMethods.normalRange = { min: null, max: null }
      
      expect(interactiveChartMethods.isAbnormalValue(80)).toBe(false)
      expect(interactiveChartMethods.isAbnormalValue(150)).toBe(false)
      
      // 恢复原始范围
      interactiveChartMethods.normalRange = originalRange
    })
  })

  describe('日期格式化', () => {
    it('应该正确格式化日期', () => {
      expect(interactiveChartMethods.formatDate('2024-03-15')).toBe('3月15日')
      expect(interactiveChartMethods.formatDate('2024-12-01')).toBe('12月1日')
      expect(interactiveChartMethods.formatDate('2024-01-31')).toBe('1月31日')
    })
  })

  describe('Canvas API 调用', () => {
    it('应该支持变换操作', () => {
      const ctx = uni.createCanvasContext('testChart')
      
      // 测试保存和恢复状态
      ctx.save()
      expect(ctx.save).toHaveBeenCalled()
      
      // 测试变换操作
      ctx.translate(10, 5)
      expect(ctx.translate).toHaveBeenCalledWith(10, 5)
      
      ctx.scale(2, 2)
      expect(ctx.scale).toHaveBeenCalledWith(2, 2)
    })
  })

  describe('边界情况处理', () => {
    it('应该处理空数据', () => {
      const cachedPoints = interactiveChartMethods.cacheDataPoints([])
      expect(cachedPoints.length).toBe(0)
    })

    it('应该处理单个数据点', () => {
      const singleData = [{ date: '2024-01-01', value: 120 }]
      const cachedPoints = interactiveChartMethods.cacheDataPoints(singleData)
      
      expect(cachedPoints.length).toBe(1)
      expect(cachedPoints[0].data.value).toBe(120)
    })

    it('应该处理相同值的数据', () => {
      const sameValueData = [
        { date: '2024-01-01', value: 120 },
        { date: '2024-02-01', value: 120 },
        { date: '2024-03-01', value: 120 }
      ]
      
      const cachedPoints = interactiveChartMethods.cacheDataPoints(sameValueData)
      
      expect(cachedPoints.length).toBe(3)
      cachedPoints.forEach(point => {
        expect(point.data.value).toBe(120)
      })
    })
  })
})