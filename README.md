# 健康报告管理应用

基于uni-app框架开发的跨平台健康管理应用，支持APP（iOS/Android）和微信小程序双端运行。

## 项目结构

```
├── components/          # 组件目录
│   ├── common/         # 通用组件
│   ├── business/       # 业务组件
│   └── layout/         # 布局组件
├── pages/              # 页面目录
│   ├── index/          # 首页
│   ├── auth/           # 认证相关页面
│   ├── report/         # 报告相关页面
│   ├── analysis/       # 数据分析页面
│   ├── profile/        # 个人中心
│   └── settings/       # 设置页面
├── services/           # 业务服务
│   ├── auth/           # 认证服务
│   ├── report/         # 报告服务
│   ├── ocr/            # OCR服务
│   ├── sync/           # 同步服务
│   └── analytics/      # 分析服务
├── stores/             # 状态管理
│   ├── app.js          # 应用状态
│   ├── user.js         # 用户状态
│   ├── report.js       # 报告状态
│   └── sync.js         # 同步状态
├── utils/              # 工具函数
│   ├── platform/       # 平台适配
│   ├── storage/        # 存储工具
│   └── errorHandler.js # 错误处理
├── config/             # 配置文件
│   └── index.js        # 应用配置
├── static/             # 静态资源
├── App.vue             # 应用入口
├── main.js             # 主入口文件
├── pages.json          # 页面配置
├── manifest.json       # 应用配置
└── package.json        # 依赖配置
```

## 技术栈

- **前端框架**: Vue 3 + uni-app
- **状态管理**: Pinia
- **本地存储**: uni-storage-async + SQLite
- **网络请求**: uni.request
- **图表组件**: uCharts
- **OCR服务**: 百度OCR API / 腾讯OCR API

## 功能特性

- ✅ 跨平台支持（APP + 微信小程序）
- ✅ 用户认证和账户管理
- ✅ 健康报告录入和管理
- ✅ OCR自动识别
- ✅ 数据可视化分析
- ✅ 云端数据同步
- ✅ 离线数据存储
- ✅ 全局错误处理

## 开发环境

1. 安装依赖
```bash
npm install
```

2. 运行开发服务器
```bash
# H5端
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# APP端
npm run dev:app-plus
```

3. 构建生产版本
```bash
# H5端
npm run build:h5

# 微信小程序
npm run build:mp-weixin

# APP端
npm run build:app-plus
```

## 项目状态

当前已完成：
- [x] 项目基础架构搭建
- [x] 目录结构创建
- [x] Pinia状态管理配置
- [x] 全局错误处理机制
- [x] 页面路由配置
- [x] 全局样式定义
- [x] 基础页面创建

待实现功能：
- [ ] 平台适配层
- [ ] 本地数据存储
- [ ] 用户认证系统
- [ ] 图片上传和OCR识别
- [ ] 健康报告管理
- [ ] 数据可视化
- [ ] 云端同步服务

## 许可证

MIT License