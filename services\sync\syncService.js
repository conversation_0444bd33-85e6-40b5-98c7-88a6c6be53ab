/**
 * 数据同步服务
 * 负责本地数据与云端的双向同步
 */

import { cloudApiService } from './cloudApi.js';
import { storage } from '../../utils/storage/index.js';
import { useSyncStore } from '../../stores/sync.js';

class SyncService {
  constructor() {
    this.isRunning = false;
    this.syncQueue = [];
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5秒
    this.batchSize = 10; // 批量同步大小
    this.syncStore = null;
  }

  /**
   * 初始化同步服务
   */
  async initialize() {
    try {
      this.syncStore = useSyncStore();
      await this.syncStore.initSync();
      console.log('同步服务初始化成功');
    } catch (error) {
      console.error('同步服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 开始同步
   * @param {Object} options 同步选项
   * @returns {Promise<Object>}
   */
  async startSync(options = {}) {
    if (this.isRunning) {
      return { success: false, message: '同步正在进行中' };
    }

    const {
      userId,
      syncType = 'full', // 'full', 'upload', 'download'
      force = false,
      background = false
    } = options;

    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    try {
      this.isRunning = true;
      this.syncStore.updateSyncStatus({ isRunning: true });

      // 检查网络连接
      const isConnected = await cloudApiService.checkNetworkConnection();
      if (!isConnected) {
        throw new Error('网络连接不可用');
      }

      // 检查WiFi限制
      if (this.syncStore.syncConfig.wifiOnly && !background) {
        const networkType = await this.getNetworkType();
        if (networkType !== 'wifi') {
          throw new Error('当前设置仅允许WiFi环境下同步');
        }
      }

      const syncResult = {
        success: true,
        startTime: Date.now(),
        uploaded: 0,
        downloaded: 0,
        conflicts: 0,
        errors: []
      };

      // 执行同步操作
      if (syncType === 'full' || syncType === 'upload') {
        const uploadResult = await this.performUpload(userId);
        syncResult.uploaded = uploadResult.count;
        syncResult.errors.push(...uploadResult.errors);
      }

      if (syncType === 'full' || syncType === 'download') {
        const downloadResult = await this.performDownload(userId);
        syncResult.downloaded = downloadResult.count;
        syncResult.conflicts = downloadResult.conflicts;
        syncResult.errors.push(...downloadResult.errors);
      }

      syncResult.endTime = Date.now();
      syncResult.duration = syncResult.endTime - syncResult.startTime;

      // 记录同步结果
      this.syncStore.addSyncRecord({
        type: 'sync',
        status: syncResult.errors.length === 0 ? 'success' : 'partial',
        details: syncResult,
        message: `上传${syncResult.uploaded}条，下载${syncResult.downloaded}条，冲突${syncResult.conflicts}条`
      });

      // 更新同步状态
      this.syncStore.updateSyncStatus({
        lastSyncTime: Date.now(),
        nextSyncTime: Date.now() + this.syncStore.syncConfig.syncInterval * 60 * 1000
      });

      return syncResult;

    } catch (error) {
      console.error('同步失败:', error);

      this.syncStore.addSyncRecord({
        type: 'sync',
        status: 'failed',
        message: error.message
      });

      return { success: false, error: error.message };

    } finally {
      this.isRunning = false;
      this.syncStore.updateSyncStatus({ isRunning: false });
    }
  }

  /**
   * 执行上传操作
   * @param {number} userId 
   * @returns {Promise<Object>}
   */
  async performUpload(userId) {
    const result = { count: 0, errors: [] };

    try {
      // 获取待同步的记录
      const pendingRecords = await storage.sync.getPendingSyncRecords(userId);
      const uploadRecords = pendingRecords.filter(r => 
        ['INSERT', 'UPDATE'].includes(r.operation_type)
      );

      // 按表分组
      const groupedRecords = this.groupRecordsByTable(uploadRecords);

      // 上传用户数据
      if (groupedRecords.users && groupedRecords.users.length > 0) {
        try {
          const userData = await storage.users.findById(userId);
          if (userData) {
            await cloudApiService.uploadUserData(userData);
            await this.markRecordsAsSynced(groupedRecords.users);
            result.count += groupedRecords.users.length;
          }
        } catch (error) {
          result.errors.push(`用户数据上传失败: ${error.message}`);
        }
      }

      // 上传健康报告
      if (groupedRecords.health_reports && groupedRecords.health_reports.length > 0) {
        try {
          const reportIds = groupedRecords.health_reports.map(r => r.record_id);
          const reports = [];
          
          for (const reportId of reportIds) {
            const report = await storage.reports.findById(reportId);
            if (report) {
              reports.push(report);
            }
          }

          if (reports.length > 0) {
            await cloudApiService.uploadHealthReports(reports);
            await this.markRecordsAsSynced(groupedRecords.health_reports);
            result.count += reports.length;
          }
        } catch (error) {
          result.errors.push(`健康报告上传失败: ${error.message}`);
        }
      }

      // 上传健康指标
      if (groupedRecords.health_indicators && groupedRecords.health_indicators.length > 0) {
        try {
          const indicatorIds = groupedRecords.health_indicators.map(r => r.record_id);
          const indicators = [];
          
          for (const indicatorId of indicatorIds) {
            const indicator = await storage.indicators.findById(indicatorId);
            if (indicator) {
              indicators.push(indicator);
            }
          }

          if (indicators.length > 0) {
            await cloudApiService.uploadHealthIndicators(indicators);
            await this.markRecordsAsSynced(groupedRecords.health_indicators);
            result.count += indicators.length;
          }
        } catch (error) {
          result.errors.push(`健康指标上传失败: ${error.message}`);
        }
      }

    } catch (error) {
      result.errors.push(`上传操作失败: ${error.message}`);
    }

    return result;
  }

  /**
   * 执行下载操作
   * @param {number} userId 
   * @returns {Promise<Object>}
   */
  async performDownload(userId) {
    const result = { count: 0, conflicts: 0, errors: [] };

    try {
      const lastSyncTime = this.syncStore.syncStatus.lastSyncTime || 0;

      // 下载用户数据
      try {
        const userResponse = await cloudApiService.downloadUserData(userId, lastSyncTime);
        if (userResponse.data) {
          const conflict = await this.mergeUserData(userId, userResponse.data);
          if (conflict) {
            result.conflicts++;
          } else {
            result.count++;
          }
        }
      } catch (error) {
        result.errors.push(`用户数据下载失败: ${error.message}`);
      }

      // 下载健康报告
      try {
        const reportsResponse = await cloudApiService.downloadHealthReports(userId, lastSyncTime);
        if (reportsResponse.data && Array.isArray(reportsResponse.data)) {
          for (const reportData of reportsResponse.data) {
            const conflict = await this.mergeHealthReport(userId, reportData);
            if (conflict) {
              result.conflicts++;
            } else {
              result.count++;
            }
          }
        }
      } catch (error) {
        result.errors.push(`健康报告下载失败: ${error.message}`);
      }

      // 下载健康指标
      try {
        const indicatorsResponse = await cloudApiService.downloadHealthIndicators(userId, lastSyncTime);
        if (indicatorsResponse.data && Array.isArray(indicatorsResponse.data)) {
          for (const indicatorData of indicatorsResponse.data) {
            const conflict = await this.mergeHealthIndicator(userId, indicatorData);
            if (conflict) {
              result.conflicts++;
            } else {
              result.count++;
            }
          }
        }
      } catch (error) {
        result.errors.push(`健康指标下载失败: ${error.message}`);
      }

    } catch (error) {
      result.errors.push(`下载操作失败: ${error.message}`);
    }

    return result;
  }

  /**
   * 合并用户数据
   * @param {number} userId 
   * @param {Object} remoteData 
   * @returns {Promise<boolean>} 是否产生冲突
   */
  async mergeUserData(userId, remoteData) {
    try {
      const localData = await storage.users.findById(userId);
      
      if (!localData) {
        // 本地没有数据，直接创建
        await storage.users.create(remoteData);
        return false;
      }

      // 检查是否有冲突
      const hasConflict = this.detectDataConflict(localData, remoteData);
      
      if (hasConflict) {
        // 添加冲突记录
        this.syncStore.addConflict({
          type: 'user_info',
          localData,
          remoteData
        });
        return true;
      } else {
        // 无冲突，更新本地数据
        await storage.users.update(userId, remoteData);
        return false;
      }
    } catch (error) {
      console.error('合并用户数据失败:', error);
      throw error;
    }
  }

  /**
   * 合并健康报告数据
   * @param {number} userId 
   * @param {Object} remoteData 
   * @returns {Promise<boolean>} 是否产生冲突
   */
  async mergeHealthReport(userId, remoteData) {
    try {
      const localData = await storage.reports.findById(remoteData.id);
      
      if (!localData) {
        // 本地没有数据，直接创建
        await storage.reports.create({ ...remoteData, user_id: userId });
        return false;
      }

      // 检查是否有冲突
      const hasConflict = this.detectDataConflict(localData, remoteData);
      
      if (hasConflict) {
        // 添加冲突记录
        this.syncStore.addConflict({
          type: 'health_report',
          localData,
          remoteData
        });
        return true;
      } else {
        // 无冲突，更新本地数据
        await storage.reports.update(remoteData.id, remoteData);
        return false;
      }
    } catch (error) {
      console.error('合并健康报告数据失败:', error);
      throw error;
    }
  }

  /**
   * 合并健康指标数据
   * @param {number} userId 
   * @param {Object} remoteData 
   * @returns {Promise<boolean>} 是否产生冲突
   */
  async mergeHealthIndicator(userId, remoteData) {
    try {
      const localData = await storage.indicators.findById(remoteData.id);
      
      if (!localData) {
        // 本地没有数据，直接创建
        await storage.indicators.create(remoteData);
        return false;
      }

      // 检查是否有冲突
      const hasConflict = this.detectDataConflict(localData, remoteData);
      
      if (hasConflict) {
        // 添加冲突记录
        this.syncStore.addConflict({
          type: 'health_indicator',
          localData,
          remoteData
        });
        return true;
      } else {
        // 无冲突，更新本地数据
        await storage.indicators.update(remoteData.id, remoteData);
        return false;
      }
    } catch (error) {
      console.error('合并健康指标数据失败:', error);
      throw error;
    }
  }

  /**
   * 检测数据冲突
   * @param {Object} localData 
   * @param {Object} remoteData 
   * @returns {boolean}
   */
  detectDataConflict(localData, remoteData) {
    // 比较更新时间
    const localUpdateTime = new Date(localData.updated_at).getTime();
    const remoteUpdateTime = new Date(remoteData.updated_at).getTime();
    
    // 如果远程数据更新时间更早，可能存在冲突
    if (remoteUpdateTime < localUpdateTime) {
      // 进一步检查关键字段是否不同
      const keyFields = ['report_title', 'report_date', 'hospital_name', 'indicator_value'];
      
      for (const field of keyFields) {
        if (localData[field] !== remoteData[field]) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 按表分组记录
   * @param {Array} records 
   * @returns {Object}
   */
  groupRecordsByTable(records) {
    return records.reduce((groups, record) => {
      const tableName = record.table_name;
      if (!groups[tableName]) {
        groups[tableName] = [];
      }
      groups[tableName].push(record);
      return groups;
    }, {});
  }

  /**
   * 标记记录为已同步
   * @param {Array} records 
   */
  async markRecordsAsSynced(records) {
    for (const record of records) {
      await storage.sync.updateSyncStatus(record.id, 1); // 1表示同步成功
    }
  }

  /**
   * 获取网络类型
   * @returns {Promise<string>}
   */
  async getNetworkType() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          resolve(res.networkType);
        },
        fail: () => {
          resolve('unknown');
        }
      });
    });
  }

  /**
   * 添加数据到同步队列
   * @param {string} tableName 
   * @param {number} recordId 
   * @param {string} operationType 
   * @param {number} userId 
   */
  async addToSyncQueue(tableName, recordId, operationType, userId) {
    try {
      await storage.sync.createSyncRecord(userId, tableName, recordId, operationType);
      
      // 如果启用自动同步，触发同步
      if (this.syncStore.syncConfig.autoSync && !this.isRunning) {
        setTimeout(() => {
          this.startSync({ userId, background: true });
        }, 1000);
      }
    } catch (error) {
      console.error('添加同步队列失败:', error);
    }
  }

  /**
   * 清理同步队列
   * @param {number} userId 
   */
  async cleanupSyncQueue(userId) {
    try {
      // 删除已成功同步的记录（保留最近7天的记录）
      const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
      
      await storage.sync.deleteMany({
        user_id: userId,
        sync_status: 1, // 已同步
        last_sync_at: { $lt: new Date(sevenDaysAgo).toISOString() }
      });
    } catch (error) {
      console.error('清理同步队列失败:', error);
    }
  }

  /**
   * 停止同步
   */
  stopSync() {
    this.isRunning = false;
    this.syncStore.updateSyncStatus({ isRunning: false });
  }

  /**
   * 获取同步状态
   * @returns {Object}
   */
  getSyncStatus() {
    return {
      isRunning: this.isRunning,
      ...this.syncStore.syncStatus
    };
  }
}

// 导出单例实例
export const syncService = new SyncService();
export default syncService;