/**
 * 用户注册功能简单测试
 */

// Mock uni-app API
global.uni = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn().mockReturnValue(null),
  removeStorageSync: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  navigateTo: jest.fn(),
  reLaunch: jest.fn(),
  request: jest.fn()
}

describe('用户注册基本功能测试', () => {
  test('基本测试', () => {
    expect(1 + 1).toBe(2)
  })

  test('手机号验证', () => {
    const phoneRegex = /^1[3-9]\d{9}$/
    
    expect(phoneRegex.test('13800138000')).toBe(true)
    expect(phoneRegex.test('15912345678')).toBe(true)
    expect(phoneRegex.test('1380013800')).toBe(false)
    expect(phoneRegex.test('138001380001')).toBe(false)
  })

  test('密码验证', () => {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/
    
    expect(passwordRegex.test('abc12345')).toBe(true)
    expect(passwordRegex.test('Test123456')).toBe(true)
    expect(passwordRegex.test('12345678')).toBe(false)
    expect(passwordRegex.test('abcdefgh')).toBe(false)
    expect(passwordRegex.test('abc123')).toBe(false)
  })

  test('验证码验证', () => {
    const codeRegex = /^\d{6}$/
    
    expect(codeRegex.test('123456')).toBe(true)
    expect(codeRegex.test('000000')).toBe(true)
    expect(codeRegex.test('12345')).toBe(false)
    expect(codeRegex.test('1234567')).toBe(false)
    expect(codeRegex.test('12345a')).toBe(false)
  })
})