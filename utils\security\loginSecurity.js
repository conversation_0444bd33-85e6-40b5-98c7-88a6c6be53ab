/**
 * 登录安全检测和异常监控
 */

import securityManager from './encryption.js'

class LoginSecurityManager {
  constructor() {
    this.maxFailedAttempts = 5 // 最大失败尝试次数
    this.lockoutDuration = 30 * 60 * 1000 // 锁定时长30分钟
    this.suspiciousThreshold = 3 // 可疑行为阈值
  }

  /**
   * 记录登录尝试
   */
  recordLoginAttempt(phone, success, deviceInfo = {}) {
    try {
      const attempt = {
        phone,
        success,
        timestamp: Date.now(),
        deviceInfo: {
          fingerprint: securityManager.generateDeviceFingerprint(),
          ip: deviceInfo.ip || 'unknown',
          location: deviceInfo.location || 'unknown',
          userAgent: deviceInfo.userAgent || uni.getSystemInfoSync().system
        }
      }

      // 获取历史记录
      let attempts = this.getLoginAttempts(phone)
      attempts.unshift(attempt)

      // 只保留最近100次记录
      if (attempts.length > 100) {
        attempts = attempts.slice(0, 100)
      }

      // 保存记录
      securityManager.setSecureStorage(`login_attempts_${phone}`, attempts)

      // 检测异常行为
      this.detectAnomalousActivity(phone, attempt)

      return attempt
    } catch (error) {
      console.error('记录登录尝试失败:', error)
    }
  }

  /**
   * 获取登录尝试记录
   */
  getLoginAttempts(phone) {
    try {
      return securityManager.getSecureStorage(`login_attempts_${phone}`) || []
    } catch (error) {
      console.error('获取登录记录失败:', error)
      return []
    }
  }

  /**
   * 检查账户是否被锁定
   */
  isAccountLocked(phone) {
    try {
      const lockInfo = securityManager.getSecureStorage(`account_lock_${phone}`)
      if (!lockInfo) return false

      // 检查锁定是否已过期
      if (Date.now() - lockInfo.timestamp > this.lockoutDuration) {
        // 解除锁定
        this.unlockAccount(phone)
        return false
      }

      return true
    } catch (error) {
      console.error('检查账户锁定状态失败:', error)
      return false
    }
  }

  /**
   * 锁定账户
   */
  lockAccount(phone, reason = '多次登录失败') {
    try {
      const lockInfo = {
        timestamp: Date.now(),
        reason,
        duration: this.lockoutDuration
      }

      securityManager.setSecureStorage(`account_lock_${phone}`, lockInfo)

      // 发送安全提醒
      this.sendSecurityAlert(phone, 'account_locked', {
        reason,
        duration: Math.floor(this.lockoutDuration / 60000) // 转换为分钟
      })

      console.log(`账户 ${phone} 已被锁定: ${reason}`)
    } catch (error) {
      console.error('锁定账户失败:', error)
    }
  }

  /**
   * 解除账户锁定
   */
  unlockAccount(phone) {
    try {
      securityManager.clearSecureStorage(`account_lock_${phone}`)
      console.log(`账户 ${phone} 锁定已解除`)
    } catch (error) {
      console.error('解除账户锁定失败:', error)
    }
  }

  /**
   * 检测连续登录失败
   */
  checkFailedAttempts(phone) {
    const attempts = this.getLoginAttempts(phone)
    const recentAttempts = attempts.filter(attempt => 
      Date.now() - attempt.timestamp < 60 * 60 * 1000 // 最近1小时
    )

    const failedAttempts = recentAttempts.filter(attempt => !attempt.success)
    
    if (failedAttempts.length >= this.maxFailedAttempts) {
      this.lockAccount(phone, `连续${failedAttempts.length}次登录失败`)
      return true
    }

    return false
  }

  /**
   * 检测异常登录行为
   */
  detectAnomalousActivity(phone, currentAttempt) {
    try {
      const attempts = this.getLoginAttempts(phone)
      const recentAttempts = attempts.filter(attempt => 
        Date.now() - attempt.timestamp < 24 * 60 * 60 * 1000 // 最近24小时
      ).slice(0, 10) // 最近10次

      if (recentAttempts.length < 2) return

      const anomalies = []

      // 检测设备变化
      const deviceFingerprints = new Set(recentAttempts.map(a => a.deviceInfo.fingerprint))
      if (deviceFingerprints.size > 3) {
        anomalies.push('多设备登录')
      }

      // 检测地理位置异常
      const locations = recentAttempts.map(a => a.deviceInfo.location).filter(l => l !== 'unknown')
      if (locations.length > 1) {
        const uniqueLocations = new Set(locations)
        if (uniqueLocations.size > 2) {
          anomalies.push('异地登录')
        }
      }

      // 检测登录频率异常
      const timeIntervals = []
      for (let i = 1; i < recentAttempts.length; i++) {
        const interval = recentAttempts[i-1].timestamp - recentAttempts[i].timestamp
        timeIntervals.push(interval)
      }
      
      const avgInterval = timeIntervals.reduce((a, b) => a + b, 0) / timeIntervals.length
      if (avgInterval < 5 * 60 * 1000) { // 平均间隔小于5分钟
        anomalies.push('高频登录')
      }

      // 检测用户代理异常
      const userAgents = new Set(recentAttempts.map(a => a.deviceInfo.userAgent))
      if (userAgents.size > 2) {
        anomalies.push('多种设备类型')
      }

      // 如果检测到异常，发送安全提醒
      if (anomalies.length >= this.suspiciousThreshold) {
        this.sendSecurityAlert(phone, 'suspicious_activity', {
          anomalies,
          attemptInfo: currentAttempt
        })
      }

    } catch (error) {
      console.error('异常行为检测失败:', error)
    }
  }

  /**
   * 发送安全提醒
   */
  async sendSecurityAlert(phone, alertType, details = {}) {
    try {
      const alertMessages = {
        account_locked: `您的账户因${details.reason}已被临时锁定${details.duration}分钟，如非本人操作请及时修改密码`,
        suspicious_activity: `检测到您的账户存在异常登录行为：${details.anomalies.join('、')}，如非本人操作请及时修改密码`,
        new_device_login: `检测到新设备登录，如非本人操作请及时修改密码`,
        password_changed: '您的密码已成功修改，如非本人操作请联系客服',
        account_deleted: '您的账户已被删除，相关数据将在7天后永久清除'
      }

      const message = alertMessages[alertType] || '检测到账户安全异常'

      // 创建安全提醒记录
      const alert = {
        id: Date.now().toString(),
        phone,
        type: alertType,
        message,
        details,
        timestamp: Date.now(),
        read: false
      }

      // 保存提醒记录
      let alerts = securityManager.getSecureStorage(`security_alerts_${phone}`) || []
      alerts.unshift(alert)

      // 只保留最近50条提醒
      if (alerts.length > 50) {
        alerts = alerts.slice(0, 50)
      }

      securityManager.setSecureStorage(`security_alerts_${phone}`, alerts)

      // 显示系统通知
      if (uni.getStorageSync('enable_security_notifications') !== false) {
        uni.showToast({
          title: '安全提醒',
          icon: 'none',
          duration: 3000
        })

        // 如果支持推送通知，发送推送
        this.sendPushNotification(phone, message)
      }

      console.log(`安全提醒已发送: ${alertType} - ${message}`)
      return alert
    } catch (error) {
      console.error('发送安全提醒失败:', error)
    }
  }

  /**
   * 发送推送通知
   */
  async sendPushNotification(phone, message) {
    try {
      // 这里应该调用推送服务API
      // 例如：极光推送、个推等
      console.log(`推送通知: ${phone} - ${message}`)
    } catch (error) {
      console.error('发送推送通知失败:', error)
    }
  }

  /**
   * 获取安全提醒列表
   */
  getSecurityAlerts(phone) {
    try {
      return securityManager.getSecureStorage(`security_alerts_${phone}`) || []
    } catch (error) {
      console.error('获取安全提醒失败:', error)
      return []
    }
  }

  /**
   * 标记提醒为已读
   */
  markAlertAsRead(phone, alertId) {
    try {
      const alerts = this.getSecurityAlerts(phone)
      const alert = alerts.find(a => a.id === alertId)
      if (alert) {
        alert.read = true
        securityManager.setSecureStorage(`security_alerts_${phone}`, alerts)
      }
    } catch (error) {
      console.error('标记提醒已读失败:', error)
    }
  }

  /**
   * 清除安全提醒
   */
  clearSecurityAlerts(phone) {
    try {
      securityManager.clearSecureStorage(`security_alerts_${phone}`)
    } catch (error) {
      console.error('清除安全提醒失败:', error)
    }
  }

  /**
   * 生成登录安全报告
   */
  generateSecurityReport(phone) {
    try {
      const attempts = this.getLoginAttempts(phone)
      const alerts = this.getSecurityAlerts(phone)
      const lockInfo = securityManager.getSecureStorage(`account_lock_${phone}`)

      const report = {
        phone,
        generatedAt: Date.now(),
        summary: {
          totalAttempts: attempts.length,
          successfulLogins: attempts.filter(a => a.success).length,
          failedAttempts: attempts.filter(a => !a.success).length,
          uniqueDevices: new Set(attempts.map(a => a.deviceInfo.fingerprint)).size,
          securityAlerts: alerts.length,
          isLocked: !!lockInfo
        },
        recentActivity: attempts.slice(0, 10),
        securityAlerts: alerts.slice(0, 10),
        lockStatus: lockInfo
      }

      return report
    } catch (error) {
      console.error('生成安全报告失败:', error)
      return null
    }
  }

  /**
   * 重置安全数据
   */
  resetSecurityData(phone) {
    try {
      securityManager.clearSecureStorage(`login_attempts_${phone}`)
      securityManager.clearSecureStorage(`security_alerts_${phone}`)
      securityManager.clearSecureStorage(`account_lock_${phone}`)
      console.log(`用户 ${phone} 的安全数据已重置`)
    } catch (error) {
      console.error('重置安全数据失败:', error)
    }
  }
}

// 创建全局登录安全管理器实例
const loginSecurityManager = new LoginSecurityManager()

export default loginSecurityManager
export { LoginSecurityManager }