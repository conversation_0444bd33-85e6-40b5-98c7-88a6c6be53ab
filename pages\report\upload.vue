<template>
  <view class="upload-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">上传健康报告</text>
      <text class="page-subtitle">拍照或选择图片，自动识别检查结果</text>
    </view>

    <!-- 图片上传和OCR组件 -->
    <ImageUploadOCR @save="handleSaveReport" />

    <!-- 最近上传的报告 -->
    <view v-if="recentReports.length > 0" class="recent-reports">
      <view class="section-title">最近上传</view>
      
      <view class="reports-list">
        <view 
          v-for="(report, index) in recentReports" 
          :key="index"
          class="report-item"
          @click="viewReport(report)"
        >
          <image 
            :src="report.imageData.processedPath" 
            class="report-thumbnail"
            mode="aspectFill"
          />
          
          <view class="report-info">
            <text class="report-date">{{ formatDate(report.ocrData.date) }}</text>
            <text class="report-hospital">{{ report.ocrData.hospital || '未知医院' }}</text>
            <text class="report-items">{{ report.ocrData.items.length }}项检查</text>
            
            <view class="report-status">
              <view 
                class="confidence-badge"
                :class="getConfidenceClass(report.confidence)"
              >
                {{ report.confidence }}%
              </view>
              
              <view v-if="!report.isValid" class="warning-badge">
                需要检查
              </view>
            </view>
          </view>
          
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>

    <!-- 使用提示 -->
    <view class="tips-section">
      <view class="section-title">拍摄建议</view>
      
      <view class="tips-list">
        <view class="tip-item">
          <text class="tip-icon">📷</text>
          <text class="tip-text">确保光线充足，避免阴影遮挡</text>
        </view>
        
        <view class="tip-item">
          <text class="tip-icon">📄</text>
          <text class="tip-text">保持报告平整，避免折叠和弯曲</text>
        </view>
        
        <view class="tip-item">
          <text class="tip-icon">🔍</text>
          <text class="tip-text">确保文字清晰可见，避免模糊</text>
        </view>
        
        <view class="tip-item">
          <text class="tip-icon">📐</text>
          <text class="tip-text">尽量保持垂直拍摄，减少倾斜</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import ImageUploadOCR from '../../components/business/ImageUploadOCR.vue'

export default {
  name: 'ReportUploadPage',
  components: {
    ImageUploadOCR
  },
  data() {
    return {
      recentReports: []
    }
  },
  onLoad() {
    this.loadRecentReports()
  },
  methods: {
    /**
     * 处理保存报告
     */
    async handleSaveReport(reportData) {
      try {
        // 这里应该调用实际的保存服务
        // 例如：await reportService.saveReport(reportData)
        
        // 暂时保存到本地数组中作为演示
        this.recentReports.unshift({
          id: Date.now(),
          timestamp: new Date(),
          ...reportData
        })
        
        // 限制最近报告数量
        if (this.recentReports.length > 5) {
          this.recentReports = this.recentReports.slice(0, 5)
        }
        
        // 保存到本地存储
        await this.saveReportsToStorage()
        
        uni.showToast({
          title: '报告保存成功',
          icon: 'success'
        })
        
        // 可以选择跳转到报告详情页
        // uni.navigateTo({
        //   url: `/pages/report/detail?id=${reportData.id}`
        // })
        
      } catch (error) {
        console.error('保存报告失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    },

    /**
     * 加载最近的报告
     */
    async loadRecentReports() {
      try {
        const reports = await uni.getStorage({
          key: 'recent_reports'
        })
        
        if (reports.data && Array.isArray(reports.data)) {
          this.recentReports = reports.data
        }
      } catch (error) {
        console.log('加载最近报告失败:', error)
        // 忽略错误，使用空数组
      }
    },

    /**
     * 保存报告到本地存储
     */
    async saveReportsToStorage() {
      try {
        await uni.setStorage({
          key: 'recent_reports',
          data: this.recentReports
        })
      } catch (error) {
        console.error('保存到本地存储失败:', error)
      }
    },

    /**
     * 查看报告详情
     */
    viewReport(report) {
      // 这里应该跳转到报告详情页
      uni.showModal({
        title: '报告详情',
        content: `日期: ${this.formatDate(report.ocrData.date)}\n医院: ${report.ocrData.hospital}\n检查项目: ${report.ocrData.items.length}项`,
        showCancel: false
      })
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '未知日期'
      
      try {
        const date = new Date(dateString)
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    },

    /**
     * 获取置信度样式类
     */
    getConfidenceClass(confidence) {
      if (confidence >= 80) return 'confidence-high'
      if (confidence >= 60) return 'confidence-medium'
      return 'confidence-low'
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  padding: 40rpx 20rpx 30rpx;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.page-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
}

.recent-reports {
  margin: 20rpx;
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.reports-list {
  .report-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background-color: #f8f9fa;
    }
  }
}

.report-thumbnail {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  background-color: #f0f0f0;
}

.report-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.report-date {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.report-hospital {
  font-size: 26rpx;
  color: #666;
}

.report-items {
  font-size: 24rpx;
  color: #999;
}

.report-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 8rpx;
}

.confidence-badge {
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: white;
  
  &.confidence-high {
    background-color: #4caf50;
  }
  
  &.confidence-medium {
    background-color: #ff9800;
  }
  
  &.confidence-low {
    background-color: #f44336;
  }
}

.warning-badge {
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  background-color: #fff3cd;
  color: #856404;
  border: 1rpx solid #ffc107;
}

.arrow-icon {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 16rpx;
}

.tips-section {
  margin: 20rpx;
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
}

.tips-list {
  .tip-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
</style>