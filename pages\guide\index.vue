<template>
	<view class="guide-container">
		<!-- 引导页面内容 -->
		<swiper 
			class="guide-swiper" 
			:indicator-dots="true" 
			:autoplay="false" 
			:current="currentIndex"
			@change="onSwiperChange"
			indicator-color="rgba(255,255,255,0.5)"
			indicator-active-color="#007AFF"
		>
			<!-- 第一页：欢迎 -->
			<swiper-item>
				<view class="guide-page">
					<view class="guide-content">
						<image class="guide-image" src="/static/guide/welcome.png" mode="aspectFit"></image>
						<view class="guide-text">
							<text class="guide-title">欢迎使用健康报告管理</text>
							<text class="guide-desc">轻松管理您的健康检查报告，随时掌握健康状况</text>
						</view>
					</view>
				</view>
			</swiper-item>
			
			<!-- 第二页：拍照识别 -->
			<swiper-item>
				<view class="guide-page">
					<view class="guide-content">
						<image class="guide-image" src="/static/guide/camera.png" mode="aspectFit"></image>
						<view class="guide-text">
							<text class="guide-title">智能拍照识别</text>
							<text class="guide-desc">拍摄体检报告，AI自动识别关键数据，省时省力</text>
						</view>
					</view>
				</view>
			</swiper-item>
			
			<!-- 第三页：数据分析 -->
			<swiper-item>
				<view class="guide-page">
					<view class="guide-content">
						<image class="guide-image" src="/static/guide/analysis.png" mode="aspectFit"></image>
						<view class="guide-text">
							<text class="guide-title">健康数据分析</text>
							<text class="guide-desc">专业的数据分析和趋势图表，让健康状况一目了然</text>
						</view>
					</view>
				</view>
			</swiper-item>
			
			<!-- 第四页：云端同步 -->
			<swiper-item>
				<view class="guide-page">
					<view class="guide-content">
						<image class="guide-image" src="/static/guide/sync.png" mode="aspectFit"></image>
						<view class="guide-text">
							<text class="guide-title">云端安全同步</text>
							<text class="guide-desc">数据安全加密存储，多设备同步，永不丢失</text>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
		
		<!-- 底部操作区域 -->
		<view class="guide-footer">
			<view class="guide-actions">
				<button 
					v-if="currentIndex < 3" 
					class="btn btn-secondary skip-btn" 
					@tap="skipGuide"
				>
					跳过
				</button>
				<button 
					v-if="currentIndex < 3" 
					class="btn btn-primary next-btn" 
					@tap="nextPage"
				>
					下一步
				</button>
				<button 
					v-if="currentIndex === 3" 
					class="btn btn-primary start-btn" 
					@tap="startApp"
				>
					开始使用
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { useAppStore } from '../../stores/app.js'
	
	export default {
		name: 'Guide',
		data() {
			return {
				currentIndex: 0
			}
		},
		onLoad() {
			console.log('引导页面加载')
		},
		methods: {
			// 滑动页面变化
			onSwiperChange(e) {
				this.currentIndex = e.detail.current
			},
			
			// 下一页
			nextPage() {
				if (this.currentIndex < 3) {
					this.currentIndex++
				}
			},
			
			// 跳过引导
			skipGuide() {
				this.completeGuide()
			},
			
			// 开始使用应用
			startApp() {
				this.completeGuide()
			},
			
			// 完成引导
			completeGuide() {
				const appStore = useAppStore()
				
				// 标记引导已完成
				appStore.updateSettings({
					hasCompletedGuide: true
				})
				
				// 跳转到首页
				uni.reLaunch({
					url: '/pages/index/index'
				})
			}
		}
	}
</script>

<style scoped>
	.guide-container {
		height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		flex-direction: column;
	}
	
	.guide-swiper {
		flex: 1;
		width: 100%;
	}
	
	.guide-page {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40px 30px;
	}
	
	.guide-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		max-width: 300px;
	}
	
	.guide-image {
		width: 200px;
		height: 200px;
		margin-bottom: 40px;
	}
	
	.guide-text {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.guide-title {
		font-size: 24px;
		font-weight: 600;
		color: #FFFFFF;
		margin-bottom: 15px;
		text-align: center;
	}
	
	.guide-desc {
		font-size: 16px;
		color: rgba(255, 255, 255, 0.8);
		line-height: 1.5;
		text-align: center;
	}
	
	.guide-footer {
		padding: 30px;
		background-color: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10px);
	}
	
	.guide-actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 15px;
	}
	
	.skip-btn {
		flex: 1;
		background-color: rgba(255, 255, 255, 0.2);
		color: #FFFFFF;
		border: 1px solid rgba(255, 255, 255, 0.3);
	}
	
	.next-btn,
	.start-btn {
		flex: 2;
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.start-btn {
		flex: 1;
	}
</style>