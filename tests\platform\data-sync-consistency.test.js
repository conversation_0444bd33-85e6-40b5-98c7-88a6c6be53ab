/**
 * 数据同步跨平台一致性测试
 * 验证数据在不同平台间的同步一致性
 */

import { syncService } from '../../services/sync/syncService.js'
import { conflictResolver } from '../../services/sync/conflictResolver.js'
import { PLATFORM_TYPES } from '../../utils/platform/constants.js'

// Mock存储服务
const mockStorage = {
  sync: {
    getPendingSyncRecords: jest.fn(),
    createSyncRecord: jest.fn(),
    updateSyncStatus: jest.fn(),
    deleteMany: jest.fn()
  },
  users: {
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  },
  reports: {
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  },
  indicators: {
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  }
}

// Mock云端API服务
const mockCloudApiService = {
  checkNetworkConnection: jest.fn(),
  uploadUserData: jest.fn(),
  uploadHealthReports: jest.fn(),
  uploadHealthIndicators: jest.fn(),
  downloadUserData: jest.fn(),
  downloadHealthReports: jest.fn(),
  downloadHealthIndicators: jest.fn()
}

// Mock同步Store
const mockSyncStore = {
  syncConfig: {
    autoSync: true,
    syncInterval: 30,
    wifiOnly: false
  },
  syncStatus: {
    isRunning: false,
    lastSyncTime: 0,
    nextSyncTime: 0
  },
  conflicts: [],
  initSync: jest.fn(),
  updateSyncStatus: jest.fn(),
  addSyncRecord: jest.fn(),
  addConflict: jest.fn(),
  resolveConflict: jest.fn(),
  saveToLocal: jest.fn()
}

// Mock uni对象
global.uni = {
  getNetworkType: jest.fn()
}

describe('数据同步跨平台一致性测试', () => {
  beforeEach(() => {
    // 重置所有mock
    Object.values(mockStorage).forEach(service => {
      Object.values(service).forEach(method => {
        if (method.mockClear) method.mockClear()
      })
    })
    
    Object.values(mockCloudApiService).forEach(method => {
      if (method.mockClear) method.mockClear()
    })

    Object.values(mockSyncStore).forEach(method => {
      if (method.mockClear) method.mockClear()
    })

    // 设置默认mock返回值
    mockCloudApiService.checkNetworkConnection.mockResolvedValue(true)
    mockSyncStore.initSync.mockResolvedValue()
    uni.getNetworkType.mockImplementation(({ success }) => {
      success({ networkType: 'wifi' })
    })
  })

  describe('数据格式一致性', () => {
    test('用户数据在不同平台应该保持相同格式', () => {
      const userData = {
        id: 1,
        username: 'testuser',
        phone: '13800138000',
        email: '<EMAIL>',
        real_name: '测试用户',
        gender: 'male',
        birth_date: '1990-01-01',
        avatar_url: 'https://example.com/avatar.jpg',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      // 验证数据结构
      expect(userData).toHaveProperty('id')
      expect(userData).toHaveProperty('username')
      expect(userData).toHaveProperty('phone')
      expect(userData).toHaveProperty('created_at')
      expect(userData).toHaveProperty('updated_at')

      // 验证数据类型
      expect(typeof userData.id).toBe('number')
      expect(typeof userData.username).toBe('string')
      expect(typeof userData.phone).toBe('string')
      expect(typeof userData.created_at).toBe('string')
      expect(typeof userData.updated_at).toBe('string')

      // 验证日期格式（ISO 8601）
      expect(new Date(userData.created_at).toISOString()).toBe(userData.created_at)
      expect(new Date(userData.updated_at).toISOString()).toBe(userData.updated_at)
    })

    test('健康报告数据在不同平台应该保持相同格式', () => {
      const reportData = {
        id: 1,
        user_id: 1,
        report_title: '血常规检查报告',
        report_date: '2024-01-01',
        hospital_name: '测试医院',
        doctor_name: '张医生',
        department: '内科',
        report_type: 'blood_test',
        image_url: 'https://example.com/report.jpg',
        notes: '检查结果正常',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      // 验证必要字段
      const requiredFields = ['id', 'user_id', 'report_title', 'report_date', 'created_at', 'updated_at']
      requiredFields.forEach(field => {
        expect(reportData).toHaveProperty(field)
      })

      // 验证外键关系
      expect(typeof reportData.user_id).toBe('number')
      expect(reportData.user_id).toBeGreaterThan(0)

      // 验证日期格式
      expect(/^\d{4}-\d{2}-\d{2}$/.test(reportData.report_date)).toBe(true)
    })

    test('健康指标数据在不同平台应该保持相同格式', () => {
      const indicatorData = {
        id: 1,
        report_id: 1,
        indicator_name: '白细胞计数',
        indicator_value: '6.5',
        indicator_unit: '×10^9/L',
        reference_range: '3.5-9.5',
        is_abnormal: false,
        abnormal_level: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      // 验证数据结构
      expect(indicatorData).toHaveProperty('id')
      expect(indicatorData).toHaveProperty('report_id')
      expect(indicatorData).toHaveProperty('indicator_name')
      expect(indicatorData).toHaveProperty('indicator_value')
      expect(indicatorData).toHaveProperty('is_abnormal')

      // 验证布尔值字段
      expect(typeof indicatorData.is_abnormal).toBe('boolean')

      // 验证外键关系
      expect(typeof indicatorData.report_id).toBe('number')
      expect(indicatorData.report_id).toBeGreaterThan(0)
    })
  })

  describe('同步操作一致性', () => {
    test('上传操作应该在所有平台保持一致', async () => {
      // Mock数据
      const userId = 1
      const pendingRecords = [
        { id: 1, table_name: 'users', record_id: 1, operation_type: 'UPDATE' },
        { id: 2, table_name: 'health_reports', record_id: 1, operation_type: 'INSERT' }
      ]

      mockStorage.sync.getPendingSyncRecords.mockResolvedValue(pendingRecords)
      mockStorage.users.findById.mockResolvedValue({ id: 1, username: 'test' })
      mockStorage.reports.findById.mockResolvedValue({ id: 1, report_title: '测试报告' })
      
      mockCloudApiService.uploadUserData.mockResolvedValue({ success: true })
      mockCloudApiService.uploadHealthReports.mockResolvedValue({ success: true })

      // 注入mock依赖
      syncService.syncStore = mockSyncStore
      
      // 执行上传
      const result = await syncService.performUpload(userId)

      // 验证结果
      expect(result.count).toBeGreaterThan(0)
      expect(result.errors).toEqual([])
      expect(mockCloudApiService.uploadUserData).toHaveBeenCalled()
      expect(mockCloudApiService.uploadHealthReports).toHaveBeenCalled()
    })

    test('下载操作应该在所有平台保持一致', async () => {
      const userId = 1
      const lastSyncTime = Date.now() - 24 * 60 * 60 * 1000 // 24小时前

      // Mock云端数据
      const remoteUserData = { id: 1, username: 'updated_user', updated_at: new Date().toISOString() }
      const remoteReports = [
        { id: 1, report_title: '新报告', updated_at: new Date().toISOString() }
      ]

      mockCloudApiService.downloadUserData.mockResolvedValue({ data: remoteUserData })
      mockCloudApiService.downloadHealthReports.mockResolvedValue({ data: remoteReports })
      mockCloudApiService.downloadHealthIndicators.mockResolvedValue({ data: [] })

      mockStorage.users.findById.mockResolvedValue(null) // 本地无数据
      mockStorage.reports.findById.mockResolvedValue(null)
      mockStorage.users.create.mockResolvedValue()
      mockStorage.reports.create.mockResolvedValue()

      syncService.syncStore = mockSyncStore
      syncService.syncStore.syncStatus.lastSyncTime = lastSyncTime

      // 执行下载
      const result = await syncService.performDownload(userId)

      // 验证结果
      expect(result.count).toBeGreaterThan(0)
      expect(result.conflicts).toBe(0)
      expect(result.errors).toEqual([])
      expect(mockStorage.users.create).toHaveBeenCalledWith(remoteUserData)
    })

    test('冲突检测应该在所有平台工作一致', () => {
      const localData = {
        id: 1,
        username: 'local_user',
        updated_at: '2024-01-01T10:00:00Z'
      }

      const remoteData = {
        id: 1,
        username: 'remote_user',
        updated_at: '2024-01-01T09:00:00Z'
      }

      conflictResolver.initialize = jest.fn()
      
      const conflict = conflictResolver.detectConflict(localData, remoteData, 'user_info')

      expect(conflict).not.toBeNull()
      expect(conflict.type).toBe('user_info')
      expect(conflict.conflictFields).toHaveLength(1)
      expect(conflict.conflictFields[0].field).toBe('username')
      expect(conflict.severity).toBeDefined()
    })
  })

  describe('网络适配一致性', () => {
    test('网络状态检测应该适配不同平台', async () => {
      const networkTypes = ['wifi', '4g', '3g', '2g', 'none']

      for (const networkType of networkTypes) {
        uni.getNetworkType.mockImplementation(({ success }) => {
          success({ networkType })
        })

        syncService.syncStore = mockSyncStore

        const detectedType = await syncService.getNetworkType()
        expect(detectedType).toBe(networkType)
      }
    })

    test('WiFi限制应该在所有平台生效', async () => {
      // 设置仅WiFi同步
      mockSyncStore.syncConfig.wifiOnly = true
      
      // 模拟4G网络
      uni.getNetworkType.mockImplementation(({ success }) => {
        success({ networkType: '4g' })
      })

      syncService.syncStore = mockSyncStore

      const result = await syncService.startSync({ userId: 1 })

      expect(result.success).toBe(false)
      expect(result.error).toContain('仅允许WiFi环境下同步')
    })

    test('网络错误处理应该一致', async () => {
      mockCloudApiService.checkNetworkConnection.mockResolvedValue(false)
      syncService.syncStore = mockSyncStore

      const result = await syncService.startSync({ userId: 1 })

      expect(result.success).toBe(false)
      expect(result.error).toContain('网络连接不可用')
    })
  })

  describe('数据完整性验证', () => {
    test('同步后数据应该保持完整性', async () => {
      const originalData = {
        id: 1,
        user_id: 1,
        report_title: '原始报告',
        report_date: '2024-01-01',
        hospital_name: '测试医院',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      // 模拟同步过程
      mockStorage.reports.findById.mockResolvedValue(originalData)
      mockCloudApiService.uploadHealthReports.mockResolvedValue({ success: true })

      syncService.syncStore = mockSyncStore
      mockStorage.sync.getPendingSyncRecords.mockResolvedValue([
        { id: 1, table_name: 'health_reports', record_id: 1, operation_type: 'UPDATE' }
      ])

      const result = await syncService.performUpload(1)

      // 验证上传的数据完整性
      expect(mockCloudApiService.uploadHealthReports).toHaveBeenCalledWith([originalData])
      expect(result.errors).toEqual([])
    })

    test('数据合并应该保持字段完整性', async () => {
      const localData = {
        id: 1,
        username: 'local_user',
        phone: '13800138000',
        email: '<EMAIL>',
        updated_at: '2024-01-01T10:00:00Z'
      }

      const remoteData = {
        id: 1,
        username: 'remote_user',
        phone: '13800138000',
        email: '<EMAIL>',
        updated_at: '2024-01-01T09:00:00Z'
      }

      conflictResolver.initialize = jest.fn()

      const conflict = {
        type: 'user_info',
        localData,
        remoteData,
        conflictFields: [
          { field: 'username', localValue: 'local_user', remoteValue: 'remote_user', severity: 'high' },
          { field: 'email', localValue: '<EMAIL>', remoteValue: '<EMAIL>', severity: 'medium' }
        ]
      }

      const mergedData = await conflictResolver.resolveWithMerge(conflict)

      // 验证合并后数据完整性
      expect(mergedData).toHaveProperty('id', 1)
      expect(mergedData).toHaveProperty('phone', '13800138000') // 无冲突字段应保持
      expect(mergedData).toHaveProperty('updated_at')
      expect(new Date(mergedData.updated_at).getTime()).toBeGreaterThan(new Date(localData.updated_at).getTime())
    })
  })

  describe('并发同步处理', () => {
    test('并发同步请求应该被正确处理', async () => {
      syncService.syncStore = mockSyncStore
      syncService.isRunning = false

      // 模拟并发同步请求
      const syncPromises = [
        syncService.startSync({ userId: 1 }),
        syncService.startSync({ userId: 1 }),
        syncService.startSync({ userId: 1 })
      ]

      const results = await Promise.all(syncPromises)

      // 只有一个同步应该成功，其他应该被拒绝
      const successCount = results.filter(r => r.success).length
      const rejectedCount = results.filter(r => !r.success && r.message === '同步正在进行中').length

      expect(successCount).toBe(1)
      expect(rejectedCount).toBe(2)
    })

    test('同步队列应该按顺序处理', async () => {
      const operations = []
      
      mockStorage.sync.createSyncRecord.mockImplementation((...args) => {
        operations.push(args)
        return Promise.resolve()
      })

      syncService.syncStore = mockSyncStore

      // 添加多个同步任务
      await syncService.addToSyncQueue('users', 1, 'UPDATE', 1)
      await syncService.addToSyncQueue('health_reports', 1, 'INSERT', 1)
      await syncService.addToSyncQueue('health_indicators', 1, 'UPDATE', 1)

      // 验证操作顺序
      expect(operations).toHaveLength(3)
      expect(operations[0]).toEqual([1, 'users', 1, 'UPDATE'])
      expect(operations[1]).toEqual([1, 'health_reports', 1, 'INSERT'])
      expect(operations[2]).toEqual([1, 'health_indicators', 1, 'UPDATE'])
    })
  })

  describe('错误恢复机制', () => {
    test('部分同步失败应该不影响其他数据', async () => {
      const userId = 1
      const pendingRecords = [
        { id: 1, table_name: 'users', record_id: 1, operation_type: 'UPDATE' },
        { id: 2, table_name: 'health_reports', record_id: 1, operation_type: 'INSERT' }
      ]

      mockStorage.sync.getPendingSyncRecords.mockResolvedValue(pendingRecords)
      mockStorage.users.findById.mockResolvedValue({ id: 1, username: 'test' })
      mockStorage.reports.findById.mockResolvedValue({ id: 1, report_title: '测试报告' })
      
      // 用户数据上传成功，报告上传失败
      mockCloudApiService.uploadUserData.mockResolvedValue({ success: true })
      mockCloudApiService.uploadHealthReports.mockRejectedValue(new Error('网络错误'))

      syncService.syncStore = mockSyncStore

      const result = await syncService.performUpload(userId)

      // 验证部分成功
      expect(result.count).toBe(1) // 用户数据上传成功
      expect(result.errors).toHaveLength(1) // 报告上传失败
      expect(result.errors[0]).toContain('健康报告上传失败')
    })

    test('同步失败应该记录错误信息', async () => {
      mockCloudApiService.checkNetworkConnection.mockRejectedValue(new Error('网络异常'))
      syncService.syncStore = mockSyncStore

      const result = await syncService.startSync({ userId: 1 })

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(mockSyncStore.addSyncRecord).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'sync',
          status: 'failed'
        })
      )
    })
  })
})