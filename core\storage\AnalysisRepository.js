/**
 * 分析数据访问层
 * 实现分析结果相关的数据库操作
 */

const { BaseRepository } = require('../base/BaseRepository.js')
const { IAnalysisRepository } = require('../interfaces/IRepository.js')
const { Analysis } = require('../../models/Analysis.js')
const { Constants } = require('../../types/index.js')
const { logger } = require('../logger/Logger.js')

class AnalysisRepository extends BaseRepository {
  constructor(storageManager) {
    super(storageManager, Analysis)
  }
  
  /**
   * 获取表名
   * @returns {String} 表名
   */
  getTableName() {
    return 'analysis'
  }
  
  /**
   * 根据用户ID和类型查找分析结果
   * @param {String} userId - 用户ID
   * @param {String} type - 分析类型
   * @returns {Promise<Array>} 分析结果列表
   */
  async findByUserIdAndType(userId, type) {
    try {
      logger.debug('根据用户ID和类型查找分析结果', { userId, type })
      
      const conditions = { userId, type }
      return await this.findBy(conditions, { sort: { createdAt: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findByUserIdAndType')
    }
  }
  
  /**
   * 获取最新分析结果
   * @param {String} userId - 用户ID
   * @param {String} type - 分析类型
   * @returns {Promise<Analysis|null>} 最新分析结果或null
   */
  async getLatestAnalysis(userId, type) {
    try {
      logger.debug('获取最新分析结果', { userId, type })
      
      const analyses = await this.findByUserIdAndType(userId, type)
      return analyses.length > 0 ? analyses[0] : null
    } catch (error) {
      throw this.handleError(error, 'getLatestAnalysis')
    }
  }
  
  /**
   * 清理过期分析结果
   * @param {Number} daysToKeep - 保留天数
   * @returns {Promise<Number>} 清理的记录数量
   */
  async cleanupExpiredAnalysis(daysToKeep = 30) {
    try {
      logger.debug('清理过期分析结果', { daysToKeep })
      
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
      
      const conditions = {
        createdAt: { $lt: cutoffDate }
      }
      
      const expiredAnalyses = await this.findBy(conditions)
      let cleanedCount = 0
      
      for (const analysis of expiredAnalyses) {
        await this.delete(analysis.id)
        cleanedCount++
      }
      
      logger.info('清理过期分析结果完成', { cleanedCount })
      return cleanedCount
    } catch (error) {
      throw this.handleError(error, 'cleanupExpiredAnalysis')
    }
  }
  
  /**
   * 根据时间范围查找分析结果
   * @param {String} userId - 用户ID
   * @param {Date} startDate - 开始时间
   * @param {Date} endDate - 结束时间
   * @returns {Promise<Array>} 分析结果列表
   */
  async findByDateRange(userId, startDate, endDate) {
    try {
      logger.debug('根据时间范围查找分析结果', { userId, startDate, endDate })
      
      const conditions = {
        userId,
        'timeRange.start': { $gte: startDate },
        'timeRange.end': { $lte: endDate }
      }
      
      return await this.findBy(conditions, { sort: { createdAt: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findByDateRange')
    }
  }
  
  /**
   * 根据分析项目查找结果
   * @param {String} userId - 用户ID
   * @param {Array} items - 分析项目列表
   * @returns {Promise<Array>} 分析结果列表
   */
  async findByItems(userId, items) {
    try {
      logger.debug('根据分析项目查找结果', { userId, items })
      
      const conditions = {
        userId,
        items: { $in: items }
      }
      
      return await this.findBy(conditions, { sort: { createdAt: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findByItems')
    }
  }
  
  /**
   * 获取趋势分析结果
   * @param {String} userId - 用户ID
   * @param {String} itemName - 项目名称
   * @param {Number} limit - 限制数量
   * @returns {Promise<Array>} 趋势分析结果
   */
  async getTrendAnalysis(userId, itemName, limit = 10) {
    try {
      logger.debug('获取趋势分析结果', { userId, itemName, limit })
      
      const conditions = {
        userId,
        type: 'trend',
        items: itemName
      }
      
      return await this.findBy(conditions, {
        sort: { createdAt: -1 },
        limit
      })
    } catch (error) {
      throw this.handleError(error, 'getTrendAnalysis')
    }
  }
  
  /**
   * 获取对比分析结果
   * @param {String} userId - 用户ID
   * @param {Array} items - 对比项目
   * @returns {Promise<Array>} 对比分析结果
   */
  async getComparisonAnalysis(userId, items) {
    try {
      logger.debug('获取对比分析结果', { userId, items })
      
      const conditions = {
        userId,
        type: 'comparison',
        items: { $all: items }
      }
      
      return await this.findBy(conditions, { sort: { createdAt: -1 } })
    } catch (error) {
      throw this.handleError(error, 'getComparisonAnalysis')
    }
  }
  
  /**
   * 获取健康摘要分析
   * @param {String} userId - 用户ID
   * @param {Number} limit - 限制数量
   * @returns {Promise<Array>} 健康摘要列表
   */
  async getHealthSummary(userId, limit = 5) {
    try {
      logger.debug('获取健康摘要分析', { userId, limit })
      
      const conditions = {
        userId,
        type: 'summary'
      }
      
      return await this.findBy(conditions, {
        sort: { createdAt: -1 },
        limit
      })
    } catch (error) {
      throw this.handleError(error, 'getHealthSummary')
    }
  }
  
  /**
   * 保存分析结果
   * @param {String} userId - 用户ID
   * @param {String} type - 分析类型
   * @param {Object} analysisData - 分析数据
   * @returns {Promise<Analysis>} 保存的分析结果
   */
  async saveAnalysis(userId, type, analysisData) {
    try {
      logger.debug('保存分析结果', { userId, type })
      
      const data = {
        userId,
        type,
        ...analysisData,
        createdAt: new Date()
      }
      
      return await this.create(data)
    } catch (error) {
      throw this.handleError(error, 'saveAnalysis')
    }
  }
  
  /**
   * 更新分析结果
   * @param {String} analysisId - 分析ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Analysis>} 更新后的分析结果
   */
  async updateAnalysis(analysisId, updateData) {
    try {
      logger.debug('更新分析结果', { analysisId })
      
      return await this.update(analysisId, updateData)
    } catch (error) {
      throw this.handleError(error, 'updateAnalysis')
    }
  }
  
  /**
   * 删除用户的所有分析结果
   * @param {String} userId - 用户ID
   * @returns {Promise<Number>} 删除的记录数量
   */
  async deleteUserAnalyses(userId) {
    try {
      logger.debug('删除用户的所有分析结果', { userId })
      
      const analyses = await this.findBy({ userId })
      let deletedCount = 0
      
      for (const analysis of analyses) {
        await this.delete(analysis.id)
        deletedCount++
      }
      
      logger.info('删除用户分析结果完成', { userId, deletedCount })
      return deletedCount
    } catch (error) {
      throw this.handleError(error, 'deleteUserAnalyses')
    }
  }
  
  /**
   * 获取分析统计信息
   * @param {String} userId - 用户ID
   * @returns {Promise<Object>} 统计信息
   */
  async getAnalysisStats(userId) {
    try {
      logger.debug('获取分析统计信息', { userId })
      
      const allAnalyses = await this.findBy({ userId })
      
      const stats = {
        totalAnalyses: allAnalyses.length,
        typeCount: {},
        recentAnalyses: 0,
        oldestAnalysis: null,
        newestAnalysis: null
      }
      
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      for (const analysis of allAnalyses) {
        // 统计类型
        stats.typeCount[analysis.type] = (stats.typeCount[analysis.type] || 0) + 1
        
        // 统计最近分析
        if (analysis.createdAt >= thirtyDaysAgo) {
          stats.recentAnalyses++
        }
        
        // 找出最早和最新的分析
        if (!stats.oldestAnalysis || analysis.createdAt < stats.oldestAnalysis.createdAt) {
          stats.oldestAnalysis = analysis
        }
        if (!stats.newestAnalysis || analysis.createdAt > stats.newestAnalysis.createdAt) {
          stats.newestAnalysis = analysis
        }
      }
      
      return stats
    } catch (error) {
      throw this.handleError(error, 'getAnalysisStats')
    }
  }
  
  /**
   * 批量创建分析结果
   * @param {Array} analysisDataList - 分析数据列表
   * @returns {Promise<Array>} 创建的分析结果列表
   */
  async batchCreateAnalyses(analysisDataList) {
    try {
      logger.debug('批量创建分析结果', { count: analysisDataList.length })
      
      const operations = analysisDataList.map(data => ({
        type: 'create',
        data
      }))
      
      return await this.batch(operations)
    } catch (error) {
      throw this.handleError(error, 'batchCreateAnalyses')
    }
  }
  
  /**
   * 查找相似的分析结果
   * @param {String} userId - 用户ID
   * @param {String} type - 分析类型
   * @param {Array} items - 分析项目
   * @param {Object} timeRange - 时间范围
   * @returns {Promise<Array>} 相似的分析结果
   */
  async findSimilarAnalyses(userId, type, items, timeRange) {
    try {
      logger.debug('查找相似的分析结果', { userId, type, items, timeRange })
      
      const conditions = {
        userId,
        type,
        items: { $in: items },
        'timeRange.start': {
          $gte: new Date(timeRange.start.getTime() - 7 * 24 * 60 * 60 * 1000), // 前后一周
          $lte: new Date(timeRange.start.getTime() + 7 * 24 * 60 * 60 * 1000)
        }
      }
      
      return await this.findBy(conditions, { sort: { createdAt: -1 } })
    } catch (error) {
      throw this.handleError(error, 'findSimilarAnalyses')
    }
  }
  
  /**
   * 导出分析结果
   * @param {String} userId - 用户ID
   * @param {String} type - 分析类型（可选）
   * @param {String} format - 导出格式
   * @returns {Promise<String>} 导出的数据
   */
  async exportAnalyses(userId, type = null, format = 'json') {
    try {
      logger.debug('导出分析结果', { userId, type, format })
      
      const conditions = { userId }
      if (type) {
        conditions.type = type
      }
      
      return await this.exportData(conditions, format)
    } catch (error) {
      throw this.handleError(error, 'exportAnalyses')
    }
  }
}

module.exports = { AnalysisRepository }