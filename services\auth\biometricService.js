/**
 * 生物识别服务
 * 处理指纹、面部识别等生物识别功能
 */

class BiometricService {
  constructor() {
    this.isSupported = false
    this.supportedTypes = []
  }

  /**
   * 初始化生物识别服务
   */
  async init() {
    try {
      const support = await this.checkSupport()
      this.isSupported = support.isSupported
      this.supportedTypes = support.types
      
      return {
        success: true,
        isSupported: this.isSupported,
        types: this.supportedTypes
      }
    } catch (error) {
      console.error('生物识别服务初始化失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 检查生物识别支持情况
   */
  async checkSupport() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      // 检查设备是否设置了锁屏密码
      plus.fingerprint.isKeyguardSecure({
        success: () => {
          // 检查指纹识别支持
          plus.fingerprint.isSupported({
            success: () => {
              resolve({
                isSupported: true,
                types: ['fingerprint']
              })
            },
            fail: () => {
              resolve({
                isSupported: false,
                types: []
              })
            }
          })
        },
        fail: () => {
          resolve({
            isSupported: false,
            types: [],
            message: '设备未设置锁屏密码'
          })
        }
      })
      // #endif

      // #ifdef MP-WEIXIN
      // 微信小程序支持生物识别
      wx.checkIsSupportSoterAuthentication({
        success: (res) => {
          resolve({
            isSupported: res.supportMode.length > 0,
            types: res.supportMode
          })
        },
        fail: () => {
          resolve({
            isSupported: false,
            types: []
          })
        }
      })
      // #endif

      // #ifndef APP-PLUS || MP-WEIXIN
      resolve({
        isSupported: false,
        types: []
      })
      // #endif
    })
  }

  /**
   * 启用生物识别
   */
  async enable() {
    try {
      if (!this.isSupported) {
        throw new Error('设备不支持生物识别')
      }

      // 进行一次生物识别验证以确保用户已设置
      const verifyResult = await this.verify('启用生物识别登录')
      if (!verifyResult.success) {
        throw new Error('生物识别验证失败')
      }

      // 保存启用状态
      uni.setStorageSync('biometric_enabled', true)

      return {
        success: true,
        message: '生物识别已启用'
      }
    } catch (error) {
      console.error('启用生物识别失败:', error)
      return {
        success: false,
        message: error.message || '启用生物识别失败'
      }
    }
  }

  /**
   * 禁用生物识别
   */
  async disable() {
    try {
      // 清除启用状态
      uni.removeStorageSync('biometric_enabled')

      return {
        success: true,
        message: '生物识别已禁用'
      }
    } catch (error) {
      console.error('禁用生物识别失败:', error)
      return {
        success: false,
        message: error.message || '禁用生物识别失败'
      }
    }
  }

  /**
   * 检查是否已启用生物识别
   */
  isEnabled() {
    return uni.getStorageSync('biometric_enabled') === true
  }

  /**
   * 进行生物识别验证
   * @param {string} reason 验证原因
   */
  async verify(reason = '身份验证') {
    try {
      if (!this.isSupported) {
        throw new Error('设备不支持生物识别')
      }

      if (!this.isEnabled()) {
        throw new Error('生物识别未启用')
      }

      return await this.performVerification(reason)
    } catch (error) {
      console.error('生物识别验证失败:', error)
      return {
        success: false,
        message: error.message || '生物识别验证失败'
      }
    }
  }

  /**
   * 执行生物识别验证
   * @param {string} reason 验证原因
   */
  async performVerification(reason) {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.fingerprint.authenticate({
        success: () => {
          resolve({
            success: true,
            message: '生物识别验证成功'
          })
        },
        fail: (err) => {
          let message = '生物识别验证失败'
          
          // 根据错误码提供更具体的错误信息
          switch (err.code) {
            case 1:
              message = '用户取消了验证'
              break
            case 2:
              message = '系统取消了验证'
              break
            case 3:
              message = '验证失败次数过多'
              break
            case 4:
              message = '验证超时'
              break
            case 5:
              message = '无法识别指纹'
              break
            case 7:
              message = '指纹传感器不可用'
              break
            default:
              message = err.message || '生物识别验证失败'
          }
          
          resolve({
            success: false,
            message: message,
            code: err.code
          })
        }
      })
      // #endif

      // #ifdef MP-WEIXIN
      wx.startSoterAuthentication({
        requestAuthModes: this.supportedTypes,
        challenge: Date.now().toString(),
        authContent: reason,
        success: (res) => {
          resolve({
            success: true,
            message: '生物识别验证成功',
            data: res
          })
        },
        fail: (err) => {
          let message = '生物识别验证失败'
          
          // 根据错误码提供更具体的错误信息
          switch (err.errCode) {
            case 90001:
              message = '本设备不支持生物识别'
              break
            case 90002:
              message = '用户未录入生物识别信息'
              break
            case 90003:
              message = '请求使用的生物识别方式不支持'
              break
            case 90007:
              message = '内部错误'
              break
            case 90008:
              message = '用户取消授权'
              break
            case 90009:
              message = '识别失败'
              break
            case 90010:
              message = '重试次数过多被冻结'
              break
            case 90011:
              message = '用户未授权使用该生物认证接口'
              break
            default:
              message = err.errMsg || '生物识别验证失败'
          }
          
          resolve({
            success: false,
            message: message,
            code: err.errCode
          })
        }
      })
      // #endif

      // #ifndef APP-PLUS || MP-WEIXIN
      resolve({
        success: false,
        message: '当前平台不支持生物识别'
      })
      // #endif
    })
  }

  /**
   * 获取生物识别类型的显示名称
   * @param {string} type 生物识别类型
   */
  getTypeName(type) {
    const typeNames = {
      'fingerprint': '指纹识别',
      'facial': '面部识别',
      'speech': '声纹识别'
    }
    
    return typeNames[type] || type
  }

  /**
   * 获取支持的生物识别类型列表
   */
  getSupportedTypes() {
    return this.supportedTypes.map(type => ({
      type: type,
      name: this.getTypeName(type)
    }))
  }
}

export default new BiometricService()