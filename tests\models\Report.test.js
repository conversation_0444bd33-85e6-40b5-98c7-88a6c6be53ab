/**
 * 报告模型单元测试
 */

import { Report, ReportItem } from '../../models/Report.js'
import { Constants } from '../../types/index.js'

describe('Report Model', () => {
  describe('创建报告', () => {
    test('应该能够创建有效的报告', () => {
      const reportData = {
        userId: 'user123',
        hospital: '测试医院',
        doctor: '张医生',
        checkDate: new Date('2024-01-01'),
        reportDate: new Date('2024-01-02')
      }
      
      const report = Report.create(reportData)
      
      expect(report.userId).toBe('user123')
      expect(report.hospital).toBe('测试医院')
      expect(report.doctor).toBe('张医生')
      expect(report.checkDate).toEqual(new Date('2024-01-01'))
      expect(report.reportDate).toEqual(new Date('2024-01-02'))
      expect(report.id).toBeDefined()
      expect(report.createdAt).toBeInstanceOf(Date)
      expect(report.updatedAt).toBeInstanceOf(Date)
    })
    
    test('创建报告时缺少用户ID应该抛出错误', () => {
      const reportData = {
        hospital: '测试医院'
      }
      
      expect(() => {
        Report.create(reportData)
      }).toThrow('用户ID是必填项')
    })
    
    test('创建报告时缺少医院名称应该抛出错误', () => {
      const reportData = {
        userId: 'user123'
      }
      
      expect(() => {
        Report.create(reportData)
      }).toThrow('医院名称是必填项')
    })
  })
  
  describe('检查项目管理', () => {
    let report
    
    beforeEach(() => {
      report = new Report({
        userId: 'user123',
        hospital: '测试医院'
      })
    })
    
    test('应该能够添加检查项目', () => {
      const itemData = {
        name: '白细胞计数',
        value: '6.5',
        unit: '×10^9/L',
        referenceRange: '3.5-9.5'
      }
      
      report.addItem(itemData)
      
      expect(report.items).toHaveLength(1)
      expect(report.items[0]).toBeInstanceOf(ReportItem)
      expect(report.items[0].name).toBe('白细胞计数')
      expect(report.items[0].value).toBe('6.5')
    })
    
    test('应该能够移除检查项目', () => {
      const item = new ReportItem({
        name: '白细胞计数',
        value: '6.5'
      })
      
      report.addItem(item)
      expect(report.items).toHaveLength(1)
      
      const removed = report.removeItem(item.id)
      expect(removed).toBe(true)
      expect(report.items).toHaveLength(0)
    })
    
    test('应该能够更新检查项目', () => {
      const item = new ReportItem({
        name: '白细胞计数',
        value: '6.5'
      })
      
      report.addItem(item)
      
      const updated = report.updateItem(item.id, { value: '7.0' })
      expect(updated).toBe(true)
      expect(report.items[0].value).toBe('7.0')
    })
    
    test('应该能够根据名称查找检查项目', () => {
      const item = new ReportItem({
        name: '白细胞计数',
        value: '6.5'
      })
      
      report.addItem(item)
      
      const foundItem = report.findItemByName('白细胞计数')
      expect(foundItem).toBe(item)
      
      const notFoundItem = report.findItemByName('不存在的项目')
      expect(notFoundItem).toBeNull()
    })
    
    test('应该能够根据分类获取检查项目', () => {
      const item1 = new ReportItem({
        name: '白细胞计数',
        value: '6.5',
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE
      })
      
      const item2 = new ReportItem({
        name: '血糖',
        value: '5.5',
        category: Constants.REPORT_CATEGORIES.BIOCHEMISTRY
      })
      
      report.addItem(item1)
      report.addItem(item2)
      
      const bloodItems = report.getItemsByCategory(Constants.REPORT_CATEGORIES.BLOOD_ROUTINE)
      expect(bloodItems).toHaveLength(1)
      expect(bloodItems[0].name).toBe('白细胞计数')
    })
    
    test('应该能够获取异常检查项目', () => {
      const normalItem = new ReportItem({
        name: '白细胞计数',
        value: '6.5',
        isAbnormal: false
      })
      
      const abnormalItem = new ReportItem({
        name: '血糖',
        value: '15.0',
        isAbnormal: true
      })
      
      report.addItem(normalItem)
      report.addItem(abnormalItem)
      
      const abnormalItems = report.getAbnormalItems()
      expect(abnormalItems).toHaveLength(1)
      expect(abnormalItems[0].name).toBe('血糖')
      
      expect(report.hasAbnormalItems()).toBe(true)
      expect(report.getAbnormalItemCount()).toBe(1)
    })
  })
  
  describe('标签管理', () => {
    let report
    
    beforeEach(() => {
      report = new Report({
        userId: 'user123',
        hospital: '测试医院'
      })
    })
    
    test('应该能够添加标签', () => {
      report.addTag('体检')
      report.addTag('复查')
      
      expect(report.tags).toContain('体检')
      expect(report.tags).toContain('复查')
      expect(report.tags).toHaveLength(2)
    })
    
    test('不应该添加重复标签', () => {
      report.addTag('体检')
      report.addTag('体检')
      
      expect(report.tags).toHaveLength(1)
    })
    
    test('应该能够移除标签', () => {
      report.addTag('体检')
      report.addTag('复查')
      
      report.removeTag('体检')
      
      expect(report.tags).not.toContain('体检')
      expect(report.tags).toContain('复查')
      expect(report.tags).toHaveLength(1)
    })
  })
  
  describe('状态管理', () => {
    let report
    
    beforeEach(() => {
      report = new Report({
        userId: 'user123',
        hospital: '测试医院'
      })
    })
    
    test('应该能够设置同步状态', () => {
      report.setSyncStatus(Constants.SYNC_STATUS.SYNCED)
      expect(report.syncStatus).toBe(Constants.SYNC_STATUS.SYNCED)
    })
    
    test('应该能够标记为已删除', () => {
      report.markAsDeleted()
      expect(report.isDeleted).toBe(true)
    })
    
    test('应该能够恢复删除', () => {
      report.markAsDeleted()
      report.restore()
      expect(report.isDeleted).toBe(false)
    })
    
    test('应该能够增加版本号', () => {
      const initialVersion = report.version
      report.incrementVersion()
      expect(report.version).toBe(initialVersion + 1)
    })
  })
  
  describe('OCR相关功能', () => {
    let report
    
    beforeEach(() => {
      report = new Report({
        userId: 'user123',
        hospital: '测试医院'
      })
    })
    
    test('应该能够设置OCR结果', () => {
      const ocrResult = { text: '测试OCR结果' }
      const confidence = 0.85
      
      report.setOCRResult(ocrResult, confidence)
      
      expect(report.ocrResult).toEqual(ocrResult)
      expect(report.ocrConfidence).toBe(confidence)
    })
    
    test('应该能够标记为手动编辑', () => {
      report.markAsManuallyEdited()
      expect(report.isManuallyEdited).toBe(true)
    })
  })
  
  describe('报告信息生成', () => {
    let report
    
    beforeEach(() => {
      report = new Report({
        userId: 'user123',
        hospital: '测试医院',
        checkDate: new Date('2024-01-01'),
        category: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE
      })
    })
    
    test('应该能够生成报告摘要', () => {
      const normalItem = new ReportItem({
        name: '白细胞计数',
        value: '6.5',
        isAbnormal: false
      })
      
      const abnormalItem = new ReportItem({
        name: '血糖',
        value: '15.0',
        isAbnormal: true
      })
      
      report.addItem(normalItem)
      report.addItem(abnormalItem)
      report.conclusion = '建议复查'
      
      const summary = report.generateSummary()
      
      expect(summary).toContain('本次检查共2项')
      expect(summary).toContain('正常1项')
      expect(summary).toContain('异常1项')
      expect(summary).toContain('建议复查')
    })
    
    test('应该能够获取报告标题', () => {
      // 测试自定义标题
      report.title = '自定义标题'
      expect(report.getTitle()).toBe('自定义标题')
      
      // 测试自动生成标题
      report.title = ''
      const title = report.getTitle()
      expect(title).toContain('测试医院')
      expect(title).toContain('血常规')
      expect(title).toContain('2024')
    })
    
    test('应该能够获取分类显示名称', () => {
      report.category = Constants.REPORT_CATEGORIES.BLOOD_ROUTINE
      expect(report.getCategoryDisplayName()).toBe('血常规')
      
      report.category = Constants.REPORT_CATEGORIES.BIOCHEMISTRY
      expect(report.getCategoryDisplayName()).toBe('生化')
    })
  })
  
  describe('数据完整性检查', () => {
    test('应该检测完整的报告', () => {
      const report = new Report({
        userId: 'user123',
        hospital: '测试医院',
        originalImage: 'image.jpg',
        ocrConfidence: 0.8,
        isManuallyEdited: true
      })
      
      report.addItem(new ReportItem({
        name: '白细胞计数',
        value: '6.5'
      }))
      
      const integrity = report.checkDataIntegrity()
      expect(integrity.isComplete).toBe(true)
      expect(integrity.issues).toHaveLength(0)
    })
    
    test('应该检测不完整的报告', () => {
      const report = new Report({
        userId: 'user123',
        hospital: '',
        ocrConfidence: 0.3,
        isManuallyEdited: false
      })
      
      const integrity = report.checkDataIntegrity()
      expect(integrity.isComplete).toBe(false)
      expect(integrity.issues.length).toBeGreaterThan(0)
    })
  })
  
  describe('数据序列化', () => {
    test('应该正确序列化为JSON', () => {
      const report = new Report({
        userId: 'user123',
        hospital: '测试医院'
      })
      
      report.addItem(new ReportItem({
        name: '白细胞计数',
        value: '6.5'
      }))
      
      const json = report.toJSON()
      
      expect(json.userId).toBe('user123')
      expect(json.hospital).toBe('测试医院')
      expect(json.items).toHaveLength(1)
      expect(json.items[0].name).toBe('白细胞计数')
    })
    
    test('应该能够从JSON创建报告实例', () => {
      const json = {
        id: 'test-id',
        userId: 'user123',
        hospital: '测试医院',
        items: [
          {
            id: 'item-id',
            name: '白细胞计数',
            value: '6.5'
          }
        ]
      }
      
      const report = Report.fromJSON(json)
      
      expect(report).toBeInstanceOf(Report)
      expect(report.userId).toBe('user123')
      expect(report.hospital).toBe('测试医院')
      expect(report.items).toHaveLength(1)
      expect(report.items[0]).toBeInstanceOf(ReportItem)
    })
  })
})

describe('ReportItem Model', () => {
  describe('创建检查项目', () => {
    test('应该能够创建有效的检查项目', () => {
      const itemData = {
        name: '白细胞计数',
        value: '6.5',
        unit: '×10^9/L',
        referenceRange: '3.5-9.5'
      }
      
      const item = ReportItem.create(itemData)
      
      expect(item.name).toBe('白细胞计数')
      expect(item.value).toBe('6.5')
      expect(item.unit).toBe('×10^9/L')
      expect(item.referenceRange).toBe('3.5-9.5')
    })
    
    test('创建项目时缺少名称应该抛出错误', () => {
      const itemData = {
        value: '6.5'
      }
      
      expect(() => {
        ReportItem.create(itemData)
      }).toThrow('项目名称是必填项')
    })
    
    test('创建项目时缺少结果应该抛出错误', () => {
      const itemData = {
        name: '白细胞计数'
      }
      
      expect(() => {
        ReportItem.create(itemData)
      }).toThrow('检查结果是必填项')
    })
  })
  
  describe('数值处理', () => {
    let item
    
    beforeEach(() => {
      item = new ReportItem({
        name: '白细胞计数',
        value: '6.5'
      })
    })
    
    test('应该能够设置数值和参考范围', () => {
      item.setNumericValue(6.5, 3.5, 9.5)
      
      expect(item.numericValue).toBe(6.5)
      expect(item.minReference).toBe(3.5)
      expect(item.maxReference).toBe(9.5)
      expect(item.isAbnormal).toBe(false)
    })
    
    test('应该能够检测异常数值', () => {
      // 测试低于下限
      item.setNumericValue(2.0, 3.5, 9.5)
      expect(item.isAbnormal).toBe(true)
      
      // 测试高于上限
      item.setNumericValue(10.0, 3.5, 9.5)
      expect(item.isAbnormal).toBe(true)
      
      // 测试正常范围
      item.setNumericValue(6.5, 3.5, 9.5)
      expect(item.isAbnormal).toBe(false)
    })
  })
  
  describe('参考范围解析', () => {
    let item
    
    beforeEach(() => {
      item = new ReportItem({
        name: '测试项目',
        value: '5.0'
      })
    })
    
    test('应该能够解析范围格式', () => {
      const testCases = [
        { input: '3.5-9.5', expected: { min: 3.5, max: 9.5 } },
        { input: '3.5 - 9.5', expected: { min: 3.5, max: 9.5 } },
        { input: '3.5~9.5', expected: { min: 3.5, max: 9.5 } },
        { input: '<5.5', expected: { min: null, max: 5.5 } },
        { input: '>3.5', expected: { min: 3.5, max: null } },
        { input: '≤5.5', expected: { min: null, max: 5.5 } },
        { input: '≥3.5', expected: { min: 3.5, max: null } }
      ]
      
      testCases.forEach(({ input, expected }) => {
        const result = item.parseReferenceRange(input)
        expect(result).toEqual(expected)
      })
    })
    
    test('应该能够设置参考范围并自动判断异常', () => {
      item.numericValue = 10.0
      item.setReferenceRange('3.5-9.5')
      
      expect(item.minReference).toBe(3.5)
      expect(item.maxReference).toBe(9.5)
      expect(item.isAbnormal).toBe(true)
    })
  })
  
  describe('异常程度评估', () => {
    test('应该能够评估异常程度', () => {
      const item = new ReportItem({
        name: '测试项目',
        value: '10.0',
        numericValue: 10.0,
        minReference: 3.5,
        maxReference: 9.5,
        isAbnormal: true
      })
      
      // 轻微异常
      item.numericValue = 10.0
      expect(item.getAbnormalSeverity()).toBe('mild')
      
      // 中度异常
      item.numericValue = 12.0
      expect(item.getAbnormalSeverity()).toBe('moderate')
      
      // 严重异常
      item.numericValue = 15.0
      expect(item.getAbnormalSeverity()).toBe('severe')
      
      // 正常
      item.isAbnormal = false
      expect(item.getAbnormalSeverity()).toBe('normal')
    })
  })
  
  describe('显示信息', () => {
    test('应该能够获取显示值', () => {
      const item = new ReportItem({
        name: '白细胞计数',
        value: '6.5',
        unit: '×10^9/L'
      })
      
      expect(item.getDisplayValue()).toBe('6.5 ×10^9/L')
    })
    
    test('应该能够获取完整显示信息', () => {
      const item = new ReportItem({
        name: '白细胞计数',
        value: '6.5',
        unit: '×10^9/L',
        referenceRange: '3.5-9.5',
        isAbnormal: true
      })
      
      const fullText = item.getFullDisplayText()
      
      expect(fullText).toContain('白细胞计数')
      expect(fullText).toContain('6.5 ×10^9/L')
      expect(fullText).toContain('参考值: 3.5-9.5')
      expect(fullText).toContain('[异常]')
    })
  })
  
  describe('状态管理', () => {
    test('应该能够标记为异常', () => {
      const item = new ReportItem({
        name: '测试项目',
        value: '10.0'
      })
      
      item.setAbnormal(true)
      expect(item.isAbnormal).toBe(true)
    })
    
    test('应该能够标记为手动编辑', () => {
      const item = new ReportItem({
        name: '测试项目',
        value: '10.0'
      })
      
      item.markAsManuallyEdited()
      expect(item.isManuallyEdited).toBe(true)
    })
  })
})