/**
 * 平台检测工具函数
 * 支持uni-app条件编译，检测当前运行平台
 */

/**
 * 获取当前平台类型
 * @returns {string} 平台标识
 */
export function getCurrentPlatform() {
  // #ifdef APP-PLUS
  return 'app-plus'
  // #endif
  
  // #ifdef MP-WEIXIN
  return 'mp-weixin'
  // #endif
  
  // #ifdef MP-ALIPAY
  return 'mp-alipay'
  // #endif
  
  // #ifdef MP-BAIDU
  return 'mp-baidu'
  // #endif
  
  // #ifdef H5
  return 'h5'
  // #endif
  
  // #ifdef MP
  return 'mp'
  // #endif
  
  return 'unknown'
}

/**
 * 检查是否为APP平台
 * @returns {boolean}
 */
export function isApp() {
  // #ifdef APP-PLUS
  return true
  // #endif
  return false
}

/**
 * 检查是否为微信小程序
 * @returns {boolean}
 */
export function isWeixinMp() {
  // #ifdef MP-WEIXIN
  return true
  // #endif
  return false
}

/**
 * 检查是否为小程序平台
 * @returns {boolean}
 */
export function isMp() {
  // #ifdef MP
  return true
  // #endif
  return false
}

/**
 * 检查是否为H5平台
 * @returns {boolean}
 */
export function isH5() {
  // #ifdef H5
  return true
  // #endif
  return false
}

/**
 * 获取系统信息
 * @returns {Promise<object>} 系统信息对象
 */
export function getSystemInfo() {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: (res) => {
        resolve({
          platform: res.platform,
          system: res.system,
          version: res.version,
          model: res.model,
          pixelRatio: res.pixelRatio,
          screenWidth: res.screenWidth,
          screenHeight: res.screenHeight,
          windowWidth: res.windowWidth,
          windowHeight: res.windowHeight,
          statusBarHeight: res.statusBarHeight,
          safeArea: res.safeArea,
          safeAreaInsets: res.safeAreaInsets
        })
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 检查是否为iOS系统
 * @returns {Promise<boolean>}
 */
export async function isIOS() {
  try {
    const systemInfo = await getSystemInfo()
    return systemInfo.platform === 'ios'
  } catch (error) {
    console.error('获取系统信息失败:', error)
    return false
  }
}

/**
 * 检查是否为Android系统
 * @returns {Promise<boolean>}
 */
export async function isAndroid() {
  try {
    const systemInfo = await getSystemInfo()
    return systemInfo.platform === 'android'
  } catch (error) {
    console.error('获取系统信息失败:', error)
    return false
  }
}

/**
 * 获取平台特定的配置
 * @returns {object} 平台配置对象
 */
export function getPlatformConfig() {
  const platform = getCurrentPlatform()
  
  const configs = {
    'app-plus': {
      supportCamera: true,
      supportShare: true,
      supportBiometric: true,
      maxImageSize: 10 * 1024 * 1024, // 10MB
      supportedImageFormats: ['jpg', 'jpeg', 'png', 'webp']
    },
    'mp-weixin': {
      supportCamera: true,
      supportShare: true,
      supportBiometric: false,
      maxImageSize: 2 * 1024 * 1024, // 2MB
      supportedImageFormats: ['jpg', 'jpeg', 'png']
    },
    'h5': {
      supportCamera: false,
      supportShare: false,
      supportBiometric: false,
      maxImageSize: 5 * 1024 * 1024, // 5MB
      supportedImageFormats: ['jpg', 'jpeg', 'png', 'webp']
    }
  }
  
  return configs[platform] || configs['h5']
}