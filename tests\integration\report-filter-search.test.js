/**
 * 报告筛选和搜索功能集成测试
 * 测试用户交互和功能完整性
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ReportFilter from '@/components/business/ReportFilter.vue'
import ReportSearch from '@/components/business/ReportSearch.vue'
import { useReportStore } from '@/stores/report.js'
import { Report, ReportItem } from '@/models/Report.js'
import { Constants } from '@/types/index.js'

// 模拟uni-app API
global.uni = {
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  showModal: jest.fn()
}

describe('报告筛选和搜索功能测试', () => {
  let pinia
  let reportStore

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    reportStore = useReportStore()
    
    // 重置模拟函数
    jest.clearAllMocks()
  })

  /**
   * 生成测试报告数据
   */
  function generateTestReports() {
    const reports = []
    const hospitals = ['北京协和医院', '上海华山医院', '广州中山医院']
    const doctors = ['张医生', '李医生', '王医生']
    const categories = [
      Constants.REPORT_CATEGORIES.BLOOD_ROUTINE,
      Constants.REPORT_CATEGORIES.BIOCHEMISTRY,
      Constants.REPORT_CATEGORIES.IMMUNOLOGY
    ]

    for (let i = 0; i < 10; i++) {
      const reportData = {
        id: `report_${i}`,
        userId: 'test_user',
        title: `检查报告 ${i + 1}`,
        hospital: hospitals[i % hospitals.length],
        doctor: doctors[i % doctors.length],
        checkDate: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        reportDate: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        category: categories[i % categories.length],
        items: [
          new ReportItem({
            id: `item_${i}_1`,
            name: '白细胞计数',
            value: '5.5',
            unit: '×10^9/L',
            referenceRange: '3.5-9.5',
            isAbnormal: i % 3 === 0,
            category: categories[i % categories.length]
          }),
          new ReportItem({
            id: `item_${i}_2`,
            name: '红细胞计数',
            value: '4.2',
            unit: '×10^12/L',
            referenceRange: '4.3-5.8',
            isAbnormal: i % 4 === 0,
            category: categories[i % categories.length]
          })
        ]
      }

      reports.push(new Report(reportData))
    }

    return reports
  }

  describe('ReportFilter 组件测试', () => {
    let wrapper
    let testReports

    beforeEach(() => {
      testReports = generateTestReports()
    })

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount()
      }
    })

    it('应该正确渲染筛选组件', () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {
            timeRange: { start: null, end: null },
            categories: [],
            hospitals: [],
            abnormalStatus: 'all',
            sortBy: 'reportDate',
            sortOrder: 'desc'
          }
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.report-filter').exists()).toBe(true)
      expect(wrapper.find('.quick-filters').exists()).toBe(true)
    })

    it('应该正确显示快速筛选选项', () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      const quickFilters = wrapper.findAll('.filter-chip')
      expect(quickFilters.length).toBeGreaterThan(0)
      
      // 验证快速筛选选项包含预期内容
      const chipTexts = quickFilters.map(chip => chip.find('.chip-text').text())
      expect(chipTexts).toContain('最近')
      expect(chipTexts).toContain('异常')
      expect(chipTexts).toContain('血液')
    })

    it('应该能够切换高级筛选显示', async () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      // 初始状态不显示高级筛选
      expect(wrapper.find('.advanced-filters').exists()).toBe(false)

      // 点击切换按钮
      const toggleBtn = wrapper.find('.toggle-btn')
      await toggleBtn.trigger('tap')

      // 应该显示高级筛选
      expect(wrapper.find('.advanced-filters').exists()).toBe(true)
      expect(wrapper.vm.showAdvanced).toBe(true)
    })

    it('应该能够选择时间范围', async () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      // 显示高级筛选
      await wrapper.setData({ showAdvanced: true })

      // 选择时间范围
      const timeOptions = wrapper.findAll('.time-option')
      expect(timeOptions.length).toBeGreaterThan(0)

      // 点击"最近一周"选项
      const weekOption = timeOptions.find(option => 
        option.find('.option-text').text() === '最近一周'
      )
      
      if (weekOption) {
        await weekOption.trigger('tap')
        expect(wrapper.vm.selectedTimeRange).toBe('week')
      }
    })

    it('应该能够选择检查类别', async () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      // 显示高级筛选
      await wrapper.setData({ showAdvanced: true })

      // 选择血常规类别
      const categoryItems = wrapper.findAll('.category-item')
      const bloodCategory = categoryItems.find(item => 
        item.find('.category-name').text() === '血常规'
      )

      if (bloodCategory) {
        await bloodCategory.trigger('tap')
        expect(wrapper.vm.selectedCategories).toContain(Constants.REPORT_CATEGORIES.BLOOD_ROUTINE)
      }
    })

    it('应该能够搜索和选择医院', async () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      // 显示高级筛选
      await wrapper.setData({ showAdvanced: true })

      // 输入医院搜索关键词
      const hospitalInput = wrapper.find('.hospital-search')
      await hospitalInput.setValue('协和')
      await hospitalInput.trigger('input')

      expect(wrapper.vm.hospitalKeyword).toBe('协和')

      // 验证医院建议列表
      const hospitalSuggestions = wrapper.vm.hospitalSuggestions
      expect(hospitalSuggestions.length).toBeGreaterThan(0)
      expect(hospitalSuggestions.some(h => h.name.includes('协和'))).toBe(true)
    })

    it('应该能够选择异常状态', async () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      // 显示高级筛选
      await wrapper.setData({ showAdvanced: true })

      // 选择异常状态
      const abnormalOptions = wrapper.findAll('.abnormal-option')
      const abnormalOption = abnormalOptions.find(option => 
        option.find('.option-label').text() === '异常'
      )

      if (abnormalOption) {
        await abnormalOption.trigger('tap')
        expect(wrapper.vm.selectedAbnormalStatus).toBe('abnormal')
      }
    })

    it('应该能够选择排序方式', async () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      // 显示高级筛选
      await wrapper.setData({ showAdvanced: true })

      // 选择排序方式
      const sortOptions = wrapper.findAll('.sort-option')
      const hospitalSort = sortOptions.find(option => 
        option.find('.sort-label').text() === '医院名称'
      )

      if (hospitalSort) {
        await hospitalSort.trigger('tap')
        expect(wrapper.vm.selectedSort).toBe('hospital')
      }
    })

    it('应该能够重置筛选条件', async () => {
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      // 设置一些筛选条件
      await wrapper.setData({
        selectedTimeRange: 'week',
        selectedCategories: [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE],
        selectedHospitals: ['北京协和医院'],
        selectedAbnormalStatus: 'abnormal'
      })

      // 点击重置按钮
      const resetBtn = wrapper.find('.reset-btn')
      await resetBtn.trigger('tap')

      // 验证筛选条件已重置
      expect(wrapper.vm.selectedTimeRange).toBe('all')
      expect(wrapper.vm.selectedCategories).toEqual([])
      expect(wrapper.vm.selectedHospitals).toEqual([])
      expect(wrapper.vm.selectedAbnormalStatus).toBe('all')
    })

    it('应该能够应用筛选条件', async () => {
      const applyHandler = jest.fn()
      
      wrapper = mount(ReportFilter, {
        props: {
          reports: testReports,
          currentFilter: {}
        },
        global: {
          stubs: {
            'uni-icons': true,
            'picker': true
          }
        }
      })

      wrapper.vm.$emit = applyHandler

      // 设置筛选条件
      await wrapper.setData({
        selectedTimeRange: 'week',
        selectedCategories: [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE],
        selectedAbnormalStatus: 'abnormal'
      })

      // 点击应用按钮
      const applyBtn = wrapper.find('.apply-btn')
      await applyBtn.trigger('tap')

      // 验证emit被调用
      expect(applyHandler).toHaveBeenCalledWith('apply', expect.objectContaining({
        categories: [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE],
        abnormalStatus: 'abnormal'
      }))
    })
  })

  describe('ReportSearch 组件测试', () => {
    let wrapper
    let testReports

    beforeEach(() => {
      testReports = generateTestReports()
      // 模拟本地存储
      global.uni.getStorageSync.mockReturnValue([])
    })

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount()
      }
    })

    it('应该正确渲染搜索组件', () => {
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.search-input').exists()).toBe(true)
      expect(wrapper.find('.search-input-container').exists()).toBe(true)
    })

    it('应该能够输入搜索关键词', async () => {
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      const searchInput = wrapper.find('.search-input')
      await searchInput.setValue('协和')
      await searchInput.trigger('input')

      expect(wrapper.vm.searchKeyword).toBe('协和')
    })

    it('应该能够生成搜索建议', async () => {
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports,
          showSuggestions: true
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      // 输入搜索关键词
      wrapper.vm.searchKeyword = '协和'
      wrapper.vm.generateSuggestions()

      // 验证生成了建议
      expect(wrapper.vm.suggestions.length).toBeGreaterThan(0)
      expect(wrapper.vm.suggestions.some(s => s.text.includes('协和'))).toBe(true)
    })

    it('应该能够选择搜索建议', async () => {
      const searchHandler = jest.fn()
      
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports,
          showSuggestions: true
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      wrapper.vm.$emit = searchHandler

      // 设置搜索建议
      await wrapper.setData({
        suggestions: [
          { id: 1, text: '北京协和医院', type: 'hospital' }
        ]
      })

      // 点击建议项
      const suggestionItem = wrapper.find('.suggestion-item')
      if (suggestionItem.exists()) {
        await suggestionItem.trigger('tap')
        
        expect(wrapper.vm.searchKeyword).toBe('北京协和医院')
        expect(searchHandler).toHaveBeenCalledWith('search', '北京协和医院')
      }
    })

    it('应该能够清除搜索', async () => {
      const clearHandler = jest.fn()
      
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      wrapper.vm.$emit = clearHandler

      // 设置搜索关键词
      await wrapper.setData({ searchKeyword: '协和' })

      // 点击清除按钮
      const clearBtn = wrapper.find('.clear-btn')
      if (clearBtn.exists()) {
        await clearBtn.trigger('tap')
        
        expect(wrapper.vm.searchKeyword).toBe('')
        expect(clearHandler).toHaveBeenCalledWith('clear')
      }
    })

    it('应该能够显示热门搜索', () => {
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports,
          showHotSearch: true
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      const hotSearches = wrapper.find('.hot-searches')
      expect(hotSearches.exists()).toBe(true)

      const hotTags = wrapper.findAll('.hot-tag')
      expect(hotTags.length).toBeGreaterThan(0)
    })

    it('应该能够选择热门搜索', async () => {
      const searchHandler = jest.fn()
      
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports,
          showHotSearch: true
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      wrapper.vm.$emit = searchHandler

      // 点击热门搜索标签
      const hotTag = wrapper.find('.hot-tag')
      if (hotTag.exists()) {
        await hotTag.trigger('tap')
        
        expect(wrapper.vm.searchKeyword).toBeTruthy()
        expect(searchHandler).toHaveBeenCalledWith('search', expect.any(String))
      }
    })

    it('应该能够管理搜索历史', async () => {
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports,
          showHistory: true
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      // 添加搜索历史
      wrapper.vm.addToHistory('血常规')
      expect(wrapper.vm.searchHistory.length).toBe(1)
      expect(wrapper.vm.searchHistory[0].text).toBe('血常规')

      // 验证保存到本地存储
      expect(global.uni.setStorageSync).toHaveBeenCalledWith(
        'search_history',
        expect.arrayContaining([
          expect.objectContaining({ text: '血常规' })
        ])
      )
    })

    it('应该能够防抖处理搜索输入', async () => {
      jest.useFakeTimers()
      
      const inputHandler = jest.fn()
      
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports,
          debounceDelay: 300
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      wrapper.vm.$emit = inputHandler

      // 快速输入多次
      wrapper.vm.searchKeyword = '协'
      wrapper.vm.handleInput()
      
      wrapper.vm.searchKeyword = '协和'
      wrapper.vm.handleInput()

      // 在防抖时间内不应该触发
      expect(inputHandler).not.toHaveBeenCalled()

      // 等待防抖时间
      jest.advanceTimersByTime(300)

      // 现在应该触发了
      expect(inputHandler).toHaveBeenCalledWith('input', '协和')

      jest.useRealTimers()
    })

    it('应该能够显示搜索结果统计', () => {
      wrapper = mount(ReportSearch, {
        props: {
          reports: testReports,
          showResultStats: true,
          resultCount: 5,
          searchTime: 120
        },
        global: {
          stubs: {
            'uni-icons': true
          }
        }
      })

      // 设置搜索关键词以显示统计
      wrapper.setData({ searchKeyword: '协和' })

      const searchStats = wrapper.find('.search-stats')
      expect(searchStats.exists()).toBe(true)
      
      const statsText = searchStats.find('.stats-text')
      expect(statsText.text()).toContain('找到 5 条相关结果')
      
      const searchTime = searchStats.find('.search-time')
      expect(searchTime.text()).toContain('120ms')
    })
  })

  describe('筛选和搜索集成测试', () => {
    it('应该能够同时使用筛选和搜索功能', async () => {
      const testReports = generateTestReports()
      reportStore.setReports(testReports)

      // 应用筛选条件
      const filterCondition = {
        categories: [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE],
        abnormalStatus: 'abnormal'
      }
      reportStore.updateFilter(filterCondition)

      // 验证筛选结果
      const filteredReports = reportStore.filteredReports
      expect(filteredReports.length).toBeLessThanOrEqual(testReports.length)
      
      // 验证筛选条件生效
      filteredReports.forEach(report => {
        expect(report.category).toBe(Constants.REPORT_CATEGORIES.BLOOD_ROUTINE)
        expect(report.items.some(item => item.isAbnormal)).toBe(true)
      })
    })

    it('应该能够正确处理复杂筛选条件', () => {
      const testReports = generateTestReports()
      reportStore.setReports(testReports)

      // 应用复杂筛选条件
      const complexFilter = {
        timeRange: {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end: new Date().toISOString().split('T')[0]
        },
        categories: [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE, Constants.REPORT_CATEGORIES.BIOCHEMISTRY],
        hospitals: ['北京协和医院'],
        abnormalStatus: 'all'
      }

      reportStore.updateFilter(complexFilter)

      const filteredReports = reportStore.filteredReports
      
      // 验证时间范围
      const startDate = new Date(complexFilter.timeRange.start)
      const endDate = new Date(complexFilter.timeRange.end)
      
      filteredReports.forEach(report => {
        const reportDate = new Date(report.reportDate)
        expect(reportDate >= startDate && reportDate <= endDate).toBe(true)
        expect(complexFilter.categories).toContain(report.category)
        expect(complexFilter.hospitals).toContain(report.hospital)
      })
    })
  })
})