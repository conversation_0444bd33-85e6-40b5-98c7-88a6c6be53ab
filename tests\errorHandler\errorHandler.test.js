/**
 * 错误处理系统测试
 */
const GlobalErrorHandler = require('../../utils/errorHandler.js')
const ErrorLogger = require('../../utils/errorHandler/ErrorLogger.js')
const NetworkErrorHandler = require('../../utils/errorHandler/NetworkErrorHandler.js')
const OCRErrorHandler = require('../../utils/errorHandler/OCRErrorHandler.js')
const ErrorMonitor = require('../../utils/errorHandler/ErrorMonitor.js')

// Mock uni-app API
global.uni = {
  showToast: jest.fn(),
  showModal: jest.fn(),
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  getSystemInfoSync: jest.fn(() => ({
    platform: 'android',
    system: 'Android 10',
    version: '10',
    model: 'Test Device',
    brand: 'Test Brand'
  })),
  onNetworkStatusChange: jest.fn(),
  getNetworkType: jest.fn(),
  request: jest.fn(),
  reLaunch: jest.fn(),
  $emit: jest.fn()
}

describe('GlobalErrorHandler', () => {
  let errorHandler

  beforeEach(() => {
    errorHandler = new GlobalErrorHandler()
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('基础错误处理', () => {
    it('应该正确安装错误处理器', () => {
      const mockApp = {
        config: {}
      }
      
      errorHandler.install(mockApp)
      
      expect(mockApp.config.errorHandler).toBeDefined()
      expect(typeof mockApp.config.errorHandler).toBe('function')
    })

    it('应该正确处理Vue错误', async () => {
      const error = new Error('Vue组件错误')
      const context = { context: 'vue', info: 'component render' }
      
      await errorHandler.handleError(error, context)
      
      expect(uni.showModal).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '错误提示',
          content: '操作失败，请稍后重试'
        })
      )
    })

    it('应该正确格式化错误信息', () => {
      const error = new Error('测试错误')
      const context = { context: 'test' }
      
      const formatted = errorHandler.formatError(error, context)
      
      expect(formatted).toMatchObject({
        message: '测试错误',
        context: 'test',
        level: 'error',
        handled: false
      })
      expect(formatted.id).toBeDefined()
      expect(formatted.timestamp).toBeDefined()
    })

    it('应该根据错误类型返回正确的级别', () => {
      const networkError = new Error('NetworkError')
      networkError.name = 'NetworkError'
      
      const validationError = new Error('ValidationError')
      validationError.name = 'ValidationError'
      
      const generalError = new Error('General error')
      
      expect(errorHandler.getErrorLevel(networkError)).toBe('warning')
      expect(errorHandler.getErrorLevel(validationError)).toBe('info')
      expect(errorHandler.getErrorLevel(generalError)).toBe('error')
    })
  })

  describe('用户友好错误提示', () => {
    it('应该为网络错误显示友好提示', () => {
      const errorInfo = {
        message: 'NetworkError: 连接失败',
        level: 'warning'
      }
      
      errorHandler.showUserFriendlyError(errorInfo)
      
      expect(uni.showToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '网络连接异常，请检查网络设置',
          icon: 'none',
          duration: 3000
        })
      )
    })

    it('应该为OCR错误显示友好提示', () => {
      const errorInfo = {
        context: 'OCRError',
        message: 'OCR识别失败',
        level: 'error'
      }
      
      errorHandler.showUserFriendlyError(errorInfo)
      
      expect(uni.showModal).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '错误提示',
          content: 'OCR识别失败，请重新拍摄清晰的图片'
        })
      )
    })

    it('应该为不同错误级别使用不同的提示方式', () => {
      const infoError = { level: 'info', message: '信息提示' }
      const warningError = { level: 'warning', message: '警告提示' }
      const criticalError = { level: 'error', message: '严重错误' }
      
      errorHandler.showUserFriendlyError(infoError)
      expect(uni.showToast).toHaveBeenCalledWith(
        expect.objectContaining({ duration: 2000 })
      )
      
      errorHandler.showUserFriendlyError(warningError)
      expect(uni.showToast).toHaveBeenCalledWith(
        expect.objectContaining({ duration: 3000 })
      )
      
      errorHandler.showUserFriendlyError(criticalError)
      expect(uni.showModal).toHaveBeenCalled()
    })
  })

  describe('错误队列处理', () => {
    it('应该正确处理错误队列', async () => {
      const error1 = new Error('错误1')
      const error2 = new Error('错误2')
      
      errorHandler.handleError(error1)
      errorHandler.handleError(error2)
      
      // 等待队列处理完成
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(errorHandler.errorQueue.length).toBe(0)
    })

    it('应该防止重复处理错误队列', async () => {
      const error = new Error('测试错误')
      
      // 同时触发多次错误处理
      errorHandler.handleError(error)
      errorHandler.handleError(error)
      errorHandler.handleError(error)
      
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // 应该只处理一次
      expect(errorHandler.isProcessing).toBe(false)
    })
  })

  describe('网络错误处理集成', () => {
    it('应该使用网络错误处理器处理请求', async () => {
      const requestOptions = {
        url: 'https://api.test.com/data',
        method: 'GET'
      }
      
      // Mock网络请求失败
      uni.request.mockImplementation((options) => {
        options.fail({ errMsg: '网络错误' })
      })
      
      try {
        await errorHandler.request(requestOptions)
      } catch (error) {
        expect(error.name).toBe('RequestError')
      }
    })

    it('应该处理网络重连', async () => {
      const networkHandler = errorHandler.networkHandler
      
      // 模拟网络断开
      networkHandler.handleNetworkDisconnect()
      expect(networkHandler.offlineMode).toBe(true)
      
      // 模拟网络重连
      await networkHandler.handleNetworkReconnect()
      expect(networkHandler.offlineMode).toBe(false)
      
      expect(uni.showToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '网络连接已恢复'
        })
      )
    })
  })

  describe('OCR错误处理集成', () => {
    it('应该使用OCR错误处理器处理OCR失败', async () => {
      const ocrError = new Error('OCR识别失败')
      ocrError.name = 'OCRError'
      
      const context = {
        service: 'baidu',
        imageData: 'base64_image_data',
        retryCount: 0
      }
      
      try {
        await errorHandler.ocrRequest(context.imageData, context.service)
      } catch (error) {
        expect(error.name).toBe('OCRError')
      }
    })
  })

  describe('错误监控集成', () => {
    it('应该记录错误到监控系统', async () => {
      const error = new Error('监控测试错误')
      const spy = jest.spyOn(errorHandler.monitor, 'recordError')
      
      await errorHandler.handleError(error)
      
      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          message: '监控测试错误'
        })
      )
    })

    it('应该处理监控告警', () => {
      const alert = {
        type: 'high_error_rate',
        data: { errorRate: 0.15 }
      }
      
      errorHandler.handleMonitorAlert(alert)
      
      expect(uni.showModal).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '系统异常'
        })
      )
    })
  })

  describe('API接口', () => {
    it('应该提供获取错误日志的接口', () => {
      const logs = errorHandler.getErrorLogs()
      expect(Array.isArray(logs)).toBe(true)
    })

    it('应该提供清除错误日志的接口', () => {
      errorHandler.clearErrorLogs()
      expect(errorHandler.logger.clearLocalLogs).toBeDefined()
    })

    it('应该提供获取错误统计的接口', () => {
      const stats = errorHandler.getErrorStats()
      expect(stats).toHaveProperty('total')
      expect(stats).toHaveProperty('uploaded')
      expect(stats).toHaveProperty('pending')
    })

    it('应该提供获取网络状态的接口', () => {
      const status = errorHandler.getNetworkStatus()
      expect(status).toHaveProperty('online')
      expect(status).toHaveProperty('offlineMode')
    })

    it('应该提供获取监控报告的接口', () => {
      const report = errorHandler.getMonitoringReport()
      expect(report).toHaveProperty('timestamp')
      expect(report).toHaveProperty('stats')
    })
  })
})

describe('ErrorLogger', () => {
  let logger

  beforeEach(() => {
    logger = new ErrorLogger()
    jest.clearAllMocks()
  })

  it('应该正确记录错误日志', () => {
    const errorInfo = {
      message: '测试错误',
      level: 'error',
      context: 'test'
    }
    
    logger.log(errorInfo)
    
    expect(uni.setStorageSync).toHaveBeenCalledWith(
      'error_logs',
      expect.arrayContaining([
        expect.objectContaining({
          message: '测试错误',
          level: 'error'
        })
      ])
    )
  })

  it('应该限制本地日志数量', () => {
    // Mock大量日志
    const largeLogs = Array(600).fill().map((_, i) => ({
      id: `log_${i}`,
      message: `错误${i}`
    }))
    
    uni.getStorageSync.mockReturnValue(largeLogs)
    
    logger.log({ message: '新错误' })
    
    const savedLogs = uni.setStorageSync.mock.calls[0][1]
    expect(savedLogs.length).toBeLessThanOrEqual(500)
  })

  it('应该正确生成日志统计', () => {
    const logs = [
      { level: 'error', context: 'network', timestamp: Date.now() },
      { level: 'warning', context: 'ocr', timestamp: Date.now() - 1000 },
      { level: 'error', context: 'network', timestamp: Date.now() - 2000 }
    ]
    
    uni.getStorageSync.mockReturnValue(logs)
    
    const stats = logger.getLogStats()
    
    expect(stats.total).toBe(3)
    expect(stats.byLevel.error).toBe(2)
    expect(stats.byLevel.warning).toBe(1)
    expect(stats.byContext.network).toBe(2)
    expect(stats.byContext.ocr).toBe(1)
  })
})

describe('NetworkErrorHandler', () => {
  let networkHandler

  beforeEach(() => {
    networkHandler = new NetworkErrorHandler()
    jest.clearAllMocks()
  })

  it('应该正确处理网络请求重试', async () => {
    let callCount = 0
    uni.request.mockImplementation((options) => {
      callCount++
      if (callCount < 3) {
        options.fail({ errMsg: '网络错误' })
      } else {
        options.success({ statusCode: 200, data: 'success' })
      }
    })
    
    const result = await networkHandler.request({
      url: 'https://api.test.com/data'
    })
    
    expect(result.statusCode).toBe(200)
    expect(callCount).toBe(3)
  })

  it('应该正确处理离线请求', async () => {
    networkHandler.offlineMode = true
    
    const promise = networkHandler.request({
      url: 'https://api.test.com/data',
      method: 'POST'
    })
    
    expect(networkHandler.offlineQueue.length).toBe(1)
    expect(uni.showToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: '网络不可用，请求已加入队列'
      })
    )
  })

  it('应该正确计算重试延迟', () => {
    const delay1 = networkHandler.calculateRetryDelay(1)
    const delay2 = networkHandler.calculateRetryDelay(2)
    const delay3 = networkHandler.calculateRetryDelay(3)
    
    expect(delay1).toBe(1000)
    expect(delay2).toBe(2000)
    expect(delay3).toBe(4000)
  })
})

describe('OCRErrorHandler', () => {
  let ocrHandler

  beforeEach(() => {
    ocrHandler = new OCRErrorHandler()
    jest.clearAllMocks()
  })

  it('应该正确执行降级策略', async () => {
    const error = new Error('OCR识别失败')
    const context = {
      service: 'baidu',
      imageData: 'test_image',
      retryCount: 0
    }
    
    // Mock手动输入策略成功
    uni.showModal.mockImplementation((options) => {
      options.success({ confirm: true })
    })
    
    try {
      const result = await ocrHandler.handleOCRError(error, context)
      expect(result.success).toBe(true)
    } catch (err) {
      // 预期会失败，因为没有实际的OCR服务
      expect(err.name).toBe('OCRError')
    }
  })

  it('应该正确设置服务可用性', () => {
    ocrHandler.setServiceAvailability('baidu', false)
    
    const status = ocrHandler.getServiceStatus()
    const baiduService = status.find(s => s.name === 'baidu')
    
    expect(baiduService.available).toBe(false)
  })
})

describe('ErrorMonitor', () => {
  let monitor

  beforeEach(() => {
    monitor = new ErrorMonitor()
    jest.clearAllMocks()
  })

  it('应该正确记录错误到监控系统', () => {
    const errorInfo = {
      message: '测试错误',
      level: 'error',
      context: 'test'
    }
    
    monitor.recordError(errorInfo)
    
    expect(monitor.recentErrors.length).toBe(1)
    expect(monitor.errorStats.total).toBe(1)
  })

  it('应该正确检测错误模式', () => {
    const alertCallback = jest.fn()
    monitor.onAlert(alertCallback)
    
    // 添加多个OCR错误
    for (let i = 0; i < 4; i++) {
      monitor.recordError({
        message: `OCR错误${i}`,
        level: 'error',
        context: 'OCRError'
      })
    }
    
    expect(alertCallback).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'ocr_failure_pattern'
      })
    )
  })

  it('应该正确生成错误报告', () => {
    // 添加一些测试错误
    monitor.recordError({ message: '错误1', level: 'error', context: 'test' })
    monitor.recordError({ message: '错误2', level: 'warning', context: 'network' })
    
    const report = monitor.generateErrorReport()
    
    expect(report).toHaveProperty('timestamp')
    expect(report).toHaveProperty('stats')
    expect(report).toHaveProperty('trends')
    expect(report).toHaveProperty('topErrors')
    expect(report).toHaveProperty('recommendations')
  })

  it('应该正确清理过期错误', () => {
    // 添加过期错误
    const oldError = {
      message: '过期错误',
      timestamp: Date.now() - 25 * 60 * 60 * 1000 // 25小时前
    }
    monitor.recentErrors.push(oldError)
    
    // 添加新错误
    monitor.recordError({
      message: '新错误',
      level: 'error'
    })
    
    monitor.cleanupExpiredErrors()
    
    expect(monitor.recentErrors.length).toBe(1)
    expect(monitor.recentErrors[0].message).toBe('新错误')
  })
})