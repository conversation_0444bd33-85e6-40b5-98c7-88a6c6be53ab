/**
 * 手动测试脚本
 * 用于验证平台适配层的基本功能
 */

// 模拟uni-app环境
global.uni = {
  chooseImage: (options) => {
    console.log('调用uni.chooseImage:', options)
    options.success({
      tempFilePaths: ['/temp/test.jpg'],
      tempFiles: [{ path: '/temp/test.jpg', size: 1024 }]
    })
  },
  setStorage: (options) => {
    console.log('调用uni.setStorage:', options.key, options.data)
    options.success()
  },
  getStorage: (options) => {
    console.log('调用uni.getStorage:', options.key)
    options.success({ data: 'test_data' })
  }
}

// 导入我们的模块
async function testPlatformAdapter() {
  try {
    console.log('=== 开始测试平台适配层 ===')
    
    // 测试平台检测
    const { getCurrentPlatform, isApp, isH5 } = await import('../../utils/platform/detector.js')
    console.log('当前平台:', getCurrentPlatform())
    console.log('是否为APP:', isApp())
    console.log('是否为H5:', isH5())
    
    // 测试常量
    const { PLATFORM_TYPES, ERROR_CODES } = await import('../../utils/platform/constants.js')
    console.log('平台类型常量:', Object.keys(PLATFORM_TYPES))
    console.log('错误码常量:', Object.keys(ERROR_CODES))
    
    // 测试适配器
    const { PlatformAdapterClass } = await import('../../utils/platform/PlatformAdapter.js')
    const adapter = new PlatformAdapterClass()
    
    console.log('适配器平台:', adapter.platform)
    console.log('适配器配置:', adapter.config)
    
    // 测试选择图片
    const imageResult = await adapter.chooseImage({ count: 1 })
    console.log('选择图片结果:', imageResult)
    
    // 测试存储
    await adapter.setStorage('test_key', 'test_value')
    const storageResult = await adapter.getStorage('test_key')
    console.log('存储测试结果:', storageResult)
    
    console.log('=== 所有测试通过 ===')
    
  } catch (error) {
    console.error('测试失败:', error)
  }
}

// 运行测试
testPlatformAdapter()