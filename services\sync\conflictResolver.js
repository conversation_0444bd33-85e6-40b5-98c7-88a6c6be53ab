/**
 * 数据冲突检测和解决机制
 * 处理本地数据与云端数据的冲突
 */

import { storage } from '../../utils/storage/index.js';
import { useSyncStore } from '../../stores/sync.js';

class ConflictResolver {
  constructor() {
    this.syncStore = null;
    this.resolutionStrategies = {
      'local_wins': this.resolveWithLocalData.bind(this),
      'remote_wins': this.resolveWithRemoteData.bind(this),
      'merge': this.resolveWithMerge.bind(this),
      'manual': this.resolveManually.bind(this)
    };
  }

  /**
   * 初始化冲突解决器
   */
  initialize() {
    this.syncStore = useSyncStore();
  }

  /**
   * 检测数据冲突
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   * @param {string} dataType 数据类型
   * @returns {Object|null} 冲突信息或null
   */
  detectConflict(localData, remoteData, dataType) {
    if (!localData || !remoteData) {
      return null;
    }

    const conflict = {
      id: `${dataType}_${localData.id}_${Date.now()}`,
      type: dataType,
      localData,
      remoteData,
      conflictFields: [],
      severity: 'low', // low, medium, high
      detectedAt: Date.now()
    };

    // 根据数据类型检测不同的冲突字段
    const conflictFields = this.getConflictFields(dataType);
    
    for (const field of conflictFields) {
      if (this.isFieldConflicted(localData[field], remoteData[field])) {
        conflict.conflictFields.push({
          field,
          localValue: localData[field],
          remoteValue: remoteData[field],
          severity: this.getFieldSeverity(field, dataType)
        });
      }
    }

    // 如果没有冲突字段，返回null
    if (conflict.conflictFields.length === 0) {
      return null;
    }

    // 计算整体冲突严重程度
    conflict.severity = this.calculateOverallSeverity(conflict.conflictFields);

    return conflict;
  }

  /**
   * 获取需要检测冲突的字段
   * @param {string} dataType 
   * @returns {Array}
   */
  getConflictFields(dataType) {
    const fieldMappings = {
      'user_info': [
        'username', 'phone', 'email', 'real_name', 
        'gender', 'birth_date', 'avatar_url'
      ],
      'health_report': [
        'report_title', 'report_date', 'hospital_name', 
        'doctor_name', 'department', 'report_type', 'notes'
      ],
      'health_indicator': [
        'indicator_name', 'indicator_value', 'indicator_unit',
        'reference_range', 'is_abnormal', 'abnormal_level'
      ]
    };

    return fieldMappings[dataType] || [];
  }

  /**
   * 检查字段是否冲突
   * @param {any} localValue 
   * @param {any} remoteValue 
   * @returns {boolean}
   */
  isFieldConflicted(localValue, remoteValue) {
    // 处理null和undefined
    if (localValue == null && remoteValue == null) {
      return false;
    }
    
    if (localValue == null || remoteValue == null) {
      return true;
    }

    // 字符串比较（忽略大小写和空格）
    if (typeof localValue === 'string' && typeof remoteValue === 'string') {
      return localValue.trim().toLowerCase() !== remoteValue.trim().toLowerCase();
    }

    // 数值比较
    if (typeof localValue === 'number' && typeof remoteValue === 'number') {
      return Math.abs(localValue - remoteValue) > 0.001; // 浮点数精度问题
    }

    // 日期比较
    if (localValue instanceof Date && remoteValue instanceof Date) {
      return localValue.getTime() !== remoteValue.getTime();
    }

    // 其他类型直接比较
    return localValue !== remoteValue;
  }

  /**
   * 获取字段冲突严重程度
   * @param {string} field 
   * @param {string} dataType 
   * @returns {string}
   */
  getFieldSeverity(field, dataType) {
    const severityMappings = {
      'user_info': {
        'username': 'high',
        'phone': 'high',
        'email': 'medium',
        'real_name': 'medium',
        'gender': 'low',
        'birth_date': 'medium',
        'avatar_url': 'low'
      },
      'health_report': {
        'report_title': 'medium',
        'report_date': 'high',
        'hospital_name': 'medium',
        'doctor_name': 'low',
        'department': 'low',
        'report_type': 'medium',
        'notes': 'low'
      },
      'health_indicator': {
        'indicator_name': 'high',
        'indicator_value': 'high',
        'indicator_unit': 'medium',
        'reference_range': 'medium',
        'is_abnormal': 'high',
        'abnormal_level': 'high'
      }
    };

    return severityMappings[dataType]?.[field] || 'low';
  }

  /**
   * 计算整体冲突严重程度
   * @param {Array} conflictFields 
   * @returns {string}
   */
  calculateOverallSeverity(conflictFields) {
    const severityScores = { 'low': 1, 'medium': 2, 'high': 3 };
    const maxScore = Math.max(...conflictFields.map(f => severityScores[f.severity]));
    
    const scoreToSeverity = { 1: 'low', 2: 'medium', 3: 'high' };
    return scoreToSeverity[maxScore];
  }

  /**
   * 自动解决冲突
   * @param {Object} conflict 冲突信息
   * @param {string} strategy 解决策略
   * @returns {Promise<Object>}
   */
  async resolveConflict(conflict, strategy = 'auto') {
    if (!this.syncStore) {
      this.initialize();
    }

    try {
      let resolvedData;
      
      if (strategy === 'auto') {
        // 自动选择解决策略
        strategy = this.selectAutoStrategy(conflict);
      }

      // 执行解决策略
      if (this.resolutionStrategies[strategy]) {
        resolvedData = await this.resolutionStrategies[strategy](conflict);
      } else {
        throw new Error(`未知的解决策略: ${strategy}`);
      }

      // 更新数据库
      await this.applyResolution(conflict, resolvedData, strategy);

      // 标记冲突为已解决
      this.syncStore.resolveConflict(conflict.id, {
        strategy,
        resolvedData,
        resolvedAt: Date.now()
      });

      return {
        success: true,
        strategy,
        resolvedData
      };

    } catch (error) {
      console.error('解决冲突失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 自动选择解决策略
   * @param {Object} conflict 
   * @returns {string}
   */
  selectAutoStrategy(conflict) {
    // 根据冲突严重程度和数据类型选择策略
    if (conflict.severity === 'high') {
      return 'manual'; // 高严重程度需要手动处理
    }

    // 比较数据的更新时间
    const localUpdateTime = new Date(conflict.localData.updated_at).getTime();
    const remoteUpdateTime = new Date(conflict.remoteData.updated_at).getTime();

    if (Math.abs(localUpdateTime - remoteUpdateTime) < 60000) { // 1分钟内
      return 'merge'; // 时间相近，尝试合并
    }

    // 选择更新时间较新的数据
    return localUpdateTime > remoteUpdateTime ? 'local_wins' : 'remote_wins';
  }

  /**
   * 使用本地数据解决冲突
   * @param {Object} conflict 
   * @returns {Promise<Object>}
   */
  async resolveWithLocalData(conflict) {
    return conflict.localData;
  }

  /**
   * 使用远程数据解决冲突
   * @param {Object} conflict 
   * @returns {Promise<Object>}
   */
  async resolveWithRemoteData(conflict) {
    return conflict.remoteData;
  }

  /**
   * 合并数据解决冲突
   * @param {Object} conflict 
   * @returns {Promise<Object>}
   */
  async resolveWithMerge(conflict) {
    const mergedData = { ...conflict.localData };

    // 根据字段优先级合并数据
    for (const conflictField of conflict.conflictFields) {
      const { field, localValue, remoteValue, severity } = conflictField;

      // 根据字段类型和优先级选择值
      if (severity === 'low') {
        // 低优先级字段，选择非空值
        mergedData[field] = localValue || remoteValue;
      } else if (severity === 'medium') {
        // 中优先级字段，选择更新的值
        const localTime = new Date(conflict.localData.updated_at).getTime();
        const remoteTime = new Date(conflict.remoteData.updated_at).getTime();
        mergedData[field] = localTime > remoteTime ? localValue : remoteValue;
      } else {
        // 高优先级字段，需要特殊处理
        mergedData[field] = this.mergeHighPriorityField(field, localValue, remoteValue);
      }
    }

    // 更新合并时间
    mergedData.updated_at = new Date().toISOString();

    return mergedData;
  }

  /**
   * 合并高优先级字段
   * @param {string} field 
   * @param {any} localValue 
   * @param {any} remoteValue 
   * @returns {any}
   */
  mergeHighPriorityField(field, localValue, remoteValue) {
    // 根据字段类型进行特殊合并逻辑
    switch (field) {
      case 'indicator_value':
        // 健康指标值，选择数值较大的（可能更准确）
        const localNum = parseFloat(localValue);
        const remoteNum = parseFloat(remoteValue);
        return !isNaN(localNum) && !isNaN(remoteNum) 
          ? (localNum > remoteNum ? localValue : remoteValue)
          : localValue || remoteValue;

      case 'report_date':
        // 报告日期，选择较新的日期
        const localDate = new Date(localValue);
        const remoteDate = new Date(remoteValue);
        return localDate > remoteDate ? localValue : remoteValue;

      default:
        // 默认选择本地值
        return localValue;
    }
  }

  /**
   * 手动解决冲突（返回冲突信息供用户选择）
   * @param {Object} conflict 
   * @returns {Promise<Object>}
   */
  async resolveManually(conflict) {
    // 手动解决需要用户界面交互，这里返回冲突信息
    return {
      requiresUserInput: true,
      conflict
    };
  }

  /**
   * 应用解决方案到数据库
   * @param {Object} conflict 
   * @param {Object} resolvedData 
   * @param {string} strategy 
   */
  async applyResolution(conflict, resolvedData, strategy) {
    if (resolvedData.requiresUserInput) {
      // 手动解决，不更新数据库
      return;
    }

    const { type, localData } = conflict;
    const recordId = localData.id;

    try {
      switch (type) {
        case 'user_info':
          await storage.users.update(recordId, resolvedData);
          break;
        case 'health_report':
          await storage.reports.update(recordId, resolvedData);
          break;
        case 'health_indicator':
          await storage.indicators.update(recordId, resolvedData);
          break;
        default:
          throw new Error(`未知的数据类型: ${type}`);
      }

      console.log(`冲突解决成功: ${type} ${recordId}, 策略: ${strategy}`);
    } catch (error) {
      console.error('应用冲突解决方案失败:', error);
      throw error;
    }
  }

  /**
   * 批量解决冲突
   * @param {Array} conflicts 
   * @param {string} strategy 
   * @returns {Promise<Array>}
   */
  async resolveConflicts(conflicts, strategy = 'auto') {
    const results = [];

    for (const conflict of conflicts) {
      try {
        const result = await this.resolveConflict(conflict, strategy);
        results.push({
          conflictId: conflict.id,
          ...result
        });
      } catch (error) {
        results.push({
          conflictId: conflict.id,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * 获取冲突统计信息
   * @returns {Object}
   */
  getConflictStatistics() {
    if (!this.syncStore) {
      this.initialize();
    }

    const conflicts = this.syncStore.conflicts;
    const stats = {
      total: conflicts.length,
      resolved: conflicts.filter(c => c.resolved).length,
      pending: conflicts.filter(c => !c.resolved).length,
      bySeverity: {
        low: conflicts.filter(c => c.severity === 'low').length,
        medium: conflicts.filter(c => c.severity === 'medium').length,
        high: conflicts.filter(c => c.severity === 'high').length
      },
      byType: {}
    };

    // 按类型统计
    for (const conflict of conflicts) {
      const type = conflict.type;
      if (!stats.byType[type]) {
        stats.byType[type] = 0;
      }
      stats.byType[type]++;
    }

    return stats;
  }

  /**
   * 清理已解决的冲突
   * @param {number} daysToKeep 保留天数
   */
  async cleanupResolvedConflicts(daysToKeep = 30) {
    if (!this.syncStore) {
      this.initialize();
    }

    const cutoffTime = Date.now() - daysToKeep * 24 * 60 * 60 * 1000;
    
    this.syncStore.conflicts = this.syncStore.conflicts.filter(conflict => {
      return !conflict.resolved || conflict.resolvedAt > cutoffTime;
    });

    this.syncStore.saveToLocal();
  }
}

// 导出单例实例
export const conflictResolver = new ConflictResolver();
export default conflictResolver;