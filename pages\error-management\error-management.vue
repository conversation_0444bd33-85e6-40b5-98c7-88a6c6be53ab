<template>
  <view class="error-management">
    <view class="header">
      <text class="title">错误管理</text>
      <view class="actions">
        <button @click="refreshData" class="refresh-btn" size="mini">刷新</button>
        <button @click="clearAllLogs" class="clear-btn" size="mini" type="warn">清空</button>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-section">
      <view class="stats-card">
        <text class="stats-title">错误统计</text>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ errorStats.total }}</text>
            <text class="stat-label">总错误数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ errorStats.pending }}</text>
            <text class="stat-label">待上报</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ errorStats.recent24h }}</text>
            <text class="stat-label">24小时内</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ networkStatus.online ? '在线' : '离线' }}</text>
            <text class="stat-label">网络状态</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 告警信息 -->
    <view class="alerts-section" v-if="alerts.length > 0">
      <text class="section-title">最近告警</text>
      <view class="alert-list">
        <view 
          v-for="alert in alerts.slice(0, 3)" 
          :key="alert.id"
          class="alert-item"
          :class="getAlertClass(alert.type)"
        >
          <view class="alert-content">
            <text class="alert-message">{{ alert.data.message }}</text>
            <text class="alert-time">{{ formatTime(alert.timestamp) }}</text>
          </view>
          <text class="alert-type">{{ getAlertTypeText(alert.type) }}</text>
        </view>
      </view>
    </view>

    <!-- 错误级别分布 -->
    <view class="level-section">
      <text class="section-title">错误级别分布</text>
      <view class="level-chart">
        <view 
          v-for="(count, level) in errorStats.byLevel" 
          :key="level"
          class="level-bar"
        >
          <text class="level-name">{{ getLevelText(level) }}</text>
          <view class="level-progress">
            <view 
              class="level-fill"
              :class="getLevelClass(level)"
              :style="{ width: getLevelPercentage(count) + '%' }"
            ></view>
          </view>
          <text class="level-count">{{ count }}</text>
        </view>
      </view>
    </view>

    <!-- 错误日志列表 -->
    <view class="logs-section">
      <view class="logs-header">
        <text class="section-title">错误日志</text>
        <view class="filter-tabs">
          <text 
            v-for="filter in filterOptions" 
            :key="filter.value"
            class="filter-tab"
            :class="{ active: currentFilter === filter.value }"
            @click="setFilter(filter.value)"
          >
            {{ filter.label }}
          </text>
        </view>
      </view>
      
      <view class="log-list">
        <view 
          v-for="log in filteredLogs" 
          :key="log.id"
          class="log-item"
          @click="showLogDetail(log)"
        >
          <view class="log-header">
            <text class="log-level" :class="getLevelClass(log.level)">
              {{ getLevelText(log.level) }}
            </text>
            <text class="log-time">{{ formatTime(log.timestamp) }}</text>
          </view>
          <text class="log-message">{{ log.message }}</text>
          <text class="log-context">{{ log.context }}</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <button @click="loadMore" size="mini">加载更多</button>
      </view>
    </view>

    <!-- 错误详情弹窗 -->
    <view class="modal-overlay" v-if="showDetailModal" @click="closeDetailModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">错误详情</text>
          <text class="modal-close" @click="closeDetailModal">×</text>
        </view>
        <view class="modal-body">
          <view class="detail-item">
            <text class="detail-label">错误消息:</text>
            <text class="detail-value">{{ selectedLog.message }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">错误级别:</text>
            <text class="detail-value">{{ getLevelText(selectedLog.level) }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">上下文:</text>
            <text class="detail-value">{{ selectedLog.context }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">时间:</text>
            <text class="detail-value">{{ formatFullTime(selectedLog.timestamp) }}</text>
          </view>
          <view class="detail-item" v-if="selectedLog.stack">
            <text class="detail-label">堆栈信息:</text>
            <text class="detail-value stack">{{ selectedLog.stack }}</text>
          </view>
          <view class="detail-item" v-if="selectedLog.deviceInfo">
            <text class="detail-label">设备信息:</text>
            <text class="detail-value">{{ formatDeviceInfo(selectedLog.deviceInfo) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import globalErrorHandler from '../../utils/errorHandler.js'

export default {
  name: 'ErrorManagement',
  data() {
    return {
      errorStats: {
        total: 0,
        uploaded: 0,
        pending: 0,
        byLevel: {},
        byContext: {},
        recent24h: 0,
        recent7d: 0
      },
      networkStatus: {
        online: true,
        offlineMode: false,
        queueLength: 0
      },
      alerts: [],
      logs: [],
      filteredLogs: [],
      currentFilter: 'all',
      filterOptions: [
        { label: '全部', value: 'all' },
        { label: '错误', value: 'error' },
        { label: '警告', value: 'warning' },
        { label: '信息', value: 'info' }
      ],
      showDetailModal: false,
      selectedLog: {},
      pageSize: 20,
      currentPage: 1,
      hasMore: true
    }
  },
  
  onLoad() {
    this.loadData()
  },
  
  methods: {
    /**
     * 加载数据
     */
    async loadData() {
      try {
        // 获取错误统计
        this.errorStats = globalErrorHandler.getErrorStats()
        
        // 获取网络状态
        this.networkStatus = globalErrorHandler.getNetworkStatus()
        
        // 获取告警历史
        this.alerts = globalErrorHandler.getAlertHistory()
        
        // 获取错误日志
        this.loadLogs()
      } catch (error) {
        console.error('加载错误管理数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
    },
    
    /**
     * 加载错误日志
     */
    loadLogs() {
      const allLogs = globalErrorHandler.getErrorLogs()
      const startIndex = (this.currentPage - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      
      if (this.currentPage === 1) {
        this.logs = allLogs.slice(0, endIndex)
      } else {
        this.logs = [...this.logs, ...allLogs.slice(startIndex, endIndex)]
      }
      
      this.hasMore = endIndex < allLogs.length
      this.applyFilter()
    },
    
    /**
     * 应用过滤器
     */
    applyFilter() {
      if (this.currentFilter === 'all') {
        this.filteredLogs = this.logs
      } else {
        this.filteredLogs = this.logs.filter(log => log.level === this.currentFilter)
      }
    },
    
    /**
     * 设置过滤器
     */
    setFilter(filter) {
      this.currentFilter = filter
      this.applyFilter()
    },
    
    /**
     * 加载更多
     */
    loadMore() {
      this.currentPage++
      this.loadLogs()
    },
    
    /**
     * 刷新数据
     */
    refreshData() {
      this.currentPage = 1
      this.logs = []
      this.loadData()
    },
    
    /**
     * 清空所有日志
     */
    clearAllLogs() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有错误日志吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            globalErrorHandler.clearErrorLogs()
            this.refreshData()
            uni.showToast({
              title: '已清空日志',
              icon: 'success'
            })
          }
        }
      })
    },
    
    /**
     * 显示日志详情
     */
    showLogDetail(log) {
      this.selectedLog = log
      this.showDetailModal = true
    },
    
    /**
     * 关闭详情弹窗
     */
    closeDetailModal() {
      this.showDetailModal = false
      this.selectedLog = {}
    },
    
    /**
     * 获取告警类型文本
     */
    getAlertTypeText(type) {
      const typeMap = {
        'high_error_rate': '高错误率',
        'network_instability': '网络不稳定',
        'ocr_failure_pattern': 'OCR失败',
        'too_many_critical_errors': '严重错误过多',
        'critical_error': '严重错误'
      }
      return typeMap[type] || type
    },
    
    /**
     * 获取告警样式类
     */
    getAlertClass(type) {
      if (type === 'too_many_critical_errors' || type === 'critical_error') {
        return 'alert-critical'
      } else if (type === 'high_error_rate') {
        return 'alert-error'
      } else {
        return 'alert-warning'
      }
    },
    
    /**
     * 获取级别文本
     */
    getLevelText(level) {
      const levelMap = {
        'critical': '严重',
        'error': '错误',
        'warning': '警告',
        'info': '信息'
      }
      return levelMap[level] || level
    },
    
    /**
     * 获取级别样式类
     */
    getLevelClass(level) {
      return `level-${level}`
    },
    
    /**
     * 获取级别百分比
     */
    getLevelPercentage(count) {
      const total = this.errorStats.total
      return total > 0 ? (count / total * 100) : 0
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60 * 1000) {
        return '刚刚'
      } else if (diff < 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 1000))}分钟前`
      } else if (diff < 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
      } else {
        return `${date.getMonth() + 1}/${date.getDate()}`
      }
    },
    
    /**
     * 格式化完整时间
     */
    formatFullTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleString()
    },
    
    /**
     * 格式化设备信息
     */
    formatDeviceInfo(deviceInfo) {
      if (!deviceInfo) return '无'
      return `${deviceInfo.brand} ${deviceInfo.model} (${deviceInfo.system})`
    }
  }
}
</script>

<style scoped>
.error-management {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.actions {
  display: flex;
  gap: 20rpx;
}

.refresh-btn, .clear-btn {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.stats-section {
  margin-bottom: 30rpx;
}

.stats-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.alerts-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.alert-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.alert-time {
  font-size: 24rpx;
  color: #999;
}

.alert-type {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  color: white;
}

.alert-critical {
  background-color: #ff4757;
}

.alert-error {
  background-color: #ff6b6b;
}

.alert-warning {
  background-color: #ffa502;
}

.level-section {
  margin-bottom: 30rpx;
}

.level-chart {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.level-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.level-bar:last-child {
  margin-bottom: 0;
}

.level-name {
  width: 120rpx;
  font-size: 26rpx;
  color: #333;
}

.level-progress {
  flex: 1;
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.level-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.level-critical {
  background-color: #ff4757;
}

.level-error {
  background-color: #ff6b6b;
}

.level-warning {
  background-color: #ffa502;
}

.level-info {
  background-color: #3742fa;
}

.level-count {
  width: 60rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
}

.logs-section {
  margin-bottom: 30rpx;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
}

.filter-tab {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
}

.filter-tab.active {
  color: #007AFF;
  background-color: #e6f3ff;
}

.log-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.log-item {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.log-level {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  color: white;
}

.log-time {
  font-size: 24rpx;
  color: #999;
}

.log-message {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.log-context {
  font-size: 24rpx;
  color: #666;
}

.load-more {
  text-align: center;
  padding: 30rpx;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16rpx;
  width: 90%;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-item {
  margin-bottom: 30rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  display: block;
}

.detail-value.stack {
  font-family: monospace;
  font-size: 24rpx;
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>