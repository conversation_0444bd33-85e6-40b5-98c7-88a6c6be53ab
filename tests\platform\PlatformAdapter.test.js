/**
 * PlatformAdapter类单元测试
 */

import { PlatformAdapterClass } from '../../utils/platform/PlatformAdapter.js'
import { PLATFORM_TYPES, ERROR_CODES } from '../../utils/platform/constants.js'

// Mock uni对象
global.uni = {
  chooseImage: jest.fn(),
  setStorage: jest.fn(),
  getStorage: jest.fn(),
  removeStorage: jest.fn(),
  clearStorage: jest.fn(),
  saveImageToPhotosAlbum: jest.fn(),
  getFileInfo: jest.fn(),
  share: jest.fn(),
  showToast: jest.fn()
}

// Mock navigator对象
global.navigator = {
  share: jest.fn(),
  clipboard: {
    writeText: jest.fn()
  }
}

// Mock getApp
global.getApp = jest.fn(() => ({
  globalData: {}
}))

describe('PlatformAdapter类测试', () => {
  let adapter

  beforeEach(() => {
    adapter = new PlatformAdapterClass()
    // 清除所有mock
    Object.values(uni).forEach(mockFn => {
      if (typeof mockFn === 'function' && mockFn.mockClear) {
        mockFn.mockClear()
      }
    })
  })

  describe('构造函数', () => {
    test('应该正确初始化', () => {
      expect(adapter.platform).toBeDefined()
      expect(adapter.config).toBeDefined()
      expect(typeof adapter.platform).toBe('string')
      expect(typeof adapter.config).toBe('object')
    })
  })

  describe('相机相关方法', () => {
    describe('chooseImage', () => {
      test('成功选择图片', async () => {
        const mockResult = {
          tempFilePaths: ['/temp/image1.jpg'],
          tempFiles: [{ path: '/temp/image1.jpg', size: 1024 }]
        }

        uni.chooseImage.mockImplementation(({ success }) => {
          success(mockResult)
        })

        const result = await adapter.chooseImage()
        
        expect(result.success).toBe(true)
        expect(result.tempFilePaths).toEqual(mockResult.tempFilePaths)
        expect(result.tempFiles).toEqual(mockResult.tempFiles)
      })

      test('选择图片失败', async () => {
        const mockError = new Error('用户取消选择')
        
        uni.chooseImage.mockImplementation(({ fail }) => {
          fail(mockError)
        })

        await expect(adapter.chooseImage()).rejects.toMatchObject({
          success: false,
          error: mockError,
          code: ERROR_CODES.FEATURE_NOT_AVAILABLE
        })
      })

      test('应该根据平台调整选项', async () => {
        uni.chooseImage.mockImplementation(({ success }) => {
          success({ tempFilePaths: [], tempFiles: [] })
        })

        await adapter.chooseImage({ count: 5 })
        
        expect(uni.chooseImage).toHaveBeenCalledWith(
          expect.objectContaining({
            count: expect.any(Number),
            sizeType: expect.any(Array),
            sourceType: expect.any(Array)
          })
        )
      })
    })

    describe('takePhoto', () => {
      test('H5平台应该抛出错误', async () => {
        adapter.platform = PLATFORM_TYPES.H5
        
        await expect(adapter.takePhoto()).rejects.toThrow('H5平台不支持直接拍照功能')
      })

      test('非H5平台应该调用chooseImage', async () => {
        adapter.platform = PLATFORM_TYPES.APP_PLUS
        
        uni.chooseImage.mockImplementation(({ success }) => {
          success({ tempFilePaths: [], tempFiles: [] })
        })

        await adapter.takePhoto()
        
        expect(uni.chooseImage).toHaveBeenCalledWith(
          expect.objectContaining({
            sourceType: ['camera']
          })
        )
      })
    })
  })

  describe('存储相关方法', () => {
    describe('setStorage', () => {
      test('成功设置存储', async () => {
        uni.setStorage.mockImplementation(({ success }) => {
          success()
        })

        const result = await adapter.setStorage('test_key', 'test_value')
        
        expect(result).toBe(true)
        expect(uni.setStorage).toHaveBeenCalledWith(
          expect.objectContaining({
            key: expect.stringContaining('test_key'),
            data: 'test_value'
          })
        )
      })

      test('设置存储失败', async () => {
        uni.setStorage.mockImplementation(({ fail }) => {
          fail(new Error('存储失败'))
        })

        const result = await adapter.setStorage('test_key', 'test_value')
        
        expect(result).toBe(false)
      })
    })

    describe('getStorage', () => {
      test('成功获取存储', async () => {
        const testData = { name: 'test' }
        
        uni.getStorage.mockImplementation(({ success }) => {
          success({ data: testData })
        })

        const result = await adapter.getStorage('test_key')
        
        expect(result).toEqual(testData)
      })

      test('获取存储失败', async () => {
        uni.getStorage.mockImplementation(({ fail }) => {
          fail(new Error('获取失败'))
        })

        const result = await adapter.getStorage('test_key')
        
        expect(result).toBeNull()
      })
    })

    describe('removeStorage', () => {
      test('成功删除存储', async () => {
        uni.removeStorage.mockImplementation(({ success }) => {
          success()
        })

        const result = await adapter.removeStorage('test_key')
        
        expect(result).toBe(true)
      })
    })

    describe('clearStorage', () => {
      test('成功清空存储', async () => {
        uni.clearStorage.mockImplementation(({ success }) => {
          success()
        })

        const result = await adapter.clearStorage()
        
        expect(result).toBe(true)
      })
    })
  })

  describe('分享相关方法', () => {
    test('H5平台使用Web Share API', async () => {
      adapter.platform = PLATFORM_TYPES.H5
      navigator.share.mockResolvedValue()

      const result = await adapter.share({
        title: '测试标题',
        content: '测试内容',
        url: 'https://example.com'
      })

      expect(result.success).toBe(true)
      expect(navigator.share).toHaveBeenCalledWith({
        title: '测试标题',
        text: '测试内容',
        url: 'https://example.com'
      })
    })

    test('H5平台降级到剪贴板', async () => {
      adapter.platform = PLATFORM_TYPES.H5
      navigator.share = undefined
      navigator.clipboard.writeText.mockResolvedValue()

      const result = await adapter.share({
        content: '测试内容'
      })

      expect(result.success).toBe(true)
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('测试内容')
    })

    test('微信小程序分享', async () => {
      adapter.platform = PLATFORM_TYPES.MP_WEIXIN
      
      const result = await adapter.share({
        title: '测试标题'
      })

      expect(result.success).toBe(true)
    })

    test('APP平台分享', async () => {
      adapter.platform = PLATFORM_TYPES.APP_PLUS
      
      uni.share.mockImplementation(({ success }) => {
        success()
      })

      const result = await adapter.share({
        title: '测试标题'
      })

      expect(result.success).toBe(true)
    })
  })

  describe('文件相关方法', () => {
    describe('saveImageToPhotosAlbum', () => {
      test('H5平台应该抛出错误', async () => {
        adapter.platform = PLATFORM_TYPES.H5
        
        await expect(adapter.saveImageToPhotosAlbum('/path/to/image.jpg'))
          .rejects.toThrow('H5平台不支持保存图片到相册')
      })

      test('非H5平台应该成功保存', async () => {
        adapter.platform = PLATFORM_TYPES.APP_PLUS
        
        uni.saveImageToPhotosAlbum.mockImplementation(({ success }) => {
          success()
        })

        const result = await adapter.saveImageToPhotosAlbum('/path/to/image.jpg')
        
        expect(result).toBe(true)
      })
    })

    describe('getFileInfo', () => {
      test('应该返回文件信息', async () => {
        const mockFileInfo = {
          size: 1024,
          digest: 'abc123'
        }
        
        uni.getFileInfo.mockImplementation(({ success }) => {
          success(mockFileInfo)
        })

        const result = await adapter.getFileInfo('/path/to/file.jpg')
        
        expect(result).toEqual(mockFileInfo)
      })
    })
  })

  describe('权限相关方法', () => {
    test('非APP平台应该默认有权限', async () => {
      adapter.platform = PLATFORM_TYPES.H5
      
      const hasPermission = await adapter.checkPermission('camera')
      const requestResult = await adapter.requestPermission('camera')
      
      expect(hasPermission).toBe(true)
      expect(requestResult).toBe(true)
    })
  })
})