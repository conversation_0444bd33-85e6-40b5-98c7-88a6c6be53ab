<template>
  <view class="image-upload-ocr">
    <!-- 图片选择区域 -->
    <view class="upload-section">
      <view class="section-title">上传健康报告</view>
      
      <view v-if="!selectedImage" class="upload-area" @click="showActionSheet">
        <view class="upload-icon">📷</view>
        <text class="upload-text">点击选择图片或拍照</text>
        <text class="upload-hint">支持JPG、PNG格式，建议图片清晰完整</text>
      </view>

      <view v-else class="image-preview">
        <image 
          :src="selectedImage.processedPath" 
          class="preview-image"
          mode="aspectFit"
          @click="previewImage"
        />
        
        <view class="image-info">
          <view class="info-item">
            <text class="info-label">文件大小:</text>
            <text class="info-value">{{ formatFileSize(selectedImage.processedSize) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">图片尺寸:</text>
            <text class="info-value">{{ selectedImage.width }}×{{ selectedImage.height }}</text>
          </view>
          <view v-if="selectedImage.quality" class="info-item">
            <text class="info-label">图片质量:</text>
            <view class="quality-indicator">
              <text 
                class="quality-score" 
                :class="getQualityClass(selectedImage.quality.score)"
              >
                {{ selectedImage.quality.score }}分
              </text>
              <text 
                v-if="!selectedImage.quality.isValid" 
                class="quality-warning"
              >
                (质量较低)
              </text>
            </view>
          </view>
        </view>

        <view class="image-actions">
          <button class="action-btn secondary" @click="reSelectImage">重新选择</button>
          <button 
            class="action-btn primary" 
            @click="startOCR"
            :disabled="isProcessing"
          >
            {{ isProcessing ? '识别中...' : '开始识别' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 图片质量提示 -->
    <view 
      v-if="selectedImage && selectedImage.quality && !selectedImage.quality.isValid" 
      class="quality-warning-section"
    >
      <view class="warning-header">
        <text class="warning-icon">⚠️</text>
        <text class="warning-title">图片质量提醒</text>
      </view>
      
      <view class="warning-content">
        <view v-for="(issue, index) in selectedImage.quality.issues" :key="index" class="issue-item">
          <text class="issue-text">• {{ issue }}</text>
        </view>
        
        <view class="suggestions">
          <text class="suggestions-title">建议:</text>
          <view v-for="(suggestion, index) in selectedImage.quality.suggestions" :key="index" class="suggestion-item">
            <text class="suggestion-text">{{ index + 1 }}. {{ suggestion }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- OCR处理状态 -->
    <view v-if="isProcessing" class="processing-section">
      <view class="processing-content">
        <view class="loading-spinner"></view>
        <text class="processing-text">{{ processingText }}</text>
        <view class="processing-steps">
          <view 
            v-for="(step, index) in processingSteps" 
            :key="index"
            class="step-item"
            :class="{ 'active': currentStep >= index, 'completed': currentStep > index }"
          >
            <view class="step-indicator">{{ index + 1 }}</view>
            <text class="step-text">{{ step }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- OCR结果 -->
    <view v-if="ocrResult && !isProcessing" class="result-section">
      <OCRResultEditor 
        :ocr-result="ocrResult"
        @save="handleSaveResult"
      />
    </view>

    <!-- 错误处理 -->
    <view v-if="error && !isProcessing" class="error-section">
      <view class="error-content">
        <text class="error-icon">❌</text>
        <text class="error-title">识别失败</text>
        <text class="error-message">{{ error.message }}</text>
        
        <view v-if="error.fallback" class="fallback-options">
          <text class="fallback-title">您可以尝试:</text>
          <view v-for="(suggestion, index) in error.fallback.suggestions" :key="index" class="fallback-item">
            <text class="fallback-text">{{ index + 1 }}. {{ suggestion }}</text>
          </view>
          
          <view class="fallback-actions">
            <button class="action-btn secondary" @click="retryOCR">重试识别</button>
            <button class="action-btn primary" @click="manualInput">手动输入</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import imageService from '../../services/ocr/imageService.js'
import ocrService from '../../services/ocr/ocrService.js'
import OCRResultEditor from './OCRResultEditor.vue'

export default {
  name: 'ImageUploadOCR',
  components: {
    OCRResultEditor
  },
  data() {
    return {
      selectedImage: null,
      isProcessing: false,
      processingText: '',
      currentStep: 0,
      processingSteps: [
        '图片预处理',
        '上传到OCR服务',
        '文字识别中',
        '解析健康数据',
        '生成结构化结果'
      ],
      ocrResult: null,
      error: null
    }
  },
  methods: {
    /**
     * 显示选择图片的操作菜单
     */
    showActionSheet() {
      const itemList = ['拍照', '从相册选择']
      
      uni.showActionSheet({
        itemList,
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto()
          } else if (res.tapIndex === 1) {
            this.selectFromAlbum()
          }
        }
      })
    },

    /**
     * 拍照
     */
    async takePhoto() {
      try {
        uni.showLoading({ title: '启动相机...' })
        
        const result = await imageService.takePhoto()
        
        uni.hideLoading()
        
        if (result.success) {
          this.selectedImage = result.image
          this.clearResults()
        } else {
          this.showError('拍照失败', result.error)
        }
      } catch (error) {
        uni.hideLoading()
        this.showError('拍照失败', error.message)
      }
    },

    /**
     * 从相册选择
     */
    async selectFromAlbum() {
      try {
        uni.showLoading({ title: '选择图片...' })
        
        const result = await imageService.selectFromAlbum({ count: 1 })
        
        uni.hideLoading()
        
        if (result.success && result.images.length > 0) {
          this.selectedImage = result.images[0]
          this.clearResults()
        } else {
          this.showError('选择图片失败', result.error)
        }
      } catch (error) {
        uni.hideLoading()
        this.showError('选择图片失败', error.message)
      }
    },

    /**
     * 重新选择图片
     */
    reSelectImage() {
      this.selectedImage = null
      this.clearResults()
      this.showActionSheet()
    },

    /**
     * 预览图片
     */
    previewImage() {
      if (this.selectedImage) {
        uni.previewImage({
          urls: [this.selectedImage.processedPath],
          current: 0
        })
      }
    },

    /**
     * 开始OCR识别
     */
    async startOCR() {
      if (!this.selectedImage) {
        uni.showToast({
          title: '请先选择图片',
          icon: 'none'
        })
        return
      }

      this.isProcessing = true
      this.currentStep = 0
      this.error = null
      this.ocrResult = null

      try {
        // 步骤1: 图片预处理
        this.updateProcessingStatus(0, '正在预处理图片...')
        await this.delay(500)

        // 步骤2: 上传到OCR服务
        this.updateProcessingStatus(1, '正在上传图片...')
        await this.delay(800)

        // 步骤3: 文字识别
        this.updateProcessingStatus(2, '正在识别文字...')
        await this.delay(1000)

        // 步骤4: 解析健康数据
        this.updateProcessingStatus(3, '正在解析健康数据...')
        
        const result = await ocrService.recognizeHealthReport(
          this.selectedImage.processedPath,
          { accurate: true }
        )

        // 步骤5: 生成结构化结果
        this.updateProcessingStatus(4, '正在生成结果...')
        await this.delay(500)

        if (result.success) {
          this.ocrResult = result
          uni.showToast({
            title: '识别完成',
            icon: 'success'
          })
        } else {
          this.error = {
            message: result.error,
            fallback: result.fallback
          }
        }
      } catch (error) {
        console.error('OCR识别失败:', error)
        this.error = {
          message: error.message || 'OCR识别过程中发生错误',
          fallback: {
            suggestions: [
              '检查网络连接是否正常',
              '确保图片清晰完整',
              '尝试重新拍摄或选择其他图片',
              '也可以选择手动输入数据'
            ]
          }
        }
      } finally {
        this.isProcessing = false
      }
    },

    /**
     * 重试OCR识别
     */
    retryOCR() {
      this.startOCR()
    },

    /**
     * 手动输入
     */
    manualInput() {
      // 创建空的OCR结果供手动编辑
      this.ocrResult = {
        success: true,
        data: {
          items: [],
          date: '',
          hospital: '',
          doctor: '',
          rawText: ''
        },
        confidence: 0
      }
      this.error = null
    },

    /**
     * 处理保存结果
     */
    handleSaveResult(resultData) {
      // 发送保存事件给父组件
      this.$emit('save', {
        imageData: this.selectedImage,
        ocrData: resultData.data,
        confidence: resultData.confidence,
        isValid: resultData.isValid
      })

      // 显示成功提示
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 重置状态
      this.resetComponent()
    },

    /**
     * 更新处理状态
     */
    updateProcessingStatus(step, text) {
      this.currentStep = step
      this.processingText = text
    },

    /**
     * 清除结果
     */
    clearResults() {
      this.ocrResult = null
      this.error = null
    },

    /**
     * 重置组件
     */
    resetComponent() {
      this.selectedImage = null
      this.isProcessing = false
      this.currentStep = 0
      this.processingText = ''
      this.ocrResult = null
      this.error = null
    },

    /**
     * 显示错误
     */
    showError(title, message) {
      uni.showModal({
        title,
        content: message,
        showCancel: false
      })
    },

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    /**
     * 获取质量等级样式类
     */
    getQualityClass(score) {
      if (score >= 80) return 'quality-good'
      if (score >= 60) return 'quality-medium'
      return 'quality-poor'
    },

    /**
     * 延迟函数
     */
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.image-upload-ocr {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.upload-section {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300rpx;
  border: 4rpx dashed #ddd;
  border-radius: 12rpx;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:active {
  background-color: #f0f0f0;
  border-color: #007aff;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.upload-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

.image-preview {
  .preview-image {
    width: 100%;
    max-height: 400rpx;
    border-radius: 8rpx;
    margin-bottom: 16rpx;
  }
}

.image-info {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  color: #333;
}

.quality-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.quality-score {
  font-size: 26rpx;
  font-weight: bold;
  
  &.quality-good {
    color: #4caf50;
  }
  
  &.quality-medium {
    color: #ff9800;
  }
  
  &.quality-poor {
    color: #f44336;
  }
}

.quality-warning {
  font-size: 22rpx;
  color: #f44336;
}

.image-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  
  &.secondary {
    background-color: #6c757d;
    color: white;
  }
  
  &.primary {
    background-color: #007aff;
    color: white;
    
    &:disabled {
      background-color: #ccc;
      color: #999;
    }
  }
}

.quality-warning-section {
  background: #fff3cd;
  border: 2rpx solid #ffc107;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.warning-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.warning-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.warning-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #856404;
}

.warning-content {
  .issue-item {
    margin-bottom: 8rpx;
  }
  
  .issue-text {
    font-size: 26rpx;
    color: #856404;
    line-height: 1.5;
  }
}

.suggestions {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #ffc107;
}

.suggestions-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
  margin-bottom: 8rpx;
}

.suggestion-item {
  margin-bottom: 6rpx;
}

.suggestion-text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.5;
}

.processing-section {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx 24rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.processing-content {
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20rpx;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 32rpx;
}

.processing-steps {
  .step-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    opacity: 0.3;
    transition: opacity 0.3s ease;
    
    &.active {
      opacity: 1;
    }
    
    &.completed {
      opacity: 0.6;
    }
  }
}

.step-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  margin-right: 16rpx;
  
  .step-item.active & {
    background-color: #007aff;
    color: white;
  }
  
  .step-item.completed & {
    background-color: #4caf50;
    color: white;
  }
}

.step-text {
  font-size: 26rpx;
  color: #666;
  
  .step-item.active & {
    color: #333;
    font-weight: bold;
  }
}

.error-section {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx 24rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.error-content {
  .error-icon {
    font-size: 80rpx;
    margin-bottom: 16rpx;
  }
  
  .error-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #f44336;
    margin-bottom: 12rpx;
  }
  
  .error-message {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 24rpx;
    line-height: 1.5;
  }
}

.fallback-options {
  text-align: left;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
}

.fallback-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.fallback-item {
  margin-bottom: 8rpx;
}

.fallback-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.fallback-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
}
</style>