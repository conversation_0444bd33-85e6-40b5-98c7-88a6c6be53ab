/**
 * 性能优化工具集 - 增强版
 */

// 导入所有性能优化模块
import { LazyImageLoader, VirtualScrollManager, lazyLoadMixin, virtualScrollMixin } from './lazyLoad.js'
import DatabaseOptimizer, { databaseOptimizationMixin } from './database.js'
import { MultiLevelCache, cacheMixin, cached } from './cache.js'
import { BundleOptimizer, CodeSplitter, ResourceOptimizer, bundleOptimizationMixin } from './bundleOptimizer.js'
import MemoryManager, { memoryOptimizationMixin } from './memoryManager.js'
import { PerformanceBenchmark, AppPerformanceBenchmark, benchmarkMixin } from './benchmark.js'

// 页面加载性能监控 - 增强版
class PerformanceMonitor {
  constructor() {
    this.metrics = {}
    this.observers = []
    this.loadingStates = new Map()
    this.performanceEntries = []
    this.vitalsMetrics = new Map()
  }

  /**
   * 开始监控页面加载
   */
  startPageLoad(pageName) {
    const startTime = Date.now()
    this.metrics[pageName] = {
      startTime,
      loadTime: null,
      renderTime: null,
      interactiveTime: null,
      resources: []
    }
    
    console.log(`[性能监控] 开始加载页面: ${pageName}`)
    return startTime
  }

  /**
   * 结束页面加载监控
   */
  endPageLoad(pageName) {
    if (!this.metrics[pageName]) return
    
    const endTime = Date.now()
    const metric = this.metrics[pageName]
    metric.loadTime = endTime - metric.startTime
    
    console.log(`[性能监控] 页面加载完成: ${pageName}, 耗时: ${metric.loadTime}ms`)
    
    // 如果加载时间超过3秒，记录警告
    if (metric.loadTime > 3000) {
      console.warn(`[性能警告] 页面 ${pageName} 加载时间过长: ${metric.loadTime}ms`)
      this.reportSlowLoading(pageName, metric.loadTime)
    }
    
    return metric.loadTime
  }

  /**
   * 记录渲染完成时间
   */
  markRenderComplete(pageName) {
    if (!this.metrics[pageName]) return
    
    const renderTime = Date.now()
    this.metrics[pageName].renderTime = renderTime - this.metrics[pageName].startTime
    
    console.log(`[性能监控] 页面渲染完成: ${pageName}, 耗时: ${this.metrics[pageName].renderTime}ms`)
  }

  /**
   * 记录交互就绪时间
   */
  markInteractive(pageName) {
    if (!this.metrics[pageName]) return
    
    const interactiveTime = Date.now()
    this.metrics[pageName].interactiveTime = interactiveTime - this.metrics[pageName].startTime
    
    console.log(`[性能监控] 页面交互就绪: ${pageName}, 耗时: ${this.metrics[pageName].interactiveTime}ms`)
  }

  /**
   * 报告慢加载
   */
  reportSlowLoading(pageName, loadTime) {
    // 这里可以上报到性能监控服务
    const report = {
      type: 'slow_loading',
      pageName,
      loadTime,
      timestamp: Date.now(),
      userAgent: uni.getSystemInfoSync()
    }
    
    // 存储到本地，用于后续分析
    this.savePerformanceReport(report)
  }

  /**
   * 保存性能报告
   */
  savePerformanceReport(report) {
    try {
      let reports = uni.getStorageSync('performance_reports') || []
      reports.unshift(report)
      
      // 只保留最近50条记录
      if (reports.length > 50) {
        reports = reports.slice(0, 50)
      }
      
      uni.setStorageSync('performance_reports', reports)
    } catch (error) {
      console.error('保存性能报告失败:', error)
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReports() {
    try {
      return uni.getStorageSync('performance_reports') || []
    } catch (error) {
      console.error('获取性能报告失败:', error)
      return []
    }
  }

  /**
   * 获取页面性能指标
   */
  getPageMetrics(pageName) {
    return this.metrics[pageName] || null
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics() {
    return { ...this.metrics }
  }
}

// 图片懒加载管理器
class LazyLoadManager {
  constructor() {
    this.observers = new Map()
    this.loadedImages = new Set()
    this.loadingImages = new Set()
  }

  /**
   * 创建懒加载观察器
   */
  createObserver(callback) {
    // 由于uni-app环境限制，使用滚动监听模拟IntersectionObserver
    return {
      observe: (element) => {
        // 模拟观察器行为
        this.checkVisibility(element, callback)
      },
      unobserve: (element) => {
        // 停止观察
      },
      disconnect: () => {
        // 断开观察器
      }
    }
  }

  /**
   * 检查元素可见性
   */
  checkVisibility(element, callback) {
    // 简化的可见性检测
    uni.createSelectorQuery()
      .select(element)
      .boundingClientRect((rect) => {
        if (rect) {
          const windowHeight = uni.getSystemInfoSync().windowHeight
          const isVisible = rect.top < windowHeight && rect.bottom > 0
          
          if (isVisible) {
            callback([{ target: element, isIntersecting: true }])
          }
        }
      })
      .exec()
  }

  /**
   * 预加载图片
   */
  preloadImage(src) {
    return new Promise((resolve, reject) => {
      if (this.loadedImages.has(src)) {
        resolve(src)
        return
      }

      if (this.loadingImages.has(src)) {
        // 如果正在加载，等待加载完成
        const checkLoaded = () => {
          if (this.loadedImages.has(src)) {
            resolve(src)
          } else {
            setTimeout(checkLoaded, 100)
          }
        }
        checkLoaded()
        return
      }

      this.loadingImages.add(src)

      // 创建图片对象进行预加载
      const image = new Image()
      image.onload = () => {
        this.loadedImages.add(src)
        this.loadingImages.delete(src)
        resolve(src)
      }
      image.onerror = () => {
        this.loadingImages.delete(src)
        reject(new Error(`图片加载失败: ${src}`))
      }
      image.src = src
    })
  }

  /**
   * 批量预加载图片
   */
  async preloadImages(srcList, options = {}) {
    const { concurrency = 3, timeout = 10000 } = options
    const results = []
    
    // 分批加载，控制并发数
    for (let i = 0; i < srcList.length; i += concurrency) {
      const batch = srcList.slice(i, i + concurrency)
      const promises = batch.map(src => 
        Promise.race([
          this.preloadImage(src),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('加载超时')), timeout)
          )
        ]).catch(error => ({ error, src }))
      )
      
      const batchResults = await Promise.all(promises)
      results.push(...batchResults)
    }
    
    return results
  }
}

// 缓存管理器
class CacheManager {
  constructor() {
    this.memoryCache = new Map()
    this.maxMemorySize = 50 // 最大缓存50个对象
  }

  /**
   * 设置内存缓存
   */
  setMemoryCache(key, data, ttl = 300000) { // 默认5分钟过期
    // 如果缓存已满，删除最旧的项
    if (this.memoryCache.size >= this.maxMemorySize) {
      const firstKey = this.memoryCache.keys().next().value
      this.memoryCache.delete(firstKey)
    }

    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * 获取内存缓存
   */
  getMemoryCache(key) {
    const cached = this.memoryCache.get(key)
    if (!cached) return null

    // 检查是否过期
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.memoryCache.delete(key)
      return null
    }

    return cached.data
  }

  /**
   * 设置本地存储缓存
   */
  setStorageCache(key, data, ttl = 86400000) { // 默认24小时过期
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        ttl
      }
      uni.setStorageSync(`cache_${key}`, cacheData)
    } catch (error) {
      console.error('设置存储缓存失败:', error)
    }
  }

  /**
   * 获取本地存储缓存
   */
  getStorageCache(key) {
    try {
      const cached = uni.getStorageSync(`cache_${key}`)
      if (!cached) return null

      // 检查是否过期
      if (Date.now() - cached.timestamp > cached.ttl) {
        uni.removeStorageSync(`cache_${key}`)
        return null
      }

      return cached.data
    } catch (error) {
      console.error('获取存储缓存失败:', error)
      return null
    }
  }

  /**
   * 清除过期缓存
   */
  clearExpiredCache() {
    // 清除内存缓存中的过期项
    for (const [key, cached] of this.memoryCache.entries()) {
      if (Date.now() - cached.timestamp > cached.ttl) {
        this.memoryCache.delete(key)
      }
    }

    // 清除本地存储中的过期缓存
    try {
      const storageInfo = uni.getStorageInfoSync()
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'))
      
      cacheKeys.forEach(key => {
        try {
          const cached = uni.getStorageSync(key)
          if (cached && Date.now() - cached.timestamp > cached.ttl) {
            uni.removeStorageSync(key)
          }
        } catch (error) {
          // 如果读取失败，直接删除
          uni.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('清除过期缓存失败:', error)
    }
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.memoryCache.clear()
    
    try {
      const storageInfo = uni.getStorageInfoSync()
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'))
      cacheKeys.forEach(key => uni.removeStorageSync(key))
    } catch (error) {
      console.error('清除所有缓存失败:', error)
    }
  }
}

// 防抖函数
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

  /**
   * 记录核心Web指标
   */
  recordWebVitals(metric) {
    this.vitalsMetrics.set(metric.name, {
      value: metric.value,
      rating: this.getMetricRating(metric.name, metric.value),
      timestamp: Date.now()
    })
    
    console.log(`Web Vitals - ${metric.name}: ${metric.value} (${this.getMetricRating(metric.name, metric.value)})`)
  }

  /**
   * 获取指标评级
   */
  getMetricRating(name, value) {
    const thresholds = {
      'FCP': { good: 1800, poor: 3000 }, // First Contentful Paint
      'LCP': { good: 2500, poor: 4000 }, // Largest Contentful Paint
      'FID': { good: 100, poor: 300 },   // First Input Delay
      'CLS': { good: 0.1, poor: 0.25 },  // Cumulative Layout Shift
      'TTFB': { good: 800, poor: 1800 }  // Time to First Byte
    }
    
    const threshold = thresholds[name]
    if (!threshold) return 'unknown'
    
    if (value <= threshold.good) return 'good'
    if (value <= threshold.poor) return 'needs-improvement'
    return 'poor'
  }

  /**
   * 获取Web指标
   */
  getWebVitals() {
    return Object.fromEntries(this.vitalsMetrics)
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()
export const lazyLoadManager = new LazyLoadManager()
export const cacheManager = new CacheManager()
export const multiLevelCache = new MultiLevelCache()
export const bundleOptimizer = new BundleOptimizer()
export const memoryManager = new MemoryManager()
export const appBenchmark = new AppPerformanceBenchmark()

// 综合性能优化 Mixin
export const performanceMixin = {
  mixins: [
    lazyLoadMixin,
    cacheMixin,
    bundleOptimizationMixin,
    memoryOptimizationMixin,
    benchmarkMixin
  ],
  
  data() {
    return {
      pageLoadStart: 0,
      isPageLoaded: false,
      performanceOptimized: false
    }
  },
  
  onLoad() {
    // 开始监控页面加载
    this.pageLoadStart = performanceMonitor.startPageLoad(this.$options.name || 'Unknown')
    
    // 预加载依赖模块
    this.preloadPageDependencies()
  },
  
  onReady() {
    // 页面渲染完成
    performanceMonitor.markRenderComplete(this.$options.name || 'Unknown')
    
    // 延迟标记交互就绪，确保所有组件都已初始化
    this.$nextTick(() => {
      setTimeout(() => {
        performanceMonitor.markInteractive(this.$options.name || 'Unknown')
        this.isPageLoaded = true
        this.performanceOptimized = true
      }, 100)
    })
  },
  
  onShow() {
    // 结束页面加载监控
    if (this.pageLoadStart && !this.isPageLoaded) {
      performanceMonitor.endPageLoad(this.$options.name || 'Unknown')
    }
    
    // 清理过期缓存
    this.cleanupExpiredCache()
  },
  
  methods: {
    // 预加载页面依赖
    async preloadPageDependencies() {
      const pageName = this.$options.name || 'Unknown'
      const dependencies = this.getPageDependencies(pageName)
      
      if (dependencies.length > 0) {
        try {
          await this.preloadModules(dependencies, { priority: 'high' })
        } catch (error) {
          console.error('预加载页面依赖失败:', error)
        }
      }
    },
    
    // 获取页面依赖
    getPageDependencies(pageName) {
      const dependencyMap = {
        'ReportList': ['components/business/ReportCard', 'services/report'],
        'ReportDetail': ['components/business/ChartView', 'utils/charts'],
        'Analysis': ['services/analytics', 'utils/charts'],
        'Upload': ['components/business/ImageUpload', 'services/ocr']
      }
      
      return dependencyMap[pageName] || []
    },
    
    // 缓存数据 - 增强版
    async cacheData(key, data, options = {}) {
      // 使用多级缓存
      await this.multiLevelCache.set(key, data, options)
      
      // 同时使用旧的缓存管理器保持兼容性
      cacheManager.setMemoryCache(key, data, options.l1TTL)
    },
    
    // 获取缓存数据 - 增强版
    async getCachedData(key) {
      // 优先从多级缓存获取
      let data = await this.multiLevelCache.get(key)
      
      // 如果没有，尝试旧的缓存管理器
      if (data === null) {
        data = cacheManager.getMemoryCache(key)
      }
      
      return data
    },
    
    // 预加载图片 - 增强版
    async preloadImages(images, options = {}) {
      try {
        // 使用新的懒加载管理器
        const results = await this.preloadImageList(images, options)
        
        // 同时使用旧的管理器保持兼容性
        await lazyLoadManager.preloadImages(images, options)
        
        return results
      } catch (error) {
        console.error('预加载图片失败:', error)
        throw error
      }
    },
    
    // 清理过期缓存
    async cleanupExpiredCache() {
      try {
        // 清理多级缓存
        await this.cleanupCache()
        
        // 清理旧缓存管理器
        cacheManager.clearExpiredCache()
        
        // 清理内存
        this.performComponentCleanup()
        
      } catch (error) {
        console.error('清理缓存失败:', error)
      }
    },
    
    // 获取页面性能指标
    getPagePerformanceMetrics() {
      const pageName = this.$options.name || 'Unknown'
      return {
        pageMetrics: performanceMonitor.getPageMetrics(pageName),
        webVitals: performanceMonitor.getWebVitals(),
        memoryStats: this.getMemoryStats(),
        cacheStats: this.getCacheStats(),
        optimizationStats: this.getOptimizationStats()
      }
    },
    
    // 运行页面性能测试
    async runPagePerformanceTest() {
      try {
        const results = await this.runCoreTests()
        console.log('页面性能测试结果:', results)
        return results
      } catch (error) {
        console.error('页面性能测试失败:', error)
        throw error
      }
    }
  },
  
  beforeDestroy() {
    // 执行清理
    this.cleanupExpiredCache()
  }
}

// 导出所有性能优化工具
export default {
  // 核心工具
  performanceMonitor,
  lazyLoadManager,
  cacheManager,
  multiLevelCache,
  bundleOptimizer,
  memoryManager,
  appBenchmark,
  
  // 工具类
  LazyImageLoader,
  VirtualScrollManager,
  DatabaseOptimizer,
  MultiLevelCache,
  BundleOptimizer,
  CodeSplitter,
  ResourceOptimizer,
  MemoryManager,
  PerformanceBenchmark,
  AppPerformanceBenchmark,
  
  // 混入
  performanceMixin,
  lazyLoadMixin,
  virtualScrollMixin,
  cacheMixin,
  databaseOptimizationMixin,
  bundleOptimizationMixin,
  memoryOptimizationMixin,
  benchmarkMixin,
  
  // 装饰器
  cached,
  
  // 工具函数
  debounce,
  throttle
}