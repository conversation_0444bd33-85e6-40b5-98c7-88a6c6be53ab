/**
 * 微信小程序安全合规工具
 */

class WechatSecurityManager {
  constructor() {
    this.isWechatMiniProgram = false
    this.securityConfig = {
      // 微信小程序安全配置
      enableDataEncryption: true,
      enableUserInfoProtection: true,
      enableLocationProtection: true,
      enableCameraProtection: true,
      enableStorageProtection: true
    }
    
    this.init()
  }

  /**
   * 初始化微信小程序安全检查
   */
  init() {
    // #ifdef MP-WEIXIN
    this.isWechatMiniProgram = true
    console.log('微信小程序安全模块已启用')
    this.setupWechatSecurity()
    // #endif
  }

  /**
   * 设置微信小程序安全配置
   */
  setupWechatSecurity() {
    // #ifdef MP-WEIXIN
    try {
      // 设置网络请求安全
      this.setupNetworkSecurity()
      
      // 设置用户信息保护
      this.setupUserInfoProtection()
      
      // 设置存储安全
      this.setupStorageSecurity()
      
      // 设置权限管理
      this.setupPermissionManagement()
      
      console.log('微信小程序安全配置完成')
    } catch (error) {
      console.error('微信小程序安全配置失败:', error)
    }
    // #endif
  }

  /**
   * 设置网络请求安全
   */
  setupNetworkSecurity() {
    // #ifdef MP-WEIXIN
    // 确保所有网络请求使用HTTPS
    const originalRequest = uni.request
    uni.request = (options) => {
      // 检查URL是否使用HTTPS
      if (options.url && !options.url.startsWith('https://')) {
        console.warn('微信小程序要求使用HTTPS协议:', options.url)
        // 自动转换为HTTPS（如果可能）
        if (options.url.startsWith('http://')) {
          options.url = options.url.replace('http://', 'https://')
          console.log('已自动转换为HTTPS:', options.url)
        }
      }
      
      // 添加安全头部
      options.header = {
        ...options.header,
        'X-Requested-With': 'XMLHttpRequest',
        'X-Platform': 'wechat-miniprogram'
      }
      
      return originalRequest(options)
    }
    // #endif
  }

  /**
   * 设置用户信息保护
   */
  setupUserInfoProtection() {
    // #ifdef MP-WEIXIN
    // 重写用户信息获取方法，添加隐私保护
    const originalGetUserInfo = wx.getUserInfo
    if (originalGetUserInfo) {
      wx.getUserInfo = (options = {}) => {
        // 检查是否有用户授权
        wx.getSetting({
          success: (res) => {
            if (!res.authSetting['scope.userInfo']) {
              console.log('用户未授权获取用户信息')
              if (options.fail) {
                options.fail({ errMsg: '用户未授权' })
              }
              return
            }
            
            // 记录用户信息访问
            this.logUserInfoAccess()
            
            // 调用原始方法
            originalGetUserInfo({
              ...options,
              success: (userInfo) => {
                // 过滤敏感信息
                const filteredUserInfo = this.filterSensitiveUserInfo(userInfo)
                if (options.success) {
                  options.success(filteredUserInfo)
                }
              }
            })
          }
        })
      }
    }
    // #endif
  }

  /**
   * 设置存储安全
   */
  setupStorageSecurity() {
    // #ifdef MP-WEIXIN
    // 重写存储方法，添加数据加密
    const originalSetStorage = wx.setStorage
    const originalSetStorageSync = wx.setStorageSync
    
    if (originalSetStorage) {
      wx.setStorage = (options) => {
        if (this.isSensitiveData(options.key)) {
          // 加密敏感数据
          options.data = this.encryptData(options.data)
          console.log('敏感数据已加密存储:', options.key)
        }
        return originalSetStorage(options)
      }
    }
    
    if (originalSetStorageSync) {
      wx.setStorageSync = (key, data) => {
        if (this.isSensitiveData(key)) {
          // 加密敏感数据
          data = this.encryptData(data)
          console.log('敏感数据已加密存储:', key)
        }
        return originalSetStorageSync(key, data)
      }
    }
    
    // 重写读取方法，添加解密
    const originalGetStorage = wx.getStorage
    const originalGetStorageSync = wx.getStorageSync
    
    if (originalGetStorage) {
      wx.getStorage = (options) => {
        return originalGetStorage({
          ...options,
          success: (res) => {
            if (this.isSensitiveData(options.key)) {
              // 解密敏感数据
              res.data = this.decryptData(res.data)
            }
            if (options.success) {
              options.success(res)
            }
          }
        })
      }
    }
    
    if (originalGetStorageSync) {
      wx.getStorageSync = (key) => {
        const data = originalGetStorageSync(key)
        if (this.isSensitiveData(key) && data) {
          // 解密敏感数据
          return this.decryptData(data)
        }
        return data
      }
    }
    // #endif
  }

  /**
   * 设置权限管理
   */
  setupPermissionManagement() {
    // #ifdef MP-WEIXIN
    // 监控权限使用情况
    const permissions = [
      'scope.userInfo',
      'scope.userLocation',
      'scope.camera',
      'scope.album',
      'scope.writePhotosAlbum'
    ]
    
    permissions.forEach(permission => {
      this.monitorPermissionUsage(permission)
    })
    // #endif
  }

  /**
   * 监控权限使用
   */
  monitorPermissionUsage(permission) {
    // #ifdef MP-WEIXIN
    // 记录权限使用情况
    const usageLog = {
      permission,
      timestamp: Date.now(),
      granted: false
    }
    
    wx.getSetting({
      success: (res) => {
        usageLog.granted = !!res.authSetting[permission]
        this.logPermissionUsage(usageLog)
      }
    })
    // #endif
  }

  /**
   * 检查微信小程序合规性
   */
  async checkWechatCompliance() {
    // #ifdef MP-WEIXIN
    try {
      const complianceReport = {
        timestamp: Date.now(),
        platform: 'wechat-miniprogram',
        checks: []
      }
      
      // 检查网络请求合规性
      const networkCheck = await this.checkNetworkCompliance()
      complianceReport.checks.push(networkCheck)
      
      // 检查用户隐私保护
      const privacyCheck = await this.checkPrivacyCompliance()
      complianceReport.checks.push(privacyCheck)
      
      // 检查数据存储合规性
      const storageCheck = await this.checkStorageCompliance()
      complianceReport.checks.push(storageCheck)
      
      // 检查权限使用合规性
      const permissionCheck = await this.checkPermissionCompliance()
      complianceReport.checks.push(permissionCheck)
      
      // 检查内容合规性
      const contentCheck = await this.checkContentCompliance()
      complianceReport.checks.push(contentCheck)
      
      // 计算总体合规分数
      const totalScore = complianceReport.checks.reduce((sum, check) => sum + check.score, 0)
      complianceReport.overallScore = totalScore / complianceReport.checks.length
      complianceReport.isCompliant = complianceReport.overallScore >= 80
      
      // 保存合规报告
      wx.setStorageSync('wechat_compliance_report', complianceReport)
      
      return complianceReport
    } catch (error) {
      console.error('微信小程序合规性检查失败:', error)
      return null
    }
    // #endif
    
    // #ifndef MP-WEIXIN
    return {
      platform: 'not-wechat',
      message: '当前不是微信小程序环境'
    }
    // #endif
  }

  /**
   * 检查网络请求合规性
   */
  async checkNetworkCompliance() {
    const check = {
      name: '网络请求合规性',
      score: 100,
      issues: []
    }
    
    // 检查是否所有请求都使用HTTPS
    // 这里应该检查实际的网络请求记录
    
    return check
  }

  /**
   * 检查隐私保护合规性
   */
  async checkPrivacyCompliance() {
    const check = {
      name: '隐私保护合规性',
      score: 100,
      issues: []
    }
    
    // #ifdef MP-WEIXIN
    try {
      // 检查是否有隐私政策
      const hasPrivacyPolicy = wx.getStorageSync('privacy_policy_agreed')
      if (!hasPrivacyPolicy) {
        check.score -= 20
        check.issues.push('未找到用户同意的隐私政策')
      }
      
      // 检查用户信息收集是否合规
      const userInfoUsage = wx.getStorageSync('user_info_usage_log') || []
      if (userInfoUsage.length === 0) {
        check.score -= 10
        check.issues.push('未记录用户信息使用情况')
      }
      
    } catch (error) {
      check.score -= 30
      check.issues.push('隐私保护检查异常')
    }
    // #endif
    
    return check
  }

  /**
   * 检查存储合规性
   */
  async checkStorageCompliance() {
    const check = {
      name: '数据存储合规性',
      score: 100,
      issues: []
    }
    
    // #ifdef MP-WEIXIN
    try {
      // 检查存储数据大小
      wx.getStorageInfo({
        success: (res) => {
          if (res.currentSize > 8 * 1024) { // 超过8MB
            check.score -= 20
            check.issues.push('存储数据量过大，可能影响性能')
          }
        }
      })
      
      // 检查是否有敏感数据未加密
      const storageKeys = wx.getStorageInfoSync().keys
      const unencryptedSensitiveKeys = storageKeys.filter(key => 
        this.isSensitiveData(key) && !key.startsWith('encrypted_')
      )
      
      if (unencryptedSensitiveKeys.length > 0) {
        check.score -= 30
        check.issues.push('发现未加密的敏感数据')
      }
      
    } catch (error) {
      check.score -= 20
      check.issues.push('存储合规性检查异常')
    }
    // #endif
    
    return check
  }

  /**
   * 检查权限使用合规性
   */
  async checkPermissionCompliance() {
    const check = {
      name: '权限使用合规性',
      score: 100,
      issues: []
    }
    
    // #ifdef MP-WEIXIN
    try {
      // 检查权限使用记录
      const permissionLogs = wx.getStorageSync('permission_usage_logs') || []
      
      // 检查是否有未经授权的权限使用
      const unauthorizedUsage = permissionLogs.filter(log => !log.granted)
      if (unauthorizedUsage.length > 0) {
        check.score -= 40
        check.issues.push('发现未经授权的权限使用')
      }
      
      // 检查权限使用是否有明确说明
      const hasPermissionExplanation = wx.getStorageSync('permission_explanations')
      if (!hasPermissionExplanation) {
        check.score -= 20
        check.issues.push('缺少权限使用说明')
      }
      
    } catch (error) {
      check.score -= 20
      check.issues.push('权限合规性检查异常')
    }
    // #endif
    
    return check
  }

  /**
   * 检查内容合规性
   */
  async checkContentCompliance() {
    const check = {
      name: '内容合规性',
      score: 100,
      issues: []
    }
    
    // 检查是否有违规内容
    // 这里应该检查应用中的文本内容、图片等
    
    return check
  }

  /**
   * 获取微信小程序安全配置
   */
  getWechatSecurityConfig() {
    // #ifdef MP-WEIXIN
    return {
      ...this.securityConfig,
      platform: 'wechat-miniprogram',
      version: wx.getSystemInfoSync().SDKVersion,
      isCompliant: this.checkBasicCompliance()
    }
    // #endif
    
    // #ifndef MP-WEIXIN
    return {
      platform: 'not-wechat',
      message: '当前不是微信小程序环境'
    }
    // #endif
  }

  /**
   * 基础合规性检查
   */
  checkBasicCompliance() {
    // #ifdef MP-WEIXIN
    try {
      // 检查基本配置
      const hasPrivacyPolicy = !!wx.getStorageSync('privacy_policy_agreed')
      const hasUserConsent = !!wx.getStorageSync('user_consent')
      const hasSecureStorage = this.securityConfig.enableDataEncryption
      
      return hasPrivacyPolicy && hasUserConsent && hasSecureStorage
    } catch (error) {
      return false
    }
    // #endif
    
    return false
  }

  /**
   * 辅助方法：判断是否为敏感数据
   */
  isSensitiveData(key) {
    const sensitiveKeys = [
      'user_info',
      'health_reports',
      'health_indicators',
      'login_token',
      'personal_data',
      'medical_data'
    ]
    
    return sensitiveKeys.some(sensitiveKey => key.includes(sensitiveKey))
  }

  /**
   * 辅助方法：加密数据
   */
  encryptData(data) {
    try {
      // 简单的加密实现（实际项目中应使用更强的加密算法）
      const jsonString = JSON.stringify(data)
      const encrypted = btoa(encodeURIComponent(jsonString))
      return `encrypted_${encrypted}`
    } catch (error) {
      console.error('数据加密失败:', error)
      return data
    }
  }

  /**
   * 辅助方法：解密数据
   */
  decryptData(encryptedData) {
    try {
      if (typeof encryptedData === 'string' && encryptedData.startsWith('encrypted_')) {
        const encrypted = encryptedData.replace('encrypted_', '')
        const jsonString = decodeURIComponent(atob(encrypted))
        return JSON.parse(jsonString)
      }
      return encryptedData
    } catch (error) {
      console.error('数据解密失败:', error)
      return encryptedData
    }
  }

  /**
   * 辅助方法：过滤敏感用户信息
   */
  filterSensitiveUserInfo(userInfo) {
    // 移除或脱敏敏感信息
    const filtered = { ...userInfo }
    
    // 脱敏手机号
    if (filtered.phoneNumber) {
      filtered.phoneNumber = filtered.phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
    
    // 移除身份证号
    if (filtered.idCard) {
      delete filtered.idCard
    }
    
    return filtered
  }

  /**
   * 辅助方法：记录用户信息访问
   */
  logUserInfoAccess() {
    try {
      const log = {
        timestamp: Date.now(),
        action: 'getUserInfo',
        source: 'wechat-api'
      }
      
      let logs = wx.getStorageSync('user_info_usage_log') || []
      logs.unshift(log)
      
      // 只保留最近100条记录
      if (logs.length > 100) {
        logs = logs.slice(0, 100)
      }
      
      wx.setStorageSync('user_info_usage_log', logs)
    } catch (error) {
      console.error('记录用户信息访问失败:', error)
    }
  }

  /**
   * 辅助方法：记录权限使用
   */
  logPermissionUsage(usageLog) {
    try {
      let logs = wx.getStorageSync('permission_usage_logs') || []
      logs.unshift(usageLog)
      
      // 只保留最近200条记录
      if (logs.length > 200) {
        logs = logs.slice(0, 200)
      }
      
      wx.setStorageSync('permission_usage_logs', logs)
    } catch (error) {
      console.error('记录权限使用失败:', error)
    }
  }

  /**
   * 生成微信小程序安全报告
   */
  async generateSecurityReport() {
    // #ifdef MP-WEIXIN
    try {
      const report = {
        timestamp: Date.now(),
        platform: 'wechat-miniprogram',
        systemInfo: wx.getSystemInfoSync(),
        securityConfig: this.securityConfig,
        complianceReport: await this.checkWechatCompliance(),
        userInfoLogs: wx.getStorageSync('user_info_usage_log') || [],
        permissionLogs: wx.getStorageSync('permission_usage_logs') || [],
        storageInfo: wx.getStorageInfoSync(),
        recommendations: this.generateSecurityRecommendations()
      }
      
      return report
    } catch (error) {
      console.error('生成安全报告失败:', error)
      return null
    }
    // #endif
    
    return null
  }

  /**
   * 生成安全建议
   */
  generateSecurityRecommendations() {
    const recommendations = []
    
    // #ifdef MP-WEIXIN
    try {
      // 检查隐私政策
      if (!wx.getStorageSync('privacy_policy_agreed')) {
        recommendations.push('建议添加隐私政策并获得用户同意')
      }
      
      // 检查数据加密
      if (!this.securityConfig.enableDataEncryption) {
        recommendations.push('建议启用数据加密功能')
      }
      
      // 检查权限使用
      const permissionLogs = wx.getStorageSync('permission_usage_logs') || []
      const unauthorizedUsage = permissionLogs.filter(log => !log.granted)
      if (unauthorizedUsage.length > 0) {
        recommendations.push('建议优化权限申请流程，确保用户授权')
      }
      
      // 检查存储使用
      const storageInfo = wx.getStorageInfoSync()
      if (storageInfo.currentSize > 6 * 1024) { // 超过6MB
        recommendations.push('建议优化数据存储，清理不必要的缓存')
      }
      
    } catch (error) {
      recommendations.push('安全检查过程中出现异常，建议进行全面安全审查')
    }
    // #endif
    
    return recommendations
  }
}

// 创建全局微信小程序安全管理器实例
const wechatSecurityManager = new WechatSecurityManager()

export default wechatSecurityManager
export { WechatSecurityManager }