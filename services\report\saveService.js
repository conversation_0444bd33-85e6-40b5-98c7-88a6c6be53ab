/**
 * 报告保存服务
 * 处理报告数据的保存、更新和删除操作
 */

const ReportValidator = require('@/utils/validation/reportValidator.js');

class ReportSaveService {
  constructor() {
    this.validator = new ReportValidator();
    this.storageKey = 'health_reports';
    this.indexKey = 'health_reports_index';
  }

  /**
   * 保存报告
   * @param {Object} reportData - 报告数据
   * @param {Object} options - 保存选项
   * @returns {Promise<Object>} 保存结果
   */
  async saveReport(reportData, options = {}) {
    try {
      // 数据验证
      const validationResult = this.validator.validateReport(reportData);
      
      if (!validationResult.isValid && !options.skipValidation) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_FAILED',
            message: '数据验证失败',
            details: validationResult.errors
          },
          validation: validationResult
        };
      }

      // 数据预处理
      const processedData = await this.preprocessReportData(reportData);
      
      // 生成或使用现有ID
      const reportId = processedData.id || this.generateReportId();
      processedData.id = reportId;
      
      // 设置时间戳
      const now = new Date().toISOString();
      if (!processedData.createdAt) {
        processedData.createdAt = now;
      }
      processedData.updatedAt = now;
      
      // 保存到存储
      const saveResult = await this.saveToStorage(processedData);
      
      if (saveResult.success) {
        // 更新索引
        await this.updateIndex(processedData);
        
        return {
          success: true,
          data: {
            id: reportId,
            ...processedData
          },
          validation: validationResult,
          message: processedData.createdAt === now ? '报告保存成功' : '报告更新成功'
        };
      } else {
        return saveResult;
      }
      
    } catch (error) {
      console.error('保存报告失败:', error);
      return {
        success: false,
        error: {
          code: 'SAVE_FAILED',
          message: '保存失败',
          details: error.message
        }
      };
    }
  }

  /**
   * 数据预处理
   * @param {Object} reportData - 原始报告数据
   * @returns {Promise<Object>} 处理后的数据
   */
  async preprocessReportData(reportData) {
    const processed = { ...reportData };

    // 清理空白字符
    if (processed.hospital) processed.hospital = processed.hospital.trim();
    if (processed.doctor) processed.doctor = processed.doctor.trim();
    if (processed.title) processed.title = processed.title.trim();
    if (processed.notes) processed.notes = processed.notes.trim();

    // 处理检查项目
    if (processed.items && Array.isArray(processed.items)) {
      processed.items = processed.items.map(item => {
        const processedItem = { ...item };
        
        // 清理字符串字段
        if (processedItem.name) processedItem.name = processedItem.name.trim();
        if (processedItem.unit) processedItem.unit = processedItem.unit.trim();
        if (processedItem.referenceRange) processedItem.referenceRange = processedItem.referenceRange.trim();
        if (processedItem.category) processedItem.category = processedItem.category.trim();
        
        // 标准化数值
        if (processedItem.value) {
          const numValue = parseFloat(processedItem.value);
          if (!isNaN(numValue)) {
            processedItem.value = numValue.toString();
          }
        }
        
        // 重新计算异常状态
        processedItem.isAbnormal = this.validator.isValueAbnormal(
          processedItem.value, 
          processedItem.referenceRange
        );
        
        return processedItem;
      });
    }

    // 生成摘要信息
    processed.summary = this.generateSummary(processed);
    
    return processed;
  }

  /**
   * 生成报告摘要
   * @param {Object} reportData - 报告数据
   * @returns {Object} 摘要信息
   */
  generateSummary(reportData) {
    const items = reportData.items || [];
    const abnormalItems = items.filter(item => item.isAbnormal);
    
    return {
      totalItems: items.length,
      abnormalItems: abnormalItems.length,
      normalItems: items.length - abnormalItems.length,
      categories: [...new Set(items.map(item => item.category).filter(Boolean))],
      hasAbnormal: abnormalItems.length > 0,
      abnormalItemNames: abnormalItems.map(item => item.name)
    };
  }

  /**
   * 保存到存储
   * @param {Object} reportData - 报告数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveToStorage(reportData) {
    try {
      // 获取现有报告列表
      const existingReports = await this.getReportsFromStorage();
      
      // 查找是否已存在
      const existingIndex = existingReports.findIndex(report => report.id === reportData.id);
      
      if (existingIndex >= 0) {
        // 更新现有报告
        existingReports[existingIndex] = reportData;
      } else {
        // 添加新报告
        existingReports.push(reportData);
      }
      
      // 保存到存储
      await this.setStorageData(this.storageKey, existingReports);
      
      return {
        success: true,
        isUpdate: existingIndex >= 0
      };
      
    } catch (error) {
      console.error('存储保存失败:', error);
      return {
        success: false,
        error: {
          code: 'STORAGE_FAILED',
          message: '存储保存失败',
          details: error.message
        }
      };
    }
  }

  /**
   * 从存储获取报告列表
   * @returns {Promise<Array>} 报告列表
   */
  async getReportsFromStorage() {
    try {
      const data = await this.getStorageData(this.storageKey);
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('获取存储数据失败:', error);
      return [];
    }
  }

  /**
   * 更新索引
   * @param {Object} reportData - 报告数据
   * @returns {Promise<void>}
   */
  async updateIndex(reportData) {
    try {
      const index = await this.getStorageData(this.indexKey) || {};
      
      // 按日期索引
      const dateKey = reportData.checkDate;
      if (dateKey) {
        if (!index.byDate) index.byDate = {};
        if (!index.byDate[dateKey]) index.byDate[dateKey] = [];
        
        const existingIndex = index.byDate[dateKey].findIndex(id => id === reportData.id);
        if (existingIndex === -1) {
          index.byDate[dateKey].push(reportData.id);
        }
      }
      
      // 按医院索引
      const hospitalKey = reportData.hospital;
      if (hospitalKey) {
        if (!index.byHospital) index.byHospital = {};
        if (!index.byHospital[hospitalKey]) index.byHospital[hospitalKey] = [];
        
        const existingIndex = index.byHospital[hospitalKey].findIndex(id => id === reportData.id);
        if (existingIndex === -1) {
          index.byHospital[hospitalKey].push(reportData.id);
        }
      }
      
      // 按分类索引
      if (reportData.summary && reportData.summary.categories) {
        if (!index.byCategory) index.byCategory = {};
        
        reportData.summary.categories.forEach(category => {
          if (!index.byCategory[category]) index.byCategory[category] = [];
          
          const existingIndex = index.byCategory[category].findIndex(id => id === reportData.id);
          if (existingIndex === -1) {
            index.byCategory[category].push(reportData.id);
          }
        });
      }
      
      // 更新最后修改时间
      index.lastUpdated = new Date().toISOString();
      
      await this.setStorageData(this.indexKey, index);
      
    } catch (error) {
      console.error('更新索引失败:', error);
      // 索引更新失败不影响主要功能
    }
  }

  /**
   * 删除报告
   * @param {string} reportId - 报告ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteReport(reportId) {
    try {
      const existingReports = await this.getReportsFromStorage();
      const reportIndex = existingReports.findIndex(report => report.id === reportId);
      
      if (reportIndex === -1) {
        return {
          success: false,
          error: {
            code: 'REPORT_NOT_FOUND',
            message: '报告不存在'
          }
        };
      }
      
      const deletedReport = existingReports[reportIndex];
      existingReports.splice(reportIndex, 1);
      
      // 保存更新后的列表
      await this.setStorageData(this.storageKey, existingReports);
      
      // 更新索引（移除相关索引）
      await this.removeFromIndex(reportId, deletedReport);
      
      return {
        success: true,
        message: '报告删除成功',
        deletedReport: deletedReport
      };
      
    } catch (error) {
      console.error('删除报告失败:', error);
      return {
        success: false,
        error: {
          code: 'DELETE_FAILED',
          message: '删除失败',
          details: error.message
        }
      };
    }
  }

  /**
   * 从索引中移除报告
   * @param {string} reportId - 报告ID
   * @param {Object} reportData - 报告数据
   * @returns {Promise<void>}
   */
  async removeFromIndex(reportId, reportData) {
    try {
      const index = await this.getStorageData(this.indexKey) || {};
      
      // 从日期索引中移除
      if (index.byDate && reportData.checkDate) {
        const dateArray = index.byDate[reportData.checkDate];
        if (dateArray) {
          const idIndex = dateArray.indexOf(reportId);
          if (idIndex >= 0) {
            dateArray.splice(idIndex, 1);
            if (dateArray.length === 0) {
              delete index.byDate[reportData.checkDate];
            }
          }
        }
      }
      
      // 从医院索引中移除
      if (index.byHospital && reportData.hospital) {
        const hospitalArray = index.byHospital[reportData.hospital];
        if (hospitalArray) {
          const idIndex = hospitalArray.indexOf(reportId);
          if (idIndex >= 0) {
            hospitalArray.splice(idIndex, 1);
            if (hospitalArray.length === 0) {
              delete index.byHospital[reportData.hospital];
            }
          }
        }
      }
      
      // 从分类索引中移除
      if (index.byCategory && reportData.summary && reportData.summary.categories) {
        reportData.summary.categories.forEach(category => {
          const categoryArray = index.byCategory[category];
          if (categoryArray) {
            const idIndex = categoryArray.indexOf(reportId);
            if (idIndex >= 0) {
              categoryArray.splice(idIndex, 1);
              if (categoryArray.length === 0) {
                delete index.byCategory[category];
              }
            }
          }
        });
      }
      
      index.lastUpdated = new Date().toISOString();
      await this.setStorageData(this.indexKey, index);
      
    } catch (error) {
      console.error('从索引中移除报告失败:', error);
    }
  }

  /**
   * 生成报告ID
   * @returns {string} 报告ID
   */
  generateReportId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `report_${timestamp}_${random}`;
  }

  /**
   * 获取存储数据
   * @param {string} key - 存储键
   * @returns {Promise<any>} 存储数据
   */
  async getStorageData(key) {
    return new Promise((resolve, reject) => {
      try {
        // #ifdef H5
        const data = localStorage.getItem(key);
        resolve(data ? JSON.parse(data) : null);
        // #endif
        
        // #ifdef APP-PLUS
        plus.storage.getItem(key, (data) => {
          resolve(data ? JSON.parse(data) : null);
        });
        // #endif
        
        // #ifdef MP
        uni.getStorage({
          key: key,
          success: (res) => {
            resolve(res.data);
          },
          fail: () => {
            resolve(null);
          }
        });
        // #endif
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 设置存储数据
   * @param {string} key - 存储键
   * @param {any} data - 存储数据
   * @returns {Promise<void>}
   */
  async setStorageData(key, data) {
    return new Promise((resolve, reject) => {
      try {
        // #ifdef H5
        localStorage.setItem(key, JSON.stringify(data));
        resolve();
        // #endif
        
        // #ifdef APP-PLUS
        plus.storage.setItem(key, JSON.stringify(data));
        resolve();
        // #endif
        
        // #ifdef MP
        uni.setStorage({
          key: key,
          data: data,
          success: () => {
            resolve();
          },
          fail: (error) => {
            reject(error);
          }
        });
        // #endif
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 批量保存报告
   * @param {Array} reports - 报告数组
   * @param {Object} options - 保存选项
   * @returns {Promise<Object>} 批量保存结果
   */
  async batchSaveReports(reports, options = {}) {
    const results = {
      success: true,
      total: reports.length,
      saved: 0,
      failed: 0,
      errors: []
    };

    for (let i = 0; i < reports.length; i++) {
      try {
        const result = await this.saveReport(reports[i], options);
        if (result.success) {
          results.saved++;
        } else {
          results.failed++;
          results.errors.push({
            index: i,
            error: result.error
          });
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          index: i,
          error: {
            code: 'BATCH_SAVE_ERROR',
            message: error.message
          }
        });
      }
    }

    results.success = results.failed === 0;
    return results;
  }

  /**
   * 获取存储统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStorageStats() {
    try {
      const reports = await this.getReportsFromStorage();
      const index = await this.getStorageData(this.indexKey) || {};
      
      return {
        totalReports: reports.length,
        totalSize: JSON.stringify(reports).length,
        indexSize: JSON.stringify(index).length,
        dateRange: this.getDateRange(reports),
        hospitals: Object.keys(index.byHospital || {}),
        categories: Object.keys(index.byCategory || {}),
        lastUpdated: index.lastUpdated
      };
    } catch (error) {
      console.error('获取存储统计失败:', error);
      return {
        totalReports: 0,
        totalSize: 0,
        error: error.message
      };
    }
  }

  /**
   * 获取日期范围
   * @param {Array} reports - 报告列表
   * @returns {Object} 日期范围
   */
  getDateRange(reports) {
    if (reports.length === 0) return null;
    
    const dates = reports
      .map(report => report.checkDate)
      .filter(Boolean)
      .sort();
    
    return {
      earliest: dates[0],
      latest: dates[dates.length - 1]
    };
  }
}

module.exports = ReportSaveService;