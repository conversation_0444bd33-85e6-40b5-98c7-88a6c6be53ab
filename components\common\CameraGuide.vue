<template>
	<view class="camera-guide-container">
		<!-- 拍摄指引遮罩 -->
		<view class="guide-overlay" v-if="showGuide">
			<view class="guide-content">
				<view class="guide-header">
					<text class="guide-title">拍摄指引</text>
					<view class="close-btn" @tap="closeGuide">
						<text class="close-icon">×</text>
					</view>
				</view>
				
				<view class="guide-tips">
					<view class="tip-item">
						<view class="tip-icon">📱</view>
						<text class="tip-text">保持手机稳定，避免抖动</text>
					</view>
					<view class="tip-item">
						<view class="tip-icon">💡</view>
						<text class="tip-text">确保光线充足，避免阴影</text>
					</view>
					<view class="tip-item">
						<view class="tip-icon">📄</view>
						<text class="tip-text">报告完整显示在取景框内</text>
					</view>
					<view class="tip-item">
						<view class="tip-icon">🔍</view>
						<text class="tip-text">文字清晰可见，无模糊</text>
					</view>
				</view>
				
				<view class="guide-actions">
					<button class="btn btn-primary" @tap="startCamera">开始拍摄</button>
				</view>
			</view>
		</view>
		
		<!-- 拍摄界面 -->
		<view class="camera-container" v-if="showCamera">
			<!-- 相机预览区域 -->
			<view class="camera-preview">
				<!-- 取景框 -->
				<view class="viewfinder">
					<view class="viewfinder-corner tl"></view>
					<view class="viewfinder-corner tr"></view>
					<view class="viewfinder-corner bl"></view>
					<view class="viewfinder-corner br"></view>
				</view>
				
				<!-- 实时提示 -->
				<view class="live-tips" :class="{ 'tips-warning': hasWarning }">
					<text class="tip-text">{{ currentTip }}</text>
				</view>
			</view>
			
			<!-- 底部控制栏 -->
			<view class="camera-controls">
				<view class="control-item" @tap="toggleFlash">
					<view class="control-icon" :class="{ 'active': flashEnabled }">⚡</view>
					<text class="control-text">闪光灯</text>
				</view>
				
				<view class="capture-btn" @tap="capturePhoto">
					<view class="capture-inner"></view>
				</view>
				
				<view class="control-item" @tap="selectFromAlbum">
					<view class="control-icon">🖼️</view>
					<text class="control-text">相册</text>
				</view>
			</view>
		</view>
		
		<!-- 图片预览和确认 -->
		<view class="preview-container" v-if="showPreview">
			<view class="preview-header">
				<text class="preview-title">预览图片</text>
				<view class="preview-actions">
					<button class="btn btn-secondary" @tap="retakePhoto">重拍</button>
					<button class="btn btn-primary" @tap="confirmPhoto">确认</button>
				</view>
			</view>
			
			<view class="preview-content">
				<image class="preview-image" :src="previewImage" mode="aspectFit"></image>
				
				<!-- 图片质量检测结果 -->
				<view class="quality-check" v-if="qualityResult">
					<view class="quality-item" :class="qualityResult.clarity.status">
						<text class="quality-label">清晰度:</text>
						<text class="quality-value">{{ qualityResult.clarity.text }}</text>
					</view>
					<view class="quality-item" :class="qualityResult.brightness.status">
						<text class="quality-label">亮度:</text>
						<text class="quality-value">{{ qualityResult.brightness.text }}</text>
					</view>
					<view class="quality-item" :class="qualityResult.completeness.status">
						<text class="quality-label">完整性:</text>
						<text class="quality-value">{{ qualityResult.completeness.text }}</text>
					</view>
				</view>
				
				<!-- 改进建议 -->
				<view class="improvement-tips" v-if="improvementTips.length > 0">
					<text class="tips-title">改进建议:</text>
					<view class="tip-item" v-for="(tip, index) in improvementTips" :key="index">
						<text class="tip-text">• {{ tip }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CameraGuide',
		props: {
			visible: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				showGuide: true,
				showCamera: false,
				showPreview: false,
				flashEnabled: false,
				previewImage: '',
				currentTip: '请将报告完整放入取景框内',
				hasWarning: false,
				qualityResult: null,
				improvementTips: []
			}
		},
		watch: {
			visible(newVal) {
				if (newVal) {
					this.showGuide = true
					this.showCamera = false
					this.showPreview = false
				}
			}
		},
		methods: {
			// 关闭指引
			closeGuide() {
				this.$emit('close')
			},
			
			// 开始拍摄
			startCamera() {
				this.showGuide = false
				this.showCamera = true
				this.initCamera()
			},
			
			// 初始化相机
			initCamera() {
				// 开始实时提示
				this.startLiveTips()
			},
			
			// 开始实时提示
			startLiveTips() {
				const tips = [
					'请将报告完整放入取景框内',
					'确保文字清晰可见',
					'避免反光和阴影',
					'保持手机稳定'
				]
				
				let tipIndex = 0
				this.tipTimer = setInterval(() => {
					this.currentTip = tips[tipIndex]
					tipIndex = (tipIndex + 1) % tips.length
				}, 3000)
			},
			
			// 切换闪光灯
			toggleFlash() {
				this.flashEnabled = !this.flashEnabled
				// 这里可以调用实际的闪光灯API
			},
			
			// 拍摄照片
			async capturePhoto() {
				try {
					const result = await uni.chooseImage({
						count: 1,
						sourceType: ['camera'],
						sizeType: ['original']
					})
					
					if (result.tempFilePaths && result.tempFilePaths.length > 0) {
						this.previewImage = result.tempFilePaths[0]
						this.showCamera = false
						this.showPreview = true
						
						// 清除实时提示定时器
						if (this.tipTimer) {
							clearInterval(this.tipTimer)
						}
						
						// 检测图片质量
						await this.checkImageQuality(this.previewImage)
					}
				} catch (error) {
					console.error('拍摄失败:', error)
					uni.showToast({
						title: '拍摄失败，请重试',
						icon: 'none'
					})
				}
			},
			
			// 从相册选择
			async selectFromAlbum() {
				try {
					const result = await uni.chooseImage({
						count: 1,
						sourceType: ['album'],
						sizeType: ['original']
					})
					
					if (result.tempFilePaths && result.tempFilePaths.length > 0) {
						this.previewImage = result.tempFilePaths[0]
						this.showCamera = false
						this.showPreview = true
						
						// 清除实时提示定时器
						if (this.tipTimer) {
							clearInterval(this.tipTimer)
						}
						
						// 检测图片质量
						await this.checkImageQuality(this.previewImage)
					}
				} catch (error) {
					console.error('选择图片失败:', error)
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					})
				}
			},
			
			// 检测图片质量
			async checkImageQuality(imagePath) {
				try {
					// 模拟图片质量检测
					// 实际项目中这里应该调用图片质量检测API
					const mockResult = {
						clarity: { status: 'good', text: '清晰' },
						brightness: { status: 'good', text: '适中' },
						completeness: { status: 'warning', text: '部分缺失' }
					}
					
					this.qualityResult = mockResult
					this.generateImprovementTips()
					
				} catch (error) {
					console.error('图片质量检测失败:', error)
				}
			},
			
			// 生成改进建议
			generateImprovementTips() {
				this.improvementTips = []
				
				if (this.qualityResult.clarity.status === 'warning') {
					this.improvementTips.push('图片模糊，请重新拍摄更清晰的照片')
				}
				
				if (this.qualityResult.brightness.status === 'warning') {
					this.improvementTips.push('光线不足，请在光线充足的地方拍摄')
				}
				
				if (this.qualityResult.completeness.status === 'warning') {
					this.improvementTips.push('报告内容不完整，请确保完整拍摄')
				}
			},
			
			// 重新拍摄
			retakePhoto() {
				this.showPreview = false
				this.showCamera = true
				this.previewImage = ''
				this.qualityResult = null
				this.improvementTips = []
				this.startLiveTips()
			},
			
			// 确认照片
			confirmPhoto() {
				this.$emit('confirm', {
					imagePath: this.previewImage,
					qualityResult: this.qualityResult
				})
				this.closeGuide()
			}
		},
		
		beforeDestroy() {
			// 清除定时器
			if (this.tipTimer) {
				clearInterval(this.tipTimer)
			}
		}
	}
</script>

<style scoped>
	.camera-guide-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: 1000;
		background-color: #000000;
	}
	
	/* 指引遮罩 */
	.guide-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1001;
	}
	
	.guide-content {
		background-color: #FFFFFF;
		border-radius: 12px;
		padding: 20px;
		margin: 20px;
		max-width: 320px;
	}
	
	.guide-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;
	}
	
	.guide-title {
		font-size: 18px;
		font-weight: 600;
		color: #333333;
	}
	
	.close-btn {
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 15px;
		background-color: #F2F2F7;
	}
	
	.close-icon {
		font-size: 20px;
		color: #8E8E93;
	}
	
	.guide-tips {
		margin-bottom: 20px;
	}
	
	.tip-item {
		display: flex;
		align-items: center;
		margin-bottom: 12px;
	}
	
	.tip-icon {
		font-size: 20px;
		margin-right: 10px;
		width: 30px;
		text-align: center;
	}
	
	.tip-text {
		font-size: 14px;
		color: #333333;
		flex: 1;
	}
	
	/* 相机界面 */
	.camera-container {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	
	.camera-preview {
		flex: 1;
		position: relative;
		background-color: #000000;
	}
	
	.viewfinder {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 280px;
		height: 400px;
		border: 2px solid rgba(255, 255, 255, 0.5);
	}
	
	.viewfinder-corner {
		position: absolute;
		width: 20px;
		height: 20px;
		border: 3px solid #007AFF;
	}
	
	.viewfinder-corner.tl {
		top: -3px;
		left: -3px;
		border-right: none;
		border-bottom: none;
	}
	
	.viewfinder-corner.tr {
		top: -3px;
		right: -3px;
		border-left: none;
		border-bottom: none;
	}
	
	.viewfinder-corner.bl {
		bottom: -3px;
		left: -3px;
		border-right: none;
		border-top: none;
	}
	
	.viewfinder-corner.br {
		bottom: -3px;
		right: -3px;
		border-left: none;
		border-top: none;
	}
	
	.live-tips {
		position: absolute;
		top: 100px;
		left: 0;
		right: 0;
		text-align: center;
		padding: 10px 20px;
		background-color: rgba(0, 0, 0, 0.6);
		backdrop-filter: blur(10px);
	}
	
	.live-tips .tip-text {
		color: #FFFFFF;
		font-size: 14px;
	}
	
	.tips-warning {
		background-color: rgba(255, 59, 48, 0.8);
	}
	
	.camera-controls {
		height: 120px;
		background-color: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: space-around;
		padding: 0 30px;
	}
	
	.control-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.control-icon {
		font-size: 24px;
		color: #FFFFFF;
		margin-bottom: 5px;
		opacity: 0.7;
		transition: opacity 0.3s ease;
	}
	
	.control-icon.active {
		opacity: 1;
		color: #007AFF;
	}
	
	.control-text {
		font-size: 12px;
		color: #FFFFFF;
		opacity: 0.7;
	}
	
	.capture-btn {
		width: 70px;
		height: 70px;
		border-radius: 35px;
		border: 3px solid #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: transparent;
	}
	
	.capture-inner {
		width: 50px;
		height: 50px;
		border-radius: 25px;
		background-color: #FFFFFF;
	}
	
	/* 预览界面 */
	.preview-container {
		width: 100%;
		height: 100%;
		background-color: #000000;
		display: flex;
		flex-direction: column;
	}
	
	.preview-header {
		height: 60px;
		background-color: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 15px;
	}
	
	.preview-title {
		color: #FFFFFF;
		font-size: 16px;
		font-weight: 600;
	}
	
	.preview-actions {
		display: flex;
		gap: 10px;
	}
	
	.preview-actions .btn {
		padding: 6px 12px;
		font-size: 14px;
	}
	
	.preview-content {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.preview-image {
		flex: 1;
		width: 100%;
	}
	
	.quality-check {
		background-color: rgba(0, 0, 0, 0.8);
		padding: 15px;
		display: flex;
		justify-content: space-around;
	}
	
	.quality-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.quality-label {
		font-size: 12px;
		color: #FFFFFF;
		opacity: 0.7;
		margin-bottom: 5px;
	}
	
	.quality-value {
		font-size: 14px;
		font-weight: 600;
	}
	
	.quality-item.good .quality-value {
		color: #34C759;
	}
	
	.quality-item.warning .quality-value {
		color: #FF9500;
	}
	
	.quality-item.error .quality-value {
		color: #FF3B30;
	}
	
	.improvement-tips {
		background-color: rgba(255, 149, 0, 0.1);
		padding: 15px;
		border-top: 1px solid rgba(255, 149, 0, 0.3);
	}
	
	.tips-title {
		font-size: 14px;
		font-weight: 600;
		color: #FF9500;
		margin-bottom: 10px;
	}
	
	.improvement-tips .tip-item {
		margin-bottom: 5px;
	}
	
	.improvement-tips .tip-text {
		font-size: 12px;
		color: #FF9500;
	}
</style>