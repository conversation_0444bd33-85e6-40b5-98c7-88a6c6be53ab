// 健康图表组件核心逻辑测试
describe('HealthChart 核心逻辑测试', () => {
  // Mock uni-app API
  global.uni = {
    createCanvasContext: jest.fn(() => ({
      clearRect: jest.fn(),
      setFillStyle: jest.fn(),
      fillRect: jest.fn(),
      beginPath: jest.fn(),
      setStrokeStyle: jest.fn(),
      setLineWidth: jest.fn(),
      moveTo: jest.fn(),
      lineTo: jest.fn(),
      stroke: jest.fn(),
      arc: jest.fn(),
      fill: jest.fn(),
      setFontSize: jest.fn(),
      fillText: jest.fn(),
      draw: jest.fn()
    })),
    createSelectorQuery: jest.fn(() => ({
      in: jest.fn(() => ({
        select: jest.fn(() => ({
          fields: jest.fn(() => ({
            exec: jest.fn()
          }))
        }))
      }))
    })),
    getSystemInfoSync: jest.fn(() => ({
      pixelRatio: 2
    }))
  }

  // 模拟图表组件的核心方法
  const chartMethods = {
    isAbnormalValue(value, normalRange = { min: 90, max: 140 }) {
      const { min, max } = normalRange
      if (min === null || max === null) return false
      return value < min || value > max
    },

    calculateStatistics(chartData) {
      if (!chartData || chartData.length === 0) {
        return {
          average: 0,
          max: 0,
          min: 0,
          range: 0,
          maxAbnormal: false,
          minAbnormal: false
        }
      }

      const values = chartData.map(item => item.value)
      const sum = values.reduce((acc, val) => acc + val, 0)
      const max = Math.max(...values)
      const min = Math.min(...values)
      const average = sum / values.length

      return {
        average: average.toFixed(1),
        max: max.toFixed(1),
        min: min.toFixed(1),
        range: (max - min).toFixed(1),
        maxAbnormal: this.isAbnormalValue(max),
        minAbnormal: this.isAbnormalValue(min)
      }
    },

    detectAbnormalValues(chartData, normalRange = { min: 90, max: 140 }) {
      if (!chartData || chartData.length === 0) return []

      return chartData
        .filter(item => this.isAbnormalValue(item.value, normalRange))
        .map(item => ({
          date: this.formatDate(item.date),
          value: item.value.toFixed(1),
          status: this.getAbnormalStatus(item.value, normalRange)
        }))
    },

    getAbnormalStatus(value, normalRange) {
      const { min, max } = normalRange
      if (value < min) return '偏低'
      if (value > max) return '偏高'
      return '正常'
    },

    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  }

  describe('异常值检测', () => {
    it('应该正确判断正常值', () => {
      expect(chartMethods.isAbnormalValue(120)).toBe(false)
      expect(chartMethods.isAbnormalValue(90)).toBe(false)
      expect(chartMethods.isAbnormalValue(140)).toBe(false)
    })

    it('应该正确判断异常值', () => {
      expect(chartMethods.isAbnormalValue(80)).toBe(true)  // 偏低
      expect(chartMethods.isAbnormalValue(150)).toBe(true) // 偏高
    })

    it('应该在没有正常范围时返回false', () => {
      const noRange = { min: null, max: null }
      expect(chartMethods.isAbnormalValue(80, noRange)).toBe(false)
      expect(chartMethods.isAbnormalValue(150, noRange)).toBe(false)
    })
  })

  describe('统计计算', () => {
    it('应该正确计算统计信息', () => {
      const testData = [
        { date: '2024-01-01', value: 120 },
        { date: '2024-02-01', value: 135 },
        { date: '2024-03-01', value: 125 }
      ]

      const stats = chartMethods.calculateStatistics(testData)

      expect(stats.average).toBe('126.7')
      expect(stats.max).toBe('135.0')
      expect(stats.min).toBe('120.0')
      expect(stats.range).toBe('15.0')
    })

    it('应该正确处理空数据', () => {
      const stats = chartMethods.calculateStatistics([])

      expect(stats.average).toBe(0)
      expect(stats.max).toBe(0)
      expect(stats.min).toBe(0)
      expect(stats.range).toBe(0)
    })
  })

  describe('异常值检测', () => {
    it('应该正确检测异常值', () => {
      const testData = [
        { date: '2024-01-01', value: 80 },  // 异常低值
        { date: '2024-02-01', value: 120 }, // 正常值
        { date: '2024-03-01', value: 160 }  // 异常高值
      ]

      const abnormalValues = chartMethods.detectAbnormalValues(testData)

      expect(abnormalValues.length).toBe(2)
      expect(abnormalValues[0].status).toBe('偏低')
      expect(abnormalValues[1].status).toBe('偏高')
    })

    it('应该正确处理无异常值的情况', () => {
      const testData = [
        { date: '2024-01-01', value: 120 },
        { date: '2024-02-01', value: 125 },
        { date: '2024-03-01', value: 130 }
      ]

      const abnormalValues = chartMethods.detectAbnormalValues(testData)
      expect(abnormalValues.length).toBe(0)
    })
  })

  describe('状态判断', () => {
    it('应该正确返回异常状态', () => {
      const normalRange = { min: 90, max: 140 }

      expect(chartMethods.getAbnormalStatus(80, normalRange)).toBe('偏低')
      expect(chartMethods.getAbnormalStatus(150, normalRange)).toBe('偏高')
      expect(chartMethods.getAbnormalStatus(120, normalRange)).toBe('正常')
    })
  })

  describe('日期格式化', () => {
    it('应该正确格式化日期', () => {
      expect(chartMethods.formatDate('2024-03-15')).toBe('3月15日')
      expect(chartMethods.formatDate('2024-12-01')).toBe('12月1日')
    })
  })

  describe('Canvas API 调用', () => {
    it('应该正确初始化canvas上下文', () => {
      const canvasId = 'testChart'
      const ctx = uni.createCanvasContext(canvasId)

      expect(uni.createCanvasContext).toHaveBeenCalledWith(canvasId)
      expect(ctx).toBeDefined()
      expect(typeof ctx.clearRect).toBe('function')
      expect(typeof ctx.setFillStyle).toBe('function')
    })

    it('应该支持基本绘图操作', () => {
      const ctx = uni.createCanvasContext('testChart')

      // 测试清理画布
      ctx.clearRect(0, 0, 350, 250)
      expect(ctx.clearRect).toHaveBeenCalledWith(0, 0, 350, 250)

      // 测试设置样式
      ctx.setFillStyle('#ffffff')
      expect(ctx.setFillStyle).toHaveBeenCalledWith('#ffffff')

      // 测试绘制矩形
      ctx.fillRect(0, 0, 350, 250)
      expect(ctx.fillRect).toHaveBeenCalledWith(0, 0, 350, 250)
    })
  })

  describe('图表数据处理', () => {
    it('应该正确处理有效的图表数据', () => {
      const validData = [
        { date: '2024-01-01', value: 120 },
        { date: '2024-02-01', value: 135 },
        { date: '2024-03-01', value: 125 }
      ]

      expect(validData.every(item => 
        item.date && typeof item.value === 'number'
      )).toBe(true)
    })

    it('应该处理包含异常值的数据', () => {
      const dataWithAbnormal = [
        { date: '2024-01-01', value: 80 },  // 异常低值
        { date: '2024-02-01', value: 120 }, // 正常值
        { date: '2024-03-01', value: 160 }  // 异常高值
      ]

      expect(chartMethods.isAbnormalValue(dataWithAbnormal[0].value)).toBe(true)
      expect(chartMethods.isAbnormalValue(dataWithAbnormal[1].value)).toBe(false)
      expect(chartMethods.isAbnormalValue(dataWithAbnormal[2].value)).toBe(true)
    })
  })

  describe('触摸交互逻辑', () => {
    let touchState

    beforeEach(() => {
      touchState = {
        touchStartX: 0,
        touchStartY: 0,
        scale: 1,
        offsetX: 0,
        offsetY: 0,
        lastDistance: null
      }
    })

    it('应该正确处理触摸开始事件', () => {
      const mockTouchEvent = {
        touches: [{ clientX: 100, clientY: 100 }]
      }

      touchState.touchStartX = mockTouchEvent.touches[0].clientX
      touchState.touchStartY = mockTouchEvent.touches[0].clientY

      expect(touchState.touchStartX).toBe(100)
      expect(touchState.touchStartY).toBe(100)
    })

    it('应该支持单指滑动', () => {
      const startEvent = { touches: [{ clientX: 100, clientY: 100 }] }
      const moveEvent = { touches: [{ clientX: 120, clientY: 100 }] }

      touchState.touchStartX = startEvent.touches[0].clientX
      const initialOffsetX = touchState.offsetX

      // 模拟滑动
      const deltaX = moveEvent.touches[0].clientX - touchState.touchStartX
      touchState.offsetX += deltaX
      touchState.touchStartX = moveEvent.touches[0].clientX

      expect(touchState.offsetX).toBe(initialOffsetX + 20)
    })

    it('应该限制缩放范围', () => {
      touchState.scale = 0.3
      const scaleChange = 0.8

      touchState.scale *= scaleChange
      touchState.scale = Math.max(0.5, Math.min(3, touchState.scale))

      expect(touchState.scale).toBeGreaterThanOrEqual(0.5)
      expect(touchState.scale).toBeLessThanOrEqual(3)
    })
  })
})