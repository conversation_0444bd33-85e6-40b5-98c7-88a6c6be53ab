/**
 * 存储系统集成测试
 */

import { storage, initializeStorage } from '../../utils/storage/index.js';

// Mock uni-app API
global.uni = {
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn()
};

// Mock crypto API
global.crypto = {
  getRandomValues: jest.fn((array) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  })
};

global.btoa = jest.fn((str) => Buffer.from(str, 'binary').toString('base64'));
global.atob = jest.fn((str) => Buffer.from(str, 'base64').toString('binary'));

describe('Storage System Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    global.uni.getStorageSync.mockReturnValue(null);
  });

  describe('初始化', () => {
    test('应该能够初始化存储系统', async () => {
      const result = await initializeStorage();
      expect(result).toBe(true);
    });

    test('storage.init()应该工作正常', async () => {
      const result = await storage.init();
      expect(result).toBe(true);
    });
  });

  describe('完整的用户流程', () => {
    beforeEach(async () => {
      await storage.init();
    });

    test('应该能够完成用户注册、登录和数据管理流程', async () => {
      // 1. 用户注册
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        phone: '13800138000',
        password: 'password123',
        real_name: '测试用户'
      };

      const user = await storage.users.createUser(userData);
      expect(user.id).toBeDefined();
      expect(user.username).toBe('testuser');

      // 2. 用户登录
      const loginUser = await storage.users.validatePassword('testuser', 'password123');
      expect(loginUser).not.toBeNull();
      expect(loginUser.username).toBe('testuser');

      // 3. 创建健康报告
      const reportData = {
        user_id: user.id,
        report_title: '年度体检报告',
        report_date: '2024-01-15',
        hospital_name: '测试医院',
        doctor_name: '张医生',
        department: '内科',
        report_type: '体检报告',
        ocr_text: '血糖：5.6 mmol/L\n血压：120/80 mmHg\n总胆固醇：4.5 mmol/L'
      };

      const report = await storage.reports.createReport(reportData, true); // 加密存储
      expect(report.id).toBeDefined();
      expect(report.is_encrypted).toBe(1);

      // 4. 添加健康指标
      const indicators = [
        {
          report_id: report.id,
          indicator_name: '血糖',
          indicator_value: '5.6',
          indicator_unit: 'mmol/L',
          reference_range: '3.9-6.1',
          is_abnormal: 0,
          category: '血液'
        },
        {
          report_id: report.id,
          indicator_name: '收缩压',
          indicator_value: '120',
          indicator_unit: 'mmHg',
          reference_range: '90-140',
          is_abnormal: 0,
          category: '生理'
        },
        {
          report_id: report.id,
          indicator_name: '总胆固醇',
          indicator_value: '6.8',
          indicator_unit: 'mmol/L',
          reference_range: '3.1-5.2',
          is_abnormal: 1,
          abnormal_level: 2,
          category: '血液'
        }
      ];

      const createdIndicators = await storage.indicators.createBatch(indicators);
      expect(createdIndicators).toHaveLength(3);

      // 5. 查询用户的所有报告
      const userReports = await storage.reports.findByUserId(user.id);
      expect(userReports).toHaveLength(1);

      // 6. 获取解密后的报告内容
      const decryptedReport = await storage.reports.getDecryptedReport(report.id);
      expect(decryptedReport.ocr_text).toContain('血糖：5.6');

      // 7. 查询报告的所有指标
      const reportIndicators = await storage.indicators.findByReportId(report.id);
      expect(reportIndicators).toHaveLength(3);

      // 8. 查询异常指标
      const abnormalIndicators = await storage.indicators.findAbnormalIndicators(report.id);
      expect(abnormalIndicators).toHaveLength(1);
      expect(abnormalIndicators[0].indicator_name).toBe('总胆固醇');

      // 9. 创建同步记录
      await storage.sync.createSyncRecord(user.id, 'health_reports', report.id, 'INSERT');
      await storage.sync.createSyncRecord(user.id, 'health_indicators', createdIndicators[0].id, 'INSERT');

      const pendingSync = await storage.sync.getPendingSyncRecords(user.id);
      expect(pendingSync).toHaveLength(2);

      // 10. 更新同步状态
      await storage.sync.updateSyncStatus(pendingSync[0].id, 1); // 同步成功

      const remainingSync = await storage.sync.getPendingSyncRecords(user.id);
      expect(remainingSync).toHaveLength(1);
    });
  });

  describe('数据查询和分析', () => {
    beforeEach(async () => {
      await storage.init();
    });

    test('应该能够进行复杂的数据查询', async () => {
      // 创建测试用户
      const user = await storage.users.createUser({
        username: 'testuser',
        password: 'password123'
      });

      // 创建多个报告
      const reports = [];
      for (let i = 1; i <= 5; i++) {
        const report = await storage.reports.create({
          user_id: user.id,
          report_title: `报告${i}`,
          report_date: `2024-01-${i.toString().padStart(2, '0')}`,
          hospital_name: '测试医院'
        });
        reports.push(report);

        // 为每个报告添加血糖指标
        await storage.indicators.create({
          report_id: report.id,
          indicator_name: '血糖',
          indicator_value: (5.0 + i * 0.2).toString(),
          indicator_unit: 'mmol/L',
          category: '血液',
          is_abnormal: i > 3 ? 1 : 0
        });
      }

      // 查询血糖历史数据
      const bloodSugarHistory = await storage.indicators.findHistoryByName(user.id, '血糖');
      expect(bloodSugarHistory).toHaveLength(5);
      expect(bloodSugarHistory[0].report_date).toBe('2024-01-05'); // 按日期倒序

      // 查询日期范围内的报告
      const rangeReports = await storage.reports.findByDateRange(user.id, '2024-01-02', '2024-01-04');
      expect(rangeReports).toHaveLength(3);

      // 分页查询报告
      const page1 = await storage.reports.paginate(1, 2, { user_id: user.id });
      expect(page1.data).toHaveLength(2);
      expect(page1.pagination.totalPages).toBe(3);
    });
  });

  describe('统计信息', () => {
    beforeEach(async () => {
      await storage.init();
    });

    test('应该能够获取系统统计信息', async () => {
      // 创建测试数据
      const user = await storage.users.createUser({
        username: 'testuser',
        password: 'password123'
      });

      const report = await storage.reports.create({
        user_id: user.id,
        report_title: '测试报告',
        report_date: '2024-01-01'
      });

      await storage.indicators.create({
        report_id: report.id,
        indicator_name: '血糖',
        indicator_value: '5.6'
      });

      // 获取统计信息
      const stats = await storage.getStats();

      expect(stats.users.recordCount).toBe(1);
      expect(stats.health_reports.recordCount).toBe(1);
      expect(stats.health_indicators.recordCount).toBe(1);
      expect(stats.sync_records.recordCount).toBe(0);
    });
  });

  describe('数据清理', () => {
    beforeEach(async () => {
      await storage.init();
    });

    test('应该能够清空所有数据', async () => {
      // 创建测试数据
      await storage.users.createUser({
        username: 'testuser',
        password: 'password123'
      });

      // 验证数据存在
      const userCount = await storage.users.count();
      expect(userCount).toBe(1);

      // 清空所有数据
      await storage.clearAll();

      // 验证数据已清空
      const stats = await storage.getStats();
      expect(stats.users.recordCount).toBe(0);
      expect(stats.health_reports.recordCount).toBe(0);
      expect(stats.health_indicators.recordCount).toBe(0);
      expect(stats.sync_records.recordCount).toBe(0);
    });
  });

  describe('错误处理', () => {
    test('初始化失败时应该抛出错误', async () => {
      // Mock存储失败
      global.uni.setStorageSync.mockImplementation(() => {
        throw new Error('存储失败');
      });

      await expect(initializeStorage()).rejects.toThrow();
    });
  });
});