/**
 * 健康报告服务
 * 提供报告的增删改查、数据验证等功能
 */

import { healthReportRepository, healthIndicatorRepository } from '@/utils/storage/index.js'
import { getEncryption } from '@/utils/storage/index.js'

class ReportService {
  constructor() {
    this.reportRepo = healthReportRepository
    this.indicatorRepo = healthIndicatorRepository
    this.encryption = getEncryption()
  }

  /**
   * 创建新报告
   * @param {Object} reportData 报告数据
   * @returns {Promise<Object>} 创建的报告
   */
  async createReport(reportData) {
    try {
      // 数据验证
      this.validateReportData(reportData)
      
      // 生成报告ID
      const reportId = this.generateReportId()
      
      // 准备报告数据
      const report = {
        id: reportId,
        userId: reportData.userId,
        reportDate: reportData.reportDate || new Date().toISOString(),
        hospitalName: reportData.hospitalName,
        doctorName: reportData.doctorName || '',
        department: reportData.department || '',
        reportType: reportData.reportType || '常规检查',
        originalImagePath: reportData.originalImagePath || '',
        ocrText: reportData.ocrText || '',
        notes: reportData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isDeleted: false
      }
      
      // 保存报告
      const savedReport = await this.reportRepo.create(report)
      
      // 保存健康指标
      if (reportData.indicators && reportData.indicators.length > 0) {
        for (const indicator of reportData.indicators) {
          await this.createIndicator(reportId, indicator)
        }
      }
      
      return await this.getReportById(reportId)
    } catch (error) {
      console.error('创建报告失败:', error)
      throw new Error(`创建报告失败: ${error.message}`)
    }
  }

  /**
   * 创建健康指标
   * @param {string} reportId 报告ID
   * @param {Object} indicatorData 指标数据
   */
  async createIndicator(reportId, indicatorData) {
    const indicator = {
      id: this.generateIndicatorId(),
      reportId: reportId,
      name: indicatorData.name,
      value: indicatorData.value,
      unit: indicatorData.unit || '',
      referenceRange: indicatorData.referenceRange || '',
      isAbnormal: indicatorData.isAbnormal || false,
      category: indicatorData.category || '其他',
      description: indicatorData.description || '',
      createdAt: new Date().toISOString()
    }
    
    return await this.indicatorRepo.create(indicator)
  }

  /**
   * 获取报告列表
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 报告列表
   */
  async getReports(options = {}) {
    try {
      const {
        userId,
        page = 1,
        pageSize = 20,
        sortBy = 'reportDate',
        sortOrder = 'desc',
        filter = {}
      } = options
      
      let query = { isDeleted: false }
      
      if (userId) {
        query.userId = userId
      }
      
      // 应用筛选条件
      if (filter.startDate && filter.endDate) {
        query.reportDate = {
          $gte: filter.startDate,
          $lte: filter.endDate
        }
      }
      
      if (filter.hospitalName) {
        query.hospitalName = { $like: `%${filter.hospitalName}%` }
      }
      
      if (filter.reportType) {
        query.reportType = filter.reportType
      }
      
      // 查询报告
      const reports = await this.reportRepo.findMany(query, {
        page,
        pageSize,
        sortBy,
        sortOrder
      })
      
      // 为每个报告加载指标
      for (const report of reports) {
        report.indicators = await this.getIndicatorsByReportId(report.id)
      }
      
      return reports
    } catch (error) {
      console.error('获取报告列表失败:', error)
      throw new Error(`获取报告列表失败: ${error.message}`)
    }
  }

  /**
   * 根据ID获取报告详情
   * @param {string} reportId 报告ID
   * @returns {Promise<Object>} 报告详情
   */
  async getReportById(reportId) {
    try {
      const report = await this.reportRepo.findById(reportId)
      if (!report || report.isDeleted) {
        throw new Error('报告不存在')
      }
      
      // 加载健康指标
      report.indicators = await this.getIndicatorsByReportId(reportId)
      
      return report
    } catch (error) {
      console.error('获取报告详情失败:', error)
      throw new Error(`获取报告详情失败: ${error.message}`)
    }
  }

  /**
   * 获取报告的健康指标
   * @param {string} reportId 报告ID
   * @returns {Promise<Array>} 健康指标列表
   */
  async getIndicatorsByReportId(reportId) {
    try {
      return await this.indicatorRepo.findMany({ reportId })
    } catch (error) {
      console.error('获取健康指标失败:', error)
      return []
    }
  }

  /**
   * 更新报告
   * @param {string} reportId 报告ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Object>} 更新后的报告
   */
  async updateReport(reportId, updateData) {
    try {
      // 验证报告是否存在
      const existingReport = await this.getReportById(reportId)
      if (!existingReport) {
        throw new Error('报告不存在')
      }
      
      // 验证更新数据
      this.validateUpdateData(updateData)
      
      // 准备更新数据
      const updateFields = {
        ...updateData,
        updatedAt: new Date().toISOString()
      }
      
      // 不允许更新的字段
      delete updateFields.id
      delete updateFields.userId
      delete updateFields.createdAt
      delete updateFields.originalImagePath // 图片不允许修改
      
      // 更新报告
      await this.reportRepo.update(reportId, updateFields)
      
      // 如果有指标更新
      if (updateData.indicators) {
        await this.updateIndicators(reportId, updateData.indicators)
      }
      
      return await this.getReportById(reportId)
    } catch (error) {
      console.error('更新报告失败:', error)
      throw new Error(`更新报告失败: ${error.message}`)
    }
  }

  /**
   * 更新健康指标
   * @param {string} reportId 报告ID
   * @param {Array} indicators 指标数据
   */
  async updateIndicators(reportId, indicators) {
    try {
      // 删除原有指标
      await this.indicatorRepo.deleteMany({ reportId })
      
      // 创建新指标
      for (const indicator of indicators) {
        await this.createIndicator(reportId, indicator)
      }
    } catch (error) {
      console.error('更新健康指标失败:', error)
      throw error
    }
  }

  /**
   * 删除报告（软删除）
   * @param {string} reportId 报告ID
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteReport(reportId) {
    try {
      // 验证报告是否存在
      const report = await this.getReportById(reportId)
      if (!report) {
        throw new Error('报告不存在')
      }
      
      // 软删除报告
      await this.reportRepo.update(reportId, {
        isDeleted: true,
        deletedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })
      
      return true
    } catch (error) {
      console.error('删除报告失败:', error)
      throw new Error(`删除报告失败: ${error.message}`)
    }
  }

  /**
   * 永久删除报告
   * @param {string} reportId 报告ID
   * @returns {Promise<boolean>} 删除结果
   */
  async permanentDeleteReport(reportId) {
    try {
      // 删除健康指标
      await this.indicatorRepo.deleteMany({ reportId })
      
      // 删除报告
      await this.reportRepo.delete(reportId)
      
      return true
    } catch (error) {
      console.error('永久删除报告失败:', error)
      throw new Error(`永久删除报告失败: ${error.message}`)
    }
  }

  /**
   * 搜索报告
   * @param {Object} searchOptions 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async searchReports(searchOptions) {
    try {
      const { keyword, userId, ...otherOptions } = searchOptions
      
      let query = { isDeleted: false }
      
      if (userId) {
        query.userId = userId
      }
      
      if (keyword) {
        query.$or = [
          { hospitalName: { $like: `%${keyword}%` } },
          { doctorName: { $like: `%${keyword}%` } },
          { department: { $like: `%${keyword}%` } },
          { notes: { $like: `%${keyword}%` } },
          { ocrText: { $like: `%${keyword}%` } }
        ]
      }
      
      return await this.getReports({ ...otherOptions, filter: query })
    } catch (error) {
      console.error('搜索报告失败:', error)
      throw new Error(`搜索报告失败: ${error.message}`)
    }
  }

  /**
   * 获取报告统计信息
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 统计信息
   */
  async getReportStatistics(userId) {
    try {
      const reports = await this.getReports({ userId, pageSize: 1000 })
      
      const stats = {
        totalReports: reports.length,
        abnormalReports: 0,
        categories: {},
        hospitals: {},
        monthlyStats: {},
        latestReport: null
      }
      
      reports.forEach(report => {
        // 统计异常报告
        const hasAbnormal = report.indicators.some(indicator => indicator.isAbnormal)
        if (hasAbnormal) {
          stats.abnormalReports++
        }
        
        // 统计医院
        if (report.hospitalName) {
          stats.hospitals[report.hospitalName] = (stats.hospitals[report.hospitalName] || 0) + 1
        }
        
        // 统计指标类别
        report.indicators.forEach(indicator => {
          if (indicator.category) {
            stats.categories[indicator.category] = (stats.categories[indicator.category] || 0) + 1
          }
        })
        
        // 统计月度数据
        const month = new Date(report.reportDate).toISOString().substring(0, 7)
        stats.monthlyStats[month] = (stats.monthlyStats[month] || 0) + 1
        
        // 最新报告
        if (!stats.latestReport || new Date(report.reportDate) > new Date(stats.latestReport.reportDate)) {
          stats.latestReport = report
        }
      })
      
      return stats
    } catch (error) {
      console.error('获取报告统计失败:', error)
      throw new Error(`获取报告统计失败: ${error.message}`)
    }
  }

  /**
   * 数据验证 - 报告数据
   * @param {Object} reportData 报告数据
   */
  validateReportData(reportData) {
    if (!reportData.userId) {
      throw new Error('用户ID不能为空')
    }
    
    if (!reportData.hospitalName || reportData.hospitalName.trim() === '') {
      throw new Error('医院名称不能为空')
    }
    
    if (reportData.reportDate && !this.isValidDate(reportData.reportDate)) {
      throw new Error('报告日期格式不正确')
    }
    
    if (reportData.indicators && !Array.isArray(reportData.indicators)) {
      throw new Error('健康指标必须是数组格式')
    }
  }

  /**
   * 数据验证 - 更新数据
   * @param {Object} updateData 更新数据
   */
  validateUpdateData(updateData) {
    if (updateData.hospitalName !== undefined && updateData.hospitalName.trim() === '') {
      throw new Error('医院名称不能为空')
    }
    
    if (updateData.reportDate && !this.isValidDate(updateData.reportDate)) {
      throw new Error('报告日期格式不正确')
    }
    
    if (updateData.indicators && !Array.isArray(updateData.indicators)) {
      throw new Error('健康指标必须是数组格式')
    }
  }

  /**
   * 验证日期格式
   * @param {string} dateString 日期字符串
   * @returns {boolean} 是否有效
   */
  isValidDate(dateString) {
    const date = new Date(dateString)
    return date instanceof Date && !isNaN(date)
  }

  /**
   * 生成报告ID
   * @returns {string} 报告ID
   */
  generateReportId() {
    return 'report_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 生成指标ID
   * @returns {string} 指标ID
   */
  generateIndicatorId() {
    return 'indicator_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
}

// 创建单例实例
const reportService = new ReportService()

export default reportService
export { ReportService }