<template>
	<view class="network-status" v-if="showStatus">
		<!-- 网络状态栏 -->
		<view class="status-bar" :class="statusClass" @tap="toggleDetails">
			<view class="status-icon">
				<text class="icon">{{ statusIcon }}</text>
			</view>
			<view class="status-text">
				<text class="status-main">{{ statusText }}</text>
				<text class="status-sub" v-if="subText">{{ subText }}</text>
			</view>
			<view class="status-action" v-if="showAction">
				<text class="action-text">{{ actionText }}</text>
			</view>
		</view>
		
		<!-- 详细信息面板 -->
		<view class="details-panel" v-if="showDetails">
			<view class="detail-item">
				<text class="detail-label">网络类型:</text>
				<text class="detail-value">{{ networkStatus.description }}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">连接质量:</text>
				<text class="detail-value" :class="qualityClass">{{ networkStatus.quality.description }}</text>
			</view>
			<view class="detail-item" v-if="networkStatus.retryQueueLength > 0">
				<text class="detail-label">待同步:</text>
				<text class="detail-value">{{ networkStatus.retryQueueLength }}个请求</text>
			</view>
			
			<view class="detail-actions">
				<button class="btn btn-small btn-primary" @tap="checkConnection">
					检测网络
				</button>
				<button class="btn btn-small btn-secondary" @tap="hideStatus">
					隐藏
				</button>
			</view>
		</view>
		
		<!-- 离线模式提示 -->
		<view class="offline-banner" v-if="!networkStatus.isOnline && showOfflineBanner">
			<view class="banner-content">
				<text class="banner-icon">📱</text>
				<view class="banner-text">
					<text class="banner-title">离线模式</text>
					<text class="banner-desc">您可以继续使用，数据将在网络恢复后同步</text>
				</view>
				<view class="banner-close" @tap="hideOfflineBanner">
					<text class="close-icon">×</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import networkManager, { networkMixin } from '../../utils/network/index.js'
	
	export default {
		name: 'NetworkStatus',
		mixins: [networkMixin],
		props: {
			// 是否自动显示
			autoShow: {
				type: Boolean,
				default: true
			},
			// 显示位置
			position: {
				type: String,
				default: 'top' // top, bottom, fixed
			},
			// 是否显示离线横幅
			showOfflineBanner: {
				type: Boolean,
				default: true
			}
		},
		
		data() {
			return {
				showStatus: false,
				showDetails: false,
				lastStatusChange: 0,
				hideTimer: null
			}
		},
		
		computed: {
			// 状态样式类
			statusClass() {
				if (!this.networkStatus.isOnline) {
					return 'status-offline'
				}
				
				const quality = this.networkStatus.quality.level
				return `status-${quality}`
			},
			
			// 状态图标
			statusIcon() {
				if (!this.networkStatus.isOnline) {
					return '📵'
				}
				
				const icons = {
					wifi: '📶',
					'5g': '📶',
					'4g': '📶',
					'3g': '📶',
					'2g': '📶',
					ethernet: '🔗'
				}
				
				return icons[this.networkStatus.networkType] || '📶'
			},
			
			// 主要状态文本
			statusText() {
				if (!this.networkStatus.isOnline) {
					return '网络连接不可用'
				}
				
				return this.networkStatus.description
			},
			
			// 副文本
			subText() {
				if (!this.networkStatus.isOnline) {
					return '点击检测网络'
				}
				
				if (this.networkStatus.retryQueueLength > 0) {
					return `${this.networkStatus.retryQueueLength}个请求待同步`
				}
				
				return this.networkStatus.quality.description
			},
			
			// 是否显示操作按钮
			showAction() {
				return !this.networkStatus.isOnline || this.networkStatus.retryQueueLength > 0
			},
			
			// 操作按钮文本
			actionText() {
				if (!this.networkStatus.isOnline) {
					return '重试'
				}
				
				if (this.networkStatus.retryQueueLength > 0) {
					return '同步'
				}
				
				return ''
			},
			
			// 质量样式类
			qualityClass() {
				const level = this.networkStatus.quality.level
				return `quality-${level}`
			}
		},
		
		watch: {
			'networkStatus.isOnline': {
				handler(newVal, oldVal) {
					if (oldVal !== undefined && newVal !== oldVal) {
						this.handleNetworkChange(newVal)
					}
				}
			}
		},
		
		mounted() {
			// 初始化显示状态
			if (this.autoShow) {
				this.updateShowStatus()
			}
		},
		
		methods: {
			// 网络状态变化处理
			onNetworkStatusChange(status) {
				this.lastStatusChange = Date.now()
				
				if (this.autoShow) {
					this.updateShowStatus()
				}
				
				// 网络状态变化提示
				if (status.wasOnline !== undefined) {
					if (status.isOnline && !status.wasOnline) {
						this.showNetworkRestored()
					} else if (!status.isOnline && status.wasOnline) {
						this.showNetworkLost()
					}
				}
			},
			
			// 处理网络变化
			handleNetworkChange(isOnline) {
				if (isOnline) {
					// 网络恢复
					this.showNetworkRestored()
				} else {
					// 网络断开
					this.showNetworkLost()
				}
			},
			
			// 更新显示状态
			updateShowStatus() {
				const shouldShow = !this.networkStatus.isOnline || 
								  this.networkStatus.quality.level === 'poor' ||
								  this.networkStatus.retryQueueLength > 0
				
				if (shouldShow) {
					this.showStatus = true
					this.autoHideStatus()
				} else {
					this.showStatus = false
				}
			},
			
			// 自动隐藏状态栏
			autoHideStatus() {
				if (this.hideTimer) {
					clearTimeout(this.hideTimer)
				}
				
				// 如果网络正常，5秒后自动隐藏
				if (this.networkStatus.isOnline && this.networkStatus.quality.level !== 'poor') {
					this.hideTimer = setTimeout(() => {
						this.showStatus = false
					}, 5000)
				}
			},
			
			// 切换详细信息
			toggleDetails() {
				this.showDetails = !this.showDetails
			},
			
			// 检测网络连接
			async checkConnection() {
				uni.showLoading({
					title: '检测网络中...'
				})
				
				try {
					const isConnected = await this.checkNetwork()
					
					uni.hideLoading()
					
					if (isConnected) {
						uni.showToast({
							title: '网络连接正常',
							icon: 'success'
						})
					} else {
						uni.showToast({
							title: '网络连接不可用',
							icon: 'none'
						})
					}
				} catch (error) {
					uni.hideLoading()
					uni.showToast({
						title: '检测失败',
						icon: 'none'
					})
				}
			},
			
			// 隐藏状态栏
			hideStatus() {
				this.showStatus = false
				this.showDetails = false
			},
			
			// 隐藏离线横幅
			hideOfflineBanner() {
				this.$emit('hide-offline-banner')
			},
			
			// 显示网络恢复提示
			showNetworkRestored() {
				uni.showToast({
					title: '网络已恢复',
					icon: 'success',
					duration: 2000
				})
			},
			
			// 显示网络断开提示
			showNetworkLost() {
				uni.showToast({
					title: '网络连接已断开',
					icon: 'none',
					duration: 3000
				})
			}
		},
		
		beforeDestroy() {
			if (this.hideTimer) {
				clearTimeout(this.hideTimer)
			}
		}
	}
</script>

<style scoped>
	.network-status {
		position: relative;
		z-index: 999;
	}
	
	/* 状态栏 */
	.status-bar {
		display: flex;
		align-items: center;
		padding: 8px 15px;
		background-color: #F2F2F7;
		border-bottom: 1px solid #E5E5EA;
		transition: all 0.3s ease;
	}
	
	.status-offline {
		background-color: #FF3B30;
		color: #FFFFFF;
	}
	
	.status-poor {
		background-color: #FF9500;
		color: #FFFFFF;
	}
	
	.status-fair {
		background-color: #FFCC00;
		color: #333333;
	}
	
	.status-good,
	.status-excellent {
		background-color: #34C759;
		color: #FFFFFF;
	}
	
	.status-icon {
		margin-right: 10px;
	}
	
	.icon {
		font-size: 16px;
	}
	
	.status-text {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.status-main {
		font-size: 14px;
		font-weight: 500;
		line-height: 1.2;
	}
	
	.status-sub {
		font-size: 12px;
		opacity: 0.8;
		margin-top: 2px;
	}
	
	.status-action {
		margin-left: 10px;
	}
	
	.action-text {
		font-size: 12px;
		font-weight: 500;
		opacity: 0.9;
	}
	
	/* 详细信息面板 */
	.details-panel {
		background-color: #FFFFFF;
		border-bottom: 1px solid #E5E5EA;
		padding: 15px;
	}
	
	.detail-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	
	.detail-item:last-of-type {
		margin-bottom: 15px;
	}
	
	.detail-label {
		font-size: 14px;
		color: #8E8E93;
	}
	
	.detail-value {
		font-size: 14px;
		color: #333333;
		font-weight: 500;
	}
	
	.quality-excellent,
	.quality-good {
		color: #34C759;
	}
	
	.quality-fair {
		color: #FF9500;
	}
	
	.quality-poor {
		color: #FF3B30;
	}
	
	.detail-actions {
		display: flex;
		gap: 10px;
		justify-content: flex-end;
	}
	
	/* 离线横幅 */
	.offline-banner {
		background-color: rgba(255, 59, 48, 0.1);
		border: 1px solid rgba(255, 59, 48, 0.2);
		border-radius: 8px;
		margin: 10px 15px;
		overflow: hidden;
	}
	
	.banner-content {
		display: flex;
		align-items: center;
		padding: 12px 15px;
	}
	
	.banner-icon {
		font-size: 20px;
		margin-right: 12px;
	}
	
	.banner-text {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.banner-title {
		font-size: 14px;
		font-weight: 600;
		color: #FF3B30;
		margin-bottom: 2px;
	}
	
	.banner-desc {
		font-size: 12px;
		color: #666666;
		line-height: 1.3;
	}
	
	.banner-close {
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10px;
	}
	
	.close-icon {
		font-size: 18px;
		color: #8E8E93;
	}
	
	/* 位置样式 */
	.network-status.position-fixed {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9999;
	}
	
	.network-status.position-bottom {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 9999;
	}
</style>