/**
 * 用户数据模型
 * 包含用户基本信息、设置偏好等字段
 */

const { BaseModel } = require('../core/base/BaseModel.js')
const { ValidationRules, Constants } = require('../types/index.js')

class User extends BaseModel {
  /**
   * 初始化用户属性
   * @param {Object} data - 初始数据
   */
  initializeProperties(data) {
    // 基本信息
    this.phone = data.phone || ''
    this.nickname = data.nickname || ''
    this.avatar = data.avatar || ''
    this.gender = data.gender || Constants.GENDERS.OTHER
    this.birthday = data.birthday ? new Date(data.birthday) : null
    this.height = data.height || null // 身高(cm)
    this.weight = data.weight || null // 体重(kg)
    
    // 认证信息
    this.passwordHash = data.passwordHash || ''
    this.salt = data.salt || ''
    this.lastLoginAt = data.lastLoginAt ? new Date(data.lastLoginAt) : null
    this.loginCount = data.loginCount || 0
    
    // 用户设置
    this.settings = {
      biometricEnabled: false,
      autoSync: true,
      notificationEnabled: true,
      theme: Constants.THEMES.AUTO,
      language: 'zh-CN',
      ...data.settings
    }
    
    // 状态信息
    this.isActive = data.isActive !== false
    this.isVerified = data.isVerified || false
    this.verificationToken = data.verificationToken || ''
  }
  
  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      phone: {
        required: true,
        type: 'string',
        pattern: ValidationRules.phone.pattern,
        message: ValidationRules.phone.message
      },
      nickname: {
        required: false,
        type: 'string',
        minLength: ValidationRules.nickname.minLength,
        maxLength: ValidationRules.nickname.maxLength,
        pattern: ValidationRules.nickname.pattern,
        message: ValidationRules.nickname.message
      },
      gender: {
        required: false,
        type: 'string',
        validator: (value) => {
          if (value && !Object.values(Constants.GENDERS).includes(value)) {
            return '性别值无效'
          }
          return true
        }
      },
      height: {
        required: false,
        type: 'number',
        validator: (value) => {
          if (value !== null && (value < 50 || value > 300)) {
            return '身高应在50-300cm之间'
          }
          return true
        }
      },
      weight: {
        required: false,
        type: 'number',
        validator: (value) => {
          if (value !== null && (value < 10 || value > 500)) {
            return '体重应在10-500kg之间'
          }
          return true
        }
      },
      birthday: {
        required: false,
        validator: (value) => {
          if (value && !(value instanceof Date)) {
            return '生日格式不正确'
          }
          if (value && value > new Date()) {
            return '生日不能是未来日期'
          }
          return true
        }
      }
    }
  }
  
  /**
   * 验证手机号格式
   * @param {String} phone - 手机号
   * @returns {Boolean} 是否有效
   */
  static validatePhone(phone) {
    return ValidationRules.phone.pattern.test(phone)
  }
  
  /**
   * 验证密码强度
   * @param {String} password - 密码
   * @returns {Object} 验证结果
   */
  static validatePassword(password) {
    const result = {
      isValid: false,
      strength: 'weak',
      errors: []
    }
    
    if (!password) {
      result.errors.push('密码不能为空')
      return result
    }
    
    if (password.length < ValidationRules.password.minLength) {
      result.errors.push(`密码长度不能少于${ValidationRules.password.minLength}位`)
    }
    
    if (password.length > ValidationRules.password.maxLength) {
      result.errors.push(`密码长度不能超过${ValidationRules.password.maxLength}位`)
    }
    
    if (!ValidationRules.password.pattern.test(password)) {
      result.errors.push(ValidationRules.password.message)
    }
    
    if (result.errors.length === 0) {
      result.isValid = true
      result.strength = this.calculatePasswordStrength(password)
    }
    
    return result
  }
  
  /**
   * 计算密码强度
   * @param {String} password - 密码
   * @returns {String} 强度等级
   */
  static calculatePasswordStrength(password) {
    let score = 0
    
    // 长度加分
    if (password.length >= 8) score += 1
    if (password.length >= 12) score += 1
    
    // 字符类型加分
    if (/[a-z]/.test(password)) score += 1
    if (/[A-Z]/.test(password)) score += 1
    if (/\d/.test(password)) score += 1
    if (/[^a-zA-Z\d]/.test(password)) score += 1
    
    if (score <= 2) return 'weak'
    if (score <= 4) return 'medium'
    return 'strong'
  }
  
  /**
   * 设置密码
   * @param {String} password - 明文密码
   */
  setPassword(password) {
    const validation = User.validatePassword(password)
    if (!validation.isValid) {
      throw new Error(`密码验证失败: ${validation.errors.join(', ')}`)
    }
    
    this.salt = this.generateSalt()
    this.passwordHash = this.hashPassword(password, this.salt)
  }
  
  /**
   * 验证密码
   * @param {String} password - 明文密码
   * @returns {Boolean} 是否匹配
   */
  verifyPassword(password) {
    if (!this.passwordHash || !this.salt) {
      return false
    }
    
    const hashedPassword = this.hashPassword(password, this.salt)
    return hashedPassword === this.passwordHash
  }
  
  /**
   * 生成盐值
   * @returns {String} 盐值
   */
  generateSalt() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let salt = ''
    for (let i = 0; i < 16; i++) {
      salt += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return salt
  }
  
  /**
   * 哈希密码
   * @param {String} password - 明文密码
   * @param {String} salt - 盐值
   * @returns {String} 哈希后的密码
   */
  hashPassword(password, salt) {
    // 简单的哈希实现，实际项目中应使用更安全的算法如bcrypt
    let hash = 0
    const str = password + salt
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }
  
  /**
   * 更新用户设置
   * @param {Object} newSettings - 新设置
   */
  updateSettings(newSettings) {
    this.settings = {
      ...this.settings,
      ...newSettings
    }
    this.updatedAt = new Date()
  }
  
  /**
   * 记录登录
   */
  recordLogin() {
    this.lastLoginAt = new Date()
    this.loginCount += 1
    this.updatedAt = new Date()
  }
  
  /**
   * 计算年龄
   * @returns {Number|null} 年龄
   */
  getAge() {
    if (!this.birthday) return null
    
    const today = new Date()
    const birthDate = new Date(this.birthday)
    let age = today.getFullYear() - birthDate.getFullYear()
    
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    
    return age
  }
  
  /**
   * 计算BMI
   * @returns {Number|null} BMI值
   */
  getBMI() {
    if (!this.height || !this.weight) return null
    
    const heightInMeters = this.height / 100
    return Math.round((this.weight / (heightInMeters * heightInMeters)) * 10) / 10
  }
  
  /**
   * 获取BMI分类
   * @returns {String|null} BMI分类
   */
  getBMICategory() {
    const bmi = this.getBMI()
    if (!bmi) return null
    
    if (bmi < 18.5) return 'underweight'
    if (bmi < 24) return 'normal'
    if (bmi < 28) return 'overweight'
    return 'obese'
  }
  
  /**
   * 获取显示名称
   * @returns {String} 显示名称
   */
  getDisplayName() {
    if (this.nickname) return this.nickname
    if (this.phone) return this.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    return '未知用户'
  }
  
  /**
   * 检查是否完善了基本信息
   * @returns {Boolean} 是否完善
   */
  isProfileComplete() {
    return !!(this.nickname && this.gender && this.birthday && this.height && this.weight)
  }
  
  /**
   * 获取可序列化的属性列表
   * @returns {Array} 属性名列表
   */
  getSerializableProperties() {
    return [
      'id', 'phone', 'nickname', 'avatar', 'gender', 'birthday',
      'height', 'weight', 'settings', 'isActive', 'isVerified',
      'lastLoginAt', 'loginCount', 'createdAt', 'updatedAt'
    ]
  }
  
  /**
   * 转换为安全的JSON对象（不包含敏感信息）
   * @returns {Object} 安全的JSON对象
   */
  toSafeJSON() {
    const json = this.toJSON()
    
    // 移除敏感信息
    delete json.passwordHash
    delete json.salt
    delete json.verificationToken
    
    return json
  }
  
  /**
   * 从JSON创建用户实例
   * @param {Object} json - JSON对象
   * @returns {User} 用户实例
   */
  static fromJSON(json) {
    return new User(json)
  }
  
  /**
   * 创建新用户
   * @param {Object} userData - 用户数据
   * @returns {User} 用户实例
   */
  static create(userData) {
    const user = new User(userData)
    
    // 验证必填字段
    if (!user.phone) {
      throw new Error('手机号是必填项')
    }
    
    // 验证数据
    const validation = user.validate()
    if (!validation.isValid) {
      throw new Error(`用户数据验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
    }
    
    return user
  }
}

module.exports = { User }