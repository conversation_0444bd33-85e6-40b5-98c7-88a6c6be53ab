/**
 * DatabaseManager 单元测试
 * 测试数据库连接和管理功能
 */

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { DatabaseManager } = require('../../core/storage/DatabaseManager.js')
const { Constants } = require('../../types/index.js')

// Mock uni-app API
global.uni = {
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  getStorageInfoSync: jest.fn(() => ({ keys: [] }))
}

// Mock plus API
global.plus = {
  sqlite: {
    openDatabase: jest.fn(),
    executeSql: jest.fn(),
    closeDatabase: jest.fn()
  }
}

// Mock IndexedDB
global.indexedDB = {
  open: jest.fn()
}

// Mock logger
jest.mock('../../core/logger/Logger.js', () => ({
  logger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}))

// Mock errorHandler
jest.mock('../../core/errors/ErrorHandler.js', () => ({
  errorHandler: {
    createError: jest.fn((type, message, options) => {
      const error = new Error(message)
      error.type = type
      Object.assign(error, options)
      return error
    })
  }
}))

describe('DatabaseManager', () => {
  let dbManager
  
  beforeEach(() => {
    jest.clearAllMocks()
    dbManager = new DatabaseManager({
      dbName: 'test.db',
      version: 1
    })
  })
  
  afterEach(async () => {
    if (dbManager && dbManager.isConnected()) {
      await dbManager.close()
    }
  })
  
  describe('构造函数', () => {
    it('应该正确初始化默认选项', () => {
      const manager = new DatabaseManager()
      
      expect(manager.options.dbName).toBe('health_app.db')
      expect(manager.options.version).toBe(1)
      expect(manager.options.enableWAL).toBe(true)
      expect(manager.options.timeout).toBe(30000)
    })
    
    it('应该正确设置自定义选项', () => {
      const options = {
        dbName: 'custom.db',
        version: 2,
        enableWAL: false,
        timeout: 10000
      }
      
      const manager = new DatabaseManager(options)
      
      expect(manager.options.dbName).toBe('custom.db')
      expect(manager.options.version).toBe(2)
      expect(manager.options.enableWAL).toBe(false)
      expect(manager.options.timeout).toBe(10000)
    })
  })
  
  describe('平台检测', () => {
    it('应该正确检测平台类型', () => {
      expect(dbManager.platform).toBe('unknown') // 在测试环境中
    })
  })
  
  describe('内存数据库初始化', () => {
    it('应该成功初始化内存数据库', async () => {
      await dbManager.initialize()
      
      expect(dbManager.isInitialized).toBe(true)
      expect(dbManager.connection).toBeDefined()
      expect(dbManager.connection.type).toBe('memory')
      expect(dbManager.connection.data).toBeInstanceOf(Map)
    })
  })
  
  describe('迁移管理', () => {
    beforeEach(async () => {
      await dbManager.initialize()
    })
    
    it('应该获取迁移脚本列表', () => {
      const migrations = dbManager.getMigrationScripts()
      
      expect(Array.isArray(migrations)).toBe(true)
      expect(migrations.length).toBeGreaterThan(0)
      
      migrations.forEach(migration => {
        expect(migration).toHaveProperty('version')
        expect(migration).toHaveProperty('description')
        expect(migration).toHaveProperty('up')
        expect(typeof migration.up).toBe('function')
      })
    })
    
    it('应该正确执行迁移', async () => {
      const mockMigration = {
        version: 'test_001',
        description: '测试迁移',
        up: jest.fn().mockResolvedValue()
      }
      
      await dbManager.executeMigration(mockMigration)
      
      expect(mockMigration.up).toHaveBeenCalled()
      expect(dbManager.migrationHistory).toHaveLength(1)
      expect(dbManager.migrationHistory[0].version).toBe('test_001')
    })
    
    it('应该处理迁移执行失败', async () => {
      const mockMigration = {
        version: 'test_002',
        description: '失败的迁移',
        up: jest.fn().mockRejectedValue(new Error('迁移失败'))
      }
      
      await expect(dbManager.executeMigration(mockMigration)).rejects.toThrow('迁移失败')
    })
  })
  
  describe('连接管理', () => {
    it('应该正确报告连接状态', async () => {
      expect(dbManager.isConnected()).toBe(false)
      
      await dbManager.initialize()
      
      expect(dbManager.isConnected()).toBe(true)
    })
    
    it('应该正确关闭连接', async () => {
      await dbManager.initialize()
      expect(dbManager.isConnected()).toBe(true)
      
      await dbManager.close()
      
      expect(dbManager.isConnected()).toBe(false)
      expect(dbManager.connection).toBeNull()
      expect(dbManager.isInitialized).toBe(false)
    })
    
    it('应该获取数据库信息', async () => {
      await dbManager.initialize()
      
      const info = dbManager.getDatabaseInfo()
      
      expect(info).toHaveProperty('platform')
      expect(info).toHaveProperty('dbName')
      expect(info).toHaveProperty('version')
      expect(info).toHaveProperty('isInitialized')
      expect(info).toHaveProperty('isConnected')
      expect(info).toHaveProperty('migrationHistory')
      
      expect(info.dbName).toBe('test.db')
      expect(info.version).toBe(1)
      expect(info.isInitialized).toBe(true)
      expect(info.isConnected).toBe(true)
    })
  })
  
  describe('健康检查', () => {
    it('应该执行健康检查', async () => {
      await dbManager.initialize()
      
      const healthResult = await dbManager.healthCheck()
      
      expect(healthResult).toHaveProperty('status')
      expect(healthResult).toHaveProperty('checks')
      expect(healthResult).toHaveProperty('timestamp')
      
      expect(healthResult.checks.connection).toBeDefined()
      expect(healthResult.checks.connection.status).toBe('pass')
    })
    
    it('应该检测不健康的连接', async () => {
      // 不初始化数据库
      const healthResult = await dbManager.healthCheck()
      
      expect(healthResult.checks.connection.status).toBe('fail')
    })
  })
  
  describe('错误处理', () => {
    it('应该处理初始化错误', async () => {
      // Mock一个会失败的初始化
      const manager = new DatabaseManager()
      manager.initializeMemoryDatabase = jest.fn().mockRejectedValue(new Error('初始化失败'))
      
      await expect(manager.initialize()).rejects.toThrow()
    })
    
    it('应该处理重复初始化', async () => {
      await dbManager.initialize()
      
      // 第二次初始化应该直接返回，不会重复执行
      const initSpy = jest.spyOn(dbManager, 'initializeMemoryDatabase')
      
      await dbManager.initialize()
      
      expect(initSpy).not.toHaveBeenCalled()
    })
  })
  
  describe('小程序存储模拟', () => {
    let mpManager
    
    beforeEach(() => {
      // 模拟小程序环境
      mpManager = new DatabaseManager()
      mpManager.platform = 'mp-weixin'
    })
    
    it('应该初始化小程序存储', async () => {
      await mpManager.initialize()
      
      expect(mpManager.isInitialized).toBe(true)
      expect(mpManager.connection.type).toBe('mp-storage')
    })
    
    it('应该从小程序存储获取迁移历史', async () => {
      const mockMigrations = ['001_initial', '002_indexes']
      uni.getStorageSync.mockReturnValue(mockMigrations)
      
      const migrations = await mpManager.getExecutedMigrationsFromMP()
      
      expect(migrations).toEqual(mockMigrations)
      expect(uni.getStorageSync).toHaveBeenCalledWith('db_migrations')
    })
    
    it('应该记录小程序迁移', async () => {
      const existingMigrations = ['001_initial']
      uni.getStorageSync.mockReturnValue(existingMigrations)
      
      await mpManager.recordMigration('002_indexes')
      
      expect(uni.setStorageSync).toHaveBeenCalledWith('db_migrations', [
        '001_initial',
        '002_indexes'
      ])
    })
  })
  
  describe('IndexedDB模拟', () => {
    let h5Manager
    
    beforeEach(() => {
      h5Manager = new DatabaseManager()
      h5Manager.platform = 'h5'
    })
    
    it('应该处理IndexedDB初始化成功', async () => {
      const mockDB = {
        objectStoreNames: { contains: jest.fn().mockReturnValue(false) },
        createObjectStore: jest.fn().mockReturnValue({
          createIndex: jest.fn()
        }),
        onversionchange: null
      }
      
      const mockRequest = {
        result: mockDB,
        error: null,
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null
      }
      
      indexedDB.open.mockReturnValue(mockRequest)
      
      const initPromise = h5Manager.initializeH5Database()
      
      // 模拟成功事件
      mockRequest.onsuccess()
      
      await initPromise
      
      expect(h5Manager.connection).toBe(mockDB)
    })
    
    it('应该处理IndexedDB初始化失败', async () => {
      const mockRequest = {
        error: new Error('IndexedDB不可用'),
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null
      }
      
      indexedDB.open.mockReturnValue(mockRequest)
      
      const initPromise = h5Manager.initializeH5Database()
      
      // 模拟错误事件
      mockRequest.onerror()
      
      await expect(initPromise).rejects.toThrow('IndexedDB连接失败')
    })
  })
  
  describe('SQLite模拟', () => {
    let appManager
    
    beforeEach(() => {
      appManager = new DatabaseManager()
      appManager.platform = 'app-plus'
    })
    
    it('应该处理SQLite初始化成功', async () => {
      plus.sqlite.openDatabase.mockImplementation(({ success }) => {
        success()
      })
      
      // Mock executeSql for PRAGMA statements
      appManager.executeSql = jest.fn().mockResolvedValue()
      
      await appManager.initializeAppDatabase()
      
      expect(plus.sqlite.openDatabase).toHaveBeenCalledWith({
        name: 'health_app.db',
        path: '_doc/health_app.db',
        success: expect.any(Function),
        fail: expect.any(Function)
      })
    })
    
    it('应该处理SQLite初始化失败', async () => {
      const mockError = { code: -1, message: 'SQLite错误' }
      plus.sqlite.openDatabase.mockImplementation(({ fail }) => {
        fail(mockError)
      })
      
      await expect(appManager.initializeAppDatabase()).rejects.toThrow('SQLite连接失败')
    })
    
    it('应该执行SQL语句', async () => {
      appManager.options.dbName = 'test.db'
      
      const mockResult = { rowsAffected: 1 }
      plus.sqlite.executeSql.mockImplementation(({ success }) => {
        success(mockResult)
      })
      
      const result = await appManager.executeSql('SELECT 1', [])
      
      expect(result).toBe(mockResult)
      expect(plus.sqlite.executeSql).toHaveBeenCalledWith({
        name: 'test.db',
        sql: 'SELECT 1',
        success: expect.any(Function),
        fail: expect.any(Function)
      })
    })
    
    it('应该处理SQL执行超时', async () => {
      appManager.options.timeout = 100 // 设置短超时时间
      
      plus.sqlite.executeSql.mockImplementation(() => {
        // 不调用success或fail，模拟超时
      })
      
      await expect(appManager.executeSql('SELECT 1')).rejects.toThrow('SQL执行超时')
    })
  })
})