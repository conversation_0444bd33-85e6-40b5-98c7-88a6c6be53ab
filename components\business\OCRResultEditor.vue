<template>
  <view class="ocr-result-editor">
    <!-- 头部信息 -->
    <view class="header-section">
      <view class="confidence-indicator">
        <text class="confidence-label">识别置信度:</text>
        <view class="confidence-bar">
          <view 
            class="confidence-fill" 
            :style="{ width: confidence + '%', backgroundColor: getConfidenceColor(confidence) }"
          ></view>
        </view>
        <text class="confidence-text">{{ confidence }}%</text>
      </view>
      
      <view v-if="!isValid" class="validation-warning">
        <text class="warning-icon">⚠️</text>
        <text class="warning-text">识别结果可能不准确，请仔细检查并修正</text>
      </view>
    </view>

    <!-- 基本信息编辑 -->
    <view class="basic-info-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-item">
        <text class="label">检查日期</text>
        <input 
          class="input-field"
          type="text"
          placeholder="请输入检查日期 (YYYY-MM-DD)"
          v-model="editableData.date"
          @blur="validateDate"
        />
        <text v-if="dateError" class="error-text">{{ dateError }}</text>
      </view>

      <view class="form-item">
        <text class="label">医院名称</text>
        <input 
          class="input-field"
          type="text"
          placeholder="请输入医院名称"
          v-model="editableData.hospital"
        />
      </view>

      <view class="form-item">
        <text class="label">医生姓名</text>
        <input 
          class="input-field"
          type="text"
          placeholder="请输入医生姓名"
          v-model="editableData.doctor"
        />
      </view>
    </view>

    <!-- 检查项目编辑 -->
    <view class="items-section">
      <view class="section-title">
        <text>检查项目</text>
        <button class="add-item-btn" @click="addNewItem">+ 添加项目</button>
      </view>

      <view v-if="editableData.items.length === 0" class="empty-items">
        <text class="empty-text">暂无检查项目，请点击"添加项目"手动添加</text>
      </view>

      <view v-else class="items-list">
        <view 
          v-for="(item, index) in editableData.items" 
          :key="index"
          class="item-card"
          :class="{ 'abnormal': item.isAbnormal }"
        >
          <view class="item-header">
            <text class="item-index">{{ index + 1 }}</text>
            <button class="delete-btn" @click="deleteItem(index)">删除</button>
          </view>

          <view class="item-content">
            <view class="form-row">
              <view class="form-col">
                <text class="label">项目名称</text>
                <input 
                  class="input-field"
                  type="text"
                  placeholder="如：血红蛋白"
                  v-model="item.name"
                  @input="validateItem(index)"
                />
              </view>
              
              <view class="form-col">
                <text class="label">检查结果</text>
                <input 
                  class="input-field"
                  type="text"
                  placeholder="如：120"
                  v-model="item.value"
                  @input="validateItem(index)"
                />
              </view>
              
              <view class="form-col">
                <text class="label">单位</text>
                <input 
                  class="input-field"
                  type="text"
                  placeholder="如：g/L"
                  v-model="item.unit"
                />
              </view>
            </view>

            <view class="form-row">
              <view class="form-col">
                <text class="label">参考值下限</text>
                <input 
                  class="input-field"
                  type="number"
                  placeholder="如：110"
                  v-model="item.referenceRange.min"
                  @input="checkAbnormal(index)"
                />
              </view>
              
              <view class="form-col">
                <text class="label">参考值上限</text>
                <input 
                  class="input-field"
                  type="number"
                  placeholder="如：160"
                  v-model="item.referenceRange.max"
                  @input="checkAbnormal(index)"
                />
              </view>
              
              <view class="form-col abnormal-indicator">
                <text class="label">状态</text>
                <view class="status-badge" :class="{ 'abnormal': item.isAbnormal }">
                  {{ item.isAbnormal ? '异常' : '正常' }}
                </view>
              </view>
            </view>

            <view v-if="item.error" class="item-error">
              <text class="error-text">{{ item.error }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 原始识别文本 -->
    <view v-if="showRawText" class="raw-text-section">
      <view class="section-title">
        <text>原始识别文本</text>
        <button class="toggle-btn" @click="toggleRawText">
          {{ showRawTextContent ? '隐藏' : '显示' }}
        </button>
      </view>
      
      <view v-if="showRawTextContent" class="raw-text-content">
        <textarea 
          class="raw-text-area"
          :value="editableData.rawText"
          readonly
          placeholder="无原始文本"
        ></textarea>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-secondary" @click="resetData">重置</button>
      <button class="btn btn-primary" @click="saveData" :disabled="!canSave">保存</button>
    </view>

    <!-- 建议提示 -->
    <view v-if="suggestions.length > 0" class="suggestions-section">
      <view class="section-title">建议</view>
      <view class="suggestions-list">
        <view v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
          <text class="suggestion-icon">💡</text>
          <text class="suggestion-text">{{ suggestion }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'OCRResultEditor',
  props: {
    ocrResult: {
      type: Object,
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editableData: {
        items: [],
        date: '',
        hospital: '',
        doctor: '',
        rawText: ''
      },
      originalData: null,
      confidence: 0,
      isValid: true,
      suggestions: [],
      dateError: '',
      showRawText: true,
      showRawTextContent: false
    }
  },
  computed: {
    canSave() {
      return this.editableData.items.length > 0 && 
             this.editableData.date && 
             !this.dateError &&
             this.editableData.items.every(item => item.name && item.value)
    }
  },
  watch: {
    ocrResult: {
      handler(newResult) {
        this.initializeData(newResult)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 初始化数据
     */
    initializeData(result) {
      if (!result || !result.data) return

      const data = result.data
      
      this.editableData = {
        items: this.processItems(data.items || []),
        date: data.date || '',
        hospital: data.hospital || '',
        doctor: data.doctor || '',
        rawText: data.rawText || ''
      }

      this.originalData = JSON.parse(JSON.stringify(this.editableData))
      this.confidence = result.confidence || 0
      this.isValid = result.success && this.confidence >= 60
      this.suggestions = this.generateSuggestions(result)
    },

    /**
     * 处理检查项目数据
     */
    processItems(items) {
      return items.map(item => ({
        name: item.name || '',
        value: item.value || '',
        unit: item.unit || '',
        referenceRange: {
          min: item.referenceRange?.min || '',
          max: item.referenceRange?.max || ''
        },
        isAbnormal: item.isAbnormal || false,
        rawText: item.rawText || '',
        error: ''
      }))
    },

    /**
     * 生成建议
     */
    generateSuggestions(result) {
      const suggestions = []
      
      if (result.fallback) {
        suggestions.push(...result.fallback.suggestions)
      }
      
      if (this.confidence < 80) {
        suggestions.push('识别置信度较低，建议仔细检查所有字段')
      }
      
      if (!this.editableData.date) {
        suggestions.push('请补充检查日期信息')
      }
      
      if (!this.editableData.hospital) {
        suggestions.push('请补充医院名称信息')
      }
      
      if (this.editableData.items.length === 0) {
        suggestions.push('未识别到检查项目，请手动添加')
      }

      return suggestions
    },

    /**
     * 获取置信度颜色
     */
    getConfidenceColor(confidence) {
      if (confidence >= 80) return '#4CAF50'
      if (confidence >= 60) return '#FF9800'
      return '#F44336'
    },

    /**
     * 添加新项目
     */
    addNewItem() {
      this.editableData.items.push({
        name: '',
        value: '',
        unit: '',
        referenceRange: {
          min: '',
          max: ''
        },
        isAbnormal: false,
        rawText: '',
        error: ''
      })
    },

    /**
     * 删除项目
     */
    deleteItem(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个检查项目吗？',
        success: (res) => {
          if (res.confirm) {
            this.editableData.items.splice(index, 1)
          }
        }
      })
    },

    /**
     * 验证日期
     */
    validateDate() {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/
      if (!this.editableData.date) {
        this.dateError = '请输入检查日期'
        return false
      }
      
      if (!dateRegex.test(this.editableData.date)) {
        this.dateError = '日期格式不正确，请使用 YYYY-MM-DD 格式'
        return false
      }
      
      const date = new Date(this.editableData.date)
      if (isNaN(date.getTime())) {
        this.dateError = '无效的日期'
        return false
      }
      
      if (date > new Date()) {
        this.dateError = '检查日期不能是未来日期'
        return false
      }
      
      this.dateError = ''
      return true
    },

    /**
     * 验证项目
     */
    validateItem(index) {
      const item = this.editableData.items[index]
      
      if (!item.name || !item.value) {
        item.error = '项目名称和检查结果不能为空'
        return false
      }
      
      if (isNaN(parseFloat(item.value))) {
        item.error = '检查结果必须是数字'
        return false
      }
      
      item.error = ''
      this.checkAbnormal(index)
      return true
    },

    /**
     * 检查异常状态
     */
    checkAbnormal(index) {
      const item = this.editableData.items[index]
      const value = parseFloat(item.value)
      const min = parseFloat(item.referenceRange.min)
      const max = parseFloat(item.referenceRange.max)
      
      if (!isNaN(value) && !isNaN(min) && !isNaN(max)) {
        item.isAbnormal = value < min || value > max
      } else {
        item.isAbnormal = false
      }
    },

    /**
     * 切换原始文本显示
     */
    toggleRawText() {
      this.showRawTextContent = !this.showRawTextContent
    },

    /**
     * 重置数据
     */
    resetData() {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置所有修改吗？',
        success: (res) => {
          if (res.confirm) {
            this.editableData = JSON.parse(JSON.stringify(this.originalData))
            this.dateError = ''
          }
        }
      })
    },

    /**
     * 保存数据
     */
    saveData() {
      // 验证所有数据
      if (!this.validateDate()) {
        uni.showToast({
          title: '请检查日期格式',
          icon: 'none'
        })
        return
      }

      let hasError = false
      this.editableData.items.forEach((item, index) => {
        if (!this.validateItem(index)) {
          hasError = true
        }
      })

      if (hasError) {
        uni.showToast({
          title: '请检查项目信息',
          icon: 'none'
        })
        return
      }

      // 发送保存事件
      this.$emit('save', {
        data: this.editableData,
        confidence: this.confidence,
        isValid: this.isValid
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ocr-result-editor {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.confidence-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.confidence-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}

.confidence-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  margin-right: 16rpx;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.confidence-text {
  font-size: 28rpx;
  font-weight: bold;
}

.validation-warning {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #fff3cd;
  border-radius: 8rpx;
  border-left: 4rpx solid #ffc107;
}

.warning-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.warning-text {
  font-size: 26rpx;
  color: #856404;
}

.basic-info-section,
.items-section,
.raw-text-section,
.suggestions-section {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.add-item-btn,
.toggle-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.input-field {
  width: 100%;
  padding: 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fafafa;
}

.input-field:focus {
  border-color: #007aff;
  background-color: white;
}

.error-text {
  color: #f44336;
  font-size: 24rpx;
  margin-top: 8rpx;
}

.empty-items {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.items-list {
  .item-card {
    border: 2rpx solid #e0e0e0;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    
    &.abnormal {
      border-color: #f44336;
      background-color: #ffebee;
    }
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e0e0e0;
}

.item-index {
  font-size: 24rpx;
  color: #666;
  background-color: #007aff;
  color: white;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 6rpx 12rpx;
  font-size: 22rpx;
}

.item-content {
  padding: 20rpx;
}

.form-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.form-col {
  flex: 1;
}

.abnormal-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background-color: #4caf50;
  color: white;
  
  &.abnormal {
    background-color: #f44336;
  }
}

.item-error {
  margin-top: 12rpx;
  padding: 12rpx;
  background-color: #ffebee;
  border-radius: 6rpx;
}

.raw-text-area {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
  background-color: #fafafa;
  resize: vertical;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 24rpx;
}

.btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  text-align: center;
  border: none;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  
  &:disabled {
    background-color: #ccc;
    color: #999;
  }
}

.suggestions-list {
  .suggestion-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;
    padding: 12rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
  }
}

.suggestion-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.suggestion-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
</style>