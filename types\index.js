/**
 * 核心数据类型定义
 * 为整个应用提供统一的类型接口
 */

// 用户相关类型
const UserTypes = {
  // 用户基本信息
  UserInfo: {
    id: String,
    phone: String,
    nickname: String,
    avatar: String,
    gender: String, // 'male' | 'female' | 'other'
    birthday: Date,
    height: Number,
    weight: Number,
    createdAt: Date,
    updatedAt: Date,
    settings: Object
  },
  
  // 用户设置
  UserSettings: {
    biometricEnabled: Boolean,
    autoSync: Boolean,
    notificationEnabled: Boolean,
    theme: String, // 'light' | 'dark' | 'auto'
    language: String
  },
  
  // 登录凭据
  LoginCredentials: {
    phone: String,
    password: String,
    verificationCode: String
  }
}

// 报告相关类型
const ReportTypes = {
  // 报告主体
  Report: {
    id: String,
    userId: String,
    title: String,
    hospital: String,
    doctor: String,
    checkDate: Date,
    reportDate: Date,
    originalImage: String,
    items: Array,
    tags: Array,
    notes: String,
    createdAt: Date,
    updatedAt: Date,
    syncStatus: String // 'local' | 'synced' | 'pending'
  },
  
  // 检查项目
  ReportItem: {
    id: String,
    name: String,
    value: String,
    unit: String,
    referenceRange: String,
    isAbnormal: Boolean,
    category: String
  },
  
  // 报告筛选条件
  ReportFilter: {
    startDate: Date,
    endDate: Date,
    categories: Array,
    hospitals: Array,
    isAbnormal: Boolean
  }
}

// 分析相关类型
const AnalysisTypes = {
  // 分析结果
  Analysis: {
    id: String,
    userId: String,
    type: String, // 'trend' | 'comparison' | 'summary'
    timeRange: Object,
    items: Array,
    results: Object,
    createdAt: Date
  },
  
  // 趋势数据
  TrendData: {
    item: String,
    data: Array,
    trend: String // 'up' | 'down' | 'stable'
  },
  
  // 数据点
  DataPoint: {
    date: Date,
    value: Number,
    isAbnormal: Boolean
  }
}

// 系统相关类型
const SystemTypes = {
  // API响应
  ApiResponse: {
    success: Boolean,
    data: Object,
    message: String,
    code: Number
  },
  
  // 错误信息
  ErrorInfo: {
    type: String,
    message: String,
    code: String,
    stack: String,
    context: Object
  },
  
  // 网络状态
  NetworkStatus: {
    isOnline: Boolean,
    networkType: String
  }
}

// 常量定义
const Constants = {
  // 错误类型
  ERROR_TYPES: {
    NETWORK_ERROR: 'network_error',
    AUTH_ERROR: 'auth_error',
    VALIDATION_ERROR: 'validation_error',
    OCR_ERROR: 'ocr_error',
    STORAGE_ERROR: 'storage_error',
    PERMISSION_ERROR: 'permission_error',
    UNKNOWN_ERROR: 'unknown_error'
  },
  
  // 同步状态
  SYNC_STATUS: {
    LOCAL: 'local',
    SYNCED: 'synced',
    PENDING: 'pending',
    FAILED: 'failed'
  },
  
  // 主题类型
  THEMES: {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
  },
  
  // 性别类型
  GENDERS: {
    MALE: 'male',
    FEMALE: 'female',
    OTHER: 'other'
  },
  
  // 报告分类
  REPORT_CATEGORIES: {
    BLOOD_ROUTINE: 'blood_routine',
    BIOCHEMISTRY: 'biochemistry',
    IMMUNOLOGY: 'immunology',
    URINE_ROUTINE: 'urine_routine',
    IMAGING: 'imaging',
    OTHER: 'other'
  }
}

// 验证规则
const ValidationRules = {
  // 手机号验证
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },
  
  // 密码验证
  password: {
    minLength: 6,
    maxLength: 20,
    pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,
    message: '密码必须包含字母和数字，长度6-20位'
  },
  
  // 验证码验证
  verificationCode: {
    pattern: /^\d{6}$/,
    message: '请输入6位数字验证码'
  },
  
  // 昵称验证
  nickname: {
    minLength: 1,
    maxLength: 20,
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
    message: '昵称只能包含中文、英文、数字、下划线和横线'
  }
}

module.exports = {
  UserTypes,
  ReportTypes,
  AnalysisTypes,
  SystemTypes,
  Constants,
  ValidationRules
}