<template>
  <view class="health-summary-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">健康分析报告</text>
      <text class="page-subtitle">基于您的检查数据生成</text>
    </view>

    <!-- 时间范围选择 -->
    <view class="time-range-selector">
      <view class="selector-item" @click="selectTimeRange('month')">
        <text :class="['selector-text', { active: timeRange === 'month' }]">近一个月</text>
      </view>
      <view class="selector-item" @click="selectTimeRange('quarter')">
        <text :class="['selector-text', { active: timeRange === 'quarter' }]">近三个月</text>
      </view>
      <view class="selector-item" @click="selectTimeRange('year')">
        <text :class="['selector-text', { active: timeRange === 'year' }]">近一年</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">正在分析健康数据...</text>
    </view>

    <!-- 健康摘要 -->
    <view v-else-if="healthSummary" class="summary-container">
      <!-- 总体概况 -->
      <view class="summary-card">
        <view class="card-header">
          <text class="card-title">总体概况</text>
          <view :class="['risk-badge', healthSummary.riskLevel]">
            <text class="risk-text">{{ getRiskLevelText(healthSummary.riskLevel) }}</text>
          </view>
        </view>
        <view class="summary-stats">
          <view class="stat-item">
            <text class="stat-number">{{ healthSummary.totalReports }}</text>
            <text class="stat-label">检查次数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ healthSummary.abnormalReports }}</text>
            <text class="stat-label">异常报告</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ Object.keys(healthSummary.categoryStats).length }}</text>
            <text class="stat-label">检查类别</text>
          </view>
        </view>
        <view class="summary-text">
          <text>{{ healthSummary.summary }}</text>
        </view>
      </view>

      <!-- 异常指标分析 -->
      <view v-if="alerts.length > 0" class="alerts-card">
        <view class="card-header">
          <text class="card-title">异常指标提醒</text>
          <text class="alert-count">{{ alerts.length }}项</text>
        </view>
        <view class="alerts-list">
          <view 
            v-for="alert in alerts" 
            :key="alert.id"
            :class="['alert-item', alert.severity]"
          >
            <view class="alert-header">
              <text class="alert-title">{{ alert.title }}</text>
              <text :class="['alert-priority', alert.severity]">
                {{ getSeverityText(alert.severity) }}
              </text>
            </view>
            <text class="alert-message">{{ alert.message }}</text>
            <text class="alert-suggestion">{{ alert.suggestion }}</text>
          </view>
        </view>
      </view>

      <!-- 健康建议 -->
      <view v-if="healthSummary.recommendations.length > 0" class="recommendations-card">
        <view class="card-header">
          <text class="card-title">健康建议</text>
        </view>
        <view class="recommendations-list">
          <view 
            v-for="(recommendation, index) in healthSummary.recommendations" 
            :key="index"
            class="recommendation-item"
          >
            <view class="recommendation-icon">💡</view>
            <text class="recommendation-text">{{ recommendation }}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="action-btn primary" @click="generateReport">
          <text class="btn-text">生成PDF报告</text>
        </button>
        <button class="action-btn secondary" @click="shareReport">
          <text class="btn-text">分享报告</text>
        </button>
      </view>
    </view>

    <!-- 无数据状态 -->
    <view v-else class="empty-state">
      <text class="empty-title">暂无健康数据</text>
      <text class="empty-subtitle">请先添加健康检查报告</text>
      <button class="empty-btn" @click="goToAddReport">
        <text class="btn-text">添加报告</text>
      </button>
    </view>

    <!-- 分享弹窗 -->
    <view v-if="showShareModal" class="share-modal" @click="closeShareModal">
      <view class="share-content" @click.stop>
        <view class="share-header">
          <text class="share-title">分享健康报告</text>
          <text class="share-close" @click="closeShareModal">×</text>
        </view>
        <view class="share-options">
          <view 
            v-for="platform in availablePlatforms" 
            :key="platform.id"
            class="share-option"
            @click="shareToplatform(platform.id)"
          >
            <image class="share-icon" :src="platform.icon" mode="aspectFit" />
            <text class="share-name">{{ platform.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useReportStore } from '../../stores/report.js'
import HealthAnalyticsService from '../../services/analytics/index.js'
import AlertService from '../../services/analytics/alertService.js'
import ReportGeneratorService from '../../services/analytics/reportGenerator.js'
import ShareService from '../../services/analytics/shareService.js'

export default {
  name: 'HealthSummary',
  data() {
    return {
      timeRange: 'quarter', // month, quarter, year
      loading: false,
      healthSummary: null,
      alerts: [],
      showShareModal: false,
      availablePlatforms: []
    }
  },
  computed: {
    reportStore() {
      return useReportStore()
    }
  },
  onLoad() {
    this.loadHealthSummary()
    this.loadAvailablePlatforms()
  },
  methods: {
    /**
     * 选择时间范围
     */
    selectTimeRange(range) {
      this.timeRange = range
      this.loadHealthSummary()
    },

    /**
     * 加载健康摘要
     */
    async loadHealthSummary() {
      try {
        this.loading = true
        
        // 获取时间范围
        const timeRangeData = this.getTimeRangeData()
        
        // 获取报告数据
        const reports = this.reportStore.reports.filter(report => {
          const reportDate = new Date(report.reportDate)
          return reportDate >= timeRangeData.start && reportDate <= timeRangeData.end
        })

        if (reports.length === 0) {
          this.healthSummary = null
          this.alerts = []
          return
        }

        // 分析健康数据
        const analyzedReports = reports.map(report => 
          HealthAnalyticsService.analyzeReport(report)
        )

        // 生成健康摘要
        this.healthSummary = HealthAnalyticsService.generateHealthSummary(
          analyzedReports, 
          timeRangeData
        )

        // 检测异常指标
        this.alerts = AlertService.detectAbnormalIndicators(reports)

      } catch (error) {
        console.error('加载健康摘要失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取时间范围数据
     */
    getTimeRangeData() {
      const end = new Date()
      const start = new Date()

      switch (this.timeRange) {
        case 'month':
          start.setMonth(start.getMonth() - 1)
          break
        case 'quarter':
          start.setMonth(start.getMonth() - 3)
          break
        case 'year':
          start.setFullYear(start.getFullYear() - 1)
          break
      }

      return { start, end }
    },

    /**
     * 获取风险等级文本
     */
    getRiskLevelText(level) {
      const texts = {
        low: '低风险',
        medium: '中风险',
        high: '高风险'
      }
      return texts[level] || '未知'
    },

    /**
     * 获取严重程度文本
     */
    getSeverityText(severity) {
      const texts = {
        low: '轻微',
        medium: '中等',
        high: '严重'
      }
      return texts[severity] || '未知'
    },

    /**
     * 生成PDF报告
     */
    async generateReport() {
      try {
        uni.showLoading({ title: '生成中...' })

        const timeRangeData = this.getTimeRangeData()
        const reports = this.reportStore.reports.filter(report => {
          const reportDate = new Date(report.reportDate)
          return reportDate >= timeRangeData.start && reportDate <= timeRangeData.end
        })

        const reportData = {
          reports,
          timeRange: timeRangeData
        }

        const pdfPath = await ReportGeneratorService.generatePDFReport(reportData)
        
        uni.hideLoading()
        uni.showToast({
          title: '报告生成成功',
          icon: 'success'
        })

        // 可以在这里处理PDF文件，比如预览或分享
        console.log('PDF报告路径:', pdfPath)

      } catch (error) {
        uni.hideLoading()
        console.error('生成报告失败:', error)
        uni.showToast({
          title: '生成失败',
          icon: 'error'
        })
      }
    },

    /**
     * 分享报告
     */
    shareReport() {
      this.showShareModal = true
    },

    /**
     * 加载可用分享平台
     */
    loadAvailablePlatforms() {
      this.availablePlatforms = ShareService.getAvailablePlatforms()
    },

    /**
     * 分享到指定平台
     */
    async shareToplatform(platformId) {
      try {
        this.closeShareModal()
        uni.showLoading({ title: '准备分享...' })

        const timeRangeData = this.getTimeRangeData()
        const reports = this.reportStore.reports.filter(report => {
          const reportDate = new Date(report.reportDate)
          return reportDate >= timeRangeData.start && reportDate <= timeRangeData.end
        })

        const reportData = {
          reports,
          timeRange: timeRangeData
        }

        const success = await ShareService.shareHealthReport(reportData, {
          platform: platformId,
          type: 'text',
          includeImage: true
        })

        uni.hideLoading()
        
        if (success) {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '分享失败',
            icon: 'error'
          })
        }

      } catch (error) {
        uni.hideLoading()
        console.error('分享失败:', error)
        uni.showToast({
          title: '分享失败',
          icon: 'error'
        })
      }
    },

    /**
     * 关闭分享弹窗
     */
    closeShareModal() {
      this.showShareModal = false
    },

    /**
     * 跳转到添加报告页面
     */
    goToAddReport() {
      uni.navigateTo({
        url: '/pages/report/add'
      })
    }
  }
}
</script>

<style scoped>
.health-summary-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2E7D32;
  display: block;
}

.page-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  display: block;
}

.time-range-selector {
  display: flex;
  background: white;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
}

.selector-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 8rpx;
}

.selector-text {
  font-size: 28rpx;
  color: #666;
}

.selector-text.active {
  color: #2E7D32;
  font-weight: bold;
  background-color: #E8F5E8;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
}

.loading-container {
  text-align: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.summary-container {
  padding-bottom: 40rpx;
}

.summary-card,
.alerts-card,
.recommendations-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.risk-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.risk-badge.low {
  background-color: #E8F5E8;
  color: #2E7D32;
}

.risk-badge.medium {
  background-color: #FFF3E0;
  color: #F57C00;
}

.risk-badge.high {
  background-color: #FFEBEE;
  color: #D32F2F;
}

.risk-text {
  font-size: 24rpx;
  font-weight: bold;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #2E7D32;
  display: block;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  display: block;
}

.summary-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.alert-count {
  background-color: #FF5722;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.alerts-list {
  space-y: 20rpx;
}

.alert-item {
  border-left: 6rpx solid;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.alert-item.low {
  border-left-color: #FFC107;
}

.alert-item.medium {
  border-left-color: #FF9800;
}

.alert-item.high {
  border-left-color: #F44336;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.alert-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.alert-priority {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.alert-priority.low {
  background-color: #FFF3E0;
  color: #F57C00;
}

.alert-priority.medium {
  background-color: #FFEBEE;
  color: #E91E63;
}

.alert-priority.high {
  background-color: #FFCDD2;
  color: #D32F2F;
}

.alert-message {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.alert-suggestion {
  font-size: 24rpx;
  color: #2E7D32;
  display: block;
}

.recommendations-list {
  space-y: 16rpx;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.recommendation-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.recommendation-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
}

.action-btn.primary {
  background-color: #2E7D32;
  color: white;
}

.action-btn.secondary {
  background-color: white;
  color: #2E7D32;
  border: 2rpx solid #2E7D32;
}

.btn-text {
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  display: block;
}

.empty-btn {
  background-color: #2E7D32;
  color: white;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
}

.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.share-content {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;
  width: 100%;
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.share-close {
  font-size: 48rpx;
  color: #666;
  line-height: 1;
}

.share-options {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  min-width: 120rpx;
}

.share-option:active {
  background-color: #f5f5f5;
}

.share-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 12rpx;
}

.share-name {
  font-size: 24rpx;
  color: #333;
}
</style>