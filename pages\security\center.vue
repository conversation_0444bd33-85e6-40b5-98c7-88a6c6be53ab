<template>
	<view class="page-container">
		<view class="security-container">
			<!-- 安全状态概览 -->
			<view class="security-overview">
				<view class="overview-header">
					<text class="overview-title">安全状态</text>
					<view class="security-score" :class="scoreClass">
						<text class="score-number">{{ securityScore }}</text>
						<text class="score-label">分</text>
					</view>
				</view>
				<text class="overview-desc">{{ securityStatusText }}</text>
			</view>
			
			<!-- 安全检查项 -->
			<view class="security-checks">
				<view class="section-title">
					<text>安全检查</text>
					<text class="check-time">最后检查：{{ lastCheckTime }}</text>
				</view>
				
				<view class="check-list">
					<view 
						class="check-item" 
						v-for="(check, index) in securityChecks" 
						:key="index"
						:class="check.status"
					>
						<view class="check-icon">
							<text class="icon">{{ check.icon }}</text>
						</view>
						<view class="check-content">
							<text class="check-name">{{ check.name }}</text>
							<text class="check-desc">{{ check.description }}</text>
						</view>
						<view class="check-status">
							<text class="status-text">{{ check.statusText }}</text>
						</view>
					</view>
				</view>
				
				<button class="btn btn-primary btn-block" @tap="performSecurityCheck">
					重新检查
				</button>
			</view>
			
			<!-- 安全提醒 -->
			<view class="security-alerts" v-if="securityAlerts.length > 0">
				<view class="section-title">
					<text>安全提醒</text>
					<text class="alert-count">{{ securityAlerts.length }}条</text>
				</view>
				
				<view class="alert-list">
					<view 
						class="alert-item" 
						v-for="(alert, index) in securityAlerts" 
						:key="alert.id"
						:class="alert.type"
						@tap="viewAlertDetail(alert)"
					>
						<view class="alert-icon">
							<text class="icon">{{ getAlertIcon(alert.type) }}</text>
						</view>
						<view class="alert-content">
							<text class="alert-title">{{ alert.title }}</text>
							<text class="alert-message">{{ alert.message }}</text>
							<text class="alert-time">{{ formatTime(alert.timestamp) }}</text>
						</view>
						<view class="alert-action" v-if="!alert.read">
							<view class="unread-dot"></view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 安全设置 -->
			<view class="security-settings">
				<view class="section-title">
					<text>安全设置</text>
				</view>
				
				<view class="setting-list">
					<view class="setting-item" @tap="navigateToLoginSecurity">
						<view class="setting-icon">🔐</view>
						<view class="setting-content">
							<text class="setting-name">登录安全</text>
							<text class="setting-desc">密码设置、登录保护</text>
						</view>
						<view class="setting-arrow">></view>
					</view>
					
					<view class="setting-item" @tap="navigateToDataProtection">
						<view class="setting-icon">🛡️</view>
						<view class="setting-content">
							<text class="setting-name">数据保护</text>
							<text class="setting-desc">数据加密、隐私设置</text>
						</view>
						<view class="setting-arrow">></view>
					</view>
					
					<view class="setting-item" @tap="navigateToDeviceSecurity">
						<view class="setting-icon">📱</view>
						<view class="setting-content">
							<text class="setting-name">设备安全</text>
							<text class="setting-desc">设备管理、异常检测</text>
						</view>
						<view class="setting-arrow">></view>
					</view>
					
					<view class="setting-item" @tap="navigateToPrivacyPolicy">
						<view class="setting-icon">📋</view>
						<view class="setting-content">
							<text class="setting-name">隐私政策</text>
							<text class="setting-desc">查看隐私政策和用户协议</text>
						</view>
						<view class="setting-arrow">></view>
					</view>
				</view>
			</view>
			
			<!-- 风险监控开关 -->
			<view class="monitoring-control">
				<view class="section-title">
					<text>风险监控</text>
				</view>
				
				<view class="control-item">
					<view class="control-info">
						<text class="control-name">实时风险监控</text>
						<text class="control-desc">自动检测和提醒安全风险</text>
					</view>
					<switch 
						:checked="monitoringEnabled" 
						@change="toggleMonitoring"
						color="#007AFF"
					/>
				</view>
				
				<view class="control-item">
					<view class="control-info">
						<text class="control-name">安全通知</text>
						<text class="control-desc">接收安全提醒和风险通知</text>
					</view>
					<switch 
						:checked="notificationEnabled" 
						@change="toggleNotification"
						color="#007AFF"
					/>
				</view>
			</view>
			
			<!-- 紧急操作 -->
			<view class="emergency-actions">
				<view class="section-title">
					<text>紧急操作</text>
				</view>
				
				<view class="action-buttons">
					<button class="btn btn-secondary" @tap="lockAccount">
						临时锁定账户
					</button>
					<button class="btn btn-danger" @tap="clearAllData">
						清除所有数据
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import riskMonitorManager from '../../utils/security/riskMonitor.js'
	import loginSecurityManager from '../../utils/security/loginSecurity.js'
	import dataCleanupManager from '../../utils/security/dataCleanup.js'
	import securityManager from '../../utils/security/encryption.js'
	
	export default {
		name: 'SecurityCenter',
		data() {
			return {
				securityScore: 85,
				lastCheckTime: '',
				securityChecks: [],
				securityAlerts: [],
				monitoringEnabled: true,
				notificationEnabled: true
			}
		},
		computed: {
			scoreClass() {
				if (this.securityScore >= 90) return 'score-excellent'
				if (this.securityScore >= 70) return 'score-good'
				if (this.securityScore >= 50) return 'score-fair'
				return 'score-poor'
			},
			
			securityStatusText() {
				if (this.securityScore >= 90) return '您的账户安全状况良好'
				if (this.securityScore >= 70) return '您的账户安全状况较好，建议关注安全提醒'
				if (this.securityScore >= 50) return '您的账户存在一些安全风险，建议及时处理'
				return '您的账户存在严重安全风险，请立即处理'
			}
		},
		onLoad() {
			console.log('安全中心页面加载')
			this.initSecurityCenter()
		},
		onShow() {
			this.refreshSecurityStatus()
		},
		methods: {
			// 初始化安全中心
			async initSecurityCenter() {
				try {
					// 加载安全检查结果
					await this.loadSecurityChecks()
					
					// 加载安全提醒
					this.loadSecurityAlerts()
					
					// 加载监控状态
					this.loadMonitoringStatus()
					
					// 计算安全分数
					this.calculateSecurityScore()
					
				} catch (error) {
					console.error('初始化安全中心失败:', error)
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}
			},
			
			// 加载安全检查结果
			async loadSecurityChecks() {
				this.securityChecks = [
					{
						name: '密码安全',
						description: '密码强度和安全性检查',
						status: 'good',
						statusText: '安全',
						icon: '🔒'
					},
					{
						name: '登录保护',
						description: '异常登录检测和保护',
						status: 'good',
						statusText: '正常',
						icon: '🛡️'
					},
					{
						name: '数据加密',
						description: '敏感数据加密存储',
						status: 'good',
						statusText: '已启用',
						icon: '🔐'
					},
					{
						name: '设备安全',
						description: '设备环境安全检查',
						status: 'warning',
						statusText: '需注意',
						icon: '📱'
					},
					{
						name: '网络安全',
						description: '网络连接安全检查',
						status: 'good',
						statusText: '安全',
						icon: '🌐'
					}
				]
				
				// 更新最后检查时间
				this.lastCheckTime = this.formatTime(Date.now())
			},
			
			// 加载安全提醒
			loadSecurityAlerts() {
				try {
					const currentUser = uni.getStorageSync('current_user')
					if (currentUser) {
						this.securityAlerts = loginSecurityManager.getSecurityAlerts(currentUser.phone)
							.slice(0, 5) // 只显示最近5条
					}
					
					// 添加风险监控提醒
					const riskAlerts = uni.getStorageSync('security_risk_alerts') || []
					this.securityAlerts = [...this.securityAlerts, ...riskAlerts.slice(0, 3)]
					
				} catch (error) {
					console.error('加载安全提醒失败:', error)
					this.securityAlerts = []
				}
			},
			
			// 加载监控状态
			loadMonitoringStatus() {
				try {
					const monitoringStatus = riskMonitorManager.getMonitoringStatus()
					this.monitoringEnabled = monitoringStatus.enabled
					
					this.notificationEnabled = uni.getStorageSync('enable_security_notifications') !== false
				} catch (error) {
					console.error('加载监控状态失败:', error)
				}
			},
			
			// 计算安全分数
			calculateSecurityScore() {
				let score = 100
				
				// 根据安全检查结果计算分数
				this.securityChecks.forEach(check => {
					if (check.status === 'warning') {
						score -= 10
					} else if (check.status === 'error') {
						score -= 20
					}
				})
				
				// 根据安全提醒数量调整分数
				const criticalAlerts = this.securityAlerts.filter(alert => 
					alert.type === 'critical_risk'
				).length
				const highAlerts = this.securityAlerts.filter(alert => 
					alert.type === 'high_risk'
				).length
				
				score -= criticalAlerts * 15
				score -= highAlerts * 10
				
				this.securityScore = Math.max(0, score)
			},
			
			// 执行安全检查
			async performSecurityCheck() {
				try {
					uni.showLoading({
						title: '检查中...'
					})
					
					// 执行风险检查
					await riskMonitorManager.performRiskCheck()
					
					// 重新加载检查结果
					await this.loadSecurityChecks()
					this.loadSecurityAlerts()
					this.calculateSecurityScore()
					
					uni.hideLoading()
					uni.showToast({
						title: '检查完成',
						icon: 'success'
					})
					
				} catch (error) {
					uni.hideLoading()
					console.error('安全检查失败:', error)
					uni.showToast({
						title: '检查失败',
						icon: 'none'
					})
				}
			},
			
			// 刷新安全状态
			refreshSecurityStatus() {
				this.loadSecurityAlerts()
				this.calculateSecurityScore()
			},
			
			// 查看提醒详情
			viewAlertDetail(alert) {
				// 标记为已读
				const currentUser = uni.getStorageSync('current_user')
				if (currentUser && alert.phone) {
					loginSecurityManager.markAlertAsRead(currentUser.phone, alert.id)
				}
				
				// 显示详情
				uni.showModal({
					title: alert.title,
					content: alert.message,
					showCancel: false,
					confirmText: '知道了'
				})
				
				// 刷新提醒列表
				this.loadSecurityAlerts()
			},
			
			// 获取提醒图标
			getAlertIcon(type) {
				const icons = {
					critical_risk: '🚨',
					high_risk: '⚠️',
					general_risk: 'ℹ️',
					account_locked: '🔒',
					suspicious_activity: '👁️',
					new_device_login: '📱'
				}
				return icons[type] || 'ℹ️'
			},
			
			// 切换监控状态
			toggleMonitoring(e) {
				this.monitoringEnabled = e.detail.value
				
				if (this.monitoringEnabled) {
					riskMonitorManager.startMonitoring()
					uni.showToast({
						title: '风险监控已启用',
						icon: 'success'
					})
				} else {
					riskMonitorManager.stopMonitoring()
					uni.showToast({
						title: '风险监控已关闭',
						icon: 'none'
					})
				}
			},
			
			// 切换通知状态
			toggleNotification(e) {
				this.notificationEnabled = e.detail.value
				uni.setStorageSync('enable_security_notifications', this.notificationEnabled)
				
				uni.showToast({
					title: this.notificationEnabled ? '安全通知已启用' : '安全通知已关闭',
					icon: 'success'
				})
			},
			
			// 导航到登录安全
			navigateToLoginSecurity() {
				uni.navigateTo({
					url: '/pages/security/login'
				})
			},
			
			// 导航到数据保护
			navigateToDataProtection() {
				uni.navigateTo({
					url: '/pages/security/data'
				})
			},
			
			// 导航到设备安全
			navigateToDeviceSecurity() {
				uni.navigateTo({
					url: '/pages/security/device'
				})
			},
			
			// 导航到隐私政策
			navigateToPrivacyPolicy() {
				uni.navigateTo({
					url: '/pages/privacy/policy'
				})
			},
			
			// 临时锁定账户
			lockAccount() {
				uni.showModal({
					title: '临时锁定账户',
					content: '确定要临时锁定您的账户吗？锁定后需要重新验证身份才能使用。',
					success: (res) => {
						if (res.confirm) {
							const currentUser = uni.getStorageSync('current_user')
							if (currentUser) {
								loginSecurityManager.lockAccount(currentUser.phone, '用户主动锁定')
								
								uni.showToast({
									title: '账户已锁定',
									icon: 'success'
								})
								
								// 跳转到登录页面
								setTimeout(() => {
									uni.reLaunch({
										url: '/pages/auth/login'
									})
								}, 1500)
							}
						}
					}
				})
			},
			
			// 清除所有数据
			clearAllData() {
				uni.showModal({
					title: '清除所有数据',
					content: '此操作将永久删除您的所有数据，包括健康报告、个人信息等，且无法恢复。确定要继续吗？',
					confirmColor: '#FF3B30',
					success: async (res) => {
						if (res.confirm) {
							try {
								uni.showLoading({
									title: '清除中...'
								})
								
								const result = await dataCleanupManager.completeDataWipe()
								
								uni.hideLoading()
								
								if (result.success) {
									uni.showToast({
										title: '数据已清除',
										icon: 'success'
									})
									
									// 跳转到引导页面
									setTimeout(() => {
										uni.reLaunch({
											url: '/pages/guide/index'
										})
									}, 1500)
								} else {
									uni.showToast({
										title: '清除失败',
										icon: 'none'
									})
								}
							} catch (error) {
								uni.hideLoading()
								console.error('清除数据失败:', error)
								uni.showToast({
									title: '清除失败',
									icon: 'none'
								})
							}
						}
					}
				})
			},
			
			// 格式化时间
			formatTime(timestamp) {
				const date = new Date(timestamp)
				const now = new Date()
				const diff = now - date
				
				if (diff < 60000) { // 1分钟内
					return '刚刚'
				} else if (diff < 3600000) { // 1小时内
					return `${Math.floor(diff / 60000)}分钟前`
				} else if (diff < 86400000) { // 24小时内
					return `${Math.floor(diff / 3600000)}小时前`
				} else {
					return `${date.getMonth() + 1}-${date.getDate()} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
				}
			}
		}
	}
</script>

<style scoped>
	.security-container {
		padding: 15px;
	}
	
	/* 安全状态概览 */
	.security-overview {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		color: #FFFFFF;
	}
	
	.overview-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	
	.overview-title {
		font-size: 18px;
		font-weight: 600;
	}
	
	.security-score {
		display: flex;
		align-items: baseline;
	}
	
	.score-number {
		font-size: 32px;
		font-weight: bold;
	}
	
	.score-label {
		font-size: 14px;
		margin-left: 4px;
	}
	
	.score-excellent {
		color: #34C759;
	}
	
	.score-good {
		color: #007AFF;
	}
	
	.score-fair {
		color: #FF9500;
	}
	
	.score-poor {
		color: #FF3B30;
	}
	
	.overview-desc {
		font-size: 14px;
		opacity: 0.9;
	}
	
	/* 区域标题 */
	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		font-size: 16px;
		font-weight: 600;
		color: #333333;
	}
	
	.check-time,
	.alert-count {
		font-size: 12px;
		color: #8E8E93;
		font-weight: normal;
	}
	
	/* 安全检查 */
	.security-checks {
		margin-bottom: 25px;
	}
	
	.check-list {
		margin-bottom: 15px;
	}
	
	.check-item {
		display: flex;
		align-items: center;
		padding: 15px;
		background-color: #FFFFFF;
		border-radius: 8px;
		margin-bottom: 8px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	}
	
	.check-item.warning {
		border-left: 4px solid #FF9500;
	}
	
	.check-item.error {
		border-left: 4px solid #FF3B30;
	}
	
	.check-item.good {
		border-left: 4px solid #34C759;
	}
	
	.check-icon {
		margin-right: 12px;
	}
	
	.check-icon .icon {
		font-size: 20px;
	}
	
	.check-content {
		flex: 1;
	}
	
	.check-name {
		font-size: 14px;
		font-weight: 500;
		color: #333333;
		display: block;
		margin-bottom: 4px;
	}
	
	.check-desc {
		font-size: 12px;
		color: #8E8E93;
	}
	
	.check-status {
		margin-left: 10px;
	}
	
	.status-text {
		font-size: 12px;
		font-weight: 500;
	}
	
	.check-item.good .status-text {
		color: #34C759;
	}
	
	.check-item.warning .status-text {
		color: #FF9500;
	}
	
	.check-item.error .status-text {
		color: #FF3B30;
	}
	
	/* 安全提醒 */
	.security-alerts {
		margin-bottom: 25px;
	}
	
	.alert-list {
		background-color: #FFFFFF;
		border-radius: 8px;
		overflow: hidden;
	}
	
	.alert-item {
		display: flex;
		align-items: center;
		padding: 15px;
		border-bottom: 1px solid #F0F0F0;
		transition: background-color 0.3s ease;
	}
	
	.alert-item:last-child {
		border-bottom: none;
	}
	
	.alert-item:active {
		background-color: #F8F9FA;
	}
	
	.alert-icon {
		margin-right: 12px;
	}
	
	.alert-icon .icon {
		font-size: 18px;
	}
	
	.alert-content {
		flex: 1;
	}
	
	.alert-title {
		font-size: 14px;
		font-weight: 500;
		color: #333333;
		display: block;
		margin-bottom: 4px;
	}
	
	.alert-message {
		font-size: 12px;
		color: #666666;
		display: block;
		margin-bottom: 4px;
		line-height: 1.4;
	}
	
	.alert-time {
		font-size: 11px;
		color: #8E8E93;
	}
	
	.alert-action {
		margin-left: 10px;
	}
	
	.unread-dot {
		width: 8px;
		height: 8px;
		border-radius: 4px;
		background-color: #FF3B30;
	}
	
	/* 安全设置 */
	.security-settings {
		margin-bottom: 25px;
	}
	
	.setting-list {
		background-color: #FFFFFF;
		border-radius: 8px;
		overflow: hidden;
	}
	
	.setting-item {
		display: flex;
		align-items: center;
		padding: 15px;
		border-bottom: 1px solid #F0F0F0;
		transition: background-color 0.3s ease;
	}
	
	.setting-item:last-child {
		border-bottom: none;
	}
	
	.setting-item:active {
		background-color: #F8F9FA;
	}
	
	.setting-icon {
		font-size: 20px;
		margin-right: 12px;
	}
	
	.setting-content {
		flex: 1;
	}
	
	.setting-name {
		font-size: 14px;
		font-weight: 500;
		color: #333333;
		display: block;
		margin-bottom: 4px;
	}
	
	.setting-desc {
		font-size: 12px;
		color: #8E8E93;
	}
	
	.setting-arrow {
		color: #C7C7CC;
		font-size: 16px;
	}
	
	/* 监控控制 */
	.monitoring-control {
		margin-bottom: 25px;
	}
	
	.control-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 15px;
		background-color: #FFFFFF;
		border-radius: 8px;
		margin-bottom: 8px;
	}
	
	.control-info {
		flex: 1;
	}
	
	.control-name {
		font-size: 14px;
		font-weight: 500;
		color: #333333;
		display: block;
		margin-bottom: 4px;
	}
	
	.control-desc {
		font-size: 12px;
		color: #8E8E93;
	}
	
	/* 紧急操作 */
	.emergency-actions {
		margin-bottom: 25px;
	}
	
	.action-buttons {
		display: flex;
		gap: 15px;
	}
	
	.action-buttons .btn {
		flex: 1;
	}
</style>