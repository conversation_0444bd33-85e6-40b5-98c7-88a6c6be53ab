/**
 * 内存使用优化和垃圾回收管理
 */

class MemoryManager {
  constructor(options = {}) {
    this.options = {
      maxMemoryUsage: options.maxMemoryUsage || 100 * 1024 * 1024, // 100MB
      gcInterval: options.gcInterval || 30000, // 30秒
      warningThreshold: options.warningThreshold || 0.8, // 80%
      ...options
    }
    
    this.memoryUsage = {
      used: 0,
      peak: 0,
      allocated: 0,
      freed: 0
    }
    
    this.objectPool = new Map()
    this.weakRefs = new Set()
    this.gcTimer = null
    this.memoryWatchers = []
    
    this.startMemoryMonitoring()
  }

  /**
   * 开始内存监控
   */
  startMemoryMonitoring() {
    this.gcTimer = setInterval(() => {
      this.performGarbageCollection()
      this.checkMemoryUsage()
    }, this.options.gcInterval)
  }

  /**
   * 停止内存监控
   */
  stopMemoryMonitoring() {
    if (this.gcTimer) {
      clearInterval(this.gcTimer)
      this.gcTimer = null
    }
  }

  /**
   * 执行垃圾回收
   */
  performGarbageCollection() {
    const beforeGC = this.getCurrentMemoryUsage()
    
    // 清理弱引用
    this.cleanupWeakReferences()
    
    // 清理对象池中的过期对象
    this.cleanupObjectPool()
    
    // 清理事件监听器
    this.cleanupEventListeners()
    
    // 强制垃圾回收（如果支持）
    this.forceGarbageCollection()
    
    const afterGC = this.getCurrentMemoryUsage()
    const freed = beforeGC - afterGC
    
    if (freed > 0) {
      this.memoryUsage.freed += freed
      console.log(`垃圾回收完成，释放内存: ${this.formatBytes(freed)}`)
    }
  }

  /**
   * 获取当前内存使用量
   */
  getCurrentMemoryUsage() {
    try {
      // #ifdef APP-PLUS
      if (plus && plus.runtime) {
        const info = plus.runtime.getProperty(plus.runtime.appid)
        return info.memory || 0
      }
      // #endif
      
      // #ifdef H5
      if (performance && performance.memory) {
        return performance.memory.usedJSHeapSize
      }
      // #endif
      
      // 其他平台的估算
      return this.estimateMemoryUsage()
    } catch (error) {
      console.error('获取内存使用量失败:', error)
      return 0
    }
  }

  /**
   * 估算内存使用量
   */
  estimateMemoryUsage() {
    // 简化的内存使用估算
    let estimated = 0
    
    // 估算对象池占用
    for (const [key, pool] of this.objectPool.entries()) {
      estimated += pool.length * 1024 // 假设每个对象1KB
    }
    
    // 估算其他占用
    estimated += this.weakRefs.size * 100 // 假设每个引用100B
    
    return estimated
  }

  /**
   * 检查内存使用情况
   */
  checkMemoryUsage() {
    const currentUsage = this.getCurrentMemoryUsage()
    this.memoryUsage.used = currentUsage
    
    if (currentUsage > this.memoryUsage.peak) {
      this.memoryUsage.peak = currentUsage
    }
    
    const usageRatio = currentUsage / this.options.maxMemoryUsage
    
    if (usageRatio > this.options.warningThreshold) {
      this.handleMemoryWarning(currentUsage, usageRatio)
    }
    
    // 通知内存观察者
    this.notifyMemoryWatchers(currentUsage, usageRatio)
  }

  /**
   * 处理内存警告
   */
  handleMemoryWarning(currentUsage, usageRatio) {
    console.warn(`内存使用警告: ${this.formatBytes(currentUsage)} (${(usageRatio * 100).toFixed(1)}%)`)
    
    // 执行紧急清理
    this.performEmergencyCleanup()
    
    // 触发内存警告事件
    this.triggerMemoryWarning(currentUsage, usageRatio)
  }

  /**
   * 执行紧急清理
   */
  performEmergencyCleanup() {
    console.log('执行紧急内存清理...')
    
    // 清空所有对象池
    this.clearAllObjectPools()
    
    // 清理所有弱引用
    this.weakRefs.clear()
    
    // 强制垃圾回收
    this.forceGarbageCollection()
    
    // 清理缓存
    this.clearCaches()
  }

  /**
   * 创建对象池
   */
  createObjectPool(poolName, factory, options = {}) {
    const pool = {
      objects: [],
      factory,
      maxSize: options.maxSize || 50,
      created: 0,
      reused: 0
    }
    
    this.objectPool.set(poolName, pool)
    return pool
  }

  /**
   * 从对象池获取对象
   */
  getFromPool(poolName, ...args) {
    const pool = this.objectPool.get(poolName)
    if (!pool) {
      throw new Error(`对象池不存在: ${poolName}`)
    }
    
    // 尝试从池中获取对象
    if (pool.objects.length > 0) {
      const obj = pool.objects.pop()
      pool.reused++
      
      // 重置对象状态
      if (obj.reset && typeof obj.reset === 'function') {
        obj.reset(...args)
      }
      
      return obj
    }
    
    // 创建新对象
    const newObj = pool.factory(...args)
    pool.created++
    
    return newObj
  }

  /**
   * 将对象返回到池中
   */
  returnToPool(poolName, obj) {
    const pool = this.objectPool.get(poolName)
    if (!pool) {
      return false
    }
    
    // 检查池是否已满
    if (pool.objects.length >= pool.maxSize) {
      return false
    }
    
    // 清理对象状态
    if (obj.cleanup && typeof obj.cleanup === 'function') {
      obj.cleanup()
    }
    
    pool.objects.push(obj)
    return true
  }

  /**
   * 清理对象池
   */
  cleanupObjectPool() {
    for (const [poolName, pool] of this.objectPool.entries()) {
      // 保留一半的对象，清理另一半
      const keepCount = Math.floor(pool.objects.length / 2)
      const toRemove = pool.objects.splice(keepCount)
      
      // 清理被移除的对象
      toRemove.forEach(obj => {
        if (obj.destroy && typeof obj.destroy === 'function') {
          obj.destroy()
        }
      })
    }
  }

  /**
   * 清空所有对象池
   */
  clearAllObjectPools() {
    for (const [poolName, pool] of this.objectPool.entries()) {
      pool.objects.forEach(obj => {
        if (obj.destroy && typeof obj.destroy === 'function') {
          obj.destroy()
        }
      })
      pool.objects = []
    }
  }

  /**
   * 添加弱引用
   */
  addWeakReference(obj, callback) {
    if (typeof WeakRef !== 'undefined') {
      const weakRef = new WeakRef(obj)
      const refData = { weakRef, callback, created: Date.now() }
      this.weakRefs.add(refData)
      return refData
    }
    
    // 如果不支持WeakRef，使用普通引用（需要手动清理）
    const refData = { obj, callback, created: Date.now() }
    this.weakRefs.add(refData)
    return refData
  }

  /**
   * 清理弱引用
   */
  cleanupWeakReferences() {
    const toRemove = []
    
    for (const refData of this.weakRefs) {
      let shouldRemove = false
      
      if (refData.weakRef) {
        // 检查WeakRef是否还有效
        const obj = refData.weakRef.deref()
        if (!obj) {
          shouldRemove = true
        }
      } else {
        // 检查普通引用是否过期（超过5分钟）
        if (Date.now() - refData.created > 300000) {
          shouldRemove = true
        }
      }
      
      if (shouldRemove) {
        toRemove.push(refData)
        if (refData.callback) {
          try {
            refData.callback()
          } catch (error) {
            console.error('弱引用回调执行失败:', error)
          }
        }
      }
    }
    
    toRemove.forEach(refData => this.weakRefs.delete(refData))
  }

  /**
   * 清理事件监听器
   */
  cleanupEventListeners() {
    // 这里可以实现全局事件监听器的清理逻辑
    // 例如清理页面级别的事件监听器
  }

  /**
   * 强制垃圾回收
   */
  forceGarbageCollection() {
    try {
      // #ifdef H5
      if (window.gc && typeof window.gc === 'function') {
        window.gc()
      }
      // #endif
      
      // #ifdef APP-PLUS
      if (plus && plus.runtime && plus.runtime.gc) {
        plus.runtime.gc()
      }
      // #endif
    } catch (error) {
      // 忽略垃圾回收失败
    }
  }

  /**
   * 清理缓存
   */
  clearCaches() {
    // 清理各种缓存
    try {
      // 清理图片缓存
      if (uni.clearStorageSync) {
        const keys = uni.getStorageInfoSync().keys
        keys.forEach(key => {
          if (key.startsWith('cache_') || key.startsWith('img_')) {
            uni.removeStorageSync(key)
          }
        })
      }
    } catch (error) {
      console.error('清理缓存失败:', error)
    }
  }

  /**
   * 添加内存观察者
   */
  addMemoryWatcher(callback) {
    this.memoryWatchers.push(callback)
  }

  /**
   * 移除内存观察者
   */
  removeMemoryWatcher(callback) {
    const index = this.memoryWatchers.indexOf(callback)
    if (index > -1) {
      this.memoryWatchers.splice(index, 1)
    }
  }

  /**
   * 通知内存观察者
   */
  notifyMemoryWatchers(currentUsage, usageRatio) {
    this.memoryWatchers.forEach(callback => {
      try {
        callback(currentUsage, usageRatio)
      } catch (error) {
        console.error('内存观察者回调失败:', error)
      }
    })
  }

  /**
   * 触发内存警告事件
   */
  triggerMemoryWarning(currentUsage, usageRatio) {
    // 可以在这里触发全局事件或通知
    uni.$emit('memoryWarning', { currentUsage, usageRatio })
  }

  /**
   * 格式化字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 获取内存统计
   */
  getMemoryStats() {
    return {
      current: this.getCurrentMemoryUsage(),
      peak: this.memoryUsage.peak,
      allocated: this.memoryUsage.allocated,
      freed: this.memoryUsage.freed,
      poolStats: this.getPoolStats(),
      weakRefCount: this.weakRefs.size
    }
  }

  /**
   * 获取对象池统计
   */
  getPoolStats() {
    const stats = {}
    for (const [poolName, pool] of this.objectPool.entries()) {
      stats[poolName] = {
        available: pool.objects.length,
        created: pool.created,
        reused: pool.reused,
        maxSize: pool.maxSize
      }
    }
    return stats
  }

  /**
   * 销毁内存管理器
   */
  destroy() {
    this.stopMemoryMonitoring()
    this.clearAllObjectPools()
    this.weakRefs.clear()
    this.memoryWatchers = []
  }
}

// 内存优化混入
export const memoryOptimizationMixin = {
  data() {
    return {
      memoryManager: null,
      componentPool: null
    }
  },
  
  created() {
    // 创建内存管理器
    this.memoryManager = new MemoryManager({
      maxMemoryUsage: 50 * 1024 * 1024, // 50MB
      gcInterval: 30000, // 30秒
      warningThreshold: 0.8
    })
    
    // 创建组件对象池
    this.createComponentPools()
    
    // 添加内存监控
    this.addMemoryMonitoring()
  },
  
  methods: {
    // 创建组件对象池
    createComponentPools() {
      // 创建常用对象的对象池
      this.memoryManager.createObjectPool('reportItem', () => ({
        id: null,
        data: null,
        reset(id, data) {
          this.id = id
          this.data = data
        },
        cleanup() {
          this.id = null
          this.data = null
        }
      }))
      
      this.memoryManager.createObjectPool('chartData', () => ({
        labels: [],
        datasets: [],
        reset(labels, datasets) {
          this.labels = labels || []
          this.datasets = datasets || []
        },
        cleanup() {
          this.labels = []
          this.datasets = []
        }
      }))
    },
    
    // 添加内存监控
    addMemoryMonitoring() {
      this.memoryManager.addMemoryWatcher((usage, ratio) => {
        if (ratio > 0.9) {
          // 内存使用超过90%，执行清理
          this.performComponentCleanup()
        }
      })
    },
    
    // 从对象池获取对象
    getPooledObject(poolName, ...args) {
      return this.memoryManager.getFromPool(poolName, ...args)
    },
    
    // 返回对象到池中
    returnPooledObject(poolName, obj) {
      return this.memoryManager.returnToPool(poolName, obj)
    },
    
    // 添加弱引用
    addWeakRef(obj, callback) {
      return this.memoryManager.addWeakReference(obj, callback)
    },
    
    // 执行组件清理
    performComponentCleanup() {
      // 清理组件内的大对象
      if (this.largeDataObjects) {
        this.largeDataObjects = null
      }
      
      // 清理事件监听器
      this.cleanupEventListeners()
      
      // 清理定时器
      this.cleanupTimers()
    },
    
    // 清理事件监听器
    cleanupEventListeners() {
      // 移除全局事件监听器
      if (this.globalEventListeners) {
        this.globalEventListeners.forEach(({ event, handler }) => {
          uni.$off(event, handler)
        })
        this.globalEventListeners = []
      }
    },
    
    // 清理定时器
    cleanupTimers() {
      if (this.timers) {
        this.timers.forEach(timer => clearTimeout(timer))
        this.timers = []
      }
      
      if (this.intervals) {
        this.intervals.forEach(interval => clearInterval(interval))
        this.intervals = []
      }
    },
    
    // 获取内存统计
    getMemoryStats() {
      return this.memoryManager.getMemoryStats()
    }
  },
  
  beforeDestroy() {
    // 执行清理
    this.performComponentCleanup()
    
    // 销毁内存管理器
    if (this.memoryManager) {
      this.memoryManager.destroy()
    }
  }
}

export default MemoryManager