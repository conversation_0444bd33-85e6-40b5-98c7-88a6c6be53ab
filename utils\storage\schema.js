/**
 * 数据库表结构定义
 * 支持SQLite数据库表结构（users, health_reports, health_indicators, sync_records）
 */

// 用户表结构
export const USERS_TABLE = {
  name: 'users',
  schema: `
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username VARCHAR(50) UNIQUE NOT NULL,
      phone VARCHAR(20) UNIQUE,
      email VARCHAR(100),
      password_hash VARCHAR(255) NOT NULL,
      salt VARCHAR(32) NOT NULL,
      avatar_url VARCHAR(255),
      real_name VARCHAR(50),
      gender INTEGER DEFAULT 0, -- 0:未知, 1:男, 2:女
      birth_date DATE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login_at DATETIME,
      is_active INTEGER DEFAULT 1,
      biometric_enabled INTEGER DEFAULT 0,
      sync_enabled INTEGER DEFAULT 1
    )
  `,
  indexes: [
    'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
    'CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone)',
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)'
  ]
};

// 健康报告表结构
export const HEALTH_REPORTS_TABLE = {
  name: 'health_reports',
  schema: `
    CREATE TABLE IF NOT EXISTS health_reports (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      report_title VARCHAR(200) NOT NULL,
      report_date DATE NOT NULL,
      hospital_name VARCHAR(200),
      doctor_name VARCHAR(100),
      department VARCHAR(100),
      report_type VARCHAR(50), -- 体检报告、血液检查、影像检查等
      original_image_path VARCHAR(500),
      thumbnail_path VARCHAR(500),
      ocr_text TEXT,
      ocr_confidence REAL DEFAULT 0,
      notes TEXT,
      is_encrypted INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      synced_at DATETIME,
      sync_status INTEGER DEFAULT 0, -- 0:未同步, 1:已同步, 2:同步失败
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `,
  indexes: [
    'CREATE INDEX IF NOT EXISTS idx_health_reports_user_id ON health_reports(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_health_reports_date ON health_reports(report_date)',
    'CREATE INDEX IF NOT EXISTS idx_health_reports_type ON health_reports(report_type)',
    'CREATE INDEX IF NOT EXISTS idx_health_reports_sync ON health_reports(sync_status)'
  ]
};

// 健康指标表结构
export const HEALTH_INDICATORS_TABLE = {
  name: 'health_indicators',
  schema: `
    CREATE TABLE IF NOT EXISTS health_indicators (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      report_id INTEGER NOT NULL,
      indicator_name VARCHAR(100) NOT NULL,
      indicator_value VARCHAR(100) NOT NULL,
      indicator_unit VARCHAR(20),
      reference_range VARCHAR(100),
      is_abnormal INTEGER DEFAULT 0,
      abnormal_level INTEGER DEFAULT 0, -- 0:正常, 1:轻微异常, 2:中度异常, 3:严重异常
      category VARCHAR(50), -- 血液、尿液、生化、免疫等
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (report_id) REFERENCES health_reports(id) ON DELETE CASCADE
    )
  `,
  indexes: [
    'CREATE INDEX IF NOT EXISTS idx_health_indicators_report_id ON health_indicators(report_id)',
    'CREATE INDEX IF NOT EXISTS idx_health_indicators_name ON health_indicators(indicator_name)',
    'CREATE INDEX IF NOT EXISTS idx_health_indicators_category ON health_indicators(category)',
    'CREATE INDEX IF NOT EXISTS idx_health_indicators_abnormal ON health_indicators(is_abnormal)'
  ]
};

// 同步记录表结构
export const SYNC_RECORDS_TABLE = {
  name: 'sync_records',
  schema: `
    CREATE TABLE IF NOT EXISTS sync_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      table_name VARCHAR(50) NOT NULL,
      record_id INTEGER NOT NULL,
      operation_type VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
      sync_status INTEGER DEFAULT 0, -- 0:待同步, 1:同步成功, 2:同步失败
      sync_attempts INTEGER DEFAULT 0,
      last_sync_at DATETIME,
      error_message TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `,
  indexes: [
    'CREATE INDEX IF NOT EXISTS idx_sync_records_user_id ON sync_records(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_sync_records_table ON sync_records(table_name)',
    'CREATE INDEX IF NOT EXISTS idx_sync_records_status ON sync_records(sync_status)',
    'CREATE INDEX IF NOT EXISTS idx_sync_records_created ON sync_records(created_at)'
  ]
};

// 所有表的定义
export const ALL_TABLES = [
  USERS_TABLE,
  HEALTH_REPORTS_TABLE,
  HEALTH_INDICATORS_TABLE,
  SYNC_RECORDS_TABLE
];

// 数据库版本和升级脚本
export const DATABASE_VERSION = 1;

export const MIGRATION_SCRIPTS = {
  1: [
    // 初始版本的所有表创建脚本
    ...ALL_TABLES.map(table => table.schema),
    ...ALL_TABLES.flatMap(table => table.indexes || [])
  ]
};