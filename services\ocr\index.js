/**
 * OCR服务主入口
 * 提供统一的OCR识别接口和降级处理机制
 */

const BaiduOCRService = require('./baidu.js');
const OCRParser = require('./parser.js');

class OCRService {
  constructor(config = {}) {
    this.config = config;
    this.parser = new OCRParser();
    
    // 初始化OCR服务提供商
    this.providers = {
      baidu: new BaiduOCRService(config.baidu || {})
    };
    
    // 默认提供商顺序（降级顺序）
    this.providerOrder = config.providerOrder || ['baidu'];
    
    // 降级策略配置
    this.fallbackConfig = {
      maxRetries: config.maxRetries || 2,
      retryDelay: config.retryDelay || 1000,
      enableFallback: config.enableFallback !== false,
      enableManualInput: config.enableManualInput !== false
    };
  }

  /**
   * 识别图片中的文字并解析为结构化数据
   * @param {string} imagePath - 图片路径
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别和解析结果
   */
  async recognizeAndParse(imagePath, options = {}) {
    const startTime = Date.now();
    let lastError = null;
    
    // 尝试所有配置的提供商
    for (const providerName of this.providerOrder) {
      const provider = this.providers[providerName];
      
      if (!provider || !provider.validateConfig()) {
        console.warn(`OCR提供商 ${providerName} 配置无效，跳过`);
        continue;
      }

      try {
        console.log(`尝试使用 ${providerName} 进行OCR识别`);
        
        // 执行OCR识别
        const ocrResult = await this.executeWithRetry(
          () => provider.recognize(imagePath, options),
          this.fallbackConfig.maxRetries
        );

        if (ocrResult.success) {
          // 解析OCR结果
          const parsedResult = this.parser.parse(ocrResult);
          
          // 验证解析结果
          const validation = this.parser.validateResult(parsedResult.data);
          
          const result = {
            success: true,
            data: parsedResult.data,
            provider: providerName,
            validation: validation,
            performance: {
              duration: Date.now() - startTime,
              provider: providerName
            },
            timestamp: new Date().toISOString()
          };

          // 如果解析结果质量较好，直接返回
          if (validation.isValid) {
            console.log(`OCR识别成功，使用提供商: ${providerName}`);
            return result;
          } else {
            console.warn(`OCR识别质量较差，尝试下一个提供商`, validation.issues);
            // 保存结果作为备选
            lastError = result;
          }
        } else {
          throw new Error(ocrResult.error?.message || 'OCR识别失败');
        }
      } catch (error) {
        console.error(`OCR提供商 ${providerName} 识别失败:`, error.message);
        lastError = error;
        
        // 如果不是最后一个提供商，继续尝试
        if (providerName !== this.providerOrder[this.providerOrder.length - 1]) {
          await this.delay(this.fallbackConfig.retryDelay);
        }
      }
    }

    // 所有提供商都失败了，返回降级处理结果
    return this.handleFallback(imagePath, lastError, startTime);
  }

  /**
   * 带重试的执行
   * @param {Function} fn - 要执行的函数
   * @param {number} maxRetries - 最大重试次数
   * @returns {Promise} 执行结果
   */
  async executeWithRetry(fn, maxRetries) {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, i), 5000);
          console.warn(`OCR识别重试 ${i + 1}/${maxRetries}, ${delay}ms后重试`);
          await this.delay(delay);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 处理降级情况
   * @param {string} imagePath - 图片路径
   * @param {Error|Object} lastError - 最后的错误或结果
   * @param {number} startTime - 开始时间
   * @returns {Object} 降级处理结果
   */
  async handleFallback(imagePath, lastError, startTime) {
    console.log('开始OCR降级处理');

    const fallbackResult = {
      success: false,
      data: null,
      provider: 'fallback',
      performance: {
        duration: Date.now() - startTime,
        provider: 'fallback'
      },
      timestamp: new Date().toISOString()
    };

    // 如果有部分识别结果（质量较差但有数据），返回该结果
    if (lastError && typeof lastError === 'object' && lastError.data) {
      console.log('返回质量较差的OCR结果作为降级方案');
      return {
        ...lastError,
        success: true,
        fallback: true,
        warning: '识别质量较差，建议手动检查和修正'
      };
    }

    // 尝试基础图像处理后重新识别
    if (this.fallbackConfig.enableFallback) {
      try {
        const processedResult = await this.tryImagePreprocessing(imagePath);
        if (processedResult) {
          return {
            ...processedResult,
            fallback: true,
            warning: '使用图像预处理后的识别结果'
          };
        }
      } catch (error) {
        console.error('图像预处理降级失败:', error);
      }
    }

    // 返回手动输入提示
    if (this.fallbackConfig.enableManualInput) {
      fallbackResult.manualInputRequired = true;
      fallbackResult.error = {
        code: 'OCR_FAILED',
        message: 'OCR识别失败，需要手动输入',
        details: lastError?.message || '所有OCR服务都不可用',
        suggestions: [
          '检查图片是否清晰',
          '确保光线充足',
          '避免图片倾斜或模糊',
          '可以尝试重新拍摄'
        ]
      };
    } else {
      fallbackResult.error = {
        code: 'OCR_FAILED',
        message: 'OCR识别失败',
        details: lastError?.message || '所有OCR服务都不可用'
      };
    }

    return fallbackResult;
  }

  /**
   * 尝试图像预处理后重新识别
   * @param {string} imagePath - 图片路径
   * @returns {Promise<Object|null>} 处理结果
   */
  async tryImagePreprocessing(imagePath) {
    try {
      // 这里可以集成图像预处理逻辑
      // 例如：调整亮度、对比度、去噪等
      console.log('尝试图像预处理降级方案');
      
      // 简化实现：直接使用原图片重试一次
      const provider = this.providers[this.providerOrder[0]];
      if (provider && provider.validateConfig()) {
        const ocrResult = await provider.recognize(imagePath, {
          // 使用更宽松的识别参数
          language: 'CHN_ENG',
          detectDirection: 'true'
        });
        
        if (ocrResult.success) {
          const parsedResult = this.parser.parse(ocrResult);
          return {
            success: true,
            data: parsedResult.data,
            provider: 'preprocessed',
            validation: this.parser.validateResult(parsedResult.data)
          };
        }
      }
    } catch (error) {
      console.error('图像预处理失败:', error);
    }
    
    return null;
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取服务状态
   * @returns {Object} 服务状态信息
   */
  getStatus() {
    const status = {
      providers: {},
      config: this.fallbackConfig
    };

    for (const [name, provider] of Object.entries(this.providers)) {
      status.providers[name] = {
        available: provider.validateConfig(),
        config: provider.config ? 'configured' : 'not configured'
      };
    }

    return status;
  }

  /**
   * 添加新的OCR提供商
   * @param {string} name - 提供商名称
   * @param {Object} provider - 提供商实例
   */
  addProvider(name, provider) {
    this.providers[name] = provider;
    if (!this.providerOrder.includes(name)) {
      this.providerOrder.push(name);
    }
  }

  /**
   * 设置提供商优先级
   * @param {Array} order - 提供商优先级数组
   */
  setProviderOrder(order) {
    this.providerOrder = order.filter(name => this.providers[name]);
  }

  /**
   * 测试OCR服务
   * @param {string} imagePath - 测试图片路径
   * @returns {Promise<Object>} 测试结果
   */
  async testService(imagePath) {
    const testResults = {};
    
    for (const [name, provider] of Object.entries(this.providers)) {
      if (!provider.validateConfig()) {
        testResults[name] = {
          success: false,
          error: '配置无效'
        };
        continue;
      }

      try {
        const startTime = Date.now();
        const result = await provider.recognize(imagePath);
        testResults[name] = {
          success: result.success,
          duration: Date.now() - startTime,
          wordsCount: result.data?.wordsCount || 0
        };
      } catch (error) {
        testResults[name] = {
          success: false,
          error: error.message
        };
      }
    }

    return testResults;
  }
}

module.exports = OCRService;