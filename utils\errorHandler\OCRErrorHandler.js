/**
 * OCR错误处理器
 * 处理OCR识别失败的多重降级策略
 */
class OCRErrorHandler {
  constructor() {
    this.fallbackStrategies = [
      'retry',
      'alternative_service',
      'manual_input',
      'image_enhancement',
      'partial_recognition'
    ]
    
    this.ocrServices = [
      {
        name: 'baidu',
        priority: 1,
        available: true
      },
      {
        name: 'tencent',
        priority: 2,
        available: true
      },
      {
        name: 'aliyun',
        priority: 3,
        available: true
      }
    ]

    this.retryConfig = {
      maxRetries: 2,
      retryDelay: 2000
    }
  }

  /**
   * 处理OCR错误
   */
  async handleOCRError(error, context) {
    console.log('OCR识别失败，开始降级处理:', error.message)
    
    const errorInfo = {
      originalError: error,
      context: context,
      timestamp: Date.now(),
      strategies: []
    }

    // 依次尝试降级策略
    for (const strategy of this.fallbackStrategies) {
      try {
        const result = await this.executeStrategy(strategy, errorInfo)
        if (result.success) {
          errorInfo.strategies.push({
            strategy,
            success: true,
            result: result.data
          })
          return result
        } else {
          errorInfo.strategies.push({
            strategy,
            success: false,
            error: result.error
          })
        }
      } catch (strategyError) {
        errorInfo.strategies.push({
          strategy,
          success: false,
          error: strategyError.message
        })
        console.error(`降级策略${strategy}执行失败:`, strategyError)
      }
    }

    // 所有策略都失败了
    throw this.createOCRError('所有OCR降级策略都失败了', errorInfo)
  }

  /**
   * 执行降级策略
   */
  async executeStrategy(strategy, errorInfo) {
    switch (strategy) {
      case 'retry':
        return await this.retryStrategy(errorInfo)
      case 'alternative_service':
        return await this.alternativeServiceStrategy(errorInfo)
      case 'manual_input':
        return await this.manualInputStrategy(errorInfo)
      case 'image_enhancement':
        return await this.imageEnhancementStrategy(errorInfo)
      case 'partial_recognition':
        return await this.partialRecognitionStrategy(errorInfo)
      default:
        throw new Error(`未知的降级策略: ${strategy}`)
    }
  }

  /**
   * 重试策略
   */
  async retryStrategy(errorInfo) {
    const { context } = errorInfo
    
    // 检查是否已经重试过
    if (context.retryCount >= this.retryConfig.maxRetries) {
      return { success: false, error: '已达到最大重试次数' }
    }

    try {
      // 等待一段时间后重试
      await this.sleep(this.retryConfig.retryDelay)
      
      // 重新调用OCR服务
      const result = await this.callOCRService(context.service, context.imageData)
      
      return {
        success: true,
        data: result,
        strategy: 'retry'
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 备用服务策略
   */
  async alternativeServiceStrategy(errorInfo) {
    const { context } = errorInfo
    const currentService = context.service
    
    // 获取可用的备用服务
    const alternativeServices = this.ocrServices
      .filter(service => service.name !== currentService && service.available)
      .sort((a, b) => a.priority - b.priority)

    if (alternativeServices.length === 0) {
      return { success: false, error: '没有可用的备用OCR服务' }
    }

    // 尝试备用服务
    for (const service of alternativeServices) {
      try {
        const result = await this.callOCRService(service.name, context.imageData)
        
        return {
          success: true,
          data: result,
          strategy: 'alternative_service',
          usedService: service.name
        }
      } catch (error) {
        console.error(`备用服务${service.name}也失败了:`, error)
        continue
      }
    }

    return { success: false, error: '所有备用OCR服务都失败了' }
  }

  /**
   * 手动输入策略
   */
  async manualInputStrategy(errorInfo) {
    try {
      // 显示手动输入提示
      const userChoice = await this.showManualInputDialog()
      
      if (userChoice.confirm) {
        return {
          success: true,
          data: {
            text: '',
            confidence: 0,
            manual: true,
            message: '请手动输入识别内容'
          },
          strategy: 'manual_input'
        }
      } else {
        return { success: false, error: '用户取消手动输入' }
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 图像增强策略
   */
  async imageEnhancementStrategy(errorInfo) {
    const { context } = errorInfo
    
    try {
      // 对图像进行增强处理
      const enhancedImage = await this.enhanceImage(context.imageData)
      
      // 使用增强后的图像重新识别
      const result = await this.callOCRService(context.service, enhancedImage)
      
      return {
        success: true,
        data: result,
        strategy: 'image_enhancement'
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 部分识别策略
   */
  async partialRecognitionStrategy(errorInfo) {
    const { context } = errorInfo
    
    try {
      // 尝试识别图像的不同区域
      const regions = await this.splitImageRegions(context.imageData)
      const partialResults = []
      
      for (const region of regions) {
        try {
          const result = await this.callOCRService(context.service, region)
          if (result.text && result.text.trim()) {
            partialResults.push(result)
          }
        } catch (error) {
          // 忽略单个区域的失败
          continue
        }
      }

      if (partialResults.length > 0) {
        return {
          success: true,
          data: {
            text: partialResults.map(r => r.text).join(' '),
            confidence: Math.max(...partialResults.map(r => r.confidence || 0)),
            partial: true,
            regions: partialResults.length
          },
          strategy: 'partial_recognition'
        }
      } else {
        return { success: false, error: '部分识别也未获得结果' }
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 调用OCR服务
   */
  async callOCRService(serviceName, imageData) {
    // 这里应该调用实际的OCR服务
    // 为了演示，这里返回模拟结果
    throw new Error('OCR服务调用失败')
  }

  /**
   * 图像增强
   */
  async enhanceImage(imageData) {
    // 这里实现图像增强逻辑
    // 可以包括：亮度调整、对比度增强、去噪等
    return imageData
  }

  /**
   * 分割图像区域
   */
  async splitImageRegions(imageData) {
    // 这里实现图像区域分割逻辑
    // 返回多个图像区域
    return [imageData]
  }

  /**
   * 显示手动输入对话框
   */
  showManualInputDialog() {
    return new Promise((resolve) => {
      uni.showModal({
        title: 'OCR识别失败',
        content: 'OCR识别失败，是否手动输入内容？',
        confirmText: '手动输入',
        cancelText: '取消',
        success: (res) => {
          resolve(res)
        },
        fail: () => {
          resolve({ confirm: false })
        }
      })
    })
  }

  /**
   * 创建OCR错误
   */
  createOCRError(message, errorInfo) {
    const error = new Error(message)
    error.name = 'OCRError'
    error.code = 'OCR_FAILED'
    error.errorInfo = errorInfo
    return error
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 设置OCR服务可用性
   */
  setServiceAvailability(serviceName, available) {
    const service = this.ocrServices.find(s => s.name === serviceName)
    if (service) {
      service.available = available
    }
  }

  /**
   * 获取OCR服务状态
   */
  getServiceStatus() {
    return this.ocrServices.map(service => ({
      name: service.name,
      priority: service.priority,
      available: service.available
    }))
  }

  /**
   * 获取降级策略统计
   */
  getStrategyStats() {
    // 这里可以实现策略使用统计
    return {
      strategies: this.fallbackStrategies,
      totalAttempts: 0,
      successRate: 0
    }
  }
}

module.exports = OCRErrorHandler