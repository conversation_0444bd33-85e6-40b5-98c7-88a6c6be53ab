/**
 * 网络状态管理和优化工具
 */

class NetworkManager {
  constructor() {
    this.isOnline = true
    this.networkType = 'wifi'
    this.retryQueue = []
    this.isRetrying = false
    this.listeners = []
    
    this.init()
  }

  /**
   * 初始化网络监听
   */
  init() {
    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      const wasOnline = this.isOnline
      this.isOnline = res.isConnected
      this.networkType = res.networkType
      
      // 网络状态变化通知
      this.notifyListeners({
        isOnline: this.isOnline,
        networkType: this.networkType,
        wasOnline
      })
      
      // 网络恢复时处理重试队列
      if (this.isOnline && !wasOnline) {
        this.handleNetworkReconnect()
      }
    })
    
    // 获取初始网络状态
    uni.getNetworkType({
      success: (res) => {
        this.isOnline = res.networkType !== 'none'
        this.networkType = res.networkType
      }
    })
  }

  /**
   * 添加网络状态监听器
   */
  addListener(callback) {
    this.listeners.push(callback)
    return () => {
      const index = this.listeners.indexOf(callback)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners(status) {
    this.listeners.forEach(callback => {
      try {
        callback(status)
      } catch (error) {
        console.error('网络状态监听器错误:', error)
      }
    })
  }

  /**
   * 检查网络连接
   */
  async checkConnection() {
    try {
      const result = await uni.getNetworkType()
      this.isOnline = result.networkType !== 'none'
      this.networkType = result.networkType
      return this.isOnline
    } catch (error) {
      console.error('检查网络连接失败:', error)
      return false
    }
  }

  /**
   * 网络请求包装器（带重试机制）
   */
  async request(options, retryOptions = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      exponentialBackoff = true,
      retryCondition = (error) => this.shouldRetry(error)
    } = retryOptions

    let lastError
    let delay = retryDelay

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // 检查网络状态
        if (!this.isOnline) {
          throw new Error('NETWORK_OFFLINE')
        }

        // 发起请求
        const result = await this.makeRequest(options)
        return result
      } catch (error) {
        lastError = error
        
        // 如果是最后一次尝试或不应该重试，直接抛出错误
        if (attempt === maxRetries || !retryCondition(error)) {
          throw error
        }

        // 等待后重试
        await this.delay(delay)
        
        // 指数退避
        if (exponentialBackoff) {
          delay *= 2
        }
      }
    }

    throw lastError
  }

  /**
   * 实际发起请求
   */
  async makeRequest(options) {
    return new Promise((resolve, reject) => {
      uni.request({
        ...options,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res)
          } else {
            reject(new Error(`HTTP_${res.statusCode}`))
          }
        },
        fail: (error) => {
          reject(new Error(error.errMsg || 'REQUEST_FAILED'))
        }
      })
    })
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    const retryableErrors = [
      'NETWORK_OFFLINE',
      'REQUEST_FAILED',
      'HTTP_500',
      'HTTP_502',
      'HTTP_503',
      'HTTP_504'
    ]
    
    return retryableErrors.some(retryableError => 
      error.message.includes(retryableError)
    )
  }

  /**
   * 添加到重试队列
   */
  addToRetryQueue(requestInfo) {
    this.retryQueue.push({
      ...requestInfo,
      timestamp: Date.now(),
      retryCount: 0
    })
  }

  /**
   * 网络重连时处理重试队列
   */
  async handleNetworkReconnect() {
    if (this.isRetrying || this.retryQueue.length === 0) {
      return
    }

    this.isRetrying = true
    
    try {
      // 显示重连提示
      uni.showToast({
        title: '网络已恢复，正在同步数据...',
        icon: 'loading',
        duration: 2000
      })

      // 处理队列中的请求
      const queue = [...this.retryQueue]
      this.retryQueue = []

      for (const item of queue) {
        try {
          await this.request(item.options, item.retryOptions)
          
          // 通知请求成功
          if (item.onSuccess) {
            item.onSuccess()
          }
        } catch (error) {
          console.error('重试请求失败:', error)
          
          // 如果重试次数未达到上限，重新加入队列
          if (item.retryCount < 3) {
            this.retryQueue.push({
              ...item,
              retryCount: item.retryCount + 1
            })
          } else if (item.onError) {
            item.onError(error)
          }
        }
      }

      // 显示同步完成提示
      if (queue.length > 0) {
        uni.showToast({
          title: '数据同步完成',
          icon: 'success',
          duration: 1500
        })
      }
    } finally {
      this.isRetrying = false
    }
  }

  /**
   * 获取网络类型描述
   */
  getNetworkTypeDescription() {
    const descriptions = {
      'wifi': 'WiFi网络',
      '2g': '2G网络',
      '3g': '3G网络',
      '4g': '4G网络',
      '5g': '5G网络',
      'ethernet': '有线网络',
      'none': '无网络连接'
    }
    
    return descriptions[this.networkType] || '未知网络'
  }

  /**
   * 获取网络质量评估
   */
  getNetworkQuality() {
    switch (this.networkType) {
      case 'wifi':
      case '5g':
      case 'ethernet':
        return { level: 'excellent', description: '网络状况良好' }
      case '4g':
        return { level: 'good', description: '网络状况较好' }
      case '3g':
        return { level: 'fair', description: '网络状况一般' }
      case '2g':
        return { level: 'poor', description: '网络状况较差' }
      case 'none':
        return { level: 'none', description: '无网络连接' }
      default:
        return { level: 'unknown', description: '网络状况未知' }
    }
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清空重试队列
   */
  clearRetryQueue() {
    this.retryQueue = []
  }

  /**
   * 获取当前网络状态
   */
  getStatus() {
    return {
      isOnline: this.isOnline,
      networkType: this.networkType,
      description: this.getNetworkTypeDescription(),
      quality: this.getNetworkQuality(),
      retryQueueLength: this.retryQueue.length
    }
  }
}

// 创建全局网络管理器实例
const networkManager = new NetworkManager()

// 网络状态组件 Mixin
export const networkMixin = {
  data() {
    return {
      networkStatus: networkManager.getStatus()
    }
  },
  
  created() {
    // 添加网络状态监听
    this.removeNetworkListener = networkManager.addListener((status) => {
      this.networkStatus = networkManager.getStatus()
      this.onNetworkStatusChange && this.onNetworkStatusChange(status)
    })
  },
  
  beforeDestroy() {
    // 移除网络状态监听
    if (this.removeNetworkListener) {
      this.removeNetworkListener()
    }
  },
  
  methods: {
    // 检查网络连接
    async checkNetwork() {
      return await networkManager.checkConnection()
    },
    
    // 网络请求（带重试）
    async networkRequest(options, retryOptions) {
      return await networkManager.request(options, retryOptions)
    },
    
    // 显示网络状态提示
    showNetworkStatus() {
      const status = networkManager.getStatus()
      const quality = status.quality
      
      let title, icon
      if (status.isOnline) {
        title = `${status.description} - ${quality.description}`
        icon = quality.level === 'excellent' || quality.level === 'good' ? 'success' : 'none'
      } else {
        title = '网络连接不可用'
        icon = 'none'
      }
      
      uni.showToast({
        title,
        icon,
        duration: 2000
      })
    }
  }
}

export default networkManager