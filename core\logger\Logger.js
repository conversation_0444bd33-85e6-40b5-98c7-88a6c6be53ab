/**
 * 统一日志系统
 * 提供应用级别的日志记录功能
 */

class Logger {
  constructor(options = {}) {
    this.options = {
      level: options.level || 'info',
      enableConsole: options.enableConsole !== false,
      enableStorage: options.enableStorage || false,
      maxStorageSize: options.maxStorageSize || 1000,
      ...options
    }
    
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    }
    
    this.logs = []
    this.listeners = []
  }
  
  /**
   * 记录错误日志
   * @param {String} message - 日志消息
   * @param {Object} context - 上下文信息
   */
  error(message, context = {}) {
    this.log('error', message, context)
  }
  
  /**
   * 记录警告日志
   * @param {String} message - 日志消息
   * @param {Object} context - 上下文信息
   */
  warn(message, context = {}) {
    this.log('warn', message, context)
  }
  
  /**
   * 记录信息日志
   * @param {String} message - 日志消息
   * @param {Object} context - 上下文信息
   */
  info(message, context = {}) {
    this.log('info', message, context)
  }
  
  /**
   * 记录调试日志
   * @param {String} message - 日志消息
   * @param {Object} context - 上下文信息
   */
  debug(message, context = {}) {
    this.log('debug', message, context)
  }
  
  /**
   * 记录日志
   * @param {String} level - 日志级别
   * @param {String} message - 日志消息
   * @param {Object} context - 上下文信息
   */
  log(level, message, context = {}) {
    // 检查日志级别
    if (this.levels[level] > this.levels[this.options.level]) {
      return
    }
    
    const logEntry = {
      timestamp: new Date(),
      level,
      message,
      context: this.sanitizeContext(context),
      id: this.generateLogId()
    }
    
    // 控制台输出
    if (this.options.enableConsole) {
      this.outputToConsole(logEntry)
    }
    
    // 存储日志
    if (this.options.enableStorage) {
      this.storeLog(logEntry)
    }
    
    // 通知监听器
    this.notifyListeners(logEntry)
  }
  
  /**
   * 输出到控制台
   * @param {Object} logEntry - 日志条目
   */
  outputToConsole(logEntry) {
    const { timestamp, level, message, context } = logEntry
    const timeStr = timestamp.toISOString()
    const prefix = `[${timeStr}] [${level.toUpperCase()}]`
    
    switch (level) {
      case 'error':
        console.error(prefix, message, context)
        break
      case 'warn':
        console.warn(prefix, message, context)
        break
      case 'info':
        console.info(prefix, message, context)
        break
      case 'debug':
        console.debug(prefix, message, context)
        break
      default:
        console.log(prefix, message, context)
    }
  }
  
  /**
   * 存储日志
   * @param {Object} logEntry - 日志条目
   */
  storeLog(logEntry) {
    this.logs.push(logEntry)
    
    // 限制存储大小
    if (this.logs.length > this.options.maxStorageSize) {
      this.logs.shift()
    }
  }
  
  /**
   * 清理上下文信息
   * @param {Object} context - 原始上下文
   * @returns {Object} 清理后的上下文
   */
  sanitizeContext(context) {
    if (!context || typeof context !== 'object') {
      return context
    }
    
    const sanitized = {}
    
    for (const [key, value] of Object.entries(context)) {
      // 过滤敏感信息
      if (this.isSensitiveKey(key)) {
        sanitized[key] = '[REDACTED]'
      } else if (value instanceof Error) {
        sanitized[key] = {
          name: value.name,
          message: value.message,
          stack: value.stack
        }
      } else if (typeof value === 'object') {
        sanitized[key] = this.sanitizeContext(value)
      } else {
        sanitized[key] = value
      }
    }
    
    return sanitized
  }
  
  /**
   * 检查是否为敏感键
   * @param {String} key - 键名
   * @returns {Boolean} 是否敏感
   */
  isSensitiveKey(key) {
    const sensitiveKeys = [
      'password', 'token', 'secret', 'key', 'auth',
      'credential', 'private', 'confidential'
    ]
    
    const lowerKey = key.toLowerCase()
    return sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))
  }
  
  /**
   * 生成日志ID
   * @returns {String} 日志ID
   */
  generateLogId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
  
  /**
   * 添加日志监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.push(listener)
  }
  
  /**
   * 移除日志监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  /**
   * 通知监听器
   * @param {Object} logEntry - 日志条目
   */
  notifyListeners(logEntry) {
    for (const listener of this.listeners) {
      try {
        listener(logEntry)
      } catch (error) {
        console.error('日志监听器执行失败:', error)
      }
    }
  }
  
  /**
   * 获取存储的日志
   * @param {Object} filter - 过滤条件
   * @returns {Array} 日志列表
   */
  getLogs(filter = {}) {
    let logs = [...this.logs]
    
    // 按级别过滤
    if (filter.level) {
      logs = logs.filter(log => log.level === filter.level)
    }
    
    // 按时间范围过滤
    if (filter.startTime) {
      logs = logs.filter(log => log.timestamp >= filter.startTime)
    }
    
    if (filter.endTime) {
      logs = logs.filter(log => log.timestamp <= filter.endTime)
    }
    
    // 按消息内容过滤
    if (filter.message) {
      const searchTerm = filter.message.toLowerCase()
      logs = logs.filter(log => log.message.toLowerCase().includes(searchTerm))
    }
    
    // 排序
    logs.sort((a, b) => b.timestamp - a.timestamp)
    
    // 限制数量
    if (filter.limit) {
      logs = logs.slice(0, filter.limit)
    }
    
    return logs
  }
  
  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = []
  }
  
  /**
   * 导出日志
   * @param {String} format - 导出格式 (json|csv|txt)
   * @returns {String} 导出的日志数据
   */
  exportLogs(format = 'json') {
    const logs = this.getLogs()
    
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(logs, null, 2)
      
      case 'csv':
        return this.logsToCSV(logs)
      
      case 'txt':
        return this.logsToText(logs)
      
      default:
        throw new Error(`不支持的导出格式: ${format}`)
    }
  }
  
  /**
   * 转换日志为CSV格式
   * @param {Array} logs - 日志列表
   * @returns {String} CSV字符串
   */
  logsToCSV(logs) {
    if (logs.length === 0) return ''
    
    const headers = ['timestamp', 'level', 'message', 'context']
    const csvRows = [headers.join(',')]
    
    for (const log of logs) {
      const values = [
        log.timestamp.toISOString(),
        log.level,
        `"${log.message.replace(/"/g, '""')}"`,
        `"${JSON.stringify(log.context).replace(/"/g, '""')}"`
      ]
      csvRows.push(values.join(','))
    }
    
    return csvRows.join('\n')
  }
  
  /**
   * 转换日志为文本格式
   * @param {Array} logs - 日志列表
   * @returns {String} 文本字符串
   */
  logsToText(logs) {
    return logs.map(log => {
      const timeStr = log.timestamp.toISOString()
      const contextStr = Object.keys(log.context).length > 0 
        ? ` | ${JSON.stringify(log.context)}`
        : ''
      
      return `[${timeStr}] [${log.level.toUpperCase()}] ${log.message}${contextStr}`
    }).join('\n')
  }
  
  /**
   * 设置日志级别
   * @param {String} level - 日志级别
   */
  setLevel(level) {
    if (!this.levels.hasOwnProperty(level)) {
      throw new Error(`无效的日志级别: ${level}`)
    }
    this.options.level = level
  }
  
  /**
   * 获取当前日志级别
   * @returns {String} 当前日志级别
   */
  getLevel() {
    return this.options.level
  }
}

// 创建全局日志实例
const logger = new Logger({
  level: 'info',
  enableConsole: true,
  enableStorage: true
})

module.exports = { Logger, logger }