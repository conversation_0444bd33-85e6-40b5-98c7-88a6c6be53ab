/**
 * OCR服务基础测试
 */

describe('OCRService', () => {
  // Mock uni API
  global.uni = {
    request: jest.fn(),
    getFileSystemManager: jest.fn(() => ({
      readFile: jest.fn()
    }))
  }

  it('应该正确初始化OCR配置', () => {
    const apiConfig = {
      apiKey: 'YOUR_BAIDU_API_KEY',
      secretKey: 'YOUR_BAIDU_SECRET_KEY',
      tokenUrl: 'https://aip.baidubce.com/oauth/2.0/token',
      ocrUrl: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic',
      accurateOcrUrl: 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic'
    }

    expect(apiConfig.apiKey).toBe('YOUR_BAIDU_API_KEY')
    expect(apiConfig.secretKey).toBe('YOUR_BAIDU_SECRET_KEY')
    expect(apiConfig.tokenUrl).toContain('oauth/2.0/token')
    expect(apiConfig.ocrUrl).toContain('ocr/v1/general_basic')
  })

  it('应该包含健康报告识别模式', () => {
    const healthReportPatterns = {
      // 检查项目名称模式
      itemName: [
        /血红蛋白|血糖|胆固醇|甘油三酯|尿酸|肌酐|谷丙转氨酶|谷草转氨酶/,
        /白细胞|红细胞|血小板|中性粒细胞|淋巴细胞/,
        /总蛋白|白蛋白|球蛋白|总胆红素|直接胆红素|间接胆红素/
      ],
      
      // 数值模式（包含单位）
      value: /(\d+\.?\d*)\s*([a-zA-Z\/μℓ%]+)/g,
      
      // 参考值范围模式
      referenceRange: /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/g,
      
      // 日期模式
      date: /(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日]?/g,
      
      // 医院名称模式
      hospital: /(医院|医疗|诊所|卫生院|中心)/,
      
      // 医生姓名模式
      doctor: /医师[:：]\s*([^\s\n]+)/
    }

    expect(healthReportPatterns.itemName).toBeInstanceOf(Array)
    expect(healthReportPatterns.value).toBeInstanceOf(RegExp)
    expect(healthReportPatterns.referenceRange).toBeInstanceOf(RegExp)
    expect(healthReportPatterns.date).toBeInstanceOf(RegExp)
    expect(healthReportPatterns.hospital).toBeInstanceOf(RegExp)
    expect(healthReportPatterns.doctor).toBeInstanceOf(RegExp)
  })

  it('应该能够测试健康项目名称模式', () => {
    const itemNamePatterns = [
      /血红蛋白|血糖|胆固醇|甘油三酯|尿酸|肌酐|谷丙转氨酶|谷草转氨酶/,
      /白细胞|红细胞|血小板|中性粒细胞|淋巴细胞/,
      /总蛋白|白蛋白|球蛋白|总胆红素|直接胆红素|间接胆红素/
    ]

    // 测试第一组模式
    expect(itemNamePatterns[0].test('血红蛋白')).toBe(true)
    expect(itemNamePatterns[0].test('血糖')).toBe(true)
    expect(itemNamePatterns[0].test('胆固醇')).toBe(true)
    expect(itemNamePatterns[0].test('无关项目')).toBe(false)

    // 测试第二组模式
    expect(itemNamePatterns[1].test('白细胞')).toBe(true)
    expect(itemNamePatterns[1].test('红细胞')).toBe(true)
    expect(itemNamePatterns[1].test('血小板')).toBe(true)

    // 测试第三组模式
    expect(itemNamePatterns[2].test('总蛋白')).toBe(true)
    expect(itemNamePatterns[2].test('白蛋白')).toBe(true)
    expect(itemNamePatterns[2].test('总胆红素')).toBe(true)
  })

  it('应该能够测试数值模式', () => {
    const valuePattern = /(\d+\.?\d*)\s*([a-zA-Z\/μℓ%]+)/g

    const testCases = [
      { text: '120 g/L', expected: ['120', 'g/L'] },
      { text: '5.5 mmol/L', expected: ['5.5', 'mmol/L'] },
      { text: '85 %', expected: ['85', '%'] },
      { text: '3.2μmol/L', expected: ['3.2', 'μmol/L'] }
    ]

    testCases.forEach(({ text, expected }) => {
      const matches = [...text.matchAll(valuePattern)]
      if (matches.length > 0) {
        expect(matches[0][1]).toBe(expected[0])
        expect(matches[0][2]).toBe(expected[1])
      }
    })
  })

  it('应该能够测试参考值范围模式', () => {
    const referenceRangePattern = /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/g

    const testCases = [
      { text: '110-160', expected: ['110', '160'] },
      { text: '3.9~6.1', expected: ['3.9', '6.1'] },
      { text: '0.5 - 1.2', expected: ['0.5', '1.2'] }
    ]

    testCases.forEach(({ text, expected }) => {
      const matches = [...text.matchAll(referenceRangePattern)]
      if (matches.length > 0) {
        expect(matches[0][1]).toBe(expected[0])
        expect(matches[0][2]).toBe(expected[1])
      }
    })
  })

  it('应该能够测试日期模式', () => {
    const datePattern = /(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日]?/g

    const testCases = [
      { text: '2023年12月15日', expected: ['2023', '12', '15'] },
      { text: '2023-12-15', expected: ['2023', '12', '15'] },
      { text: '2023/12/15', expected: ['2023', '12', '15'] }
    ]

    testCases.forEach(({ text, expected }) => {
      const matches = [...text.matchAll(datePattern)]
      if (matches.length > 0) {
        expect(matches[0][1]).toBe(expected[0])
        expect(matches[0][2]).toBe(expected[1])
        expect(matches[0][3]).toBe(expected[2])
      }
    })
  })

  it('应该能够测试医院名称模式', () => {
    const hospitalPattern = /(医院|医疗|诊所|卫生院|中心)/

    expect(hospitalPattern.test('北京协和医院')).toBe(true)
    expect(hospitalPattern.test('上海市第一人民医院')).toBe(true)
    expect(hospitalPattern.test('社区卫生服务中心')).toBe(true)
    expect(hospitalPattern.test('某某诊所')).toBe(true)
    expect(hospitalPattern.test('普通文本')).toBe(false)
  })

  it('应该能够测试医生姓名模式', () => {
    const doctorPattern = /医师[:：]\s*([^\s\n]+)/

    const testCases = [
      { text: '医师：张医生', expected: '张医生' },
      { text: '医师: 李主任', expected: '李主任' },
      { text: '主治医师：王大夫', expected: '王大夫' }
    ]

    testCases.forEach(({ text, expected }) => {
      const match = text.match(doctorPattern)
      if (match) {
        expect(match[1]).toBe(expected)
      }
    })
  })

  it('应该能够模拟获取访问令牌', async () => {
    global.uni.request.mockResolvedValue({
      statusCode: 200,
      data: {
        access_token: 'mock_token',
        expires_in: 2592000
      }
    })

    const result = await new Promise((resolve) => {
      global.uni.request({
        url: 'https://aip.baidubce.com/oauth/2.0/token',
        method: 'POST',
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
          grant_type: 'client_credentials',
          client_id: 'test_api_key',
          client_secret: 'test_secret_key'
        }
      }).then(resolve)
    })

    expect(result.statusCode).toBe(200)
    expect(result.data.access_token).toBe('mock_token')
    expect(result.data.expires_in).toBe(2592000)
  })

  it('应该能够模拟文件读取', async () => {
    const mockFileManager = {
      readFile: jest.fn().mockImplementation((options) => {
        options.success({ data: 'base64string' })
      })
    }
    
    global.uni.getFileSystemManager.mockReturnValue(mockFileManager)

    const fileManager = global.uni.getFileSystemManager()
    
    const result = await new Promise((resolve) => {
      fileManager.readFile({
        filePath: '/mock/path/image.jpg',
        encoding: 'base64',
        success: (res) => resolve(res.data),
        fail: (error) => resolve(null)
      })
    })

    expect(result).toBe('base64string')
    expect(mockFileManager.readFile).toHaveBeenCalledWith({
      filePath: '/mock/path/image.jpg',
      encoding: 'base64',
      success: expect.any(Function),
      fail: expect.any(Function)
    })
  })
})