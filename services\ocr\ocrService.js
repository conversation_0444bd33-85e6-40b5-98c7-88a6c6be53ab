/**
 * OCR识别服务
 * 集成百度OCR API，提供健康报告识别功能
 */

import { ERROR_CODES } from '../../utils/platform/constants.js'

class OCRService {
  constructor() {
    // 百度OCR API配置
    this.apiConfig = {
      // 这些配置应该从环境变量或配置文件中读取
      apiKey: 'YOUR_BAIDU_API_KEY',
      secretKey: 'YOUR_BAIDU_SECRET_KEY',
      tokenUrl: 'https://aip.baidubce.com/oauth/2.0/token',
      ocrUrl: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic',
      accurateOcrUrl: 'https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic'
    }
    
    this.accessToken = null
    this.tokenExpireTime = 0
    
    // 健康报告关键字段模式
    this.healthReportPatterns = {
      // 检查项目名称模式
      itemName: [
        /血红蛋白|血糖|胆固醇|甘油三酯|尿酸|肌酐|谷丙转氨酶|谷草转氨酶/,
        /白细胞|红细胞|血小板|中性粒细胞|淋巴细胞/,
        /总蛋白|白蛋白|球蛋白|总胆红素|直接胆红素|间接胆红素/
      ],
      
      // 数值模式（包含单位）
      value: /(\d+\.?\d*)\s*([a-zA-Z\/μℓ%]+)/g,
      
      // 参考值范围模式
      referenceRange: /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/g,
      
      // 日期模式
      date: /(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日]?/g,
      
      // 医院名称模式
      hospital: /(医院|医疗|诊所|卫生院|中心)/,
      
      // 医生姓名模式
      doctor: /医师[:：]\s*([^\s\n]+)/
    }
  }

  /**
   * 识别健康报告图片
   * @param {string} imagePath 图片路径
   * @param {Object} options 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeHealthReport(imagePath, options = {}) {
    try {
      // 获取访问令牌
      await this._ensureAccessToken()
      
      // 将图片转换为base64
      const base64Image = await this._imageToBase64(imagePath)
      
      // 调用OCR API
      const ocrResult = await this._callOCRAPI(base64Image, options)
      
      if (!ocrResult.success) {
        throw new Error(ocrResult.error || 'OCR识别失败')
      }

      // 解析健康报告数据
      const parsedData = await this._parseHealthReportData(ocrResult.data)
      
      return {
        success: true,
        data: parsedData,
        rawOcrResult: ocrResult.data,
        confidence: this._calculateConfidence(parsedData)
      }
    } catch (error) {
      console.error('健康报告识别失败:', error)
      
      // 返回降级处理结果
      return this._getFallbackResult(error)
    }
  }

  /**
   * 批量识别多张图片
   * @param {Array<string>} imagePaths 图片路径数组
   * @param {Object} options 识别选项
   * @returns {Promise<Object>} 批量识别结果
   */
  async recognizeMultipleImages(imagePaths, options = {}) {
    try {
      const results = []
      const errors = []

      for (let i = 0; i < imagePaths.length; i++) {
        const imagePath = imagePaths[i]
        
        try {
          const result = await this.recognizeHealthReport(imagePath, options)
          results.push({
            index: i,
            imagePath,
            result
          })
        } catch (error) {
          errors.push({
            index: i,
            imagePath,
            error: error.message
          })
        }
      }

      return {
        success: true,
        results,
        errors,
        totalCount: imagePaths.length,
        successCount: results.length,
        errorCount: errors.length
      }
    } catch (error) {
      console.error('批量识别失败:', error)
      return {
        success: false,
        error: error.message,
        code: ERROR_CODES.OCR_BATCH_FAILED
      }
    }
  }

  /**
   * 验证OCR识别结果
   * @param {Object} ocrData OCR识别数据
   * @returns {Object} 验证结果
   */
  validateOCRResult(ocrData) {
    const validation = {
      isValid: true,
      issues: [],
      suggestions: [],
      confidence: 0
    }

    // 检查必要字段
    const requiredFields = ['items', 'date', 'hospital']
    for (const field of requiredFields) {
      if (!ocrData[field] || (Array.isArray(ocrData[field]) && ocrData[field].length === 0)) {
        validation.issues.push(`缺少${field}字段`)
        validation.suggestions.push(`请确保图片包含${field}信息`)
      }
    }

    // 检查检查项目数据完整性
    if (ocrData.items && ocrData.items.length > 0) {
      let validItemsCount = 0
      for (const item of ocrData.items) {
        if (item.name && item.value) {
          validItemsCount++
        }
      }
      
      validation.confidence = (validItemsCount / ocrData.items.length) * 100
      
      if (validItemsCount === 0) {
        validation.issues.push('未识别到有效的检查项目')
        validation.suggestions.push('请确保图片清晰，包含检查项目和数值')
      }
    }

    // 检查日期格式
    if (ocrData.date && !this._isValidDate(ocrData.date)) {
      validation.issues.push('日期格式不正确')
      validation.suggestions.push('请确保图片中的日期清晰可见')
    }

    validation.isValid = validation.issues.length === 0
    
    return validation
  }

  /**
   * 私有方法：确保访问令牌有效
   * @private
   */
  async _ensureAccessToken() {
    const now = Date.now()
    
    // 如果令牌不存在或已过期，重新获取
    if (!this.accessToken || now >= this.tokenExpireTime) {
      await this._getAccessToken()
    }
  }

  /**
   * 私有方法：获取百度API访问令牌
   * @private
   */
  async _getAccessToken() {
    try {
      const response = await uni.request({
        url: this.apiConfig.tokenUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
          grant_type: 'client_credentials',
          client_id: this.apiConfig.apiKey,
          client_secret: this.apiConfig.secretKey
        }
      })

      if (response.statusCode === 200 && response.data.access_token) {
        this.accessToken = response.data.access_token
        // 设置过期时间（提前5分钟刷新）
        this.tokenExpireTime = Date.now() + (response.data.expires_in - 300) * 1000
      } else {
        throw new Error('获取访问令牌失败: ' + JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('获取百度API访问令牌失败:', error)
      throw new Error('OCR服务初始化失败')
    }
  }

  /**
   * 私有方法：将图片转换为base64
   * @private
   */
  async _imageToBase64(imagePath) {
    return new Promise((resolve, reject) => {
      uni.getFileSystemManager().readFile({
        filePath: imagePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data)
        },
        fail: (error) => {
          reject(new Error('图片转换失败: ' + error.errMsg))
        }
      })
    })
  }

  /**
   * 私有方法：调用OCR API
   * @private
   */
  async _callOCRAPI(base64Image, options = {}) {
    try {
      const apiUrl = options.accurate ? 
        this.apiConfig.accurateOcrUrl : 
        this.apiConfig.ocrUrl

      const response = await uni.request({
        url: `${apiUrl}?access_token=${this.accessToken}`,
        method: 'POST',
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
          image: base64Image,
          language_type: 'CHN_ENG',
          detect_direction: 'true',
          paragraph: 'false',
          probability: 'true'
        }
      })

      if (response.statusCode === 200 && response.data.words_result) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error('OCR API调用失败: ' + JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('OCR API调用失败:', error)
      return {
        success: false,
        error: error.message,
        code: ERROR_CODES.OCR_API_FAILED
      }
    }
  }

  /**
   * 私有方法：解析健康报告数据
   * @private
   */
  async _parseHealthReportData(ocrData) {
    const parsedData = {
      items: [],
      date: null,
      hospital: null,
      doctor: null,
      rawText: '',
      metadata: {
        totalWords: ocrData.words_result ? ocrData.words_result.length : 0,
        averageConfidence: 0
      }
    }

    if (!ocrData.words_result || ocrData.words_result.length === 0) {
      return parsedData
    }

    // 合并所有识别的文本
    const allText = ocrData.words_result.map(item => item.words).join('\n')
    parsedData.rawText = allText

    // 计算平均置信度
    const totalConfidence = ocrData.words_result.reduce((sum, item) => {
      return sum + (item.probability ? item.probability.average : 0)
    }, 0)
    parsedData.metadata.averageConfidence = totalConfidence / ocrData.words_result.length

    // 解析检查项目
    parsedData.items = this._extractHealthItems(allText)

    // 解析日期
    parsedData.date = this._extractDate(allText)

    // 解析医院名称
    parsedData.hospital = this._extractHospital(allText)

    // 解析医生姓名
    parsedData.doctor = this._extractDoctor(allText)

    return parsedData
  }

  /**
   * 私有方法：提取健康检查项目
   * @private
   */
  _extractHealthItems(text) {
    const items = []
    const lines = text.split('\n')

    for (const line of lines) {
      // 查找包含健康指标的行
      for (const pattern of this.healthReportPatterns.itemName) {
        if (pattern.test(line)) {
          const item = this._parseHealthItem(line)
          if (item) {
            items.push(item)
          }
          break
        }
      }
    }

    return items
  }

  /**
   * 私有方法：解析单个健康检查项目
   * @private
   */
  _parseHealthItem(line) {
    try {
      // 提取项目名称（通常在行首）
      const nameMatch = line.match(/^([^\d]+?)[\s\d]/);
      if (!nameMatch) return null;

      const name = nameMatch[1].trim();

      // 提取数值和单位
      const valueMatches = [...line.matchAll(this.healthReportPatterns.value)];
      if (valueMatches.length === 0) return null;

      const value = valueMatches[0][1];
      const unit = valueMatches[0][2];

      // 提取参考值范围
      const rangeMatches = [...line.matchAll(this.healthReportPatterns.referenceRange)];
      let referenceRange = null;
      if (rangeMatches.length > 0) {
        referenceRange = {
          min: parseFloat(rangeMatches[0][1]),
          max: parseFloat(rangeMatches[0][2])
        };
      }

      // 判断是否异常
      let isAbnormal = false;
      if (referenceRange) {
        const numValue = parseFloat(value);
        isAbnormal = numValue < referenceRange.min || numValue > referenceRange.max;
      }

      return {
        name: name,
        value: value,
        unit: unit,
        referenceRange: referenceRange,
        isAbnormal: isAbnormal,
        rawText: line.trim()
      };
    } catch (error) {
      console.error('解析健康项目失败:', error);
      return null;
    }
  }

  /**
   * 私有方法：提取日期
   * @private
   */
  _extractDate(text) {
    const dateMatches = [...text.matchAll(this.healthReportPatterns.date)]
    if (dateMatches.length > 0) {
      const match = dateMatches[0]
      const year = match[1]
      const month = match[2].padStart(2, '0')
      const day = match[3].padStart(2, '0')
      return `${year}-${month}-${day}`
    }
    return null
  }

  /**
   * 私有方法：提取医院名称
   * @private
   */
  _extractHospital(text) {
    const lines = text.split('\n')
    for (const line of lines) {
      if (this.healthReportPatterns.hospital.test(line)) {
        // 提取包含医院关键词的行，通常是医院全名
        return line.trim()
      }
    }
    return null
  }

  /**
   * 私有方法：提取医生姓名
   * @private
   */
  _extractDoctor(text) {
    const doctorMatch = text.match(this.healthReportPatterns.doctor)
    if (doctorMatch) {
      return doctorMatch[1].trim()
    }
    return null
  }

  /**
   * 私有方法：计算识别置信度
   * @private
   */
  _calculateConfidence(parsedData) {
    let score = 0
    let maxScore = 100

    // 基于识别到的字段数量计算置信度
    if (parsedData.items && parsedData.items.length > 0) score += 40
    if (parsedData.date) score += 20
    if (parsedData.hospital) score += 20
    if (parsedData.doctor) score += 10
    if (parsedData.metadata.averageConfidence > 0.8) score += 10

    return Math.min(score, maxScore)
  }

  /**
   * 私有方法：获取降级处理结果
   * @private
   */
  _getFallbackResult(error) {
    return {
      success: false,
      error: error.message,
      code: ERROR_CODES.OCR_RECOGNITION_FAILED,
      fallback: {
        manualInput: true,
        suggestions: [
          '请检查图片是否清晰',
          '确保图片包含完整的检查报告',
          '可以尝试重新拍摄或选择其他图片',
          '也可以选择手动输入检查结果'
        ]
      }
    }
  }

  /**
   * 私有方法：验证日期格式
   * @private
   */
  _isValidDate(dateString) {
    const date = new Date(dateString)
    return date instanceof Date && !isNaN(date)
  }
}

// 创建单例实例
const ocrService = new OCRService()

export default ocrService
export { OCRService }