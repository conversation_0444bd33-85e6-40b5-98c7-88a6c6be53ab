/**
 * 图片服务单元测试
 */

const { ImageService } = require('../../services/ocr/imageService.js')

// Mock uni API
global.uni = {
  chooseImage: jest.fn(),
  compressImage: jest.fn(),
  getImageInfo: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  previewImage: jest.fn()
}

// Mock platform adapter
jest.mock('../../utils/platform/PlatformAdapter.js', () => ({
  default: {
    chooseImage: jest.fn(),
    takePhoto: jest.fn(),
    chooseFromAlbum: jest.fn(),
    checkPermission: jest.fn(),
    requestPermission: jest.fn()
  }
}))

describe('ImageService', () => {
  let service

  beforeEach(() => {
    service = new ImageService()
    jest.clearAllMocks()
  })

  describe('构造函数', () => {
    it('应该正确初始化默认配置', () => {
      expect(service.maxImageSize).toBe(2 * 1024 * 1024)
      expect(service.maxWidth).toBe(1920)
      expect(service.maxHeight).toBe(1080)
      expect(service.quality).toBe(0.8)
      expect(service.supportedFormats).toEqual(['jpg', 'jpeg', 'png', 'webp'])
    })
  })

  describe('selectImage', () => {
    it('应该成功选择图片', async () => {
      const mockPlatformAdapter = await import('../../utils/platform/PlatformAdapter.js')
      mockPlatformAdapter.default.chooseImage.mockResolvedValue({
        success: true,
        tempFilePaths: ['/mock/path/image.jpg'],
        tempFiles: [{ size: 1024 }]
      })

      // Mock _processImage method
      service._processImage = jest.fn().mockResolvedValue({
        originalPath: '/mock/path/image.jpg',
        processedPath: '/mock/path/image.jpg',
        originalSize: 1024,
        processedSize: 1024,
        width: 800,
        height: 600,
        format: 'jpg',
        quality: { isValid: true, score: 85 },
        timestamp: Date.now()
      })

      const result = await service.selectImage()

      expect(result.success).toBe(true)
      expect(result.images).toHaveLength(1)
      expect(result.images[0].originalPath).toBe('/mock/path/image.jpg')
    })

    it('应该处理选择图片失败的情况', async () => {
      const mockPlatformAdapter = await import('../../utils/platform/PlatformAdapter.js')
      mockPlatformAdapter.default.chooseImage.mockResolvedValue({
        success: false,
        error: '用户取消选择'
      })

      const result = await service.selectImage()

      expect(result.success).toBe(false)
      expect(result.error).toBe('图片选择失败')
    })
  })

  describe('takePhoto', () => {
    it('应该成功拍照', async () => {
      const mockPlatformAdapter = await import('../../utils/platform/PlatformAdapter.js')
      mockPlatformAdapter.default.takePhoto.mockResolvedValue({
        success: true,
        tempFilePaths: ['/mock/path/photo.jpg']
      })

      service._processImage = vi.fn().mockResolvedValue({
        originalPath: '/mock/path/photo.jpg',
        processedPath: '/mock/path/photo.jpg',
        originalSize: 2048,
        processedSize: 1024,
        width: 1200,
        height: 900,
        format: 'jpg',
        quality: { isValid: true, score: 90 },
        timestamp: Date.now()
      })

      const result = await service.takePhoto()

      expect(result.success).toBe(true)
      expect(result.image.originalPath).toBe('/mock/path/photo.jpg')
    })

    it('应该处理拍照失败的情况', async () => {
      const mockPlatformAdapter = await import('../../utils/platform/PlatformAdapter.js')
      mockPlatformAdapter.default.takePhoto.mockRejectedValue(new Error('相机权限未授权'))

      const result = await service.takePhoto()

      expect(result.success).toBe(false)
      expect(result.error).toBe('相机权限未授权')
    })
  })

  describe('compressImage', () => {
    it('应该成功压缩图片', async () => {
      global.uni.compressImage.mockImplementation((options) => {
        options.success({
          tempFilePath: '/mock/path/compressed.jpg',
          size: 512
        })
      })

      const result = await service.compressImage('/mock/path/original.jpg')

      expect(result.success).toBe(true)
      expect(result.tempFilePath).toBe('/mock/path/compressed.jpg')
      expect(result.size).toBe(512)
    })

    it('应该处理压缩失败的情况', async () => {
      global.uni.compressImage.mockImplementation((options) => {
        options.fail({
          errMsg: '压缩失败'
        })
      })

      const result = await service.compressImage('/mock/path/original.jpg')

      expect(result.success).toBe(false)
      expect(result.error).toBe('压缩失败')
    })
  })

  describe('checkImageQuality', () => {
    beforeEach(() => {
      service._getImageInfo = vi.fn().mockResolvedValue({
        width: 800,
        height: 600,
        size: 1024 * 1024, // 1MB
        path: '/mock/path/image.jpg'
      })
      service._detectBlur = vi.fn().mockResolvedValue(0.8)
      service._getImageFormat = vi.fn().mockReturnValue('jpg')
    })

    it('应该返回高质量图片的正确评分', async () => {
      const result = await service.checkImageQuality('/mock/path/image.jpg')

      expect(result.success).toBe(true)
      expect(result.quality.isValid).toBe(true)
      expect(result.quality.score).toBeGreaterThan(60)
      expect(result.quality.issues).toHaveLength(0)
    })

    it('应该检测出文件过大的问题', async () => {
      service._getImageInfo.mockResolvedValue({
        width: 800,
        height: 600,
        size: 3 * 1024 * 1024, // 3MB，超过限制
        path: '/mock/path/image.jpg'
      })

      const result = await service.checkImageQuality('/mock/path/image.jpg')

      expect(result.success).toBe(true)
      expect(result.quality.issues).toContain('文件过大')
      expect(result.quality.suggestions).toContain('建议压缩图片或重新拍摄')
    })

    it('应该检测出分辨率过低的问题', async () => {
      service._getImageInfo.mockResolvedValue({
        width: 200,
        height: 150,
        size: 1024,
        path: '/mock/path/image.jpg'
      })

      const result = await service.checkImageQuality('/mock/path/image.jpg')

      expect(result.success).toBe(true)
      expect(result.quality.issues).toContain('图片分辨率过低')
      expect(result.quality.suggestions).toContain('建议使用更高分辨率的图片')
    })

    it('应该检测出不支持的格式', async () => {
      service._getImageFormat.mockReturnValue('bmp')

      const result = await service.checkImageQuality('/mock/path/image.bmp')

      expect(result.success).toBe(true)
      expect(result.quality.issues).toContain('不支持的图片格式')
      expect(result.quality.suggestions).toContain('请使用JPG、PNG或WebP格式的图片')
    })

    it('应该检测出模糊图片', async () => {
      service._detectBlur.mockResolvedValue(0.3) // 低模糊分数

      const result = await service.checkImageQuality('/mock/path/image.jpg')

      expect(result.success).toBe(true)
      expect(result.quality.issues).toContain('图片可能模糊')
      expect(result.quality.suggestions).toContain('建议重新拍摄更清晰的图片')
    })
  })

  describe('_getImageInfo', () => {
    it('应该成功获取图片信息', async () => {
      global.uni.getImageInfo.mockImplementation((options) => {
        options.success({
          width: 800,
          height: 600,
          path: '/mock/path/image.jpg',
          size: 1024,
          type: 'jpeg'
        })
      })

      const result = await service._getImageInfo('/mock/path/image.jpg')

      expect(result.width).toBe(800)
      expect(result.height).toBe(600)
      expect(result.size).toBe(1024)
    })

    it('应该处理获取图片信息失败的情况', async () => {
      global.uni.getImageInfo.mockImplementation((options) => {
        options.fail({
          errMsg: '文件不存在'
        })
      })

      await expect(service._getImageInfo('/mock/path/nonexistent.jpg'))
        .rejects.toThrow('文件不存在')
    })
  })

  describe('_getImageFormat', () => {
    it('应该正确提取图片格式', () => {
      expect(service._getImageFormat('/path/image.jpg')).toBe('jpg')
      expect(service._getImageFormat('/path/image.PNG')).toBe('png')
      expect(service._getImageFormat('/path/image.jpeg')).toBe('jpeg')
      expect(service._getImageFormat('/path/image')).toBe('unknown')
    })
  })

  describe('_detectBlur', () => {
    it('应该基于图片尺寸返回模糊分数', async () => {
      service._getImageInfo = vi.fn().mockResolvedValue({
        width: 1200,
        height: 900
      })

      const score = await service._detectBlur('/mock/path/image.jpg')

      expect(score).toBe(0.8) // 高分辨率图片应该得到高分
    })

    it('应该为低分辨率图片返回低分', async () => {
      service._getImageInfo = vi.fn().mockResolvedValue({
        width: 200,
        height: 150
      })

      const score = await service._detectBlur('/mock/path/image.jpg')

      expect(score).toBe(0.3) // 低分辨率图片应该得到低分
    })
  })
})

describe('imageService 单例', () => {
  it('应该导出单例实例', () => {
    expect(imageService).toBeInstanceOf(ImageService)
  })

  it('应该在多次导入时返回同一个实例', async () => {
    const { default: imageService2 } = await import('../../services/ocr/imageService.js')
    expect(imageService).toBe(imageService2)
  })
})