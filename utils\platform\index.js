/**
 * 平台适配层统一入口
 */

// 导出平台检测工具
export {
  getCurrentPlatform,
  isApp,
  isWeixinMp,
  isMp,
  isH5,
  isIOS,
  isAndroid,
  getSystemInfo,
  getPlatformConfig
} from './detector.js'

// 导出平台常量
export {
  PLATFORM_TYPES,
  SYSTEM_TYPES,
  CAMERA_CONFIG,
  STORAGE_CONFIG,
  SHARE_CONFIG,
  NETWORK_CONFIG,
  FILE_CONFIG,
  PERMISSION_CONFIG,
  ERROR_CODES
} from './constants.js'

// 导出平台适配器
export { default as PlatformAdapter, PlatformAdapter as PlatformAdapterClass } from './PlatformAdapter.js'

// 默认导出适配器实例
import PlatformAdapter from './PlatformAdapter.js'
export default PlatformAdapter