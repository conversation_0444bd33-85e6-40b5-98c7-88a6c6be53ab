/**
 * ReportRepository 单元测试
 * 测试报告数据访问层功能
 */

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { ReportRepository } = require('../../core/storage/ReportRepository.js')
const { Report } = require('../../models/Report.js')

// Mock StorageManager
const mockStorageManager = {
  insert: jest.fn(),
  findById: jest.fn(),
  findAll: jest.fn(),
  findBy: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  beginTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn()
}

// Mock logger
jest.mock('../../core/logger/Logger.js', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}))

describe('ReportRepository', () => {
  let reportRepository
  
  beforeEach(() => {
    jest.clearAllMocks()
    reportRepository = new ReportRepository(mockStorageManager)
  })
  
  describe('构造函数', () => {
    it('应该正确初始化', () => {
      expect(reportRepository.storageManager).toBe(mockStorageManager)
      expect(reportRepository.modelClass).toBe(Report)
      expect(reportRepository.tableName).toBe('reports')
    })
  })
  
  describe('findByUserId', () => {
    it('应该根据用户ID查找报告', async () => {
      const mockReports = [
        { id: 'report1', userId: 'user1', title: '体检报告1' },
        { id: 'report2', userId: 'user1', title: '体检报告2' }
      ]
      
      mockStorageManager.findBy.mockResolvedValue(mockReports)
      Report.fromJSON = jest.fn()
        .mockReturnValueOnce(mockReports[0])
        .mockReturnValueOnce(mockReports[1])
      
      const result = await reportRepository.findByUserId('user1')
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('reports', 
        { userId: 'user1' }, 
        { sort: { checkDate: -1 } }
      )
      expect(result).toHaveLength(2)
    })
    
    it('应该支持自定义查询选项', async () => {
      mockStorageManager.findBy.mockResolvedValue([])
      
      await reportRepository.findByUserId('user1', { limit: 10 })
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('reports',
        { userId: 'user1' },
        { sort: { checkDate: -1 }, limit: 10 }
      )
    })
  })
  
  describe('findByDateRange', () => {
    it('应该根据时间范围查找报告', async () => {
      const startDate = new Date('2023-01-01')
      const endDate = new Date('2023-12-31')
      
      mockStorageManager.findBy.mockResolvedValue([])
      
      await reportRepository.findByDateRange('user1', startDate, endDate)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('reports', {
        userId: 'user1',
        checkDate: {
          $gte: startDate,
          $lte: endDate
        }
      }, { sort: { checkDate: -1 } })
    })
  })
  
  describe('findByCategories', () => {
    it('应该根据分类查找报告', async () => {
      const categories = ['血常规', '生化检查']
      
      mockStorageManager.findBy.mockResolvedValue([])
      
      await reportRepository.findByCategories('user1', categories)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('reports', {
        userId: 'user1',
        'items.category': { $in: categories }
      }, { sort: { checkDate: -1 } })
    })
  })
  
  describe('findAbnormalReports', () => {
    it('应该查找异常报告', async () => {
      mockStorageManager.findBy.mockResolvedValue([])
      
      await reportRepository.findAbnormalReports('user1')
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('reports', {
        userId: 'user1',
        'items.isAbnormal': true
      }, { sort: { checkDate: -1 } })
    })
  })
  
  describe('updateSyncStatus', () => {
    it('应该更新同步状态', async () => {
      reportRepository.update = jest.fn().mockResolvedValue(true)
      
      const result = await reportRepository.updateSyncStatus('report1', 'synced')
      
      expect(reportRepository.update).toHaveBeenCalledWith('report1', {
        syncStatus: 'synced',
        lastSyncAt: expect.any(Date)
      })
      expect(result).toBe(true)
    })
    
    it('应该验证同步状态的有效性', async () => {
      await expect(reportRepository.updateSyncStatus('report1', 'invalid')).rejects.toThrow('无效的同步状态: invalid')
    })
  })
  
  describe('searchReports', () => {
    it('应该搜索报告', async () => {
      const keyword = '血常规'
      
      mockStorageManager.findBy.mockResolvedValue([])
      
      await reportRepository.searchReports('user1', keyword)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('reports', {
        userId: 'user1',
        $or: [
          { title: { $regex: keyword, $options: 'i' } },
          { hospital: { $regex: keyword, $options: 'i' } },
          { doctor: { $regex: keyword, $options: 'i' } },
          { notes: { $regex: keyword, $options: 'i' } },
          { 'items.name': { $regex: keyword, $options: 'i' } }
        ]
      }, { sort: { checkDate: -1 } })
    })
  })
  
  describe('getReportStats', () => {
    it('应该获取报告统计信息', async () => {
      const mockReports = [
        {
          id: 'report1',
          hospital: '医院A',
          checkDate: new Date('2023-12-01'),
          items: [
            { category: '血常规', isAbnormal: true },
            { category: '生化检查', isAbnormal: false }
          ]
        },
        {
          id: 'report2',
          hospital: '医院B',
          checkDate: new Date('2023-11-01'),
          items: [
            { category: '血常规', isAbnormal: false }
          ]
        }
      ]
      
      reportRepository.findByUserId = jest.fn().mockResolvedValue(mockReports)
      
      const result = await reportRepository.getReportStats('user1')
      
      expect(result.totalReports).toBe(2)
      expect(result.abnormalReports).toBe(1)
      expect(result.hospitalCount).toBe(2)
      expect(result.categoryCount['血常规']).toBe(2)
      expect(result.categoryCount['生化检查']).toBe(1)
    })
  })
  
  describe('getItemHistory', () => {
    it('应该获取检查项目历史数据', async () => {
      const mockReports = [
        {
          id: 'report1',
          checkDate: new Date('2023-12-01'),
          hospital: '医院A',
          items: [
            {
              name: '白细胞计数',
              value: '6.5',
              unit: '10^9/L',
              referenceRange: '3.5-9.5',
              isAbnormal: false
            }
          ]
        },
        {
          id: 'report2',
          checkDate: new Date('2023-11-01'),
          hospital: '医院B',
          items: [
            {
              name: '白细胞计数',
              value: '12.0',
              unit: '10^9/L',
              referenceRange: '3.5-9.5',
              isAbnormal: true
            }
          ]
        }
      ]
      
      reportRepository.findByUserId = jest.fn().mockResolvedValue(mockReports)
      
      const result = await reportRepository.getItemHistory('user1', '白细胞计数', 5)
      
      expect(result).toHaveLength(2)
      expect(result[0].reportId).toBe('report1')
      expect(result[0].value).toBe('6.5')
      expect(result[1].reportId).toBe('report2')
      expect(result[1].isAbnormal).toBe(true)
    })
    
    it('应该限制返回数量', async () => {
      const mockReports = Array.from({ length: 5 }, (_, i) => ({
        id: `report${i + 1}`,
        checkDate: new Date(`2023-${12 - i}-01`),
        items: [
          {
            name: '白细胞计数',
            value: `${6 + i}.0`,
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            isAbnormal: false
          }
        ]
      }))
      
      reportRepository.findByUserId = jest.fn().mockResolvedValue(mockReports)
      
      const result = await reportRepository.getItemHistory('user1', '白细胞计数', 3)
      
      expect(result).toHaveLength(3)
    })
  })
  
  describe('batchUpdateTags', () => {
    it('应该批量更新报告标签', async () => {
      const reportIds = ['report1', 'report2', 'report3']
      const tags = ['重要', '异常']
      
      reportRepository.update = jest.fn()
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(true)
        .mockRejectedValueOnce(new Error('更新失败'))
      
      const result = await reportRepository.batchUpdateTags(reportIds, tags)
      
      expect(reportRepository.update).toHaveBeenCalledTimes(3)
      expect(result).toBe(2) // 只有2个成功更新
    })
  })
  
  describe('getPendingSyncReports', () => {
    it('应该获取需要同步的报告', async () => {
      mockStorageManager.findBy.mockResolvedValue([])
      
      await reportRepository.getPendingSyncReports('user1', 20)
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('reports', {
        userId: 'user1',
        $or: [
          { syncStatus: 'local' },
          { syncStatus: 'pending' },
          { syncStatus: 'failed' }
        ]
      }, {
        sort: { updatedAt: 1 },
        limit: 20
      })
    })
  })
  
  describe('cleanupTempReports', () => {
    it('应该清理过期的临时报告', async () => {
      const tempReports = [
        { id: 'temp1', isTemporary: true },
        { id: 'temp2', isTemporary: true }
      ]
      
      reportRepository.findBy = jest.fn().mockResolvedValue(tempReports)
      reportRepository.delete = jest.fn().mockResolvedValue(true)
      
      const result = await reportRepository.cleanupTempReports(24)
      
      expect(reportRepository.findBy).toHaveBeenCalledWith({
        isTemporary: true,
        createdAt: { $lt: expect.any(Date) }
      })
      expect(reportRepository.delete).toHaveBeenCalledTimes(2)
      expect(result).toBe(2)
    })
  })
  
  describe('exportUserReports', () => {
    it('应该导出用户报告数据', async () => {
      reportRepository.exportData = jest.fn().mockResolvedValue('{"reports": []}')
      
      const result = await reportRepository.exportUserReports('user1', 'json')
      
      expect(reportRepository.exportData).toHaveBeenCalledWith({ userId: 'user1' }, 'json')
      expect(result).toBe('{"reports": []}')
    })
  })
})