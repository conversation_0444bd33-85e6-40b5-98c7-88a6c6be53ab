<template>
  <view class="report-list-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <text class="page-title">健康报告</text>
        <view class="header-actions">
          <button class="action-btn search-btn" @tap="showSearchModal">
            <uni-icons type="search" size="20" color="#666"></uni-icons>
          </button>
          <button class="action-btn filter-btn" @tap="showFilterModal">
            <uni-icons type="tune" size="20" color="#666"></uni-icons>
            <view v-if="hasActiveFilter" class="filter-dot"></view>
          </button>
        </view>
      </view>
      
      <!-- 统计信息 -->
      <view class="stats-bar">
        <view class="stat-item">
          <text class="stat-number">{{ statistics.totalReports }}</text>
          <text class="stat-label">总报告</text>
        </view>
        <view class="stat-item">
          <text class="stat-number abnormal">{{ statistics.abnormalCount }}</text>
          <text class="stat-label">异常</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ filteredReports.length }}</text>
          <text class="stat-label">当前显示</text>
        </view>
      </view>
    </view>
    
    <!-- 筛选条件显示 -->
    <view v-if="hasActiveFilter" class="active-filters">
      <scroll-view class="filter-tags" scroll-x="true">
        <view class="filter-tag-list">
          <view v-if="filter.timeRange.start" class="filter-tag">
            <text class="tag-text">
              {{ formatFilterDate(filter.timeRange.start) }} - 
              {{ formatFilterDate(filter.timeRange.end) }}
            </text>
            <button class="tag-close" @tap="clearTimeFilter">×</button>
          </view>
          <view v-if="filter.category" class="filter-tag">
            <text class="tag-text">{{ getCategoryName(filter.category) }}</text>
            <button class="tag-close" @tap="clearCategoryFilter">×</button>
          </view>
          <view v-if="filter.hospital" class="filter-tag">
            <text class="tag-text">{{ filter.hospital }}</text>
            <button class="tag-close" @tap="clearHospitalFilter">×</button>
          </view>
          <view v-if="filter.abnormalOnly" class="filter-tag">
            <text class="tag-text">仅异常</text>
            <button class="tag-close" @tap="clearAbnormalFilter">×</button>
          </view>
          <button class="clear-all-btn" @tap="clearAllFilters">清除全部</button>
        </view>
      </scroll-view>
    </view>
    
    <!-- 报告列表 -->
    <view class="list-container">
      <!-- 空状态 -->
      <view v-if="filteredReports.length === 0 && !loading.list" class="empty-state">
        <image class="empty-image" src="/static/images/empty-reports.png" mode="aspectFit"></image>
        <text class="empty-title">
          {{ hasActiveFilter ? '没有符合条件的报告' : '还没有健康报告' }}
        </text>
        <text class="empty-desc">
          {{ hasActiveFilter ? '试试调整筛选条件' : '点击右下角按钮添加第一份报告' }}
        </text>
        <button v-if="!hasActiveFilter" class="add-first-btn" @tap="goToAdd">
          添加报告
        </button>
      </view>
      
      <!-- 虚拟滚动列表 -->
      <VirtualList
        v-else
        :items="filteredReports"
        :item-height="itemHeight"
        :container-height="listHeight"
        :loading="loading.list"
        :has-more="pagination.hasMore"
        @loadMore="loadMoreReports"
        class="virtual-list"
      >
        <template #default="{ item }">
          <ReportCard
            :report="item"
            :show-actions="false"
            @tap="goToDetail"
            @edit="goToEdit"
            @delete="confirmDelete"
          />
        </template>
      </VirtualList>
    </view>
    
    <!-- 加载指示器 -->
    <view v-if="loading.list" class="loading-overlay">
      <uni-load-more status="loading"></uni-load-more>
    </view>
    
    <!-- 悬浮添加按钮 -->
    <view class="fab-container">
      <button class="fab-btn" @tap="goToAdd">
        <uni-icons type="plus" size="24" color="#ffffff"></uni-icons>
      </button>
    </view>
    
    <!-- 搜索弹窗 -->
    <uni-popup ref="searchPopup" type="top">
      <view class="search-modal">
        <view class="search-header">
          <input 
            class="search-input"
            v-model="searchKeyword"
            placeholder="搜索医院、医生或报告内容"
            @input="handleSearch"
            @confirm="handleSearchConfirm"
          />
          <button class="search-cancel" @tap="hideSearchModal">取消</button>
        </view>
        
        <!-- 搜索建议 -->
        <view v-if="searchSuggestions.length > 0" class="search-suggestions">
          <view 
            v-for="suggestion in searchSuggestions"
            :key="suggestion.id"
            class="suggestion-item"
            @tap="selectSuggestion(suggestion)"
          >
            <uni-icons type="search" size="16" color="#999"></uni-icons>
            <text class="suggestion-text">{{ suggestion.text }}</text>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <button class="modal-close" @tap="hideFilterModal">
            <uni-icons type="close" size="20" color="#666"></uni-icons>
          </button>
        </view>
        
        <view class="filter-content">
          <!-- 时间范围 -->
          <view class="filter-section">
            <text class="section-title">时间范围</text>
            <view class="time-range-picker">
              <picker 
                mode="date" 
                :value="tempFilter.timeRange.start"
                @change="onStartDateChange"
              >
                <view class="date-picker">
                  <text class="date-text">
                    {{ tempFilter.timeRange.start || '开始日期' }}
                  </text>
                </view>
              </picker>
              <text class="date-separator">至</text>
              <picker 
                mode="date" 
                :value="tempFilter.timeRange.end"
                @change="onEndDateChange"
              >
                <view class="date-picker">
                  <text class="date-text">
                    {{ tempFilter.timeRange.end || '结束日期' }}
                  </text>
                </view>
              </picker>
            </view>
          </view>
          
          <!-- 检查类别 -->
          <view class="filter-section">
            <text class="section-title">检查类别</text>
            <view class="category-options">
              <view 
                v-for="category in availableCategories"
                :key="category.value"
                :class="['category-option', { active: tempFilter.category === category.value }]"
                @tap="selectCategory(category.value)"
              >
                <text class="option-text">{{ category.label }}</text>
              </view>
            </view>
          </view>
          
          <!-- 医院筛选 -->
          <view class="filter-section">
            <text class="section-title">医院</text>
            <input 
              class="hospital-input"
              v-model="tempFilter.hospital"
              placeholder="输入医院名称"
            />
          </view>
          
          <!-- 其他选项 -->
          <view class="filter-section">
            <view class="option-row">
              <text class="option-label">仅显示异常指标</text>
              <switch 
                :checked="tempFilter.abnormalOnly"
                @change="onAbnormalOnlyChange"
                color="#007AFF"
              />
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <button class="action-btn reset-btn" @tap="resetFilter">重置</button>
          <button class="action-btn apply-btn" @tap="applyFilter">应用</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { useReportStore } from '@/stores/report.js'
import { Constants } from '@/types/index.js'
import VirtualList from '@/components/common/VirtualList.vue'
import ReportCard from '@/components/business/ReportCard.vue'

export default {
  name: 'ReportList',
  components: {
    VirtualList,
    ReportCard
  },
  
  data() {
    return {
      // 搜索相关
      searchKeyword: '',
      searchTimer: null,
      searchSuggestions: [],
      
      // 筛选条件
      filter: {
        timeRange: {
          start: null,
          end: null
        },
        category: '',
        hospital: '',
        abnormalOnly: false
      },
      
      // 临时筛选条件
      tempFilter: {
        timeRange: {
          start: null,
          end: null
        },
        category: '',
        hospital: '',
        abnormalOnly: false
      },
      
      // 虚拟滚动配置
      itemHeight: 200, // 每个报告卡片的高度
      listHeight: 0,   // 列表容器高度
      
      // 可用分类
      availableCategories: [
        { value: '', label: '全部' },
        { value: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE, label: '血常规' },
        { value: Constants.REPORT_CATEGORIES.BIOCHEMISTRY, label: '生化' },
        { value: Constants.REPORT_CATEGORIES.IMMUNOLOGY, label: '免疫' },
        { value: Constants.REPORT_CATEGORIES.URINE_ROUTINE, label: '尿常规' },
        { value: Constants.REPORT_CATEGORIES.IMAGING, label: '影像' },
        { value: Constants.REPORT_CATEGORIES.OTHER, label: '其他' }
      ]
    }
  },
  
  computed: {
    reportStore() {
      return useReportStore()
    },
    
    // 过滤后的报告列表
    filteredReports() {
      return this.reportStore.filteredReports
    },
    
    // 统计信息
    statistics() {
      return this.reportStore.statistics
    },
    
    // 分页信息
    pagination() {
      return this.reportStore.pagination
    },
    
    // 加载状态
    loading() {
      return this.reportStore.loading
    },
    
    // 是否有激活的筛选条件
    hasActiveFilter() {
      return this.filter.timeRange.start || 
             this.filter.category || 
             this.filter.hospital.trim() || 
             this.filter.abnormalOnly
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshReports()
  },
  
  onReady() {
    this.calculateListHeight()
  },
  
  onPullDownRefresh() {
    this.refreshReports().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  
  methods: {
    // 初始化页面
    async initPage() {
      try {
        await this.reportStore.initReports()
        this.calculateListHeight()
      } catch (error) {
        console.error('初始化页面失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    // 刷新报告列表
    async refreshReports() {
      try {
        await this.reportStore.initReports()
      } catch (error) {
        console.error('刷新失败:', error)
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        })
      }
    },
    
    // 加载更多报告
    async loadMoreReports() {
      // 这里可以实现分页加载逻辑
      console.log('加载更多报告')
    },
    
    // 计算列表高度
    calculateListHeight() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.list-container').boundingClientRect((data) => {
        if (data) {
          this.listHeight = data.height || 600
        }
      }).exec()
    },
    
    // 显示搜索弹窗
    showSearchModal() {
      this.$refs.searchPopup.open()
    },
    
    // 隐藏搜索弹窗
    hideSearchModal() {
      this.$refs.searchPopup.close()
      this.searchKeyword = ''
      this.searchSuggestions = []
    },
    
    // 处理搜索输入
    handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      this.searchTimer = setTimeout(() => {
        this.generateSearchSuggestions()
      }, 300)
    },
    
    // 处理搜索确认
    handleSearchConfirm() {
      this.applySearch()
      this.hideSearchModal()
    },
    
    // 生成搜索建议
    generateSearchSuggestions() {
      if (!this.searchKeyword.trim()) {
        this.searchSuggestions = []
        return
      }
      
      const keyword = this.searchKeyword.toLowerCase()
      const suggestions = []
      
      // 从报告中提取建议
      this.reportStore.reports.forEach(report => {
        if (report.hospital.toLowerCase().includes(keyword)) {
          suggestions.push({
            id: `hospital-${report.hospital}`,
            text: report.hospital,
            type: 'hospital'
          })
        }
        
        if (report.doctor && report.doctor.toLowerCase().includes(keyword)) {
          suggestions.push({
            id: `doctor-${report.doctor}`,
            text: report.doctor,
            type: 'doctor'
          })
        }
      })
      
      // 去重并限制数量
      this.searchSuggestions = suggestions
        .filter((item, index, self) => 
          index === self.findIndex(t => t.text === item.text)
        )
        .slice(0, 5)
    },
    
    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.searchKeyword = suggestion.text
      this.applySearch()
      this.hideSearchModal()
    },
    
    // 应用搜索
    applySearch() {
      if (this.searchKeyword.trim()) {
        // 这里可以实现搜索逻辑
        console.log('搜索:', this.searchKeyword)
      }
    },
    
    // 显示筛选弹窗
    showFilterModal() {
      this.tempFilter = JSON.parse(JSON.stringify(this.filter))
      this.$refs.filterPopup.open()
    },
    
    // 隐藏筛选弹窗
    hideFilterModal() {
      this.$refs.filterPopup.close()
    },
    
    // 应用筛选
    applyFilter() {
      this.filter = JSON.parse(JSON.stringify(this.tempFilter))
      this.reportStore.updateFilter(this.filter)
      this.hideFilterModal()
    },
    
    // 重置筛选
    resetFilter() {
      this.tempFilter = {
        timeRange: { start: null, end: null },
        category: '',
        hospital: '',
        abnormalOnly: false
      }
    },
    
    // 清除所有筛选
    clearAllFilters() {
      this.filter = {
        timeRange: { start: null, end: null },
        category: '',
        hospital: '',
        abnormalOnly: false
      }
      this.reportStore.clearFilter()
    },
    
    // 清除时间筛选
    clearTimeFilter() {
      this.filter.timeRange = { start: null, end: null }
      this.reportStore.updateFilter(this.filter)
    },
    
    // 清除分类筛选
    clearCategoryFilter() {
      this.filter.category = ''
      this.reportStore.updateFilter(this.filter)
    },
    
    // 清除医院筛选
    clearHospitalFilter() {
      this.filter.hospital = ''
      this.reportStore.updateFilter(this.filter)
    },
    
    // 清除异常筛选
    clearAbnormalFilter() {
      this.filter.abnormalOnly = false
      this.reportStore.updateFilter(this.filter)
    },
    
    // 开始日期变化
    onStartDateChange(e) {
      this.tempFilter.timeRange.start = e.detail.value
    },
    
    // 结束日期变化
    onEndDateChange(e) {
      this.tempFilter.timeRange.end = e.detail.value
    },
    
    // 选择分类
    selectCategory(category) {
      this.tempFilter.category = this.tempFilter.category === category ? '' : category
    },
    
    // 异常筛选变化
    onAbnormalOnlyChange(e) {
      this.tempFilter.abnormalOnly = e.detail.value
    },
    
    // 跳转到详情页
    goToDetail(report) {
      uni.navigateTo({
        url: `/pages/report/detail?id=${report.id}`
      })
    },
    
    // 跳转到编辑页
    goToEdit(report) {
      uni.navigateTo({
        url: `/pages/report/add?reportId=${report.id}`
      })
    },
    
    // 跳转到添加页
    goToAdd() {
      uni.navigateTo({
        url: '/pages/report/add'
      })
    },
    
    // 确认删除
    confirmDelete(report) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除 ${report.hospital} 的检查报告吗？删除后无法恢复。`,
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            this.deleteReport(report)
          }
        }
      })
    },
    
    // 删除报告
    async deleteReport(report) {
      try {
        this.reportStore.deleteReport(report.id)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('删除报告失败:', error)
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        })
      }
    },
    
    // 格式化筛选日期
    formatFilterDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getMonth() + 1}/${date.getDate()}`
    },
    
    // 获取分类名称
    getCategoryName(category) {
      const categoryItem = this.availableCategories.find(item => item.value === category)
      return categoryItem ? categoryItem.label : '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
.report-list-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.page-header {
  background: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #f8f9fa;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.filter-dot {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background: #ff5252;
  border-radius: 6rpx;
}

.stats-bar {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  
  &.abnormal {
    color: #ff5252;
  }
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
}

.active-filters {
  background: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.filter-tags {
  white-space: nowrap;
}

.filter-tag-list {
  display: flex;
  gap: 16rpx;
}

.filter-tag {
  background: #e3f2fd;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.tag-text {
  font-size: 24rpx;
  color: #1976d2;
}

.tag-close {
  font-size: 28rpx;
  color: #1976d2;
  background: none;
  border: none;
  padding: 0;
  line-height: 1;
}

.clear-all-btn {
  background: #ffebee;
  color: #d32f2f;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  border: none;
  flex-shrink: 0;
}

.list-container {
  flex: 1;
  position: relative;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 60rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
  margin-bottom: 40rpx;
}

.add-first-btn {
  background: #007AFF;
  color: #ffffff;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

.virtual-list {
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 999;
}

.fab-btn {
  width: 112rpx;
  height: 112rpx;
  border-radius: 56rpx;
  background: linear-gradient(135deg, #007AFF, #0056CC);
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

// 搜索弹窗样式
.search-modal {
  background: #ffffff;
  padding: 20rpx;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.search-cancel {
  color: #007AFF;
  font-size: 28rpx;
  background: none;
  border: none;
}

.search-suggestions {
  margin-top: 20rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333333;
}

// 筛选弹窗样式
.filter-modal {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  background: none;
  border: none;
  padding: 0;
}

.filter-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.time-range-picker {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.date-picker {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-text {
  font-size: 28rpx;
  color: #333333;
}

.date-separator {
  font-size: 24rpx;
  color: #999999;
}

.category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.category-option {
  padding: 16rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 20rpx;
  background: #ffffff;
  
  &.active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.option-text {
  font-size: 26rpx;
  color: #333333;
  
  .active & {
    color: #1976d2;
  }
}

.hospital-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.option-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-label {
  font-size: 28rpx;
  color: #333333;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.reset-btn {
  background: #f5f5f5;
  color: #666666;
}

.apply-btn {
  background: #007AFF;
  color: #ffffff;
}
</style>