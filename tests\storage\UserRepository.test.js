/**
 * UserRepository 单元测试
 * 测试用户数据访问层功能
 */

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { UserRepository } = require('../../core/storage/UserRepository.js')
const { User } = require('../../models/User.js')

// Mock StorageManager
const mockStorageManager = {
  insert: jest.fn(),
  findById: jest.fn(),
  findAll: jest.fn(),
  findBy: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  beginTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn()
}

// Mock logger
jest.mock('../../core/logger/Logger.js', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}))

describe('UserRepository', () => {
  let userRepository
  
  beforeEach(() => {
    jest.clearAllMocks()
    userRepository = new UserRepository(mockStorageManager)
  })
  
  describe('构造函数', () => {
    it('应该正确初始化', () => {
      expect(userRepository.storageManager).toBe(mockStorageManager)
      expect(userRepository.modelClass).toBe(User)
      expect(userRepository.tableName).toBe('users')
    })
  })
  
  describe('findByPhone', () => {
    it('应该根据手机号查找用户', async () => {
      const mockUserData = {
        id: 'user1',
        phone: '13800138000',
        nickname: '测试用户'
      }
      
      mockStorageManager.findBy.mockResolvedValue([mockUserData])
      User.fromJSON = jest.fn().mockReturnValue(new User(mockUserData))
      
      const result = await userRepository.findByPhone('13800138000')
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('users', { phone: '13800138000' }, {})
      expect(result).toBeInstanceOf(User)
      expect(result.phone).toBe('13800138000')
    })
    
    it('应该在用户不存在时返回null', async () => {
      mockStorageManager.findBy.mockResolvedValue([])
      
      const result = await userRepository.findByPhone('13800138000')
      
      expect(result).toBeNull()
    })
    
    it('应该处理查找错误', async () => {
      mockStorageManager.findBy.mockRejectedValue(new Error('数据库错误'))
      
      await expect(userRepository.findByPhone('13800138000')).rejects.toThrow()
    })
  })
  
  describe('validateCredentials', () => {
    it('应该验证正确的用户凭据', async () => {
      const mockUser = {
        id: 'user1',
        phone: '13800138000',
        validatePassword: jest.fn().mockResolvedValue(true)
      }
      
      userRepository.findByPhone = jest.fn().mockResolvedValue(mockUser)
      
      const result = await userRepository.validateCredentials('13800138000', 'password123')
      
      expect(userRepository.findByPhone).toHaveBeenCalledWith('13800138000')
      expect(mockUser.validatePassword).toHaveBeenCalledWith('password123')
      expect(result).toBe(mockUser)
    })
    
    it('应该在用户不存在时返回null', async () => {
      userRepository.findByPhone = jest.fn().mockResolvedValue(null)
      
      const result = await userRepository.validateCredentials('13800138000', 'password123')
      
      expect(result).toBeNull()
    })
    
    it('应该在密码错误时返回null', async () => {
      const mockUser = {
        id: 'user1',
        phone: '13800138000',
        validatePassword: jest.fn().mockResolvedValue(false)
      }
      
      userRepository.findByPhone = jest.fn().mockResolvedValue(mockUser)
      
      const result = await userRepository.validateCredentials('13800138000', 'wrongpassword')
      
      expect(result).toBeNull()
    })
  })
  
  describe('updateSettings', () => {
    it('应该更新用户设置', async () => {
      const mockUser = {
        id: 'user1',
        settings: {
          theme: 'light',
          notifications: true
        }
      }
      
      const newSettings = {
        theme: 'dark',
        autoSync: true
      }
      
      const expectedSettings = {
        theme: 'dark',
        notifications: true,
        autoSync: true
      }
      
      userRepository.findById = jest.fn().mockResolvedValue(mockUser)
      userRepository.update = jest.fn().mockResolvedValue({
        ...mockUser,
        settings: expectedSettings
      })
      
      const result = await userRepository.updateSettings('user1', newSettings)
      
      expect(userRepository.findById).toHaveBeenCalledWith('user1')
      expect(userRepository.update).toHaveBeenCalledWith('user1', {
        settings: expectedSettings
      })
      expect(result.settings).toEqual(expectedSettings)
    })
    
    it('应该在用户不存在时抛出错误', async () => {
      userRepository.findById = jest.fn().mockResolvedValue(null)
      
      await expect(userRepository.updateSettings('user1', {})).rejects.toThrow('用户不存在: user1')
    })
  })
  
  describe('updatePassword', () => {
    it('应该更新用户密码', async () => {
      const mockUser = {
        id: 'user1',
        hashPassword: jest.fn().mockResolvedValue('hashedPassword123')
      }
      
      userRepository.findById = jest.fn().mockResolvedValue(mockUser)
      userRepository.update = jest.fn().mockResolvedValue({
        ...mockUser,
        password: 'hashedPassword123'
      })
      
      const result = await userRepository.updatePassword('user1', 'newPassword123')
      
      expect(userRepository.findById).toHaveBeenCalledWith('user1')
      expect(mockUser.hashPassword).toHaveBeenCalledWith('newPassword123')
      expect(userRepository.update).toHaveBeenCalledWith('user1', {
        password: 'hashedPassword123'
      })
    })
    
    it('应该在用户不存在时抛出错误', async () => {
      userRepository.findById = jest.fn().mockResolvedValue(null)
      
      await expect(userRepository.updatePassword('user1', 'newPassword')).rejects.toThrow('用户不存在: user1')
    })
  })
  
  describe('updateProfile', () => {
    it('应该更新用户基本信息', async () => {
      const profileData = {
        nickname: '新昵称',
        gender: 'male',
        height: 175,
        invalidField: '不应该被更新'
      }
      
      const expectedUpdateData = {
        nickname: '新昵称',
        gender: 'male',
        height: 175
      }
      
      userRepository.update = jest.fn().mockResolvedValue({
        id: 'user1',
        ...expectedUpdateData
      })
      
      const result = await userRepository.updateProfile('user1', profileData)
      
      expect(userRepository.update).toHaveBeenCalledWith('user1', expectedUpdateData)
      expect(result.nickname).toBe('新昵称')
    })
    
    it('应该在没有有效字段时抛出错误', async () => {
      const profileData = {
        invalidField: '无效字段'
      }
      
      await expect(userRepository.updateProfile('user1', profileData)).rejects.toThrow('没有有效的更新字段')
    })
  })
  
  describe('isPhoneExists', () => {
    it('应该检查手机号是否存在', async () => {
      mockStorageManager.findBy.mockResolvedValue([{ id: 'user1', phone: '13800138000' }])
      User.fromJSON = jest.fn().mockReturnValue({ id: 'user1', phone: '13800138000' })
      
      const result = await userRepository.isPhoneExists('13800138000')
      
      expect(result).toBe(true)
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('users', { phone: '13800138000' }, {})
    })
    
    it('应该排除指定用户ID', async () => {
      mockStorageManager.findBy.mockResolvedValue([
        { id: 'user1', phone: '13800138000' },
        { id: 'user2', phone: '13800138000' }
      ])
      User.fromJSON = jest.fn()
        .mockReturnValueOnce({ id: 'user1', phone: '13800138000' })
        .mockReturnValueOnce({ id: 'user2', phone: '13800138000' })
      
      const result = await userRepository.isPhoneExists('13800138000', 'user1')
      
      expect(result).toBe(true) // user2 still exists
    })
    
    it('应该在手机号不存在时返回false', async () => {
      mockStorageManager.findBy.mockResolvedValue([])
      
      const result = await userRepository.isPhoneExists('13800138000')
      
      expect(result).toBe(false)
    })
  })
  
  describe('getUserStats', () => {
    it('应该获取用户统计信息', async () => {
      const mockUser = {
        id: 'user1',
        nickname: '测试用户',
        gender: 'male',
        birthday: new Date('1990-01-01'),
        height: 175,
        weight: 70,
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-12-01')
      }
      
      userRepository.findById = jest.fn().mockResolvedValue(mockUser)
      
      const result = await userRepository.getUserStats('user1')
      
      expect(result).toHaveProperty('userId', 'user1')
      expect(result).toHaveProperty('registrationDate')
      expect(result).toHaveProperty('lastUpdated')
      expect(result).toHaveProperty('profileCompleteness')
      expect(result.profileCompleteness).toBe(100) // 所有字段都填写了
    })
    
    it('应该计算正确的资料完整度', () => {
      const user = {
        nickname: '测试用户',
        gender: 'male',
        birthday: null,
        height: 175,
        weight: null
      }
      
      const completeness = userRepository.calculateProfileCompleteness(user)
      
      expect(completeness).toBe(60) // 5个字段中有3个填写了
    })
  })
  
  describe('softDelete', () => {
    it('应该软删除用户', async () => {
      userRepository.update = jest.fn().mockResolvedValue(true)
      
      const result = await userRepository.softDelete('user1')
      
      expect(userRepository.update).toHaveBeenCalledWith('user1', {
        isDeleted: true,
        deletedAt: expect.any(Date)
      })
      expect(result).toBe(true)
    })
  })
  
  describe('restore', () => {
    it('应该恢复已删除的用户', async () => {
      const restoredUser = {
        id: 'user1',
        isDeleted: false,
        deletedAt: null
      }
      
      userRepository.update = jest.fn().mockResolvedValue(restoredUser)
      
      const result = await userRepository.restore('user1')
      
      expect(userRepository.update).toHaveBeenCalledWith('user1', {
        isDeleted: false,
        deletedAt: null
      })
      expect(result).toBe(restoredUser)
    })
  })
  
  describe('findActiveUsers', () => {
    it('应该查找活跃用户', async () => {
      const mockUsers = [
        { id: 'user1', isDeleted: false },
        { id: 'user2' } // 没有isDeleted字段
      ]
      
      mockStorageManager.findBy.mockResolvedValue(mockUsers)
      User.fromJSON = jest.fn()
        .mockReturnValueOnce(mockUsers[0])
        .mockReturnValueOnce(mockUsers[1])
      
      const result = await userRepository.findActiveUsers()
      
      expect(mockStorageManager.findBy).toHaveBeenCalledWith('users', {
        $or: [
          { isDeleted: false },
          { isDeleted: { $exists: false } }
        ]
      }, {})
      expect(result).toHaveLength(2)
    })
  })
  
  describe('cleanupDeletedUsers', () => {
    it('应该清理已删除的用户', async () => {
      const deletedUsers = [
        { id: 'user1', isDeleted: true, deletedAt: new Date('2023-01-01') },
        { id: 'user2', isDeleted: true, deletedAt: new Date('2023-01-02') }
      ]
      
      userRepository.findBy = jest.fn().mockResolvedValue(deletedUsers)
      userRepository.delete = jest.fn().mockResolvedValue(true)
      
      const result = await userRepository.cleanupDeletedUsers(30)
      
      expect(userRepository.findBy).toHaveBeenCalledWith({
        isDeleted: true,
        deletedAt: { $lt: expect.any(Date) }
      })
      expect(userRepository.delete).toHaveBeenCalledTimes(2)
      expect(result).toBe(2)
    })
  })
})