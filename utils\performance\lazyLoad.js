/**
 * 图片懒加载优化组件
 */

class LazyImageLoader {
  constructor(options = {}) {
    this.options = {
      threshold: 0.1, // 触发加载的阈值
      rootMargin: '50px', // 预加载边距
      placeholder: '/static/placeholder.png', // 占位图
      errorImage: '/static/error.png', // 错误图片
      fadeInDuration: 300, // 淡入动画时长
      retryCount: 3, // 重试次数
      retryDelay: 1000, // 重试延迟
      ...options
    }
    
    this.loadingImages = new Map()
    this.loadedImages = new Set()
    this.errorImages = new Set()
    this.observers = new Map()
  }

  /**
   * 创建懒加载组件
   */
  createLazyImage(src, options = {}) {
    const config = { ...this.options, ...options }
    const imageId = this.generateImageId()
    
    return {
      id: imageId,
      src: config.placeholder,
      realSrc: src,
      loading: false,
      loaded: false,
      error: false,
      retryCount: 0,
      
      // 开始加载
      startLoad: () => this.loadImage(imageId, src, config),
      
      // 重试加载
      retry: () => this.retryLoad(imageId, src, config)
    }
  }

  /**
   * 加载图片
   */
  async loadImage(imageId, src, config) {
    if (this.loadedImages.has(src)) {
      return this.getLoadedImage(src)
    }

    if (this.loadingImages.has(src)) {
      return this.loadingImages.get(src)
    }

    const loadPromise = this.performLoad(src, config)
    this.loadingImages.set(src, loadPromise)

    try {
      const result = await loadPromise
      this.loadedImages.add(src)
      this.loadingImages.delete(src)
      return result
    } catch (error) {
      this.loadingImages.delete(src)
      this.errorImages.add(src)
      throw error
    }
  }

  /**
   * 执行图片加载
   */
  performLoad(src, config) {
    return new Promise((resolve, reject) => {
      const img = new Image()
      
      const timeout = setTimeout(() => {
        reject(new Error('图片加载超时'))
      }, 10000)

      img.onload = () => {
        clearTimeout(timeout)
        resolve({
          src,
          width: img.width,
          height: img.height,
          loaded: true
        })
      }

      img.onerror = () => {
        clearTimeout(timeout)
        reject(new Error(`图片加载失败: ${src}`))
      }

      img.src = src
    })
  }

  /**
   * 重试加载
   */
  async retryLoad(imageId, src, config) {
    const image = this.getImageById(imageId)
    if (!image || image.retryCount >= config.retryCount) {
      return false
    }

    image.retryCount++
    
    // 延迟重试
    await new Promise(resolve => 
      setTimeout(resolve, config.retryDelay * image.retryCount)
    )

    try {
      await this.loadImage(imageId, src, config)
      return true
    } catch (error) {
      console.error(`图片重试加载失败 (${image.retryCount}/${config.retryCount}):`, error)
      return false
    }
  }

  /**
   * 批量预加载图片
   */
  async preloadImages(srcList, options = {}) {
    const { concurrency = 3, priority = 'normal' } = options
    const results = []
    
    // 按优先级排序
    const sortedSrcList = this.sortByPriority(srcList, priority)
    
    // 分批并发加载
    for (let i = 0; i < sortedSrcList.length; i += concurrency) {
      const batch = sortedSrcList.slice(i, i + concurrency)
      const batchPromises = batch.map(src => 
        this.loadImage(this.generateImageId(), src, this.options)
          .catch(error => ({ error, src }))
      )
      
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
      
      // 给UI线程一些时间
      await new Promise(resolve => setTimeout(resolve, 10))
    }
    
    return results
  }

  /**
   * 按优先级排序
   */
  sortByPriority(srcList, priority) {
    if (priority === 'high') {
      // 高优先级：首屏图片优先
      return srcList
    } else if (priority === 'low') {
      // 低优先级：随机顺序，避免同时请求
      return this.shuffleArray([...srcList])
    }
    return srcList
  }

  /**
   * 数组随机排序
   */
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]
    }
    return array
  }

  /**
   * 生成图片ID
   */
  generateImageId() {
    return `lazy_img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取已加载的图片
   */
  getLoadedImage(src) {
    return {
      src,
      loaded: true,
      error: false
    }
  }

  /**
   * 根据ID获取图片
   */
  getImageById(imageId) {
    // 这里需要根据实际的图片管理方式实现
    return null
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.loadingImages.clear()
    this.loadedImages.clear()
    this.errorImages.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      loading: this.loadingImages.size,
      loaded: this.loadedImages.size,
      error: this.errorImages.size
    }
  }
}

// 虚拟滚动组件
class VirtualScrollManager {
  constructor(options = {}) {
    this.options = {
      itemHeight: 100, // 每项高度
      bufferSize: 5, // 缓冲区大小
      threshold: 200, // 滚动阈值
      ...options
    }
    
    this.scrollTop = 0
    this.containerHeight = 0
    this.totalItems = 0
    this.visibleItems = []
    this.startIndex = 0
    this.endIndex = 0
  }

  /**
   * 更新滚动位置
   */
  updateScrollPosition(scrollTop, containerHeight) {
    this.scrollTop = scrollTop
    this.containerHeight = containerHeight
    this.calculateVisibleRange()
  }

  /**
   * 设置总项目数
   */
  setTotalItems(count) {
    this.totalItems = count
    this.calculateVisibleRange()
  }

  /**
   * 计算可见范围
   */
  calculateVisibleRange() {
    const { itemHeight, bufferSize } = this.options
    
    // 计算可见区域的开始和结束索引
    const visibleStart = Math.floor(this.scrollTop / itemHeight)
    const visibleEnd = Math.min(
      this.totalItems - 1,
      Math.ceil((this.scrollTop + this.containerHeight) / itemHeight)
    )
    
    // 添加缓冲区
    this.startIndex = Math.max(0, visibleStart - bufferSize)
    this.endIndex = Math.min(this.totalItems - 1, visibleEnd + bufferSize)
    
    // 生成可见项目列表
    this.visibleItems = []
    for (let i = this.startIndex; i <= this.endIndex; i++) {
      this.visibleItems.push({
        index: i,
        top: i * itemHeight,
        height: itemHeight
      })
    }
  }

  /**
   * 获取可见项目
   */
  getVisibleItems() {
    return this.visibleItems
  }

  /**
   * 获取总高度
   */
  getTotalHeight() {
    return this.totalItems * this.options.itemHeight
  }

  /**
   * 获取偏移量
   */
  getOffset() {
    return this.startIndex * this.options.itemHeight
  }

  /**
   * 是否需要更新
   */
  shouldUpdate(newScrollTop) {
    return Math.abs(newScrollTop - this.scrollTop) > this.options.threshold
  }
}

// 导出懒加载混入
export const lazyLoadMixin = {
  data() {
    return {
      lazyLoader: null,
      lazyImages: []
    }
  },
  
  created() {
    this.lazyLoader = new LazyImageLoader()
  },
  
  methods: {
    // 创建懒加载图片
    createLazyImage(src, options) {
      const lazyImage = this.lazyLoader.createLazyImage(src, options)
      this.lazyImages.push(lazyImage)
      return lazyImage
    },
    
    // 预加载图片列表
    async preloadImageList(srcList, options) {
      return await this.lazyLoader.preloadImages(srcList, options)
    },
    
    // 清理懒加载缓存
    clearLazyCache() {
      this.lazyLoader.clearCache()
      this.lazyImages = []
    }
  },
  
  beforeDestroy() {
    if (this.lazyLoader) {
      this.lazyLoader.clearCache()
    }
  }
}

// 导出虚拟滚动混入
export const virtualScrollMixin = {
  data() {
    return {
      virtualScroll: null,
      virtualItems: [],
      virtualHeight: 0,
      virtualOffset: 0
    }
  },
  
  created() {
    this.virtualScroll = new VirtualScrollManager(this.virtualScrollOptions || {})
  },
  
  methods: {
    // 处理滚动事件
    handleVirtualScroll(e) {
      const scrollTop = e.detail.scrollTop
      const containerHeight = this.containerHeight || 500
      
      if (this.virtualScroll.shouldUpdate(scrollTop)) {
        this.virtualScroll.updateScrollPosition(scrollTop, containerHeight)
        this.updateVirtualItems()
      }
    },
    
    // 更新虚拟项目
    updateVirtualItems() {
      this.virtualItems = this.virtualScroll.getVisibleItems()
      this.virtualHeight = this.virtualScroll.getTotalHeight()
      this.virtualOffset = this.virtualScroll.getOffset()
    },
    
    // 设置虚拟列表数据
    setVirtualData(data) {
      this.virtualScroll.setTotalItems(data.length)
      this.updateVirtualItems()
    }
  }
}

export { LazyImageLoader, VirtualScrollManager }