/**
 * 统一错误处理系统
 * 提供全局错误处理和错误分类功能
 */

const { Constants } = require('../../types/index.js')
const { logger } = require('../logger/Logger.js')

class ErrorHandler {
  constructor() {
    this.errorHandlers = new Map()
    this.errorListeners = []
    this.setupDefaultHandlers()
  }
  
  /**
   * 设置默认错误处理器
   */
  setupDefaultHandlers() {
    // 网络错误处理器
    this.registerHandler(Constants.ERROR_TYPES.NETWORK_ERROR, (error) => {
      return {
        userMessage: '网络连接异常，请检查网络设置',
        shouldRetry: true,
        retryDelay: 3000
      }
    })
    
    // 认证错误处理器
    this.registerHandler(Constants.ERROR_TYPES.AUTH_ERROR, (error) => {
      return {
        userMessage: '身份验证失败，请重新登录',
        shouldRetry: false,
        requiresReauth: true
      }
    })
    
    // 验证错误处理器
    this.registerHandler(Constants.ERROR_TYPES.VALIDATION_ERROR, (error) => {
      return {
        userMessage: error.message || '输入数据格式不正确',
        shouldRetry: false,
        validationErrors: error.validationErrors || []
      }
    })
    
    // OCR错误处理器
    this.registerHandler(Constants.ERROR_TYPES.OCR_ERROR, (error) => {
      return {
        userMessage: '图片识别失败，请重新拍摄或手动输入',
        shouldRetry: true,
        retryDelay: 1000,
        fallbackAction: 'manual_input'
      }
    })
    
    // 存储错误处理器
    this.registerHandler(Constants.ERROR_TYPES.STORAGE_ERROR, (error) => {
      return {
        userMessage: '数据保存失败，请稍后重试',
        shouldRetry: true,
        retryDelay: 2000
      }
    })
    
    // 权限错误处理器
    this.registerHandler(Constants.ERROR_TYPES.PERMISSION_ERROR, (error) => {
      return {
        userMessage: '权限不足，请检查应用权限设置',
        shouldRetry: false,
        requiresPermission: true
      }
    })
    
    // 未知错误处理器
    this.registerHandler(Constants.ERROR_TYPES.UNKNOWN_ERROR, (error) => {
      return {
        userMessage: '系统异常，请稍后重试',
        shouldRetry: true,
        retryDelay: 5000
      }
    })
  }
  
  /**
   * 注册错误处理器
   * @param {String} errorType - 错误类型
   * @param {Function} handler - 处理器函数
   */
  registerHandler(errorType, handler) {
    this.errorHandlers.set(errorType, handler)
  }
  
  /**
   * 处理错误
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleError(error, context = {}) {
    try {
      // 标准化错误对象
      const standardError = this.standardizeError(error)
      
      // 记录错误日志
      this.logError(standardError, context)
      
      // 获取错误处理器
      const handler = this.errorHandlers.get(standardError.type) || 
                     this.errorHandlers.get(Constants.ERROR_TYPES.UNKNOWN_ERROR)
      
      // 执行错误处理
      const handlerResult = handler(standardError, context)
      
      // 构建处理结果
      const result = {
        error: standardError,
        context,
        ...handlerResult,
        timestamp: new Date(),
        handled: true
      }
      
      // 通知错误监听器
      this.notifyListeners(result)
      
      return result
    } catch (handlerError) {
      logger.error('错误处理器执行失败', {
        originalError: error,
        handlerError: handlerError.message,
        context
      })
      
      return {
        error: this.standardizeError(error),
        context,
        userMessage: '系统异常，请稍后重试',
        shouldRetry: false,
        handled: false,
        timestamp: new Date()
      }
    }
  }
  
  /**
   * 标准化错误对象
   * @param {*} error - 原始错误
   * @returns {Object} 标准化的错误对象
   */
  standardizeError(error) {
    if (!error) {
      return {
        type: Constants.ERROR_TYPES.UNKNOWN_ERROR,
        message: '未知错误',
        code: 'UNKNOWN',
        stack: new Error().stack
      }
    }
    
    if (typeof error === 'string') {
      return {
        type: Constants.ERROR_TYPES.UNKNOWN_ERROR,
        message: error,
        code: 'STRING_ERROR',
        stack: new Error().stack
      }
    }
    
    if (error instanceof Error) {
      return {
        type: error.type || this.inferErrorType(error),
        message: error.message,
        code: error.code || error.name,
        stack: error.stack,
        originalError: error
      }
    }
    
    // 对象形式的错误
    return {
      type: error.type || Constants.ERROR_TYPES.UNKNOWN_ERROR,
      message: error.message || '未知错误',
      code: error.code || 'OBJECT_ERROR',
      stack: error.stack || new Error().stack,
      originalError: error
    }
  }
  
  /**
   * 推断错误类型
   * @param {Error} error - 错误对象
   * @returns {String} 错误类型
   */
  inferErrorType(error) {
    const message = error.message.toLowerCase()
    const name = error.name.toLowerCase()
    
    // 网络相关错误
    if (message.includes('network') || message.includes('timeout') || 
        message.includes('connection') || name.includes('networkerror')) {
      return Constants.ERROR_TYPES.NETWORK_ERROR
    }
    
    // 认证相关错误
    if (message.includes('unauthorized') || message.includes('forbidden') ||
        message.includes('authentication') || message.includes('token')) {
      return Constants.ERROR_TYPES.AUTH_ERROR
    }
    
    // 验证相关错误
    if (message.includes('validation') || message.includes('invalid') ||
        name.includes('validationerror')) {
      return Constants.ERROR_TYPES.VALIDATION_ERROR
    }
    
    // 权限相关错误
    if (message.includes('permission') || message.includes('access denied')) {
      return Constants.ERROR_TYPES.PERMISSION_ERROR
    }
    
    // 存储相关错误
    if (message.includes('storage') || message.includes('database') ||
        message.includes('save') || message.includes('store')) {
      return Constants.ERROR_TYPES.STORAGE_ERROR
    }
    
    return Constants.ERROR_TYPES.UNKNOWN_ERROR
  }
  
  /**
   * 记录错误日志
   * @param {Object} error - 标准化错误对象
   * @param {Object} context - 错误上下文
   */
  logError(error, context) {
    logger.error(error.message, {
      type: error.type,
      code: error.code,
      stack: error.stack,
      context,
      timestamp: new Date()
    })
  }
  
  /**
   * 添加错误监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.errorListeners.push(listener)
  }
  
  /**
   * 移除错误监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this.errorListeners.indexOf(listener)
    if (index > -1) {
      this.errorListeners.splice(index, 1)
    }
  }
  
  /**
   * 通知错误监听器
   * @param {Object} errorResult - 错误处理结果
   */
  notifyListeners(errorResult) {
    for (const listener of this.errorListeners) {
      try {
        listener(errorResult)
      } catch (error) {
        logger.error('错误监听器执行失败', {
          listenerError: error.message,
          originalError: errorResult.error
        })
      }
    }
  }
  
  /**
   * 创建错误对象
   * @param {String} type - 错误类型
   * @param {String} message - 错误消息
   * @param {Object} options - 额外选项
   * @returns {Error} 错误对象
   */
  createError(type, message, options = {}) {
    const error = new Error(message)
    error.type = type
    error.code = options.code || type.toUpperCase()
    
    // 添加额外属性
    Object.assign(error, options)
    
    return error
  }
  
  /**
   * 包装异步函数，自动处理错误
   * @param {Function} fn - 异步函数
   * @param {Object} context - 错误上下文
   * @returns {Function} 包装后的函数
   */
  wrapAsync(fn, context = {}) {
    return async (...args) => {
      try {
        return await fn(...args)
      } catch (error) {
        const result = this.handleError(error, {
          ...context,
          function: fn.name,
          arguments: args
        })
        
        // 如果错误不应该重新抛出，返回默认值
        if (result.suppressError) {
          return result.defaultValue
        }
        
        throw error
      }
    }
  }
  
  /**
   * 重试执行函数
   * @param {Function} fn - 要执行的函数
   * @param {Object} options - 重试选项
   * @returns {Promise<*>} 执行结果
   */
  async retryWithErrorHandling(fn, options = {}) {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      backoffFactor = 2,
      shouldRetry = (error) => true
    } = options
    
    let lastError
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        const errorResult = this.handleError(error, {
          attempt: attempt + 1,
          maxRetries: maxRetries + 1
        })
        
        // 检查是否应该重试
        if (attempt === maxRetries || !errorResult.shouldRetry || !shouldRetry(error)) {
          break
        }
        
        // 计算延迟时间
        const delay = errorResult.retryDelay || (baseDelay * Math.pow(backoffFactor, attempt))
        
        logger.info(`操作失败，${delay}ms后重试`, {
          attempt: attempt + 1,
          maxRetries: maxRetries + 1,
          error: error.message
        })
        
        await this.sleep(delay)
      }
    }
    
    throw lastError
  }
  
  /**
   * 睡眠指定时间
   * @param {Number} ms - 毫秒数
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler()

// Vue.js 错误处理器安装函数
function installErrorHandler(app) {
  // 全局错误处理
  app.config.errorHandler = (error, instance, info) => {
    errorHandler.handleError(error, {
      component: instance?.$options.name || 'Unknown',
      errorInfo: info
    })
  }
  
  // 全局警告处理
  app.config.warnHandler = (msg, instance, trace) => {
    logger.warn('Vue警告', {
      message: msg,
      component: instance?.$options.name || 'Unknown',
      trace
    })
  }
  
  // 未捕获的Promise拒绝
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      errorHandler.handleError(event.reason, {
        type: 'unhandledrejection',
        promise: event.promise
      })
    })
    
    // 全局错误事件
    window.addEventListener('error', (event) => {
      errorHandler.handleError(event.error || new Error(event.message), {
        type: 'global',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    })
  }
}

module.exports = { ErrorHandler, errorHandler, installErrorHandler }