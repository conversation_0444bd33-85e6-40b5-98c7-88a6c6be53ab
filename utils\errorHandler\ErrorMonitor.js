/**
 * 错误监控和分析工具
 * 提供错误统计、分析和监控功能
 */
class ErrorMonitor {
  constructor() {
    this.monitoringEnabled = true
    this.alertThresholds = {
      errorRate: 0.1, // 10%错误率
      criticalErrors: 5, // 5个严重错误
      timeWindow: 60 * 1000 // 1分钟时间窗口
    }
    
    this.recentErrors = []
    this.errorStats = {
      total: 0,
      byLevel: {},
      byContext: {},
      byTime: {}
    }
    
    this.alertCallbacks = []
    this.startMonitoring()
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (!this.monitoringEnabled) {
      return
    }

    // 定期清理过期的错误记录
    setInterval(() => {
      this.cleanupExpiredErrors()
    }, 30 * 1000) // 每30秒清理一次

    // 定期检查错误阈值
    setInterval(() => {
      this.checkErrorThresholds()
    }, 10 * 1000) // 每10秒检查一次

    // 定期生成错误报告
    setInterval(() => {
      this.generateErrorReport()
    }, 5 * 60 * 1000) // 每5分钟生成一次报告
  }

  /**
   * 记录错误
   */
  recordError(errorInfo) {
    if (!this.monitoringEnabled) {
      return
    }

    const timestamp = Date.now()
    const errorRecord = {
      ...errorInfo,
      timestamp,
      id: this.generateErrorId()
    }

    // 添加到最近错误列表
    this.recentErrors.push(errorRecord)
    
    // 更新统计信息
    this.updateStats(errorRecord)
    
    // 检查是否需要触发告警
    this.checkImmediateAlerts(errorRecord)
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 更新统计信息
   */
  updateStats(errorRecord) {
    this.errorStats.total++
    
    // 按级别统计
    const level = errorRecord.level || 'unknown'
    this.errorStats.byLevel[level] = (this.errorStats.byLevel[level] || 0) + 1
    
    // 按上下文统计
    const context = errorRecord.context || 'unknown'
    this.errorStats.byContext[context] = (this.errorStats.byContext[context] || 0) + 1
    
    // 按时间统计（小时级别）
    const hour = new Date(errorRecord.timestamp).getHours()
    this.errorStats.byTime[hour] = (this.errorStats.byTime[hour] || 0) + 1
  }

  /**
   * 检查即时告警
   */
  checkImmediateAlerts(errorRecord) {
    // 检查严重错误
    if (errorRecord.level === 'error' || errorRecord.level === 'critical') {
      this.triggerAlert('critical_error', {
        message: '检测到严重错误',
        error: errorRecord
      })
    }

    // 检查特定错误类型
    if (errorRecord.context === 'OCRError') {
      this.checkOCRErrorPattern(errorRecord)
    }

    if (errorRecord.context === 'NetworkError') {
      this.checkNetworkErrorPattern(errorRecord)
    }
  }

  /**
   * 检查OCR错误模式
   */
  checkOCRErrorPattern(errorRecord) {
    const recentOCRErrors = this.recentErrors.filter(error => 
      error.context === 'OCRError' && 
      Date.now() - error.timestamp < this.alertThresholds.timeWindow
    )

    if (recentOCRErrors.length >= 3) {
      this.triggerAlert('ocr_failure_pattern', {
        message: 'OCR服务出现连续失败',
        count: recentOCRErrors.length,
        timeWindow: this.alertThresholds.timeWindow
      })
    }
  }

  /**
   * 检查网络错误模式
   */
  checkNetworkErrorPattern(errorRecord) {
    const recentNetworkErrors = this.recentErrors.filter(error => 
      error.context === 'NetworkError' && 
      Date.now() - error.timestamp < this.alertThresholds.timeWindow
    )

    if (recentNetworkErrors.length >= 5) {
      this.triggerAlert('network_instability', {
        message: '网络连接不稳定',
        count: recentNetworkErrors.length,
        timeWindow: this.alertThresholds.timeWindow
      })
    }
  }

  /**
   * 检查错误阈值
   */
  checkErrorThresholds() {
    const now = Date.now()
    const timeWindow = this.alertThresholds.timeWindow
    
    // 获取时间窗口内的错误
    const recentErrors = this.recentErrors.filter(error => 
      now - error.timestamp < timeWindow
    )

    // 检查错误率
    if (recentErrors.length > 0) {
      const criticalErrors = recentErrors.filter(error => 
        error.level === 'error' || error.level === 'critical'
      )
      
      const errorRate = criticalErrors.length / recentErrors.length
      
      if (errorRate > this.alertThresholds.errorRate) {
        this.triggerAlert('high_error_rate', {
          message: '错误率过高',
          errorRate: errorRate,
          totalErrors: recentErrors.length,
          criticalErrors: criticalErrors.length
        })
      }
    }

    // 检查严重错误数量
    const criticalErrorsCount = recentErrors.filter(error => 
      error.level === 'critical'
    ).length

    if (criticalErrorsCount >= this.alertThresholds.criticalErrors) {
      this.triggerAlert('too_many_critical_errors', {
        message: '严重错误数量过多',
        count: criticalErrorsCount,
        threshold: this.alertThresholds.criticalErrors
      })
    }
  }

  /**
   * 触发告警
   */
  triggerAlert(type, data) {
    const alert = {
      type,
      data,
      timestamp: Date.now(),
      id: this.generateErrorId()
    }

    console.warn('错误监控告警:', alert)

    // 调用注册的告警回调
    this.alertCallbacks.forEach(callback => {
      try {
        callback(alert)
      } catch (error) {
        console.error('告警回调执行失败:', error)
      }
    })

    // 保存告警记录
    this.saveAlert(alert)
  }

  /**
   * 保存告警记录
   */
  saveAlert(alert) {
    try {
      let alerts = uni.getStorageSync('error_alerts') || []
      alerts.unshift(alert)
      
      // 只保留最近50条告警
      if (alerts.length > 50) {
        alerts = alerts.slice(0, 50)
      }
      
      uni.setStorageSync('error_alerts', alerts)
    } catch (error) {
      console.error('保存告警记录失败:', error)
    }
  }

  /**
   * 清理过期错误
   */
  cleanupExpiredErrors() {
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24小时
    
    this.recentErrors = this.recentErrors.filter(error => 
      now - error.timestamp < maxAge
    )
  }

  /**
   * 生成错误报告
   */
  generateErrorReport() {
    const now = Date.now()
    const report = {
      timestamp: now,
      period: '5min',
      stats: this.getErrorStats(),
      trends: this.getErrorTrends(),
      topErrors: this.getTopErrors(),
      recommendations: this.getRecommendations()
    }

    console.log('错误监控报告:', report)
    
    // 保存报告
    this.saveReport(report)
    
    return report
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const now = Date.now()
    const timeWindows = {
      '1min': 60 * 1000,
      '5min': 5 * 60 * 1000,
      '1hour': 60 * 60 * 1000,
      '24hour': 24 * 60 * 60 * 1000
    }

    const stats = {}
    
    Object.entries(timeWindows).forEach(([period, window]) => {
      const errors = this.recentErrors.filter(error => 
        now - error.timestamp < window
      )
      
      stats[period] = {
        total: errors.length,
        byLevel: this.groupBy(errors, 'level'),
        byContext: this.groupBy(errors, 'context')
      }
    })

    return stats
  }

  /**
   * 获取错误趋势
   */
  getErrorTrends() {
    const now = Date.now()
    const hourlyTrends = {}
    
    // 计算过去24小时的每小时错误数
    for (let i = 0; i < 24; i++) {
      const hourStart = now - (i + 1) * 60 * 60 * 1000
      const hourEnd = now - i * 60 * 60 * 1000
      
      const hourErrors = this.recentErrors.filter(error => 
        error.timestamp >= hourStart && error.timestamp < hourEnd
      )
      
      const hour = new Date(hourStart).getHours()
      hourlyTrends[hour] = hourErrors.length
    }

    return {
      hourly: hourlyTrends
    }
  }

  /**
   * 获取高频错误
   */
  getTopErrors() {
    const errorCounts = {}
    
    this.recentErrors.forEach(error => {
      const key = `${error.context}_${error.message}`
      errorCounts[key] = (errorCounts[key] || 0) + 1
    })

    return Object.entries(errorCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([error, count]) => ({ error, count }))
  }

  /**
   * 获取建议
   */
  getRecommendations() {
    const recommendations = []
    const stats = this.getErrorStats()

    // 网络错误建议
    if (stats['5min'].byContext.NetworkError > 3) {
      recommendations.push({
        type: 'network',
        message: '网络错误频发，建议检查网络连接或增加重试机制',
        priority: 'high'
      })
    }

    // OCR错误建议
    if (stats['5min'].byContext.OCRError > 2) {
      recommendations.push({
        type: 'ocr',
        message: 'OCR识别失败较多，建议优化图像质量或更换OCR服务',
        priority: 'medium'
      })
    }

    // 严重错误建议
    if (stats['5min'].byLevel.error > 5) {
      recommendations.push({
        type: 'critical',
        message: '严重错误数量过多，需要立即排查',
        priority: 'critical'
      })
    }

    return recommendations
  }

  /**
   * 保存报告
   */
  saveReport(report) {
    try {
      let reports = uni.getStorageSync('error_reports') || []
      reports.unshift(report)
      
      // 只保留最近20份报告
      if (reports.length > 20) {
        reports = reports.slice(0, 20)
      }
      
      uni.setStorageSync('error_reports', reports)
    } catch (error) {
      console.error('保存错误报告失败:', error)
    }
  }

  /**
   * 分组统计
   */
  groupBy(array, key) {
    return array.reduce((groups, item) => {
      const group = item[key] || 'unknown'
      groups[group] = (groups[group] || 0) + 1
      return groups
    }, {})
  }

  /**
   * 注册告警回调
   */
  onAlert(callback) {
    this.alertCallbacks.push(callback)
  }

  /**
   * 移除告警回调
   */
  offAlert(callback) {
    const index = this.alertCallbacks.indexOf(callback)
    if (index > -1) {
      this.alertCallbacks.splice(index, 1)
    }
  }

  /**
   * 设置告警阈值
   */
  setAlertThresholds(thresholds) {
    this.alertThresholds = { ...this.alertThresholds, ...thresholds }
  }

  /**
   * 启用/禁用监控
   */
  setMonitoringEnabled(enabled) {
    this.monitoringEnabled = enabled
  }

  /**
   * 获取告警历史
   */
  getAlertHistory() {
    try {
      return uni.getStorageSync('error_alerts') || []
    } catch (error) {
      console.error('获取告警历史失败:', error)
      return []
    }
  }

  /**
   * 获取报告历史
   */
  getReportHistory() {
    try {
      return uni.getStorageSync('error_reports') || []
    } catch (error) {
      console.error('获取报告历史失败:', error)
      return []
    }
  }

  /**
   * 清除监控数据
   */
  clearMonitoringData() {
    this.recentErrors = []
    this.errorStats = {
      total: 0,
      byLevel: {},
      byContext: {},
      byTime: {}
    }
    
    try {
      uni.removeStorageSync('error_alerts')
      uni.removeStorageSync('error_reports')
    } catch (error) {
      console.error('清除监控数据失败:', error)
    }
  }
}

module.exports = ErrorMonitor