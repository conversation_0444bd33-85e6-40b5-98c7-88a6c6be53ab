// 统计分析页面测试
describe('StatisticsAnalysis 页面测试', () => {
  // Mock uni-app API
  global.uni = {
    navigateBack: jest.fn(),
    showToast: jest.fn()
  }

  // 模拟页面核心方法
  const statisticsAnalysisMethods = {
    // 模拟数据
    availableIndicators: [
      { key: 'blood_pressure_systolic', name: '收缩压', unit: 'mmHg', normalRange: { min: 90, max: 140 } },
      { key: 'blood_pressure_diastolic', name: '舒张压', unit: 'mmHg', normalRange: { min: 60, max: 90 } },
      { key: 'heart_rate', name: '心率', unit: 'bpm', normalRange: { min: 60, max: 100 } },
      { key: 'blood_glucose', name: '血糖', unit: 'mmol/L', normalRange: { min: 3.9, max: 6.1 } }
    ],

    healthReports: [
      {
        id: 1,
        date: '2024-01-15',
        indicators: {
          blood_pressure_systolic: 120,
          blood_pressure_diastolic: 80,
          heart_rate: 72,
          blood_glucose: 5.2
        }
      },
      {
        id: 2,
        date: '2024-02-15',
        indicators: {
          blood_pressure_systolic: 135,
          blood_pressure_diastolic: 85,
          heart_rate: 78,
          blood_glucose: 5.8
        }
      },
      {
        id: 3,
        date: '2024-03-15',
        indicators: {
          blood_pressure_systolic: 145,
          blood_pressure_diastolic: 92,
          heart_rate: 82,
          blood_glucose: 6.5
        }
      },
      {
        id: 4,
        date: '2024-04-15',
        indicators: {
          blood_pressure_systolic: 125,
          blood_pressure_diastolic: 78,
          heart_rate: 70,
          blood_glucose: 5.0
        }
      }
    ],

    // 计算整体统计
    calculateOverviewStatistics(reports) {
      const totalReports = reports.length
      
      if (totalReports === 0) {
        return {
          totalReports: 0,
          abnormalReports: 0,
          abnormalRate: 0,
          averageInterval: 0
        }
      }
      
      // 计算异常报告数
      let abnormalCount = 0
      reports.forEach(report => {
        let hasAbnormal = false
        this.availableIndicators.forEach(indicator => {
          const value = report.indicators[indicator.key]
          if (value && this.isAbnormalValue(value, indicator.normalRange)) {
            hasAbnormal = true
          }
        })
        if (hasAbnormal) abnormalCount++
      })
      
      const abnormalRate = Math.round((abnormalCount / totalReports) * 100)
      
      // 计算平均间隔
      let averageInterval = 0
      if (reports.length > 1) {
        const firstDate = new Date(reports[0].date)
        const lastDate = new Date(reports[reports.length - 1].date)
        const totalDays = Math.ceil((lastDate - firstDate) / (1000 * 60 * 60 * 24))
        averageInterval = Math.round(totalDays / (reports.length - 1))
      }
      
      return {
        totalReports,
        abnormalReports: abnormalCount,
        abnormalRate,
        averageInterval
      }
    },

    // 计算指标统计
    calculateIndicatorStatistics(reports) {
      return this.availableIndicators.map(indicator => {
        const values = reports
          .map(report => report.indicators[indicator.key])
          .filter(value => value !== undefined && value !== null)
        
        if (values.length === 0) {
          return {
            key: indicator.key,
            name: indicator.name,
            unit: indicator.unit,
            normalRange: indicator.normalRange,
            average: 0,
            max: 0,
            min: 0,
            stdDev: 0,
            range: 0,
            abnormalCount: 0,
            maxAbnormal: false,
            minAbnormal: false,
            trend: 'stable'
          }
        }
        
        const sum = values.reduce((acc, val) => acc + val, 0)
        const average = sum / values.length
        const max = Math.max(...values)
        const min = Math.min(...values)
        
        // 计算标准差
        const variance = values.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / values.length
        const stdDev = Math.sqrt(variance)
        
        // 计算异常值数量
        const abnormalCount = values.filter(value => 
          this.isAbnormalValue(value, indicator.normalRange)
        ).length
        
        // 计算趋势
        const trend = this.calculateTrend(values)
        
        return {
          key: indicator.key,
          name: indicator.name,
          unit: indicator.unit,
          normalRange: indicator.normalRange,
          average: average.toFixed(1),
          max: max.toFixed(1),
          min: min.toFixed(1),
          stdDev: stdDev.toFixed(1),
          range: (max - min).toFixed(1),
          abnormalCount,
          maxAbnormal: this.isAbnormalValue(max, indicator.normalRange),
          minAbnormal: this.isAbnormalValue(min, indicator.normalRange),
          trend
        }
      })
    },

    // 计算趋势
    calculateTrend(values) {
      if (values.length < 2) return 'stable'
      
      const firstHalf = values.slice(0, Math.floor(values.length / 2))
      const secondHalf = values.slice(Math.floor(values.length / 2))
      
      const firstAvg = firstHalf.reduce((acc, val) => acc + val, 0) / firstHalf.length
      const secondAvg = secondHalf.reduce((acc, val) => acc + val, 0) / secondHalf.length
      
      const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100
      
      if (changePercent > 10) return 'up'
      if (changePercent < -10) return 'down'
      return 'stable'
    },

    // 计算健康评分
    calculateHealthScore(reports) {
      if (reports.length === 0) {
        return {
          healthScore: 0,
          scoreBreakdown: []
        }
      }
      
      let totalScore = 0
      const categories = [
        { name: '血压', indicators: ['blood_pressure_systolic', 'blood_pressure_diastolic'], weight: 0.3 },
        { name: '心率', indicators: ['heart_rate'], weight: 0.2 },
        { name: '血糖', indicators: ['blood_glucose'], weight: 0.25 },
        { name: '其他', indicators: [], weight: 0.25 }
      ]
      
      const scoreBreakdown = categories.map(category => {
        let categoryScore = 100
        let abnormalCount = 0
        let totalCount = 0
        
        category.indicators.forEach(indicatorKey => {
          const indicator = this.availableIndicators.find(ind => ind.key === indicatorKey)
          if (!indicator) return
          
          reports.forEach(report => {
            const value = report.indicators[indicatorKey]
            if (value !== undefined && value !== null) {
              totalCount++
              if (this.isAbnormalValue(value, indicator.normalRange)) {
                abnormalCount++
              }
            }
          })
        })
        
        if (totalCount > 0) {
          const abnormalRate = abnormalCount / totalCount
          categoryScore = Math.max(0, 100 - (abnormalRate * 100))
        }
        
        totalScore += categoryScore * category.weight
        
        return {
          category: category.name,
          score: Math.round(categoryScore),
          percentage: categoryScore,
          color: this.getScoreColor(categoryScore)
        }
      })
      
      return {
        healthScore: Math.round(totalScore),
        scoreBreakdown
      }
    },

    // 生成建议
    generateRecommendations(reports, abnormalRate, averageInterval, indicatorStatistics) {
      const recommendations = []
      
      // 基于异常率生成建议
      if (abnormalRate > 50) {
        recommendations.push({
          priority: 'high',
          title: '多项指标异常',
          content: '您的多项健康指标存在异常，建议尽快咨询医生，制定针对性的治疗方案。'
        })
      } else if (abnormalRate > 20) {
        recommendations.push({
          priority: 'medium',
          title: '部分指标需关注',
          content: '部分健康指标超出正常范围，建议调整生活方式，定期复查。'
        })
      }
      
      // 基于检查频率生成建议
      if (averageInterval > 90) {
        recommendations.push({
          priority: 'low',
          title: '建议增加检查频率',
          content: '您的检查间隔较长，建议定期进行健康检查，及时了解身体状况。'
        })
      }
      
      // 基于趋势生成建议
      const worseningIndicators = indicatorStatistics.filter(ind => ind.trend === 'up' && ind.abnormalCount > 0)
      if (worseningIndicators.length > 0) {
        recommendations.push({
          priority: 'medium',
          title: '指标趋势恶化',
          content: `${worseningIndicators.map(ind => ind.name).join('、')}呈恶化趋势，建议重点关注。`
        })
      }
      
      // 如果没有特殊建议，给出一般性建议
      if (recommendations.length === 0) {
        recommendations.push({
          priority: 'low',
          title: '保持良好习惯',
          content: '您的健康状况良好，建议继续保持健康的生活方式，定期体检。'
        })
      }
      
      return recommendations
    },

    // 判断异常值
    isAbnormalValue(value, normalRange) {
      const { min, max } = normalRange
      if (min === null || max === null) return false
      return value < min || value > max
    },

    // 获取评分颜色
    getScoreColor(score) {
      if (score >= 90) return '#34C759'
      if (score >= 80) return '#007AFF'
      if (score >= 70) return '#FF9500'
      if (score >= 60) return '#FF3B30'
      return '#8E8E93'
    },

    // 获取评分等级
    getScoreLevel(score) {
      if (score >= 90) return '优秀'
      if (score >= 80) return '良好'
      if (score >= 70) return '一般'
      if (score >= 60) return '需改善'
      return '需重视'
    }
  }

  describe('整体统计计算', () => {
    it('应该正确计算整体统计信息', () => {
      const stats = statisticsAnalysisMethods.calculateOverviewStatistics(
        statisticsAnalysisMethods.healthReports
      )
      
      expect(stats.totalReports).toBe(4)
      expect(stats.abnormalReports).toBeGreaterThan(0) // 应该有异常报告
      expect(stats.abnormalRate).toBeGreaterThan(0)
      expect(stats.averageInterval).toBeGreaterThan(0)
    })

    it('应该正确处理空报告', () => {
      const stats = statisticsAnalysisMethods.calculateOverviewStatistics([])
      
      expect(stats.totalReports).toBe(0)
      expect(stats.abnormalReports).toBe(0)
      expect(stats.abnormalRate).toBe(0)
      expect(stats.averageInterval).toBe(0)
    })

    it('应该正确处理单个报告', () => {
      const singleReport = [statisticsAnalysisMethods.healthReports[0]]
      const stats = statisticsAnalysisMethods.calculateOverviewStatistics(singleReport)
      
      expect(stats.totalReports).toBe(1)
      expect(stats.averageInterval).toBe(0) // 单个报告无间隔
    })
  })

  describe('指标统计计算', () => {
    it('应该正确计算指标统计信息', () => {
      const indicatorStats = statisticsAnalysisMethods.calculateIndicatorStatistics(
        statisticsAnalysisMethods.healthReports
      )
      
      expect(indicatorStats.length).toBe(4) // 4个指标
      
      const systolicStats = indicatorStats.find(stat => stat.key === 'blood_pressure_systolic')
      expect(systolicStats).toBeDefined()
      expect(parseFloat(systolicStats.average)).toBeGreaterThan(0)
      expect(parseFloat(systolicStats.max)).toBeGreaterThan(0)
      expect(parseFloat(systolicStats.min)).toBeGreaterThan(0)
      expect(parseFloat(systolicStats.stdDev)).toBeGreaterThanOrEqual(0)
    })

    it('应该正确识别异常值', () => {
      const indicatorStats = statisticsAnalysisMethods.calculateIndicatorStatistics(
        statisticsAnalysisMethods.healthReports
      )
      
      const systolicStats = indicatorStats.find(stat => stat.key === 'blood_pressure_systolic')
      expect(systolicStats.abnormalCount).toBeGreaterThan(0) // 应该有异常值（145超出140）
    })

    it('应该正确处理空数据', () => {
      const indicatorStats = statisticsAnalysisMethods.calculateIndicatorStatistics([])
      
      expect(indicatorStats.length).toBe(4)
      indicatorStats.forEach(stat => {
        expect(stat.average).toBe(0)
        expect(stat.max).toBe(0)
        expect(stat.min).toBe(0)
        expect(stat.abnormalCount).toBe(0)
      })
    })
  })

  describe('趋势计算', () => {
    it('应该正确识别上升趋势', () => {
      const values = [100, 110, 120, 130] // 明显上升
      const trend = statisticsAnalysisMethods.calculateTrend(values)
      expect(trend).toBe('up')
    })

    it('应该正确识别下降趋势', () => {
      const values = [130, 120, 110, 100] // 明显下降
      const trend = statisticsAnalysisMethods.calculateTrend(values)
      expect(trend).toBe('down')
    })

    it('应该正确识别稳定趋势', () => {
      const values = [120, 122, 118, 121] // 相对稳定
      const trend = statisticsAnalysisMethods.calculateTrend(values)
      expect(trend).toBe('stable')
    })

    it('应该处理单个值', () => {
      const values = [120]
      const trend = statisticsAnalysisMethods.calculateTrend(values)
      expect(trend).toBe('stable')
    })
  })

  describe('健康评分计算', () => {
    it('应该正确计算健康评分', () => {
      const result = statisticsAnalysisMethods.calculateHealthScore(
        statisticsAnalysisMethods.healthReports
      )
      
      expect(result.healthScore).toBeGreaterThan(0)
      expect(result.healthScore).toBeLessThanOrEqual(100)
      expect(result.scoreBreakdown.length).toBeGreaterThan(0)
    })

    it('应该正确计算各类别评分', () => {
      const result = statisticsAnalysisMethods.calculateHealthScore(
        statisticsAnalysisMethods.healthReports
      )
      
      result.scoreBreakdown.forEach(breakdown => {
        expect(breakdown.score).toBeGreaterThanOrEqual(0)
        expect(breakdown.score).toBeLessThanOrEqual(100)
        expect(breakdown.percentage).toBeGreaterThanOrEqual(0)
        expect(breakdown.percentage).toBeLessThanOrEqual(100)
        expect(breakdown.color).toBeDefined()
      })
    })

    it('应该处理空报告', () => {
      const result = statisticsAnalysisMethods.calculateHealthScore([])
      
      expect(result.healthScore).toBe(0)
      expect(result.scoreBreakdown.length).toBe(0)
    })
  })

  describe('建议生成', () => {
    it('应该为高异常率生成高优先级建议', () => {
      const recommendations = statisticsAnalysisMethods.generateRecommendations(
        statisticsAnalysisMethods.healthReports,
        60, // 高异常率
        30, // 正常间隔
        []
      )
      
      const highPriorityRec = recommendations.find(rec => rec.priority === 'high')
      expect(highPriorityRec).toBeDefined()
      expect(highPriorityRec.title).toContain('多项指标异常')
    })

    it('应该为中等异常率生成中优先级建议', () => {
      const recommendations = statisticsAnalysisMethods.generateRecommendations(
        statisticsAnalysisMethods.healthReports,
        30, // 中等异常率
        30, // 正常间隔
        []
      )
      
      const mediumPriorityRec = recommendations.find(rec => rec.priority === 'medium')
      expect(mediumPriorityRec).toBeDefined()
      expect(mediumPriorityRec.title).toContain('部分指标需关注')
    })

    it('应该为长间隔生成建议', () => {
      const recommendations = statisticsAnalysisMethods.generateRecommendations(
        statisticsAnalysisMethods.healthReports,
        10, // 低异常率
        100, // 长间隔
        []
      )
      
      const intervalRec = recommendations.find(rec => rec.title.includes('检查频率'))
      expect(intervalRec).toBeDefined()
    })

    it('应该为恶化趋势生成建议', () => {
      const worseningIndicators = [
        { name: '收缩压', trend: 'up', abnormalCount: 2 }
      ]
      
      const recommendations = statisticsAnalysisMethods.generateRecommendations(
        statisticsAnalysisMethods.healthReports,
        10, // 低异常率
        30, // 正常间隔
        worseningIndicators
      )
      
      const trendRec = recommendations.find(rec => rec.title.includes('趋势恶化'))
      expect(trendRec).toBeDefined()
      expect(trendRec.content).toContain('收缩压')
    })

    it('应该在无特殊情况时生成一般建议', () => {
      const recommendations = statisticsAnalysisMethods.generateRecommendations(
        statisticsAnalysisMethods.healthReports,
        0, // 无异常
        30, // 正常间隔
        []
      )
      
      expect(recommendations.length).toBe(1)
      expect(recommendations[0].title).toContain('保持良好习惯')
    })
  })

  describe('异常值判断', () => {
    it('应该正确判断异常值', () => {
      const normalRange = { min: 90, max: 140 }
      
      expect(statisticsAnalysisMethods.isAbnormalValue(80, normalRange)).toBe(true)  // 偏低
      expect(statisticsAnalysisMethods.isAbnormalValue(150, normalRange)).toBe(true) // 偏高
      expect(statisticsAnalysisMethods.isAbnormalValue(120, normalRange)).toBe(false) // 正常
    })

    it('应该处理无范围的情况', () => {
      const noRange = { min: null, max: null }
      expect(statisticsAnalysisMethods.isAbnormalValue(120, noRange)).toBe(false)
    })
  })

  describe('评分等级和颜色', () => {
    it('应该返回正确的评分等级', () => {
      expect(statisticsAnalysisMethods.getScoreLevel(95)).toBe('优秀')
      expect(statisticsAnalysisMethods.getScoreLevel(85)).toBe('良好')
      expect(statisticsAnalysisMethods.getScoreLevel(75)).toBe('一般')
      expect(statisticsAnalysisMethods.getScoreLevel(65)).toBe('需改善')
      expect(statisticsAnalysisMethods.getScoreLevel(55)).toBe('需重视')
    })

    it('应该返回正确的评分颜色', () => {
      expect(statisticsAnalysisMethods.getScoreColor(95)).toBe('#34C759') // 绿色
      expect(statisticsAnalysisMethods.getScoreColor(85)).toBe('#007AFF') // 蓝色
      expect(statisticsAnalysisMethods.getScoreColor(75)).toBe('#FF9500') // 橙色
      expect(statisticsAnalysisMethods.getScoreColor(65)).toBe('#FF3B30') // 红色
      expect(statisticsAnalysisMethods.getScoreColor(55)).toBe('#8E8E93') // 灰色
    })
  })

  describe('数据完整性验证', () => {
    it('应该正确处理缺失指标数据', () => {
      const incompleteReports = [
        {
          id: 1,
          date: '2024-01-01',
          indicators: {
            blood_pressure_systolic: 120
            // 缺少其他指标
          }
        }
      ]
      
      const indicatorStats = statisticsAnalysisMethods.calculateIndicatorStatistics(incompleteReports)
      
      const systolicStats = indicatorStats.find(stat => stat.key === 'blood_pressure_systolic')
      const heartRateStats = indicatorStats.find(stat => stat.key === 'heart_rate')
      
      expect(parseFloat(systolicStats.average)).toBe(120)
      expect(heartRateStats.average).toBe(0) // 无数据应为0
    })
  })
})