<template>
  <view class="report-card" @tap="handleTap">
    <!-- 报告头部信息 -->
    <view class="card-header">
      <view class="hospital-info">
        <text class="hospital-name">{{ report.hospital }}</text>
        <text v-if="report.doctor" class="doctor-name">{{ report.doctor }}</text>
      </view>
      <view class="date-info">
        <text class="report-date">{{ formatDate(report.reportDate) }}</text>
      </view>
    </view>
    
    <!-- 报告标题 -->
    <view class="card-title">
      <text class="title-text">{{ getReportTitle() }}</text>
      <view v-if="hasAbnormalItems" class="abnormal-badge">
        <text class="badge-text">{{ abnormalCount }}项异常</text>
      </view>
    </view>
    
    <!-- 检查项目摘要 -->
    <view class="items-summary">
      <view class="summary-stats">
        <text class="stats-text">共{{ report.items.length }}项检查</text>
        <text v-if="hasAbnormalItems" class="abnormal-text">
          {{ abnormalCount }}项异常
        </text>
      </view>
      
      <!-- 异常项目预览 -->
      <view v-if="hasAbnormalItems && showAbnormalPreview" class="abnormal-preview">
        <view 
          v-for="item in abnormalItems.slice(0, 2)" 
          :key="item.id"
          class="abnormal-item"
        >
          <text class="item-name">{{ item.name }}</text>
          <text class="item-value abnormal">{{ item.getDisplayValue() }}</text>
        </view>
        <text v-if="abnormalItems.length > 2" class="more-text">
          等{{ abnormalItems.length - 2 }}项...
        </text>
      </view>
    </view>
    
    <!-- 报告分类标签 -->
    <view class="card-tags">
      <view class="category-tag">
        <text class="tag-text">{{ getCategoryName() }}</text>
      </view>
      <view v-for="tag in report.tags.slice(0, 2)" :key="tag" class="custom-tag">
        <text class="tag-text">{{ tag }}</text>
      </view>
    </view>
    
    <!-- 同步状态指示器 -->
    <view class="sync-status">
      <view :class="['status-indicator', syncStatusClass]">
        <text class="status-text">{{ getSyncStatusText() }}</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view v-if="showActions" class="card-actions">
      <button class="action-btn edit-btn" @tap.stop="handleEdit">
        <text class="btn-text">编辑</text>
      </button>
      <button class="action-btn delete-btn" @tap.stop="handleDelete">
        <text class="btn-text">删除</text>
      </button>
    </view>
  </view>
</template>

<script>
import { Constants } from '@/types/index.js'

export default {
  name: 'ReportCard',
  props: {
    // 报告数据
    report: {
      type: Object,
      required: true
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: false
    },
    // 是否显示异常项目预览
    showAbnormalPreview: {
      type: Boolean,
      default: true
    }
  },
  
  computed: {
    // 是否有异常项目
    hasAbnormalItems() {
      return this.report.items.some(item => item.isAbnormal)
    },
    
    // 异常项目列表
    abnormalItems() {
      return this.report.items.filter(item => item.isAbnormal)
    },
    
    // 异常项目数量
    abnormalCount() {
      return this.abnormalItems.length
    },
    
    // 同步状态样式类
    syncStatusClass() {
      const statusMap = {
        [Constants.SYNC_STATUS.LOCAL]: 'local',
        [Constants.SYNC_STATUS.SYNCED]: 'synced',
        [Constants.SYNC_STATUS.PENDING]: 'pending',
        [Constants.SYNC_STATUS.ERROR]: 'error'
      }
      return statusMap[this.report.syncStatus] || 'local'
    }
  },
  
  methods: {
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      const now = new Date()
      const diffTime = now - d
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return d.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      }
    },
    
    // 获取报告标题
    getReportTitle() {
      if (this.report.title) {
        return this.report.title
      }
      
      // 自动生成标题
      const categoryName = this.getCategoryName()
      return `${categoryName}检查报告`
    },
    
    // 获取分类名称
    getCategoryName() {
      const categoryNames = {
        [Constants.REPORT_CATEGORIES.BLOOD_ROUTINE]: '血常规',
        [Constants.REPORT_CATEGORIES.BIOCHEMISTRY]: '生化',
        [Constants.REPORT_CATEGORIES.IMMUNOLOGY]: '免疫',
        [Constants.REPORT_CATEGORIES.URINE_ROUTINE]: '尿常规',
        [Constants.REPORT_CATEGORIES.IMAGING]: '影像',
        [Constants.REPORT_CATEGORIES.OTHER]: '其他'
      }
      return categoryNames[this.report.category] || '检查'
    },
    
    // 获取同步状态文本
    getSyncStatusText() {
      const statusTexts = {
        [Constants.SYNC_STATUS.LOCAL]: '本地',
        [Constants.SYNC_STATUS.SYNCED]: '已同步',
        [Constants.SYNC_STATUS.PENDING]: '待同步',
        [Constants.SYNC_STATUS.ERROR]: '同步失败'
      }
      return statusTexts[this.report.syncStatus] || '本地'
    },
    
    // 处理卡片点击
    handleTap() {
      this.$emit('tap', this.report)
    },
    
    // 处理编辑
    handleEdit() {
      this.$emit('edit', this.report)
    },
    
    // 处理删除
    handleDelete() {
      this.$emit('delete', this.report)
    }
  }
}
</script>

<style lang="scss" scoped>
.report-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.hospital-info {
  flex: 1;
}

.hospital-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.doctor-name {
  font-size: 26rpx;
  color: #666666;
  display: block;
}

.date-info {
  text-align: right;
}

.report-date {
  font-size: 26rpx;
  color: #999999;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  flex: 1;
}

.abnormal-badge {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  margin-left: 20rpx;
}

.badge-text {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 500;
}

.items-summary {
  margin-bottom: 24rpx;
}

.summary-stats {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.stats-text {
  font-size: 26rpx;
  color: #666666;
  margin-right: 20rpx;
}

.abnormal-text {
  font-size: 26rpx;
  color: #ff5252;
  font-weight: 500;
}

.abnormal-preview {
  background: #fff5f5;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 6rpx solid #ff5252;
}

.abnormal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.item-name {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.item-value {
  font-size: 26rpx;
  font-weight: 500;
  
  &.abnormal {
    color: #ff5252;
  }
}

.more-text {
  font-size: 24rpx;
  color: #999999;
  font-style: italic;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.category-tag,
.custom-tag {
  background: #f0f9ff;
  border: 2rpx solid #e0f2fe;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}

.category-tag {
  background: #e3f2fd;
  border-color: #bbdefb;
}

.tag-text {
  font-size: 22rpx;
  color: #1976d2;
}

.sync-status {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}

.status-indicator {
  border-radius: 12rpx;
  padding: 6rpx 12rpx;
  
  &.local {
    background: #f5f5f5;
    border: 2rpx solid #e0e0e0;
  }
  
  &.synced {
    background: #e8f5e8;
    border: 2rpx solid #c8e6c9;
  }
  
  &.pending {
    background: #fff3e0;
    border: 2rpx solid #ffcc02;
  }
  
  &.error {
    background: #ffebee;
    border: 2rpx solid #ffcdd2;
  }
}

.status-text {
  font-size: 20rpx;
  
  .local & {
    color: #757575;
  }
  
  .synced & {
    color: #388e3c;
  }
  
  .pending & {
    color: #f57c00;
  }
  
  .error & {
    color: #d32f2f;
  }
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.action-btn {
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  border: none;
  
  &.edit-btn {
    background: #e3f2fd;
    color: #1976d2;
  }
  
  &.delete-btn {
    background: #ffebee;
    color: #d32f2f;
  }
}

.btn-text {
  font-size: 26rpx;
}
</style>