<script>
	import { useAppStore } from './stores/app.js'
	import { useUserStore } from './stores/user.js'
	import { useReportStore } from './stores/report.js'
	import { useSyncStore } from './stores/sync.js'
	import { performanceMonitor } from './utils/performance/index.js'
	import networkManager from './utils/network/index.js'
	import riskMonitorManager from './utils/security/riskMonitor.js'
	import wechatSecurityManager from './utils/security/wechatSecurity.js'
	
	export default {
		onLaunch: function() {
			console.log('健康报告应用启动')
			
			// 开始性能监控
			performanceMonitor.startPageLoad('App')
			
			this.initApp()
		},
		onShow: function() {
			console.log('应用显示')
			this.onAppShow()
		},
		onHide: function() {
			console.log('应用隐藏')
			this.onAppHide()
		},
		methods: {
			// 初始化应用
			async initApp() {
				try {
					// 初始化各个store
					const appStore = useAppStore()
					const userStore = useUserStore()
					const reportStore = useReportStore()
					const syncStore = useSyncStore()
					
					// 并行初始化
					await Promise.all([
						appStore.initApp(),
						userStore.initUser(),
						reportStore.initReports(),
						syncStore.initSync()
					])
					
					console.log('应用初始化完成')
					
					// 结束性能监控
					performanceMonitor.endPageLoad('App')
					
					// 检查是否需要显示引导页面
					this.checkGuideStatus()
					
					// 启动安全监控
					this.initSecurity()
					
				} catch (error) {
					console.error('应用初始化失败:', error)
					uni.showToast({
						title: '应用初始化失败',
						icon: 'none'
					})
				}
			},
			
			// 应用显示时的处理
			onAppShow() {
				const appStore = useAppStore()
				const syncStore = useSyncStore()
				
				// 检查网络状态
				uni.getNetworkType({
					success: (res) => {
						appStore.updateNetworkStatus({
							isOnline: res.networkType !== 'none',
							networkType: res.networkType
						})
					}
				})
				
				// 如果启用自动同步且有待同步数据，启动同步
				if (syncStore.syncConfig.autoSync && syncStore.needSync) {
					setTimeout(() => {
						syncStore.startSync({ background: true })
					}, 1000)
				}
			},
			
			// 应用隐藏时的处理
			onAppHide() {
				// 保存当前状态
				const reportStore = useReportStore()
				const syncStore = useSyncStore()
				
				reportStore.saveToLocal()
				syncStore.saveToLocal()
				
				// 清理过期缓存
				const { cacheManager } = require('./utils/performance/index.js')
				cacheManager.clearExpiredCache()
			},
			
			// 检查引导状态
			checkGuideStatus() {
				const appStore = useAppStore()
				
				// 如果是首次启动且未完成引导，跳转到引导页面
				if (!appStore.settings.hasCompletedGuide) {
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/guide/index'
						})
					}, 1000)
				}
			},
			
			// 初始化安全功能
			initSecurity() {
				try {
					// 启动风险监控
					riskMonitorManager.startMonitoring()
					
					// 初始化微信小程序安全
					wechatSecurityManager.init()
					
					console.log('安全功能初始化完成')
				} catch (error) {
					console.error('安全功能初始化失败:', error)
				}
			}
		}
	}
</script>

<style>
	/* 全局样式 */
	
	/* 重置样式 */
	* {
		box-sizing: border-box;
	}
	
	page {
		background-color: #F5F5F5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
		font-size: 14px;
		line-height: 1.6;
		color: #333333;
	}
	
	/* 通用容器 */
	.container {
		padding: 0 15px;
	}
	
	.page-container {
		min-height: 100vh;
		background-color: #F5F5F5;
	}
	
	/* 卡片样式 */
	.card {
		background-color: #FFFFFF;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 10px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	}
	
	.card-title {
		font-size: 16px;
		font-weight: 600;
		color: #333333;
		margin-bottom: 10px;
	}
	
	/* 按钮样式 */
	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		padding: 10px 20px;
		border-radius: 6px;
		font-size: 14px;
		font-weight: 500;
		text-align: center;
		cursor: pointer;
		transition: all 0.3s ease;
		border: none;
		outline: none;
	}
	
	.btn-primary {
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.btn-primary:active {
		background-color: #0056CC;
	}
	
	.btn-secondary {
		background-color: #F2F2F7;
		color: #333333;
	}
	
	.btn-secondary:active {
		background-color: #E5E5EA;
	}
	
	.btn-danger {
		background-color: #FF3B30;
		color: #FFFFFF;
	}
	
	.btn-danger:active {
		background-color: #D70015;
	}
	
	.btn-small {
		padding: 6px 12px;
		font-size: 12px;
	}
	
	.btn-large {
		padding: 15px 30px;
		font-size: 16px;
	}
	
	.btn-block {
		width: 100%;
	}
	
	.btn-disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}
	
	/* 输入框样式 */
	.input {
		width: 100%;
		padding: 12px 15px;
		border: 1px solid #E5E5EA;
		border-radius: 6px;
		font-size: 14px;
		background-color: #FFFFFF;
		transition: border-color 0.3s ease;
	}
	
	.input:focus {
		border-color: #007AFF;
		outline: none;
	}
	
	.input-error {
		border-color: #FF3B30;
	}
	
	/* 文本样式 */
	.text-primary {
		color: #007AFF;
	}
	
	.text-secondary {
		color: #8E8E93;
	}
	
	.text-success {
		color: #34C759;
	}
	
	.text-warning {
		color: #FF9500;
	}
	
	.text-danger {
		color: #FF3B30;
	}
	
	.text-muted {
		color: #8E8E93;
	}
	
	.text-center {
		text-align: center;
	}
	
	.text-right {
		text-align: right;
	}
	
	/* 间距工具类 */
	.mt-5 { margin-top: 5px; }
	.mt-10 { margin-top: 10px; }
	.mt-15 { margin-top: 15px; }
	.mt-20 { margin-top: 20px; }
	
	.mb-5 { margin-bottom: 5px; }
	.mb-10 { margin-bottom: 10px; }
	.mb-15 { margin-bottom: 15px; }
	.mb-20 { margin-bottom: 20px; }
	
	.ml-5 { margin-left: 5px; }
	.ml-10 { margin-left: 10px; }
	.ml-15 { margin-left: 15px; }
	.ml-20 { margin-left: 20px; }
	
	.mr-5 { margin-right: 5px; }
	.mr-10 { margin-right: 10px; }
	.mr-15 { margin-right: 15px; }
	.mr-20 { margin-right: 20px; }
	
	.pt-5 { padding-top: 5px; }
	.pt-10 { padding-top: 10px; }
	.pt-15 { padding-top: 15px; }
	.pt-20 { padding-top: 20px; }
	
	.pb-5 { padding-bottom: 5px; }
	.pb-10 { padding-bottom: 10px; }
	.pb-15 { padding-bottom: 15px; }
	.pb-20 { padding-bottom: 20px; }
	
	.pl-5 { padding-left: 5px; }
	.pl-10 { padding-left: 10px; }
	.pl-15 { padding-left: 15px; }
	.pl-20 { padding-left: 20px; }
	
	.pr-5 { padding-right: 5px; }
	.pr-10 { padding-right: 10px; }
	.pr-15 { padding-right: 15px; }
	.pr-20 { padding-right: 20px; }
	
	/* 布局工具类 */
	.flex {
		display: flex;
	}
	
	.flex-column {
		flex-direction: column;
	}
	
	.flex-center {
		align-items: center;
		justify-content: center;
	}
	
	.flex-between {
		justify-content: space-between;
	}
	
	.flex-around {
		justify-content: space-around;
	}
	
	.flex-1 {
		flex: 1;
	}
	
	/* 加载状态 */
	.loading {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px;
		color: #8E8E93;
	}
	
	/* 空状态 */
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40px 20px;
		color: #8E8E93;
		text-align: center;
	}
	
	.empty-state-icon {
		font-size: 48px;
		margin-bottom: 15px;
		opacity: 0.5;
	}
	
	.empty-state-text {
		font-size: 14px;
		line-height: 1.5;
	}
	
	/* 异常状态标记 */
	.abnormal-indicator {
		color: #FF3B30;
		font-weight: 600;
	}
	
	.normal-indicator {
		color: #34C759;
	}
	
	/* 状态徽章 */
	.badge {
		display: inline-block;
		padding: 2px 8px;
		border-radius: 12px;
		font-size: 12px;
		font-weight: 500;
		text-align: center;
		white-space: nowrap;
	}
	
	.badge-primary {
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.badge-success {
		background-color: #34C759;
		color: #FFFFFF;
	}
	
	.badge-warning {
		background-color: #FF9500;
		color: #FFFFFF;
	}
	
	.badge-danger {
		background-color: #FF3B30;
		color: #FFFFFF;
	}
	
	.badge-secondary {
		background-color: #8E8E93;
		color: #FFFFFF;
	}
</style>
