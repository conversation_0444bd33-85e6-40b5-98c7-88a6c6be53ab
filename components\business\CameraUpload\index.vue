<template>
  <view class="camera-upload">
    <!-- 图片预览区域 -->
    <view class="preview-area" v-if="imageList.length > 0">
      <view class="image-item" v-for="(item, index) in imageList" :key="index">
        <image 
          :src="item.path" 
          class="preview-image"
          mode="aspectFit"
          @click="previewImage(item.path)"
        />
        <view class="image-actions">
          <text class="action-btn" @click="removeImage(index)">删除</text>
          <text class="action-btn" @click="retakeImage(index)">重拍</text>
        </view>
        <view class="quality-indicator" :class="item.quality.passed ? 'good' : 'poor'">
          {{ item.quality.passed ? '质量良好' : '质量较差' }}
        </view>
      </view>
    </view>

    <!-- 上传按钮区域 -->
    <view class="upload-actions">
      <button 
        class="upload-btn camera-btn" 
        @click="takePhoto"
        :disabled="loading"
      >
        <text class="btn-icon">📷</text>
        <text class="btn-text">拍照</text>
      </button>
      
      <button 
        class="upload-btn album-btn" 
        @click="chooseFromAlbum"
        :disabled="loading"
      >
        <text class="btn-icon">🖼️</text>
        <text class="btn-text">从相册选择</text>
      </button>
    </view>

    <!-- 质量提示 -->
    <view class="quality-tips" v-if="showTips">
      <text class="tips-title">拍照建议：</text>
      <text class="tips-item">• 确保光线充足，避免阴影遮挡</text>
      <text class="tips-item">• 保持报告平整，避免折叠和弯曲</text>
      <text class="tips-item">• 镜头与报告保持垂直，避免倾斜</text>
      <text class="tips-item">• 确保文字清晰可见，避免模糊</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="loading">
      <text class="loading-text">处理中...</text>
    </view>

    <!-- 质量检查结果弹窗 -->
    <uni-popup ref="qualityPopup" type="dialog">
      <uni-popup-dialog 
        :title="qualityResult.title"
        :content="qualityResult.content"
        :before-close="true"
        @close="handleQualityDialogClose"
        @confirm="handleQualityDialogConfirm"
      />
    </uni-popup>
  </view>
</template>

<script>
import ImageProcessor from '@/utils/image/processor.js'

export default {
  name: 'CameraUpload',
  props: {
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 5
    },
    // 是否显示拍照提示
    showTips: {
      type: Boolean,
      default: true
    },
    // 是否自动压缩
    autoCompress: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      imageList: [],
      loading: false,
      qualityResult: {
        title: '',
        content: '',
        action: null
      }
    }
  },
  methods: {
    /**
     * 拍照
     */
    async takePhoto() {
      if (this.imageList.length >= this.maxCount) {
        uni.showToast({
          title: `最多只能上传${this.maxCount}张图片`,
          icon: 'none'
        });
        return;
      }

      try {
        this.loading = true;
        
        // 检查相机权限
        const hasPermission = await this.checkCameraPermission();
        if (!hasPermission) {
          return;
        }

        // 调用相机
        uni.chooseImage({
          count: 1,
          sourceType: ['camera'],
          sizeType: ['original'],
          success: async (res) => {
            const imagePath = res.tempFilePaths[0];
            await this.processImage(imagePath);
          },
          fail: (error) => {
            console.error('拍照失败:', error);
            uni.showToast({
              title: '拍照失败，请重试',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('拍照过程出错:', error);
        uni.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * 从相册选择
     */
    async chooseFromAlbum() {
      if (this.imageList.length >= this.maxCount) {
        uni.showToast({
          title: `最多只能上传${this.maxCount}张图片`,
          icon: 'none'
        });
        return;
      }

      try {
        this.loading = true;
        
        const remainingCount = this.maxCount - this.imageList.length;
        
        uni.chooseImage({
          count: remainingCount,
          sourceType: ['album'],
          sizeType: ['original'],
          success: async (res) => {
            for (const imagePath of res.tempFilePaths) {
              await this.processImage(imagePath);
            }
          },
          fail: (error) => {
            console.error('选择图片失败:', error);
            uni.showToast({
              title: '选择图片失败，请重试',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('选择图片过程出错:', error);
        uni.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * 处理图片
     */
    async processImage(imagePath) {
      try {
        // 检查图片质量
        const qualityCheck = await ImageProcessor.validateImageQuality(imagePath);
        
        let processedPath = imagePath;
        
        // 如果需要预处理
        if (this.autoCompress || !qualityCheck.passed) {
          processedPath = await ImageProcessor.preprocessForOCR(imagePath);
        }
        
        // 生成缩略图
        const thumbnail = await ImageProcessor.generateThumbnail(processedPath);
        
        const imageItem = {
          path: processedPath,
          originalPath: imagePath,
          thumbnail: thumbnail,
          quality: qualityCheck,
          timestamp: Date.now()
        };
        
        this.imageList.push(imageItem);
        
        // 如果质量不佳，显示提示
        if (!qualityCheck.passed) {
          this.showQualityWarning(qualityCheck);
        }
        
        // 触发上传事件
        this.$emit('upload', imageItem);
        
        uni.showToast({
          title: '图片添加成功',
          icon: 'success'
        });
        
      } catch (error) {
        console.error('图片处理失败:', error);
        uni.showToast({
          title: '图片处理失败',
          icon: 'none'
        });
      }
    },

    /**
     * 显示质量警告
     */
    showQualityWarning(qualityCheck) {
      const issues = qualityCheck.issues.join('\n');
      this.qualityResult = {
        title: '图片质量提醒',
        content: `检测到以下问题：\n${issues}\n\n是否继续使用此图片？`,
        action: 'quality_warning'
      };
      this.$refs.qualityPopup.open();
    },

    /**
     * 预览图片
     */
    previewImage(imagePath) {
      uni.previewImage({
        urls: [imagePath],
        current: imagePath
      });
    },

    /**
     * 删除图片
     */
    removeImage(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        success: (res) => {
          if (res.confirm) {
            const removedItem = this.imageList.splice(index, 1)[0];
            this.$emit('remove', removedItem);
            
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },

    /**
     * 重新拍摄
     */
    async retakeImage(index) {
      try {
        this.loading = true;
        
        uni.chooseImage({
          count: 1,
          sourceType: ['camera'],
          sizeType: ['original'],
          success: async (res) => {
            const imagePath = res.tempFilePaths[0];
            
            // 处理新图片
            const qualityCheck = await ImageProcessor.validateImageQuality(imagePath);
            let processedPath = imagePath;
            
            if (this.autoCompress || !qualityCheck.passed) {
              processedPath = await ImageProcessor.preprocessForOCR(imagePath);
            }
            
            const thumbnail = await ImageProcessor.generateThumbnail(processedPath);
            
            // 替换原图片
            const newImageItem = {
              path: processedPath,
              originalPath: imagePath,
              thumbnail: thumbnail,
              quality: qualityCheck,
              timestamp: Date.now()
            };
            
            const oldItem = this.imageList[index];
            this.imageList.splice(index, 1, newImageItem);
            
            this.$emit('replace', { oldItem, newItem: newImageItem });
            
            uni.showToast({
              title: '重拍成功',
              icon: 'success'
            });
          }
        });
      } catch (error) {
        console.error('重拍失败:', error);
        uni.showToast({
          title: '重拍失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * 检查相机权限
     */
    async checkCameraPermission() {
      return new Promise((resolve) => {
        // #ifdef APP-PLUS
        const permissions = ['camera'];
        plus.android.requestPermissions(permissions, (result) => {
          if (result.granted && result.granted.length > 0) {
            resolve(true);
          } else {
            uni.showModal({
              title: '权限申请',
              content: '需要相机权限才能拍照，请在设置中开启',
              showCancel: false
            });
            resolve(false);
          }
        });
        // #endif
        
        // #ifdef H5 || MP
        resolve(true);
        // #endif
      });
    },

    /**
     * 处理质量对话框关闭
     */
    handleQualityDialogClose() {
      this.qualityResult = { title: '', content: '', action: null };
    },

    /**
     * 处理质量对话框确认
     */
    handleQualityDialogConfirm() {
      // 用户确认继续使用质量不佳的图片
      this.handleQualityDialogClose();
    },

    /**
     * 获取所有图片
     */
    getImages() {
      return this.imageList;
    },

    /**
     * 清空所有图片
     */
    clearImages() {
      this.imageList = [];
      this.$emit('clear');
    }
  }
}
</script>

<style lang="scss" scoped>
.camera-upload {
  padding: 20rpx;
}

.preview-area {
  margin-bottom: 30rpx;
}

.image-item {
  position: relative;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f8f8;
}

.preview-image {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
}

.image-actions {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  display: flex;
  gap: 10rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.quality-indicator {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: white;
  
  &.good {
    background: #4CAF50;
  }
  
  &.poor {
    background: #FF9800;
  }
}

.upload-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.upload-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  border: 2rpx dashed #ddd;
  background: #fafafa;
  
  &:not([disabled]):active {
    background: #f0f0f0;
  }
  
  &[disabled] {
    opacity: 0.6;
  }
}

.btn-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.btn-text {
  font-size: 28rpx;
  color: #666;
}

.quality-tips {
  padding: 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #2196F3;
}

.tips-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 10rpx;
}

.tips-item {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 5rpx;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-text {
  color: white;
  font-size: 32rpx;
}
</style>