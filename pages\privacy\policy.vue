<template>
	<view class="page-container">
		<view class="privacy-container">
			<!-- 页面头部 -->
			<view class="privacy-header">
				<text class="header-title">隐私政策</text>
				<text class="header-subtitle">最后更新时间：{{ lastUpdated }}</text>
			</view>
			
			<!-- 隐私政策内容 -->
			<scroll-view class="privacy-content" scroll-y="true">
				<!-- 概述 -->
				<view class="section">
					<text class="section-title">概述</text>
					<text class="section-content">
						健康报告管理应用（以下简称"本应用"）非常重视用户的隐私保护。本隐私政策详细说明了我们如何收集、使用、存储和保护您的个人信息。使用本应用即表示您同意本隐私政策的条款。
					</text>
				</view>
				
				<!-- 信息收集 -->
				<view class="section">
					<text class="section-title">我们收集的信息</text>
					
					<view class="subsection">
						<text class="subsection-title">1. 个人身份信息</text>
						<view class="info-list">
							<text class="info-item">• 手机号码（用于账户注册和登录）</text>
							<text class="info-item">• 姓名（用于个人资料管理）</text>
							<text class="info-item">• 身份证号（可选，用于身份验证）</text>
							<text class="info-item">• 邮箱地址（可选，用于账户找回）</text>
						</view>
					</view>
					
					<view class="subsection">
						<text class="subsection-title">2. 健康数据</text>
						<view class="info-list">
							<text class="info-item">• 体检报告图片和文字信息</text>
							<text class="info-item">• 健康指标数据（血压、血糖、胆固醇等）</text>
							<text class="info-item">• 医院和医生信息</text>
							<text class="info-item">• 检查日期和报告类型</text>
						</view>
					</view>
					
					<view class="subsection">
						<text class="subsection-title">3. 设备和使用信息</text>
						<view class="info-list">
							<text class="info-item">• 设备型号、操作系统版本</text>
							<text class="info-item">• 应用使用情况和操作日志</text>
							<text class="info-item">• 网络连接信息</text>
							<text class="info-item">• 位置信息（仅在您授权时）</text>
						</view>
					</view>
				</view>
				
				<!-- 信息使用 -->
				<view class="section">
					<text class="section-title">信息使用目的</text>
					<view class="info-list">
						<text class="info-item">• 提供健康报告管理和分析服务</text>
						<text class="info-item">• 进行身份验证和账户安全保护</text>
						<text class="info-item">• 改善应用功能和用户体验</text>
						<text class="info-item">• 发送重要通知和安全提醒</text>
						<text class="info-item">• 提供客户支持和技术服务</text>
						<text class="info-item">• 遵守法律法规要求</text>
					</view>
				</view>
				
				<!-- 信息共享 -->
				<view class="section">
					<text class="section-title">信息共享和披露</text>
					<text class="section-content">
						我们承诺不会出售、出租或以其他方式向第三方披露您的个人信息，除非：
					</text>
					<view class="info-list">
						<text class="info-item">• 获得您的明确同意</text>
						<text class="info-item">• 法律法规要求或政府部门要求</text>
						<text class="info-item">• 为保护用户或公众的安全、财产或权利</text>
						<text class="info-item">• 与可信的第三方服务提供商合作（如云存储服务）</text>
					</view>
				</view>
				
				<!-- 数据安全 -->
				<view class="section">
					<text class="section-title">数据安全保护</text>
					<view class="security-measures">
						<view class="measure-item">
							<text class="measure-title">加密传输</text>
							<text class="measure-desc">所有数据传输均采用HTTPS加密协议</text>
						</view>
						<view class="measure-item">
							<text class="measure-title">本地加密</text>
							<text class="measure-desc">敏感数据在设备本地采用AES-256加密存储</text>
						</view>
						<view class="measure-item">
							<text class="measure-title">访问控制</text>
							<text class="measure-desc">严格限制数据访问权限，定期审查访问日志</text>
						</view>
						<view class="measure-item">
							<text class="measure-title">安全监控</text>
							<text class="measure-desc">24小时安全监控，及时发现和处理安全威胁</text>
						</view>
					</view>
				</view>
				
				<!-- 数据保留 -->
				<view class="section">
					<text class="section-title">数据保留和删除</text>
					<text class="section-content">
						我们仅在必要期间保留您的个人信息：
					</text>
					<view class="info-list">
						<text class="info-item">• 账户信息：账户存续期间</text>
						<text class="info-item">• 健康数据：用户主动删除前</text>
						<text class="info-item">• 操作日志：最多保留12个月</text>
						<text class="info-item">• 账户注销后，所有数据将在7天内完全删除</text>
					</view>
				</view>
				
				<!-- 用户权利 -->
				<view class="section">
					<text class="section-title">您的权利</text>
					<view class="rights-list">
						<view class="right-item">
							<text class="right-title">访问权</text>
							<text class="right-desc">您有权了解我们收集的关于您的个人信息</text>
						</view>
						<view class="right-item">
							<text class="right-title">更正权</text>
							<text class="right-desc">您有权要求更正不准确的个人信息</text>
						</view>
						<view class="right-item">
							<text class="right-title">删除权</text>
							<text class="right-desc">您有权要求删除您的个人信息</text>
						</view>
						<view class="right-item">
							<text class="right-title">撤回同意</text>
							<text class="right-desc">您有权随时撤回对数据处理的同意</text>
						</view>
					</view>
				</view>
				
				<!-- 第三方服务 -->
				<view class="section">
					<text class="section-title">第三方服务</text>
					<text class="section-content">
						本应用可能使用以下第三方服务，这些服务有各自的隐私政策：
					</text>
					<view class="service-list">
						<view class="service-item">
							<text class="service-name">微信开放平台</text>
							<text class="service-purpose">用于微信登录和分享功能</text>
						</view>
						<view class="service-item">
							<text class="service-name">百度OCR</text>
							<text class="service-purpose">用于图片文字识别</text>
						</view>
						<view class="service-item">
							<text class="service-name">阿里云存储</text>
							<text class="service-purpose">用于数据备份和同步</text>
						</view>
					</view>
				</view>
				
				<!-- 儿童隐私 -->
				<view class="section">
					<text class="section-title">儿童隐私保护</text>
					<text class="section-content">
						我们不会故意收集14岁以下儿童的个人信息。如果您是儿童的监护人，发现我们收集了您孩子的信息，请联系我们，我们将及时删除相关信息。
					</text>
				</view>
				
				<!-- 政策更新 -->
				<view class="section">
					<text class="section-title">隐私政策更新</text>
					<text class="section-content">
						我们可能会不时更新本隐私政策。重大变更时，我们会通过应用内通知或其他方式告知您。继续使用本应用即表示您接受更新后的隐私政策。
					</text>
				</view>
				
				<!-- 联系我们 -->
				<view class="section">
					<text class="section-title">联系我们</text>
					<text class="section-content">
						如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：
					</text>
					<view class="contact-info">
						<text class="contact-item">邮箱：<EMAIL></text>
						<text class="contact-item">电话：400-123-4567</text>
						<text class="contact-item">地址：北京市朝阳区xxx大厦xxx室</text>
					</view>
				</view>
			</scroll-view>
			
			<!-- 底部操作 -->
			<view class="privacy-footer">
				<view class="agreement-section">
					<checkbox-group @change="onAgreementChange">
						<label class="agreement-label">
							<checkbox :checked="hasAgreed" color="#007AFF" />
							<text class="agreement-text">我已阅读并同意上述隐私政策</text>
						</label>
					</checkbox-group>
				</view>
				
				<view class="action-buttons">
					<button class="btn btn-secondary" @tap="goBack">返回</button>
					<button 
						class="btn btn-primary" 
						:class="{ 'btn-disabled': !hasAgreed }"
						:disabled="!hasAgreed"
						@tap="confirmAgreement"
					>
						确认同意
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'PrivacyPolicy',
		data() {
			return {
				hasAgreed: false,
				lastUpdated: '2024年1月1日',
				fromPage: '' // 来源页面
			}
		},
		onLoad(options) {
			console.log('隐私政策页面加载')
			this.fromPage = options.from || ''
		},
		methods: {
			// 同意状态变化
			onAgreementChange(e) {
				this.hasAgreed = e.detail.value.length > 0
			},
			
			// 确认同意
			confirmAgreement() {
				if (!this.hasAgreed) {
					uni.showToast({
						title: '请先阅读并同意隐私政策',
						icon: 'none'
					})
					return
				}
				
				// 记录用户同意
				uni.setStorageSync('privacy_policy_agreed', {
					agreed: true,
					timestamp: Date.now(),
					version: '1.0'
				})
				
				uni.showToast({
					title: '感谢您的同意',
					icon: 'success'
				})
				
				// 根据来源页面决定跳转
				setTimeout(() => {
					if (this.fromPage === 'register') {
						uni.navigateBack()
					} else {
						uni.switchTab({
							url: '/pages/index/index'
						})
					}
				}, 1500)
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style scoped>
	.privacy-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #FFFFFF;
	}
	
	/* 页面头部 */
	.privacy-header {
		padding: 20px 15px 15px;
		border-bottom: 1px solid #F0F0F0;
		background-color: #FFFFFF;
	}
	
	.header-title {
		font-size: 20px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 5px;
	}
	
	.header-subtitle {
		font-size: 12px;
		color: #8E8E93;
	}
	
	/* 内容区域 */
	.privacy-content {
		flex: 1;
		padding: 0 15px;
	}
	
	.section {
		margin-bottom: 25px;
		padding-top: 15px;
	}
	
	.section-title {
		font-size: 16px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 12px;
	}
	
	.section-content {
		font-size: 14px;
		color: #666666;
		line-height: 1.6;
		display: block;
		margin-bottom: 10px;
	}
	
	/* 子章节 */
	.subsection {
		margin-bottom: 15px;
	}
	
	.subsection-title {
		font-size: 14px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 8px;
	}
	
	/* 信息列表 */
	.info-list {
		margin-left: 10px;
	}
	
	.info-item {
		font-size: 14px;
		color: #666666;
		line-height: 1.6;
		display: block;
		margin-bottom: 6px;
	}
	
	/* 安全措施 */
	.security-measures {
		margin-top: 10px;
	}
	
	.measure-item {
		background-color: #F8F9FA;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 10px;
	}
	
	.measure-title {
		font-size: 14px;
		font-weight: 600;
		color: #007AFF;
		display: block;
		margin-bottom: 4px;
	}
	
	.measure-desc {
		font-size: 13px;
		color: #666666;
		line-height: 1.4;
	}
	
	/* 权利列表 */
	.rights-list {
		margin-top: 10px;
	}
	
	.right-item {
		border-left: 3px solid #007AFF;
		padding-left: 12px;
		margin-bottom: 12px;
	}
	
	.right-title {
		font-size: 14px;
		font-weight: 600;
		color: #333333;
		display: block;
		margin-bottom: 4px;
	}
	
	.right-desc {
		font-size: 13px;
		color: #666666;
		line-height: 1.4;
	}
	
	/* 第三方服务 */
	.service-list {
		margin-top: 10px;
	}
	
	.service-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px 0;
		border-bottom: 1px solid #F0F0F0;
	}
	
	.service-item:last-child {
		border-bottom: none;
	}
	
	.service-name {
		font-size: 14px;
		font-weight: 500;
		color: #333333;
	}
	
	.service-purpose {
		font-size: 12px;
		color: #8E8E93;
		text-align: right;
		flex: 1;
		margin-left: 15px;
	}
	
	/* 联系信息 */
	.contact-info {
		margin-top: 10px;
		background-color: #F8F9FA;
		border-radius: 8px;
		padding: 15px;
	}
	
	.contact-item {
		font-size: 14px;
		color: #333333;
		display: block;
		margin-bottom: 8px;
	}
	
	.contact-item:last-child {
		margin-bottom: 0;
	}
	
	/* 底部操作区 */
	.privacy-footer {
		background-color: #FFFFFF;
		border-top: 1px solid #F0F0F0;
		padding: 15px;
	}
	
	.agreement-section {
		margin-bottom: 15px;
	}
	
	.agreement-label {
		display: flex;
		align-items: center;
	}
	
	.agreement-text {
		font-size: 14px;
		color: #333333;
		margin-left: 8px;
	}
	
	.action-buttons {
		display: flex;
		gap: 15px;
	}
	
	.action-buttons .btn {
		flex: 1;
	}
</style>