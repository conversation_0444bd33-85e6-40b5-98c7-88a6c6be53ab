/**
 * 健康报告分享服务
 * 适配不同平台的分享机制
 */

class ShareService {
  constructor() {
    this.shareConfig = {
      // 分享类型配置
      types: {
        text: 'text',
        image: 'image',
        file: 'file',
        link: 'link'
      },
      // 平台特定配置
      platforms: {
        wechat: { name: '微信', icon: '/static/icons/wechat.png' },
        moments: { name: '朋友圈', icon: '/static/icons/moments.png' },
        qq: { name: 'QQ', icon: '/static/icons/qq.png' },
        weibo: { name: '微博', icon: '/static/icons/weibo.png' },
        system: { name: '系统分享', icon: '/static/icons/share.png' }
      }
    }
  }

  /**
   * 分享健康报告
   * @param {Object} reportData 报告数据
   * @param {Object} options 分享选项
   * @returns {Promise<boolean>} 分享结果
   */
  async shareHealthReport(reportData, options = {}) {
    try {
      const {
        type = 'text',
        platform = 'system',
        includeImage = false,
        includeFile = false
      } = options

      // 准备分享内容
      const shareContent = await this.prepareShareContent(reportData, {
        type,
        includeImage,
        includeFile
      })

      // 根据平台执行分享
      const result = await this.executeShare(shareContent, platform)
      
      // 记录分享历史
      this.recordShareHistory(reportData, options, result)

      return result
    } catch (error) {
      console.error('分享失败:', error)
      throw new Error('报告分享失败')
    }
  }

  /**
   * 准备分享内容
   * @param {Object} reportData 报告数据
   * @param {Object} options 选项
   * @returns {Promise<Object>} 分享内容
   */
  async prepareShareContent(reportData, options) {
    const { type, includeImage, includeFile } = options
    const content = {
      title: '个人健康检查报告',
      summary: this.generateShareSummary(reportData),
      text: this.generateShareText(reportData),
      imageUrl: null,
      fileUrl: null
    }

    // 生成分享图片
    if (includeImage) {
      content.imageUrl = await this.generateShareImage(reportData)
    }

    // 生成分享文件
    if (includeFile) {
      content.fileUrl = await this.generateShareFile(reportData)
    }

    return content
  }

  /**
   * 生成分享摘要
   * @param {Object} reportData 报告数据
   * @returns {string} 摘要文本
   */
  generateShareSummary(reportData) {
    const { reports, timeRange } = reportData
    const totalReports = reports.length
    const abnormalCount = reports.filter(report => 
      report.indicators.some(indicator => indicator.isAbnormal)
    ).length

    const startDate = new Date(timeRange.start).toLocaleDateString('zh-CN')
    const endDate = new Date(timeRange.end).toLocaleDateString('zh-CN')

    return `${startDate}至${endDate}期间共进行${totalReports}次健康检查，${abnormalCount > 0 ? `发现${abnormalCount}次异常` : '各项指标正常'}`
  }

  /**
   * 生成分享文本
   * @param {Object} reportData 报告数据
   * @returns {string} 分享文本
   */
  generateShareText(reportData) {
    const summary = this.generateShareSummary(reportData)
    const { reports } = reportData

    let text = `📋 ${summary}\n\n`

    // 添加最新报告信息
    if (reports.length > 0) {
      const latestReport = reports[0]
      const abnormalIndicators = latestReport.indicators.filter(i => i.isAbnormal)
      
      text += `🏥 最新检查：${new Date(latestReport.reportDate).toLocaleDateString('zh-CN')}\n`
      text += `📍 检查医院：${latestReport.hospitalName}\n`
      
      if (abnormalIndicators.length > 0) {
        text += `⚠️ 异常指标：${abnormalIndicators.map(i => i.name).join('、')}\n`
      } else {
        text += `✅ 各项指标正常\n`
      }
    }

    text += `\n💡 健康管理，从记录开始！`
    return text
  }

  /**
   * 生成分享图片
   * @param {Object} reportData 报告数据
   * @returns {Promise<string>} 图片URL
   */
  async generateShareImage(reportData) {
    try {
      // 创建画布生成分享图片
      const canvas = await this.createShareCanvas(reportData)
      
      // #ifdef APP-PLUS
      return await this.saveCanvasAsImageApp(canvas)
      // #endif

      // #ifdef H5
      return this.saveCanvasAsImageH5(canvas)
      // #endif

      // #ifdef MP-WEIXIN
      return await this.saveCanvasAsImageMP(canvas)
      // #endif

      return null
    } catch (error) {
      console.error('生成分享图片失败:', error)
      return null
    }
  }

  /**
   * 创建分享画布
   * @param {Object} reportData 报告数据
   * @returns {Promise<Object>} 画布对象
   */
  async createShareCanvas(reportData) {
    const { reports } = reportData
    const canvas = {
      width: 750,
      height: 1334,
      backgroundColor: '#ffffff'
    }

    // 这里应该使用实际的画布API来绘制内容
    // 由于uni-app的限制，这里提供一个简化的结构
    canvas.elements = [
      {
        type: 'text',
        content: '个人健康检查报告',
        x: 375,
        y: 100,
        fontSize: 32,
        color: '#2E7D32',
        textAlign: 'center'
      },
      {
        type: 'text',
        content: this.generateShareSummary(reportData),
        x: 50,
        y: 200,
        fontSize: 24,
        color: '#333333',
        maxWidth: 650
      }
    ]

    return canvas
  }

  /**
   * APP平台保存画布为图片
   * @param {Object} canvas 画布对象
   * @returns {Promise<string>} 图片路径
   */
  async saveCanvasAsImageApp(canvas) {
    return new Promise((resolve, reject) => {
      const fileName = `share_${Date.now()}.png`
      const filePath = `_doc/shares/${fileName}`

      // 实际实现中需要使用canvas API绘制并保存
      // 这里提供一个简化的实现
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
        fs.root.getDirectory('shares', { create: true }, (dirEntry) => {
          dirEntry.getFile(fileName, { create: true }, (fileEntry) => {
            resolve(fileEntry.fullPath)
          }, reject)
        }, reject)
      }, reject)
    })
  }

  /**
   * H5平台保存画布为图片
   * @param {Object} canvas 画布对象
   * @returns {string} 图片URL
   */
  saveCanvasAsImageH5(canvas) {
    // H5平台可以使用HTML5 Canvas API
    // 这里提供一个简化的实现
    return '/static/images/share_placeholder.png'
  }

  /**
   * 小程序平台保存画布为图片
   * @param {Object} canvas 画布对象
   * @returns {Promise<string>} 图片路径
   */
  async saveCanvasAsImageMP(canvas) {
    return new Promise((resolve, reject) => {
      // 小程序使用canvas组件绘制
      const ctx = uni.createCanvasContext('shareCanvas')
      
      // 绘制背景
      ctx.setFillStyle(canvas.backgroundColor)
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // 绘制元素
      canvas.elements.forEach(element => {
        if (element.type === 'text') {
          ctx.setFillStyle(element.color)
          ctx.setFontSize(element.fontSize)
          ctx.setTextAlign(element.textAlign || 'left')
          ctx.fillText(element.content, element.x, element.y)
        }
      })

      ctx.draw(false, () => {
        uni.canvasToTempFilePath({
          canvasId: 'shareCanvas',
          success: (res) => resolve(res.tempFilePath),
          fail: reject
        })
      })
    })
  }

  /**
   * 生成分享文件
   * @param {Object} reportData 报告数据
   * @returns {Promise<string>} 文件URL
   */
  async generateShareFile(reportData) {
    try {
      // 导入报告生成服务
      const reportGenerator = (await import('./reportGenerator.js')).default
      
      // 生成PDF文件
      const pdfPath = await reportGenerator.generatePDFReport(reportData, {
        includeCharts: false, // 分享版本不包含图表
        includeAnalysis: true,
        includeRecommendations: true
      })

      return pdfPath
    } catch (error) {
      console.error('生成分享文件失败:', error)
      return null
    }
  }

  /**
   * 执行分享
   * @param {Object} content 分享内容
   * @param {string} platform 分享平台
   * @returns {Promise<boolean>} 分享结果
   */
  async executeShare(content, platform) {
    try {
      switch (platform) {
        case 'system':
          return await this.shareToSystem(content)
        case 'wechat':
          return await this.shareToWeChat(content)
        case 'moments':
          return await this.shareToMoments(content)
        case 'qq':
          return await this.shareToQQ(content)
        default:
          return await this.shareToSystem(content)
      }
    } catch (error) {
      console.error(`分享到${platform}失败:`, error)
      return false
    }
  }

  /**
   * 系统分享
   * @param {Object} content 分享内容
   * @returns {Promise<boolean>} 分享结果
   */
  async shareToSystem(content) {
    return new Promise((resolve) => {
      const shareOptions = {
        title: content.title,
        summary: content.summary,
        href: content.fileUrl || '',
        success: () => resolve(true),
        fail: () => resolve(false)
      }

      // #ifdef APP-PLUS
      plus.share.sendWithSystem(shareOptions)
      // #endif

      // #ifdef H5
      if (navigator.share) {
        navigator.share({
          title: content.title,
          text: content.text,
          url: content.fileUrl || window.location.href
        }).then(() => resolve(true)).catch(() => resolve(false))
      } else {
        // 降级到复制文本
        this.copyToClipboard(content.text)
        resolve(true)
      }
      // #endif

      // #ifdef MP-WEIXIN
      // 小程序使用转发功能
      resolve(true)
      // #endif
    })
  }

  /**
   * 微信分享
   * @param {Object} content 分享内容
   * @returns {Promise<boolean>} 分享结果
   */
  async shareToWeChat(content) {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      const wechatService = plus.share.getServices().find(s => s.id === 'weixin')
      if (wechatService) {
        wechatService.send({
          type: 'text',
          content: content.text,
          success: () => resolve(true),
          fail: () => resolve(false)
        })
      } else {
        resolve(false)
      }
      // #endif

      // #ifdef MP-WEIXIN
      // 小程序内部分享
      resolve(true)
      // #endif

      // #ifdef H5
      // H5环境下提示用户手动分享
      this.showShareTip('请复制以下内容到微信分享：\n' + content.text)
      resolve(true)
      // #endif
    })
  }

  /**
   * 朋友圈分享
   * @param {Object} content 分享内容
   * @returns {Promise<boolean>} 分享结果
   */
  async shareToMoments(content) {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      const wechatService = plus.share.getServices().find(s => s.id === 'weixin')
      if (wechatService) {
        wechatService.send({
          type: 'image',
          pictures: [content.imageUrl],
          extra: { scene: 'WXSceneTimeline' },
          success: () => resolve(true),
          fail: () => resolve(false)
        })
      } else {
        resolve(false)
      }
      // #endif

      // 其他平台不支持朋友圈分享
      resolve(false)
    })
  }

  /**
   * QQ分享
   * @param {Object} content 分享内容
   * @returns {Promise<boolean>} 分享结果
   */
  async shareToQQ(content) {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      const qqService = plus.share.getServices().find(s => s.id === 'qq')
      if (qqService) {
        qqService.send({
          type: 'text',
          content: content.text,
          success: () => resolve(true),
          fail: () => resolve(false)
        })
      } else {
        resolve(false)
      }
      // #endif

      // 其他平台降级处理
      this.copyToClipboard(content.text)
      resolve(true)
    })
  }

  /**
   * 复制到剪贴板
   * @param {string} text 要复制的文本
   */
  copyToClipboard(text) {
    try {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '内容已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    } catch (error) {
      console.error('复制到剪贴板失败:', error)
    }
  }

  /**
   * 显示分享提示
   * @param {string} message 提示信息
   */
  showShareTip(message) {
    uni.showModal({
      title: '分享提示',
      content: message,
      showCancel: false,
      confirmText: '知道了'
    })
  }

  /**
   * 记录分享历史
   * @param {Object} reportData 报告数据
   * @param {Object} options 分享选项
   * @param {boolean} result 分享结果
   */
  recordShareHistory(reportData, options, result) {
    try {
      const history = uni.getStorageSync('share_history') || []
      
      history.push({
        id: `share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        reportCount: reportData.reports.length,
        timeRange: reportData.timeRange,
        platform: options.platform,
        type: options.type,
        success: result,
        createdAt: new Date().toISOString()
      })

      // 只保留最近50条记录
      if (history.length > 50) {
        history.splice(0, history.length - 50)
      }

      uni.setStorageSync('share_history', history)
    } catch (error) {
      console.error('记录分享历史失败:', error)
    }
  }

  /**
   * 获取分享历史
   * @returns {Array} 分享历史列表
   */
  getShareHistory() {
    try {
      return uni.getStorageSync('share_history') || []
    } catch (error) {
      console.error('获取分享历史失败:', error)
      return []
    }
  }

  /**
   * 获取可用的分享平台
   * @returns {Array} 平台列表
   */
  getAvailablePlatforms() {
    const platforms = []

    // #ifdef APP-PLUS
    const services = plus.share.getServices()
    services.forEach(service => {
      if (this.shareConfig.platforms[service.id]) {
        platforms.push({
          id: service.id,
          ...this.shareConfig.platforms[service.id],
          available: true
        })
      }
    })
    // #endif

    // #ifdef H5
    platforms.push({
      id: 'system',
      ...this.shareConfig.platforms.system,
      available: !!navigator.share
    })
    // #endif

    // #ifdef MP-WEIXIN
    platforms.push({
      id: 'wechat',
      ...this.shareConfig.platforms.wechat,
      available: true
    })
    // #endif

    // 添加系统分享作为默认选项
    if (!platforms.find(p => p.id === 'system')) {
      platforms.push({
        id: 'system',
        ...this.shareConfig.platforms.system,
        available: true
      })
    }

    return platforms
  }
}

export default new ShareService()