<template>
  <view class="report-editor">
    <!-- 编辑表单 -->
    <view class="editor-form">
      <!-- 基本信息编辑 -->
      <view class="form-section">
        <text class="section-title">基本信息</text>
        
        <view class="form-group">
          <text class="form-label">报告标题</text>
          <input 
            class="form-input"
            v-model="formData.title"
            placeholder="请输入报告标题"
            maxlength="100"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">医院名称 *</text>
          <input 
            class="form-input"
            v-model="formData.hospital"
            placeholder="请输入医院名称"
            maxlength="100"
            :class="{ error: errors.hospital }"
          />
          <text v-if="errors.hospital" class="error-text">{{ errors.hospital }}</text>
        </view>
        
        <view class="form-group">
          <text class="form-label">医生姓名</text>
          <input 
            class="form-input"
            v-model="formData.doctor"
            placeholder="请输入医生姓名"
            maxlength="50"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">科室</text>
          <input 
            class="form-input"
            v-model="formData.department"
            placeholder="请输入科室名称"
            maxlength="50"
          />
        </view>
        
        <view class="form-row">
          <view class="form-group half">
            <text class="form-label">检查日期 *</text>
            <picker 
              mode="date" 
              :value="formData.checkDate"
              @change="onCheckDateChange"
            >
              <view class="date-picker" :class="{ error: errors.checkDate }">
                <text class="date-text">
                  {{ formData.checkDate || '选择日期' }}
                </text>
                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
              </view>
            </picker>
            <text v-if="errors.checkDate" class="error-text">{{ errors.checkDate }}</text>
          </view>
          
          <view class="form-group half">
            <text class="form-label">报告日期 *</text>
            <picker 
              mode="date" 
              :value="formData.reportDate"
              @change="onReportDateChange"
            >
              <view class="date-picker" :class="{ error: errors.reportDate }">
                <text class="date-text">
                  {{ formData.reportDate || '选择日期' }}
                </text>
                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
              </view>
            </picker>
            <text v-if="errors.reportDate" class="error-text">{{ errors.reportDate }}</text>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">检查类别</text>
          <picker 
            :range="categoryOptions" 
            range-key="label"
            :value="selectedCategoryIndex"
            @change="onCategoryChange"
          >
            <view class="picker-input">
              <text class="picker-text">
                {{ selectedCategory?.label || '选择检查类别' }}
              </text>
              <uni-icons type="arrow-down" size="16" color="#999"></uni-icons>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 检查项目编辑 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">检查项目</text>
          <button class="add-item-btn" @tap="addNewItem">
            <uni-icons type="plus" size="16" color="#007AFF"></uni-icons>
            <text class="btn-text">添加项目</text>
          </button>
        </view>
        
        <view v-if="formData.items.length === 0" class="empty-items">
          <uni-icons type="info" size="32" color="#CCCCCC"></uni-icons>
          <text class="empty-text">暂无检查项目，点击上方按钮添加</text>
        </view>
        
        <view v-else class="items-list">
          <view 
            v-for="(item, index) in formData.items"
            :key="item.id || index"
            class="item-editor"
          >
            <view class="item-header">
              <text class="item-index">{{ index + 1 }}</text>
              <button class="remove-item-btn" @tap="removeItem(index)">
                <uni-icons type="trash" size="16" color="#FF5722"></uni-icons>
              </button>
            </view>
            
            <view class="item-form">
              <view class="form-group">
                <text class="form-label">项目名称 *</text>
                <input 
                  class="form-input"
                  v-model="item.name"
                  placeholder="请输入项目名称"
                  maxlength="100"
                  :class="{ error: errors[`items.${index}.name`] }"
                />
                <text v-if="errors[`items.${index}.name`]" class="error-text">
                  {{ errors[`items.${index}.name`] }}
                </text>
              </view>
              
              <view class="form-row">
                <view class="form-group half">
                  <text class="form-label">检查结果 *</text>
                  <input 
                    class="form-input"
                    v-model="item.value"
                    placeholder="请输入结果"
                    :class="{ error: errors[`items.${index}.value`] }"
                  />
                  <text v-if="errors[`items.${index}.value`]" class="error-text">
                    {{ errors[`items.${index}.value`] }}
                  </text>
                </view>
                
                <view class="form-group half">
                  <text class="form-label">单位</text>
                  <input 
                    class="form-input"
                    v-model="item.unit"
                    placeholder="如：mg/dL"
                    maxlength="20"
                  />
                </view>
              </view>
              
              <view class="form-group">
                <text class="form-label">参考范围</text>
                <input 
                  class="form-input"
                  v-model="item.referenceRange"
                  placeholder="如：3.5-5.5"
                  maxlength="50"
                  @input="updateItemAbnormalStatus(item, index)"
                />
              </view>
              
              <view class="form-group">
                <view class="checkbox-group">
                  <checkbox 
                    :checked="item.isAbnormal"
                    @change="onAbnormalChange(item, $event)"
                    color="#FF5722"
                  />
                  <text class="checkbox-label">标记为异常</text>
                </view>
              </view>
              
              <view class="form-group">
                <text class="form-label">备注</text>
                <textarea 
                  class="form-textarea"
                  v-model="item.notes"
                  placeholder="请输入备注信息"
                  maxlength="200"
                  :show-count="true"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 备注信息编辑 -->
      <view class="form-section">
        <text class="section-title">备注信息</text>
        
        <view class="form-group">
          <textarea 
            class="form-textarea large"
            v-model="formData.notes"
            placeholder="请输入备注信息，如医生建议、注意事项等"
            maxlength="500"
            :show-count="true"
          />
        </view>
      </view>
      
      <!-- 标签编辑 -->
      <view class="form-section">
        <text class="section-title">标签</text>
        
        <view class="tags-input">
          <view class="current-tags">
            <view 
              v-for="(tag, index) in formData.tags"
              :key="index"
              class="tag-item"
            >
              <text class="tag-text">{{ tag }}</text>
              <button class="tag-remove" @tap="removeTag(index)">
                <uni-icons type="close" size="12" color="#999"></uni-icons>
              </button>
            </view>
          </view>
          
          <view class="add-tag-input">
            <input 
              class="tag-input"
              v-model="newTag"
              placeholder="输入标签后按回车添加"
              maxlength="20"
              @confirm="addTag"
            />
            <button class="add-tag-btn" @tap="addTag">
              <uni-icons type="plus" size="16" color="#007AFF"></uni-icons>
            </button>
          </view>
        </view>
        
        <!-- 推荐标签 -->
        <view class="recommended-tags">
          <text class="recommend-title">推荐标签：</text>
          <view class="recommend-list">
            <view 
              v-for="tag in recommendedTags"
              :key="tag"
              class="recommend-tag"
              @tap="addRecommendedTag(tag)"
            >
              <text class="recommend-text">{{ tag }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="editor-actions">
      <button class="action-btn cancel" @tap="handleCancel">
        <text class="btn-text">取消</text>
      </button>
      <button class="action-btn save" @tap="handleSave" :disabled="saving">
        <uni-icons v-if="saving" type="spinner-cycle" size="16" color="#FFFFFF"></uni-icons>
        <text class="btn-text">{{ saving ? '保存中...' : '保存' }}</text>
      </button>
    </view>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog 
        type="warn"
        title="确认删除"
        :content="deleteConfirmText"
        :before-close="true"
        @close="handleDeleteCancel"
        @confirm="handleDeleteConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { Constants } from '@/types/index.js'
import { ReportItem } from '@/models/Report.js'

export default {
  name: 'ReportEditor',
  props: {
    // 报告数据
    report: {
      type: Object,
      default: null
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      // 表单数据
      formData: {
        title: '',
        hospital: '',
        doctor: '',
        department: '',
        checkDate: '',
        reportDate: '',
        category: '',
        items: [],
        notes: '',
        tags: []
      },
      
      // 表单验证错误
      errors: {},
      
      // 状态
      saving: false,
      
      // 新标签输入
      newTag: '',
      
      // 分类选项
      categoryOptions: [
        { value: Constants.REPORT_CATEGORIES.BLOOD_ROUTINE, label: '血常规' },
        { value: Constants.REPORT_CATEGORIES.BIOCHEMISTRY, label: '生化' },
        { value: Constants.REPORT_CATEGORIES.IMMUNOLOGY, label: '免疫' },
        { value: Constants.REPORT_CATEGORIES.URINE_ROUTINE, label: '尿常规' },
        { value: Constants.REPORT_CATEGORIES.IMAGING, label: '影像' },
        { value: Constants.REPORT_CATEGORIES.OTHER, label: '其他' }
      ],
      
      // 推荐标签
      recommendedTags: ['复查', '异常', '正常', '重要', '急诊', '体检'],
      
      // 删除确认文本
      deleteConfirmText: ''
    }
  },
  
  computed: {
    // 选中的分类索引
    selectedCategoryIndex() {
      return this.categoryOptions.findIndex(option => option.value === this.formData.category)
    },
    
    // 选中的分类
    selectedCategory() {
      return this.categoryOptions.find(option => option.value === this.formData.category)
    }
  },
  
  watch: {
    report: {
      handler(newReport) {
        if (newReport) {
          this.initFormData(newReport)
        }
      },
      immediate: true
    }
  },
  
  methods: {
    // 初始化表单数据
    initFormData(report) {
      this.formData = {
        title: report.title || '',
        hospital: report.hospital || '',
        doctor: report.doctor || '',
        department: report.department || '',
        checkDate: report.checkDate ? this.formatDate(report.checkDate) : '',
        reportDate: report.reportDate ? this.formatDate(report.reportDate) : '',
        category: report.category || '',
        items: report.items ? report.items.map(item => ({ ...item })) : [],
        notes: report.notes || '',
        tags: report.tags ? [...report.tags] : []
      }
      
      // 清除错误
      this.errors = {}
    },
    
    // 检查日期变化
    onCheckDateChange(e) {
      this.formData.checkDate = e.detail.value
      this.clearError('checkDate')
    },
    
    // 报告日期变化
    onReportDateChange(e) {
      this.formData.reportDate = e.detail.value
      this.clearError('reportDate')
    },
    
    // 分类变化
    onCategoryChange(e) {
      const index = e.detail.value
      this.formData.category = this.categoryOptions[index].value
    },
    
    // 添加新项目
    addNewItem() {
      const newItem = {
        id: `item_${Date.now()}`,
        name: '',
        value: '',
        unit: '',
        referenceRange: '',
        isAbnormal: false,
        category: this.formData.category || Constants.REPORT_CATEGORIES.OTHER,
        notes: ''
      }
      
      this.formData.items.push(newItem)
    },
    
    // 移除项目
    removeItem(index) {
      this.formData.items.splice(index, 1)
      
      // 清除相关错误
      Object.keys(this.errors).forEach(key => {
        if (key.startsWith(`items.${index}.`)) {
          delete this.errors[key]
        }
      })
    },
    
    // 异常状态变化
    onAbnormalChange(item, e) {
      item.isAbnormal = e.detail.value.length > 0
    },
    
    // 更新项目异常状态
    updateItemAbnormalStatus(item, index) {
      // 这里可以根据参考范围自动判断是否异常
      if (item.referenceRange && item.value) {
        const { min, max } = this.parseReferenceRange(item.referenceRange)
        const numericValue = parseFloat(item.value)
        
        if (!isNaN(numericValue) && (min !== null || max !== null)) {
          item.isAbnormal = (min !== null && numericValue < min) || 
                           (max !== null && numericValue > max)
        }
      }
    },
    
    // 解析参考范围
    parseReferenceRange(rangeStr) {
      const patterns = [
        /(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/, // 3.5-5.5
        /<\s*(\d+\.?\d*)/, // <5.5
        />\s*(\d+\.?\d*)/, // >3.5
        /≤\s*(\d+\.?\d*)/, // ≤5.5
        /≥\s*(\d+\.?\d*)/ // ≥3.5
      ]
      
      for (const pattern of patterns) {
        const match = rangeStr.match(pattern)
        if (match) {
          if (match.length === 3) {
            return {
              min: parseFloat(match[1]),
              max: parseFloat(match[2])
            }
          } else if (match.length === 2) {
            const value = parseFloat(match[1])
            if (rangeStr.includes('<') || rangeStr.includes('≤')) {
              return { min: null, max: value }
            } else {
              return { min: value, max: null }
            }
          }
        }
      }
      
      return { min: null, max: null }
    },
    
    // 添加标签
    addTag() {
      const tag = this.newTag.trim()
      if (tag && !this.formData.tags.includes(tag)) {
        this.formData.tags.push(tag)
        this.newTag = ''
      }
    },
    
    // 移除标签
    removeTag(index) {
      this.formData.tags.splice(index, 1)
    },
    
    // 添加推荐标签
    addRecommendedTag(tag) {
      if (!this.formData.tags.includes(tag)) {
        this.formData.tags.push(tag)
      }
    },
    
    // 表单验证
    validateForm() {
      this.errors = {}
      
      // 验证必填字段
      if (!this.formData.hospital.trim()) {
        this.errors.hospital = '医院名称不能为空'
      }
      
      if (!this.formData.checkDate) {
        this.errors.checkDate = '检查日期不能为空'
      }
      
      if (!this.formData.reportDate) {
        this.errors.reportDate = '报告日期不能为空'
      }
      
      // 验证日期逻辑
      if (this.formData.checkDate && this.formData.reportDate) {
        const checkDate = new Date(this.formData.checkDate)
        const reportDate = new Date(this.formData.reportDate)
        
        if (reportDate < checkDate) {
          this.errors.reportDate = '报告日期不能早于检查日期'
        }
        
        if (checkDate > new Date()) {
          this.errors.checkDate = '检查日期不能是未来日期'
        }
      }
      
      // 验证检查项目
      this.formData.items.forEach((item, index) => {
        if (!item.name.trim()) {
          this.errors[`items.${index}.name`] = '项目名称不能为空'
        }
        
        if (!item.value.trim()) {
          this.errors[`items.${index}.value`] = '检查结果不能为空'
        }
      })
      
      return Object.keys(this.errors).length === 0
    },
    
    // 清除错误
    clearError(field) {
      if (this.errors[field]) {
        delete this.errors[field]
      }
    },
    
    // 处理保存
    async handleSave() {
      if (!this.validateForm()) {
        uni.showToast({
          title: '请检查表单信息',
          icon: 'none'
        })
        return
      }
      
      try {
        this.saving = true
        
        // 构建报告数据
        const reportData = {
          ...this.formData,
          checkDate: new Date(this.formData.checkDate),
          reportDate: new Date(this.formData.reportDate),
          items: this.formData.items.map(item => new ReportItem(item)),
          updatedAt: new Date()
        }
        
        // 如果是编辑模式，保留原有ID和创建时间
        if (this.isEdit && this.report) {
          reportData.id = this.report.id
          reportData.createdAt = this.report.createdAt
        }
        
        this.$emit('save', reportData)
        
      } catch (error) {
        console.error('保存报告失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      } finally {
        this.saving = false
      }
    },
    
    // 处理取消
    handleCancel() {
      // 检查是否有未保存的更改
      if (this.hasUnsavedChanges()) {
        uni.showModal({
          title: '确认取消',
          content: '您有未保存的更改，确定要取消吗？',
          success: (res) => {
            if (res.confirm) {
              this.$emit('cancel')
            }
          }
        })
      } else {
        this.$emit('cancel')
      }
    },
    
    // 检查是否有未保存的更改
    hasUnsavedChanges() {
      if (!this.report) {
        // 新建模式，检查是否有任何输入
        return this.formData.hospital.trim() || 
               this.formData.doctor.trim() || 
               this.formData.items.length > 0 ||
               this.formData.notes.trim()
      }
      
      // 编辑模式，比较与原始数据的差异
      return JSON.stringify(this.formData) !== JSON.stringify({
        title: this.report.title || '',
        hospital: this.report.hospital || '',
        doctor: this.report.doctor || '',
        department: this.report.department || '',
        checkDate: this.report.checkDate ? this.formatDate(this.report.checkDate) : '',
        reportDate: this.report.reportDate ? this.formatDate(this.report.reportDate) : '',
        category: this.report.category || '',
        items: this.report.items || [],
        notes: this.report.notes || '',
        tags: this.report.tags || []
      })
    },
    
    // 显示删除确认
    showDeleteConfirm() {
      this.deleteConfirmText = `确定要删除报告"${this.report?.title || this.report?.hospital}"吗？删除后无法恢复。`
      this.$refs.deleteConfirmPopup.open()
    },
    
    // 处理删除取消
    handleDeleteCancel() {
      this.$refs.deleteConfirmPopup.close()
    },
    
    // 处理删除确认
    handleDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close()
      this.$emit('delete', this.report)
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.toISOString().split('T')[0]
    }
  }
}
</script>

<style lang="scss" scoped>
.report-editor {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.editor-form {
  padding: 20rpx;
}

// 表单区域
.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
  display: block;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.add-item-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #e3f2fd;
  border: 2rpx solid #1976d2;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.btn-text {
  color: #007AFF;
}

// 表单组件
.form-group {
  margin-bottom: 30rpx;
  
  &.half {
    flex: 1;
  }
}

.form-row {
  display: flex;
  gap: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
  
  &::after {
    content: ' *';
    color: #FF5722;
  }
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  box-sizing: border-box;
  
  &:focus {
    border-color: #007AFF;
  }
  
  &.error {
    border-color: #FF5722;
  }
  
  &::placeholder {
    color: #999999;
  }
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  box-sizing: border-box;
  resize: none;
  
  &.large {
    min-height: 200rpx;
  }
  
  &:focus {
    border-color: #007AFF;
  }
  
  &::placeholder {
    color: #999999;
  }
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #ffffff;
  
  &.error {
    border-color: #FF5722;
  }
}

.date-text {
  font-size: 28rpx;
  color: #333333;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #ffffff;
}

.picker-text {
  font-size: 28rpx;
  color: #333333;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.checkbox-label {
  font-size: 26rpx;
  color: #333333;
}

.error-text {
  font-size: 22rpx;
  color: #FF5722;
  margin-top: 8rpx;
  display: block;
}

// 检查项目编辑
.empty-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999999;
  margin-top: 20rpx;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.item-editor {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx;
  background: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-index {
  font-size: 24rpx;
  color: #007AFF;
  background: #e3f2fd;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 600;
}

.remove-item-btn {
  background: #ffebee;
  border: 2rpx solid #ffcdd2;
  border-radius: 20rpx;
  padding: 8rpx 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-form {
  .form-group {
    margin-bottom: 20rpx;
  }
}

// 标签编辑
.tags-input {
  margin-bottom: 20rpx;
}

.current-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #e3f2fd;
  border-radius: 20rpx;
  border: 2rpx solid #1976d2;
}

.tag-text {
  font-size: 24rpx;
  color: #1976d2;
}

.tag-remove {
  background: none;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-tag-input {
  display: flex;
  gap: 12rpx;
}

.tag-input {
  flex: 1;
  height: 60rpx;
  padding: 0 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 30rpx;
  font-size: 24rpx;
  background: #ffffff;
}

.add-tag-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #e3f2fd;
  border: 2rpx solid #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recommended-tags {
  margin-top: 20rpx;
}

.recommend-title {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
}

.recommend-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.recommend-tag {
  padding: 8rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  
  &:active {
    background: #e3f2fd;
    border-color: #1976d2;
  }
}

.recommend-text {
  font-size: 22rpx;
  color: #666666;
}

// 操作按钮
.editor-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  z-index: 999;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  border: none;
  
  &.cancel {
    background: #f8f9fa;
    color: #666666;
    border: 2rpx solid #e9ecef;
  }
  
  &.save {
    background: #007AFF;
    color: #ffffff;
    
    &:disabled {
      background: #CCCCCC;
      color: #999999;
    }
  }
}
</style>