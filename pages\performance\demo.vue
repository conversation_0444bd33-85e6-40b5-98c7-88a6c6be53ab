<template>
  <view class="performance-demo">
    <view class="header">
      <text class="title">性能优化演示</text>
      <text class="subtitle">展示各种性能优化技术的效果</text>
    </view>

    <scroll-view 
      class="content" 
      scroll-y="true"
      @scroll="handleScroll"
      :scroll-top="scrollTop"
    >
      <!-- 性能监控面板 -->
      <view class="section">
        <view class="section-title">性能监控</view>
        <view class="metrics-grid">
          <view class="metric-card">
            <text class="metric-label">页面加载时间</text>
            <text class="metric-value">{{ pageMetrics.loadTime || 0 }}ms</text>
          </view>
          <view class="metric-card">
            <text class="metric-label">渲染时间</text>
            <text class="metric-value">{{ pageMetrics.renderTime || 0 }}ms</text>
          </view>
          <view class="metric-card">
            <text class="metric-label">交互就绪时间</text>
            <text class="metric-value">{{ pageMetrics.interactiveTime || 0 }}ms</text>
          </view>
          <view class="metric-card">
            <text class="metric-label">内存使用</text>
            <text class="metric-value">{{ formatBytes(memoryStats.current) }}</text>
          </view>
        </view>
      </view>

      <!-- 缓存演示 -->
      <view class="section">
        <view class="section-title">多级缓存演示</view>
        <view class="demo-controls">
          <button class="demo-btn" @click="testCache">测试缓存</button>
          <button class="demo-btn" @click="clearCache">清除缓存</button>
        </view>
        <view class="cache-stats">
          <text>L1缓存命中: {{ cacheStats.l1Hits }}</text>
          <text>L2缓存命中: {{ cacheStats.l2Hits }}</text>
          <text>缓存未命中: {{ cacheStats.misses }}</text>
          <text>命中率: {{ (cacheStats.hitRate * 100).toFixed(1) }}%</text>
        </view>
      </view>

      <!-- 虚拟滚动演示 -->
      <view class="section">
        <view class="section-title">虚拟滚动演示</view>
        <view class="virtual-list-container">
          <view 
            class="virtual-list"
            :style="{ height: virtualHeight + 'px' }"
          >
            <view 
              class="virtual-spacer"
              :style="{ height: virtualOffset + 'px' }"
            ></view>
            <view 
              v-for="item in virtualItems" 
              :key="item.index"
              class="virtual-item"
              :style="{ height: item.height + 'px' }"
            >
              <text>虚拟列表项 {{ item.index }}</text>
              <text class="item-detail">这是第{{ item.index }}个项目的详细内容</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 懒加载图片演示 -->
      <view class="section">
        <view class="section-title">图片懒加载演示</view>
        <view class="image-grid">
          <view 
            v-for="(img, index) in lazyImages" 
            :key="index"
            class="image-item"
          >
            <image 
              :src="img.src"
              class="lazy-image"
              :class="{ 'loaded': img.loaded }"
              @load="onImageLoad(index)"
              @error="onImageError(index)"
            />
            <view class="image-status">
              <text v-if="img.loading">加载中...</text>
              <text v-else-if="img.error">加载失败</text>
              <text v-else-if="img.loaded">已加载</text>
              <text v-else>等待加载</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 性能基准测试 -->
      <view class="section">
        <view class="section-title">性能基准测试</view>
        <view class="demo-controls">
          <button class="demo-btn" @click="runBenchmark">运行基准测试</button>
          <button class="demo-btn" @click="runCoreTests">运行核心测试</button>
        </view>
        <view v-if="benchmarkResults" class="benchmark-results">
          <view class="result-item" v-for="result in benchmarkResults" :key="result.name">
            <text class="result-name">{{ result.name }}</text>
            <text class="result-time">{{ result.stats ? result.stats.mean.toFixed(2) : 'N/A' }}ms</text>
            <text class="result-status" :class="result.error ? 'error' : 'success'">
              {{ result.error ? '失败' : '成功' }}
            </text>
          </view>
        </view>
      </view>

      <!-- 内存管理演示 -->
      <view class="section">
        <view class="section-title">内存管理演示</view>
        <view class="demo-controls">
          <button class="demo-btn" @click="createObjects">创建对象</button>
          <button class="demo-btn" @click="cleanupMemory">清理内存</button>
        </view>
        <view class="memory-stats">
          <text>对象池统计:</text>
          <view v-for="(pool, name) in memoryStats.poolStats" :key="name" class="pool-stat">
            <text>{{ name }}: 可用{{ pool.available }}, 已创建{{ pool.created }}, 已复用{{ pool.reused }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 浮动性能指示器 -->
    <view class="performance-indicator" :class="performanceGrade.toLowerCase()">
      <text class="grade">{{ performanceGrade }}</text>
      <text class="score">{{ performanceScore }}</text>
    </view>
  </view>
</template>

<script>
import { performanceMixin } from '../../utils/performance/index.js'

export default {
  name: 'PerformanceDemo',
  mixins: [performanceMixin],
  
  data() {
    return {
      scrollTop: 0,
      pageMetrics: {},
      memoryStats: {},
      cacheStats: {},
      virtualItems: [],
      virtualHeight: 0,
      virtualOffset: 0,
      lazyImages: [],
      benchmarkResults: null,
      performanceGrade: 'A',
      performanceScore: 100,
      
      // 虚拟滚动配置
      virtualScrollOptions: {
        itemHeight: 80,
        bufferSize: 5
      },
      containerHeight: 400,
      
      // 演示数据
      demoData: Array.from({ length: 10000 }, (_, i) => ({
        id: i,
        name: `演示数据项 ${i}`,
        value: Math.random() * 1000
      }))
    }
  },
  
  onLoad() {
    console.log('性能演示页面加载')
    this.initDemo()
  },
  
  onReady() {
    this.updateMetrics()
    this.setupVirtualScroll()
    this.setupLazyImages()
  },
  
  onShow() {
    this.startPerformanceMonitoring()
  },
  
  methods: {
    // 初始化演示
    async initDemo() {
      try {
        // 预加载一些模块
        await this.preloadModules([
          'utils/performance/cache',
          'utils/performance/lazyLoad'
        ], { priority: 'high' })
        
        console.log('演示初始化完成')
      } catch (error) {
        console.error('演示初始化失败:', error)
      }
    },
    
    // 更新性能指标
    updateMetrics() {
      this.pageMetrics = this.getPagePerformanceMetrics().pageMetrics || {}
      this.memoryStats = this.getPagePerformanceMetrics().memoryStats || {}
      this.cacheStats = this.getPagePerformanceMetrics().cacheStats || {}
    },
    
    // 设置虚拟滚动
    setupVirtualScroll() {
      this.setVirtualData(this.demoData)
    },
    
    // 设置懒加载图片
    setupLazyImages() {
      const imageUrls = [
        'https://picsum.photos/200/200?random=1',
        'https://picsum.photos/200/200?random=2',
        'https://picsum.photos/200/200?random=3',
        'https://picsum.photos/200/200?random=4',
        'https://picsum.photos/200/200?random=5',
        'https://picsum.photos/200/200?random=6'
      ]
      
      this.lazyImages = imageUrls.map(url => this.createLazyImage(url, {
        placeholder: '/static/placeholder.png',
        errorImage: '/static/error.png'
      }))
    },
    
    // 处理滚动事件
    handleScroll(e) {
      this.handleVirtualScroll(e)
      
      // 节流更新指标
      if (!this.metricsUpdateTimer) {
        this.metricsUpdateTimer = setTimeout(() => {
          this.updateMetrics()
          this.metricsUpdateTimer = null
        }, 1000)
      }
    },
    
    // 测试缓存
    async testCache() {
      const testData = {
        timestamp: Date.now(),
        data: Array.from({ length: 100 }, (_, i) => `测试数据${i}`)
      }
      
      // 测试缓存写入
      await this.cacheData('demo_test', testData, {
        l1TTL: 60000, // 1分钟
        l2TTL: 300000 // 5分钟
      })
      
      // 测试缓存读取
      const cached = await this.getCachedData('demo_test')
      console.log('缓存测试结果:', cached)
      
      this.updateMetrics()
      
      uni.showToast({
        title: '缓存测试完成',
        icon: 'success'
      })
    },
    
    // 清除缓存
    async clearCache() {
      await this.cleanupCache()
      this.updateMetrics()
      
      uni.showToast({
        title: '缓存已清除',
        icon: 'success'
      })
    },
    
    // 图片加载完成
    onImageLoad(index) {
      this.lazyImages[index].loaded = true
      this.lazyImages[index].loading = false
      this.lazyImages[index].error = false
      this.$forceUpdate()
    },
    
    // 图片加载失败
    onImageError(index) {
      this.lazyImages[index].loaded = false
      this.lazyImages[index].loading = false
      this.lazyImages[index].error = true
      this.lazyImages[index].src = '/static/error.png'
      this.$forceUpdate()
    },
    
    // 运行基准测试
    async runBenchmark() {
      try {
        uni.showLoading({ title: '运行基准测试...' })
        
        const results = await this.runAllPerformanceTests()
        this.benchmarkResults = results.results || []
        
        // 计算性能评级
        const report = this.generatePerformanceReport()
        this.performanceGrade = report.performanceGrade.grade
        this.performanceScore = report.performanceGrade.score
        
        uni.hideLoading()
        uni.showToast({
          title: '基准测试完成',
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        console.error('基准测试失败:', error)
        uni.showToast({
          title: '基准测试失败',
          icon: 'error'
        })
      }
    },
    
    // 运行核心测试
    async runCoreTests() {
      try {
        uni.showLoading({ title: '运行核心测试...' })
        
        const results = await this.runCoreTests()
        this.benchmarkResults = results || []
        
        uni.hideLoading()
        uni.showToast({
          title: '核心测试完成',
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        console.error('核心测试失败:', error)
        uni.showToast({
          title: '核心测试失败',
          icon: 'error'
        })
      }
    },
    
    // 创建对象
    createObjects() {
      // 从对象池获取对象
      for (let i = 0; i < 10; i++) {
        const obj = this.getPooledObject('demoPool', `数据${i}`)
        console.log('创建对象:', obj)
      }
      
      this.updateMetrics()
      
      uni.showToast({
        title: '对象创建完成',
        icon: 'success'
      })
    },
    
    // 清理内存
    cleanupMemory() {
      this.performComponentCleanup()
      this.updateMetrics()
      
      uni.showToast({
        title: '内存清理完成',
        icon: 'success'
      })
    },
    
    // 开始性能监控
    startPerformanceMonitoring() {
      // 定期更新性能指标
      this.performanceTimer = setInterval(() => {
        this.updateMetrics()
      }, 5000)
    },
    
    // 格式化字节数
    formatBytes(bytes) {
      if (!bytes || bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  },
  
  beforeDestroy() {
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer)
    }
    if (this.metricsUpdateTimer) {
      clearTimeout(this.metricsUpdateTimer)
    }
  }
}
</script>

<style scoped>
.performance-demo {
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.title {
  font-size: 24px;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.8;
}

.content {
  height: calc(100vh - 120px);
  padding: 0 15px;
}

.section {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 2px solid #667eea;
  padding-bottom: 5px;
}

.metrics-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.metric-card {
  flex: 1;
  min-width: 120px;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.demo-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.demo-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
}

.demo-btn:active {
  background: #5a67d8;
}

.cache-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 14px;
  color: #666;
}

.virtual-list-container {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.virtual-list {
  position: relative;
}

.virtual-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 15px;
  border-bottom: 1px solid #eee;
  background: white;
}

.item-detail {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  width: 100px;
  text-align: center;
}

.lazy-image {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  background: #f0f0f0;
  transition: opacity 0.3s ease;
  opacity: 0.5;
}

.lazy-image.loaded {
  opacity: 1;
}

.image-status {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.benchmark-results {
  max-height: 200px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.result-name {
  flex: 1;
  font-size: 14px;
}

.result-time {
  font-size: 14px;
  font-weight: bold;
  margin: 0 10px;
}

.result-status.success {
  color: #28a745;
}

.result-status.error {
  color: #dc3545;
}

.memory-stats {
  font-size: 14px;
  color: #666;
}

.pool-stat {
  margin: 5px 0;
  padding-left: 10px;
}

.performance-indicator {
  position: fixed;
  top: 100px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.performance-indicator.a {
  background: #28a745;
}

.performance-indicator.b {
  background: #17a2b8;
}

.performance-indicator.c {
  background: #ffc107;
  color: #333;
}

.performance-indicator.d {
  background: #fd7e14;
}

.performance-indicator.f {
  background: #dc3545;
}

.grade {
  font-size: 18px;
}

.score {
  font-size: 10px;
}
</style>