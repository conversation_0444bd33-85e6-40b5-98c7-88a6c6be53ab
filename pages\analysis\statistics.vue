<template>
  <view class="statistics-analysis">
    <uni-nav-bar 
      title="统计分析" 
      left-icon="back" 
      @clickLeft="goBack"
      background-color="#007AFF"
      color="#ffffff"
    />
    
    <!-- 时间范围选择 -->
    <view class="time-range-section">
      <view class="section-title">统计时间范围</view>
      <view class="range-selector">
        <button 
          v-for="range in timeRanges" 
          :key="range.key"
          class="range-btn"
          :class="{ active: selectedTimeRange === range.key }"
          @click="selectTimeRange(range.key)"
        >
          {{ range.label }}
        </button>
      </view>
    </view>
    
    <!-- 整体统计概览 -->
    <view class="overview-section">
      <view class="section-title">整体概览</view>
      <view class="overview-cards">
        <view class="overview-card">
          <text class="card-number">{{ totalReports }}</text>
          <text class="card-label">总检查次数</text>
        </view>
        <view class="overview-card">
          <text class="card-number">{{ abnormalReports }}</text>
          <text class="card-label">异常次数</text>
        </view>
        <view class="overview-card">
          <text class="card-number">{{ abnormalRate }}%</text>
          <text class="card-label">异常率</text>
        </view>
        <view class="overview-card">
          <text class="card-number">{{ averageInterval }}</text>
          <text class="card-label">平均间隔(天)</text>
        </view>
      </view>
    </view>
    
    <!-- 各指标统计详情 -->
    <view class="indicators-section">
      <view class="section-title">各指标统计</view>
      <view 
        v-for="indicator in indicatorStatistics" 
        :key="indicator.key"
        class="indicator-stat-card"
      >
        <view class="indicator-header">
          <text class="indicator-name">{{ indicator.name }}</text>
          <text class="indicator-unit">({{ indicator.unit }})</text>
        </view>
        
        <view class="stat-grid">
          <view class="stat-item">
            <text class="stat-label">平均值</text>
            <text class="stat-value">{{ indicator.average }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">最高值</text>
            <text class="stat-value" :class="{ abnormal: indicator.maxAbnormal }">
              {{ indicator.max }}
            </text>
          </view>
          <view class="stat-item">
            <text class="stat-label">最低值</text>
            <text class="stat-value" :class="{ abnormal: indicator.minAbnormal }">
              {{ indicator.min }}
            </text>
          </view>
          <view class="stat-item">
            <text class="stat-label">标准差</text>
            <text class="stat-value">{{ indicator.stdDev }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">变化幅度</text>
            <text class="stat-value">{{ indicator.range }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">异常次数</text>
            <text class="stat-value abnormal">{{ indicator.abnormalCount }}</text>
          </view>
        </view>
        
        <!-- 趋势指示器 -->
        <view class="trend-section">
          <text class="trend-label">趋势：</text>
          <view class="trend-indicator" :class="indicator.trend">
            <uni-icons 
              :type="getTrendIcon(indicator.trend)" 
              size="16"
              :color="getTrendColor(indicator.trend)"
            ></uni-icons>
            <text class="trend-text">{{ getTrendText(indicator.trend) }}</text>
          </view>
        </view>
        
        <!-- 迷你图表 -->
        <view class="mini-chart">
          <HealthChart
            :canvas-id="`miniChart_${indicator.key}`"
            :chart-data="indicator.chartData"
            :chart-type="'line'"
            :width="300"
            :height="120"
            :show-grid="false"
            :show-legend="false"
            :normal-range="indicator.normalRange"
          />
        </view>
      </view>
    </view>
    
    <!-- 健康评分 -->
    <view class="health-score-section">
      <view class="section-title">健康评分</view>
      <view class="score-card">
        <view class="score-circle">
          <text class="score-number">{{ healthScore }}</text>
          <text class="score-total">/100</text>
        </view>
        <view class="score-details">
          <text class="score-level">{{ getScoreLevel() }}</text>
          <text class="score-description">{{ getScoreDescription() }}</text>
        </view>
      </view>
      
      <view class="score-breakdown">
        <view class="breakdown-title">评分详情</view>
        <view 
          v-for="item in scoreBreakdown" 
          :key="item.category"
          class="breakdown-item"
        >
          <text class="breakdown-label">{{ item.category }}</text>
          <view class="breakdown-bar">
            <view 
              class="breakdown-fill" 
              :style="{ width: item.percentage + '%', backgroundColor: item.color }"
            ></view>
          </view>
          <text class="breakdown-score">{{ item.score }}</text>
        </view>
      </view>
    </view>
    
    <!-- 建议和提醒 -->
    <view class="recommendations-section">
      <view class="section-title">健康建议</view>
      <view class="recommendations-list">
        <view 
          v-for="(recommendation, index) in recommendations" 
          :key="index"
          class="recommendation-item"
          :class="recommendation.priority"
        >
          <view class="recommendation-header">
            <uni-icons 
              :type="getRecommendationIcon(recommendation.priority)" 
              size="16"
              :color="getRecommendationColor(recommendation.priority)"
            ></uni-icons>
            <text class="recommendation-title">{{ recommendation.title }}</text>
          </view>
          <text class="recommendation-content">{{ recommendation.content }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import HealthChart from '@/components/common/HealthChart.vue'

export default {
  name: 'StatisticsAnalysis',
  components: {
    HealthChart
  },
  data() {
    return {
      selectedTimeRange: '6months',
      timeRanges: [
        { key: '1month', label: '近1个月', days: 30 },
        { key: '3months', label: '近3个月', days: 90 },
        { key: '6months', label: '近6个月', days: 180 },
        { key: '1year', label: '近1年', days: 365 }
      ],
      availableIndicators: [
        { key: 'blood_pressure_systolic', name: '收缩压', unit: 'mmHg', normalRange: { min: 90, max: 140 } },
        { key: 'blood_pressure_diastolic', name: '舒张压', unit: 'mmHg', normalRange: { min: 60, max: 90 } },
        { key: 'heart_rate', name: '心率', unit: 'bpm', normalRange: { min: 60, max: 100 } },
        { key: 'blood_glucose', name: '血糖', unit: 'mmol/L', normalRange: { min: 3.9, max: 6.1 } },
        { key: 'cholesterol', name: '胆固醇', unit: 'mmol/L', normalRange: { min: 0, max: 5.2 } },
        { key: 'triglycerides', name: '甘油三酯', unit: 'mmol/L', normalRange: { min: 0, max: 1.7 } }
      ],
      healthReports: [],
      totalReports: 0,
      abnormalReports: 0,
      abnormalRate: 0,
      averageInterval: 0,
      indicatorStatistics: [],
      healthScore: 85,
      scoreBreakdown: [],
      recommendations: []
    }
  },
  onLoad() {
    this.loadHealthReports()
  },
  methods: {
    async loadHealthReports() {
      try {
        this.healthReports = await this.getHealthReportsFromDB()
        this.calculateStatistics()
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'error'
        })
      }
    },
    
    async getHealthReportsFromDB() {
      // 模拟更多数据用于统计
      return [
        {
          id: 1,
          date: '2024-01-15',
          indicators: {
            blood_pressure_systolic: 120,
            blood_pressure_diastolic: 80,
            heart_rate: 72,
            blood_glucose: 5.2,
            cholesterol: 4.8,
            triglycerides: 1.2
          }
        },
        {
          id: 2,
          date: '2024-02-15',
          indicators: {
            blood_pressure_systolic: 135,
            blood_pressure_diastolic: 85,
            heart_rate: 78,
            blood_glucose: 5.8,
            cholesterol: 5.1,
            triglycerides: 1.5
          }
        },
        {
          id: 3,
          date: '2024-03-15',
          indicators: {
            blood_pressure_systolic: 145,
            blood_pressure_diastolic: 92,
            heart_rate: 82,
            blood_glucose: 6.5,
            cholesterol: 5.5,
            triglycerides: 1.8
          }
        },
        {
          id: 4,
          date: '2024-04-15',
          indicators: {
            blood_pressure_systolic: 125,
            blood_pressure_diastolic: 78,
            heart_rate: 70,
            blood_glucose: 5.0,
            cholesterol: 4.9,
            triglycerides: 1.3
          }
        },
        {
          id: 5,
          date: '2024-05-15',
          indicators: {
            blood_pressure_systolic: 130,
            blood_pressure_diastolic: 82,
            heart_rate: 75,
            blood_glucose: 5.4,
            cholesterol: 5.0,
            triglycerides: 1.4
          }
        },
        {
          id: 6,
          date: '2024-06-15',
          indicators: {
            blood_pressure_systolic: 128,
            blood_pressure_diastolic: 79,
            heart_rate: 73,
            blood_glucose: 5.1,
            cholesterol: 4.7,
            triglycerides: 1.2
          }
        }
      ]
    },
    
    selectTimeRange(rangeKey) {
      this.selectedTimeRange = rangeKey
      this.calculateStatistics()
    },
    
    calculateStatistics() {
      const timeRange = this.timeRanges.find(range => range.key === this.selectedTimeRange)
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - timeRange.days)
      
      // 过滤时间范围内的数据
      const filteredReports = this.healthReports.filter(report => {
        const reportDate = new Date(report.date)
        return reportDate >= cutoffDate
      }).sort((a, b) => new Date(a.date) - new Date(b.date))
      
      this.calculateOverviewStatistics(filteredReports)
      this.calculateIndicatorStatistics(filteredReports)
      this.calculateHealthScore(filteredReports)
      this.generateRecommendations(filteredReports)
    },
    
    calculateOverviewStatistics(reports) {
      this.totalReports = reports.length
      
      if (reports.length === 0) {
        this.abnormalReports = 0
        this.abnormalRate = 0
        this.averageInterval = 0
        return
      }
      
      // 计算异常报告数
      let abnormalCount = 0
      reports.forEach(report => {
        let hasAbnormal = false
        this.availableIndicators.forEach(indicator => {
          const value = report.indicators[indicator.key]
          if (value && this.isAbnormalValue(value, indicator.normalRange)) {
            hasAbnormal = true
          }
        })
        if (hasAbnormal) abnormalCount++
      })
      
      this.abnormalReports = abnormalCount
      this.abnormalRate = Math.round((abnormalCount / reports.length) * 100)
      
      // 计算平均间隔
      if (reports.length > 1) {
        const firstDate = new Date(reports[0].date)
        const lastDate = new Date(reports[reports.length - 1].date)
        const totalDays = Math.ceil((lastDate - firstDate) / (1000 * 60 * 60 * 24))
        this.averageInterval = Math.round(totalDays / (reports.length - 1))
      } else {
        this.averageInterval = 0
      }
    },
    
    calculateIndicatorStatistics(reports) {
      this.indicatorStatistics = this.availableIndicators.map(indicator => {
        const values = reports
          .map(report => report.indicators[indicator.key])
          .filter(value => value !== undefined && value !== null)
        
        if (values.length === 0) {
          return {
            key: indicator.key,
            name: indicator.name,
            unit: indicator.unit,
            normalRange: indicator.normalRange,
            average: 0,
            max: 0,
            min: 0,
            stdDev: 0,
            range: 0,
            abnormalCount: 0,
            maxAbnormal: false,
            minAbnormal: false,
            trend: 'stable',
            chartData: []
          }
        }
        
        const sum = values.reduce((acc, val) => acc + val, 0)
        const average = sum / values.length
        const max = Math.max(...values)
        const min = Math.min(...values)
        
        // 计算标准差
        const variance = values.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / values.length
        const stdDev = Math.sqrt(variance)
        
        // 计算异常值数量
        const abnormalCount = values.filter(value => 
          this.isAbnormalValue(value, indicator.normalRange)
        ).length
        
        // 计算趋势
        const trend = this.calculateTrend(values)
        
        // 准备图表数据
        const chartData = reports
          .filter(report => report.indicators[indicator.key] !== undefined)
          .map(report => ({
            date: this.formatDate(report.date),
            value: report.indicators[indicator.key]
          }))
        
        return {
          key: indicator.key,
          name: indicator.name,
          unit: indicator.unit,
          normalRange: indicator.normalRange,
          average: average.toFixed(1),
          max: max.toFixed(1),
          min: min.toFixed(1),
          stdDev: stdDev.toFixed(1),
          range: (max - min).toFixed(1),
          abnormalCount,
          maxAbnormal: this.isAbnormalValue(max, indicator.normalRange),
          minAbnormal: this.isAbnormalValue(min, indicator.normalRange),
          trend,
          chartData
        }
      })
    },
    
    calculateTrend(values) {
      if (values.length < 2) return 'stable'
      
      const firstHalf = values.slice(0, Math.floor(values.length / 2))
      const secondHalf = values.slice(Math.floor(values.length / 2))
      
      const firstAvg = firstHalf.reduce((acc, val) => acc + val, 0) / firstHalf.length
      const secondAvg = secondHalf.reduce((acc, val) => acc + val, 0) / secondHalf.length
      
      const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100
      
      if (changePercent > 10) return 'up'
      if (changePercent < -10) return 'down'
      return 'stable'
    },
    
    calculateHealthScore(reports) {
      if (reports.length === 0) {
        this.healthScore = 0
        this.scoreBreakdown = []
        return
      }
      
      let totalScore = 0
      const categories = [
        { name: '血压', indicators: ['blood_pressure_systolic', 'blood_pressure_diastolic'], weight: 0.3 },
        { name: '心率', indicators: ['heart_rate'], weight: 0.2 },
        { name: '血糖', indicators: ['blood_glucose'], weight: 0.25 },
        { name: '血脂', indicators: ['cholesterol', 'triglycerides'], weight: 0.25 }
      ]
      
      this.scoreBreakdown = categories.map(category => {
        let categoryScore = 100
        let abnormalCount = 0
        let totalCount = 0
        
        category.indicators.forEach(indicatorKey => {
          const indicator = this.availableIndicators.find(ind => ind.key === indicatorKey)
          if (!indicator) return
          
          reports.forEach(report => {
            const value = report.indicators[indicatorKey]
            if (value !== undefined && value !== null) {
              totalCount++
              if (this.isAbnormalValue(value, indicator.normalRange)) {
                abnormalCount++
              }
            }
          })
        })
        
        if (totalCount > 0) {
          const abnormalRate = abnormalCount / totalCount
          categoryScore = Math.max(0, 100 - (abnormalRate * 100))
        }
        
        totalScore += categoryScore * category.weight
        
        return {
          category: category.name,
          score: Math.round(categoryScore),
          percentage: categoryScore,
          color: this.getScoreColor(categoryScore)
        }
      })
      
      this.healthScore = Math.round(totalScore)
    },
    
    generateRecommendations(reports) {
      this.recommendations = []
      
      // 基于异常率生成建议
      if (this.abnormalRate > 50) {
        this.recommendations.push({
          priority: 'high',
          title: '多项指标异常',
          content: '您的多项健康指标存在异常，建议尽快咨询医生，制定针对性的治疗方案。'
        })
      } else if (this.abnormalRate > 20) {
        this.recommendations.push({
          priority: 'medium',
          title: '部分指标需关注',
          content: '部分健康指标超出正常范围，建议调整生活方式，定期复查。'
        })
      }
      
      // 基于检查频率生成建议
      if (this.averageInterval > 90) {
        this.recommendations.push({
          priority: 'low',
          title: '建议增加检查频率',
          content: '您的检查间隔较长，建议定期进行健康检查，及时了解身体状况。'
        })
      }
      
      // 基于趋势生成建议
      const worseningIndicators = this.indicatorStatistics.filter(ind => ind.trend === 'up' && ind.abnormalCount > 0)
      if (worseningIndicators.length > 0) {
        this.recommendations.push({
          priority: 'medium',
          title: '指标趋势恶化',
          content: `${worseningIndicators.map(ind => ind.name).join('、')}呈恶化趋势，建议重点关注。`
        })
      }
      
      // 如果没有特殊建议，给出一般性建议
      if (this.recommendations.length === 0) {
        this.recommendations.push({
          priority: 'low',
          title: '保持良好习惯',
          content: '您的健康状况良好，建议继续保持健康的生活方式，定期体检。'
        })
      }
    },
    
    isAbnormalValue(value, normalRange) {
      const { min, max } = normalRange
      if (min === null || max === null) return false
      return value < min || value > max
    },
    
    getScoreLevel() {
      if (this.healthScore >= 90) return '优秀'
      if (this.healthScore >= 80) return '良好'
      if (this.healthScore >= 70) return '一般'
      if (this.healthScore >= 60) return '需改善'
      return '需重视'
    },
    
    getScoreDescription() {
      if (this.healthScore >= 90) return '您的健康状况非常好，请继续保持'
      if (this.healthScore >= 80) return '您的健康状况良好，注意保持'
      if (this.healthScore >= 70) return '您的健康状况一般，建议改善生活方式'
      if (this.healthScore >= 60) return '您的健康状况需要改善，建议咨询医生'
      return '您的健康状况需要重视，请及时就医'
    },
    
    getScoreColor(score) {
      if (score >= 90) return '#34C759'
      if (score >= 80) return '#007AFF'
      if (score >= 70) return '#FF9500'
      if (score >= 60) return '#FF3B30'
      return '#8E8E93'
    },
    
    getTrendIcon(trend) {
      switch (trend) {
        case 'up': return 'arrowup'
        case 'down': return 'arrowdown'
        default: return 'minus'
      }
    },
    
    getTrendColor(trend) {
      switch (trend) {
        case 'up': return '#FF3B30'
        case 'down': return '#34C759'
        default: return '#007AFF'
      }
    },
    
    getTrendText(trend) {
      switch (trend) {
        case 'up': return '上升'
        case 'down': return '下降'
        default: return '稳定'
      }
    },
    
    getRecommendationIcon(priority) {
      switch (priority) {
        case 'high': return 'info-filled'
        case 'medium': return 'info'
        default: return 'checkmarkempty'
      }
    },
    
    getRecommendationColor(priority) {
      switch (priority) {
        case 'high': return '#FF3B30'
        case 'medium': return '#FF9500'
        default: return '#007AFF'
      }
    },
    
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}/${date.getDate()}`
    },
    
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.statistics-analysis {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.time-range-section,
.overview-section,
.indicators-section,
.health-score-section,
.recommendations-section {
  background-color: #ffffff;
  margin-bottom: 10px;
  padding: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15px;
}

.range-selector {
  display: flex;
  justify-content: space-between;
}

.range-btn {
  flex: 1;
  margin: 0 5px;
  padding: 8px 12px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  color: #666666;
}

.range-btn.active {
  background-color: #007AFF;
  color: #ffffff;
}

.overview-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.overview-card {
  width: 48%;
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  text-align: center;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  color: #007AFF;
  display: block;
  margin-bottom: 5px;
}

.card-label {
  font-size: 14px;
  color: #666666;
}

.indicator-stat-card {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.indicator-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 15px;
}

.indicator-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.indicator-unit {
  font-size: 12px;
  color: #666666;
  margin-left: 5px;
}

.stat-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  width: 32%;
  text-align: center;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 12px;
  color: #666666;
  display: block;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.stat-value.abnormal {
  color: #FF3B30;
}

.trend-section {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.trend-label {
  font-size: 14px;
  color: #666666;
  margin-right: 10px;
}

.trend-indicator {
  display: flex;
  align-items: center;
}

.trend-text {
  font-size: 14px;
  margin-left: 5px;
}

.mini-chart {
  margin-top: 10px;
}

.score-card {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: #007AFF;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.score-number {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
}

.score-total {
  font-size: 12px;
  color: #ffffff;
}

.score-details {
  flex: 1;
}

.score-level {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 5px;
}

.score-description {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.score-breakdown {
  margin-top: 20px;
}

.breakdown-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15px;
}

.breakdown-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.breakdown-label {
  width: 60px;
  font-size: 14px;
  color: #333333;
}

.breakdown-bar {
  flex: 1;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin: 0 10px;
  overflow: hidden;
}

.breakdown-fill {
  height: 100%;
  border-radius: 4px;
}

.breakdown-score {
  width: 30px;
  text-align: right;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}

.recommendations-list {
  margin-top: 15px;
}

.recommendation-item {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  border-left: 4px solid #007AFF;
}

.recommendation-item.high {
  border-left-color: #FF3B30;
}

.recommendation-item.medium {
  border-left-color: #FF9500;
}

.recommendation-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.recommendation-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-left: 8px;
}

.recommendation-content {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}
</style>