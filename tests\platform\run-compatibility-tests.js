/**
 * 跨平台兼容性测试运行器
 * 执行所有跨平台兼容性测试并生成报告
 */

import { execSync } from 'child_process'
import path from 'path'
import CompatibilityReportGenerator from './compatibility-report-generator.js'

class CompatibilityTestRunner {
  constructor() {
    this.reportGenerator = new CompatibilityReportGenerator()
    this.testFiles = [
      'cross-platform-compatibility.test.js',
      'data-sync-consistency.test.js',
      'platform-degradation.test.js',
      'performance-optimization.test.js',
      'user-experience-consistency.test.js'
    ]
  }

  /**
   * 运行所有兼容性测试
   */
  async runAllTests() {
    console.log('🚀 开始执行跨平台兼容性测试套件...\n')
    
    this.reportGenerator.startTesting()
    
    const results = {
      totalFiles: this.testFiles.length,
      passedFiles: 0,
      failedFiles: 0,
      testResults: []
    }

    for (const testFile of this.testFiles) {
      console.log(`📋 执行测试文件: ${testFile}`)
      
      try {
        const result = await this.runSingleTest(testFile)
        results.testResults.push(result)
        
        if (result.success) {
          results.passedFiles++
          console.log(`✅ ${testFile} 测试通过`)
        } else {
          results.failedFiles++
          console.log(`❌ ${testFile} 测试失败`)
        }
        
        // 将测试结果记录到报告生成器
        this.recordTestResults(testFile, result)
        
      } catch (error) {
        console.error(`💥 ${testFile} 执行出错:`, error.message)
        results.failedFiles++
        results.testResults.push({
          file: testFile,
          success: false,
          error: error.message
        })
      }
      
      console.log('') // 空行分隔
    }

    this.reportGenerator.endTesting()
    
    // 生成测试报告
    console.log('📊 生成测试报告...')
    const reportPaths = await this.reportGenerator.saveReport('html')
    await this.reportGenerator.saveReport('json')
    
    // 打印测试总结
    this.printTestSummary(results)
    
    return {
      ...results,
      reportPath: reportPaths
    }
  }

  /**
   * 运行单个测试文件
   * @param {string} testFile 测试文件名
   * @returns {Promise<Object>} 测试结果
   */
  async runSingleTest(testFile) {
    const testPath = path.join(process.cwd(), 'tests', 'platform', testFile)
    
    try {
      // 使用Jest运行测试
      const command = `npx jest "${testPath}" --json --silent`
      const output = execSync(command, { 
        encoding: 'utf8',
        timeout: 60000 // 60秒超时
      })
      
      const jestResult = JSON.parse(output)
      
      return {
        file: testFile,
        success: jestResult.success,
        numTotalTests: jestResult.numTotalTests,
        numPassedTests: jestResult.numPassedTests,
        numFailedTests: jestResult.numFailedTests,
        testResults: jestResult.testResults,
        startTime: jestResult.startTime,
        endTime: jestResult.endTime,
        duration: jestResult.endTime - jestResult.startTime
      }
      
    } catch (error) {
      // 如果Jest执行失败，尝试解析错误输出
      let jestResult = null
      try {
        jestResult = JSON.parse(error.stdout || error.message)
      } catch (parseError) {
        // 解析失败，返回基本错误信息
      }
      
      return {
        file: testFile,
        success: false,
        error: error.message,
        jestResult
      }
    }
  }

  /**
   * 将测试结果记录到报告生成器
   * @param {string} testFile 测试文件名
   * @param {Object} result 测试结果
   */
  recordTestResults(testFile, result) {
    if (!result.testResults) return

    // 根据测试文件类型确定测试的功能类别
    const fileFeatureMap = {
      'cross-platform-compatibility.test.js': ['camera', 'storage', 'sharing', 'fileSystem'],
      'data-sync-consistency.test.js': ['network', 'storage'],
      'platform-degradation.test.js': ['camera', 'sharing', 'permissions', 'ui'],
      'performance-optimization.test.js': ['performance'],
      'user-experience-consistency.test.js': ['ui']
    }

    const features = fileFeatureMap[testFile] || ['general']
    const platforms = ['app-plus', 'mp-weixin', 'h5'] // 模拟平台

    // 为每个平台和功能记录结果
    platforms.forEach(platform => {
      features.forEach(feature => {
        const testResult = {
          passed: result.success && result.numFailedTests === 0,
          skipped: false,
          performance: {
            duration: result.duration || 0
          }
        }

        if (!testResult.passed && result.error) {
          testResult.error = result.error
          testResult.severity = 'medium'
        }

        this.reportGenerator.recordTestResult(platform, feature, testResult)
      })
    })
  }

  /**
   * 打印测试总结
   * @param {Object} results 测试结果
   */
  printTestSummary(results) {
    console.log('📈 测试总结:')
    console.log('=' * 50)
    console.log(`总测试文件: ${results.totalFiles}`)
    console.log(`通过文件: ${results.passedFiles}`)
    console.log(`失败文件: ${results.failedFiles}`)
    console.log(`成功率: ${(results.passedFiles / results.totalFiles * 100).toFixed(2)}%`)
    
    const totalTests = results.testResults.reduce((sum, r) => sum + (r.numTotalTests || 0), 0)
    const passedTests = results.testResults.reduce((sum, r) => sum + (r.numPassedTests || 0), 0)
    const failedTests = results.testResults.reduce((sum, r) => sum + (r.numFailedTests || 0), 0)
    
    console.log(`\n总测试用例: ${totalTests}`)
    console.log(`通过用例: ${passedTests}`)
    console.log(`失败用例: ${failedTests}`)
    console.log(`用例通过率: ${totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) : 0}%`)
    
    // 显示失败的测试文件
    const failedFiles = results.testResults.filter(r => !r.success)
    if (failedFiles.length > 0) {
      console.log('\n❌ 失败的测试文件:')
      failedFiles.forEach(file => {
        console.log(`  - ${file.file}: ${file.error || '测试失败'}`)
      })
    }
    
    console.log('\n📊 详细报告已生成，请查看HTML报告文件')
  }

  /**
   * 运行特定平台的测试
   * @param {string} platform 平台类型
   */
  async runPlatformSpecificTests(platform) {
    console.log(`🎯 运行 ${platform} 平台特定测试...`)
    
    // 设置环境变量指定平台
    process.env.UNI_PLATFORM = platform
    
    const results = await this.runAllTests()
    
    // 清除环境变量
    delete process.env.UNI_PLATFORM
    
    return results
  }

  /**
   * 运行性能基准测试
   */
  async runPerformanceBenchmarks() {
    console.log('⚡ 运行性能基准测试...')
    
    const benchmarks = [
      {
        name: '图片处理性能',
        test: () => this.benchmarkImageProcessing(),
        threshold: 2000 // 2秒阈值
      },
      {
        name: '存储操作性能',
        test: () => this.benchmarkStorageOperations(),
        threshold: 1000 // 1秒阈值
      },
      {
        name: '网络请求性能',
        test: () => this.benchmarkNetworkRequests(),
        threshold: 5000 // 5秒阈值
      }
    ]

    const results = []
    
    for (const benchmark of benchmarks) {
      console.log(`  测试: ${benchmark.name}`)
      
      try {
        const startTime = Date.now()
        await benchmark.test()
        const duration = Date.now() - startTime
        
        const passed = duration <= benchmark.threshold
        results.push({
          name: benchmark.name,
          duration,
          threshold: benchmark.threshold,
          passed,
          status: passed ? '✅ 通过' : '⚠️  超时'
        })
        
        console.log(`    耗时: ${duration}ms ${passed ? '✅' : '⚠️'}`)
        
      } catch (error) {
        results.push({
          name: benchmark.name,
          error: error.message,
          passed: false,
          status: '❌ 失败'
        })
        
        console.log(`    错误: ${error.message} ❌`)
      }
    }
    
    return results
  }

  /**
   * 图片处理性能基准测试
   */
  async benchmarkImageProcessing() {
    // 模拟图片处理操作
    const operations = [
      () => new Promise(resolve => setTimeout(resolve, 100)), // 选择图片
      () => new Promise(resolve => setTimeout(resolve, 200)), // 压缩图片
      () => new Promise(resolve => setTimeout(resolve, 150)), // 格式转换
      () => new Promise(resolve => setTimeout(resolve, 100))  // 保存图片
    ]
    
    for (const operation of operations) {
      await operation()
    }
  }

  /**
   * 存储操作性能基准测试
   */
  async benchmarkStorageOperations() {
    // 模拟存储操作
    const data = 'x'.repeat(1024 * 100) // 100KB数据
    
    const operations = [
      () => new Promise(resolve => setTimeout(resolve, 50)),  // 写入
      () => new Promise(resolve => setTimeout(resolve, 30)),  // 读取
      () => new Promise(resolve => setTimeout(resolve, 40)),  // 更新
      () => new Promise(resolve => setTimeout(resolve, 20))   // 删除
    ]
    
    for (const operation of operations) {
      await operation()
    }
  }

  /**
   * 网络请求性能基准测试
   */
  async benchmarkNetworkRequests() {
    // 模拟网络请求
    const requests = [
      () => new Promise(resolve => setTimeout(resolve, 200)), // 用户数据
      () => new Promise(resolve => setTimeout(resolve, 300)), // 报告列表
      () => new Promise(resolve => setTimeout(resolve, 400)), // 图片上传
      () => new Promise(resolve => setTimeout(resolve, 250))  // 数据同步
    ]
    
    // 并发执行请求
    await Promise.all(requests.map(req => req()))
  }
}

// 主执行函数
async function main() {
  const runner = new CompatibilityTestRunner()
  
  try {
    // 检查命令行参数
    const args = process.argv.slice(2)
    
    if (args.includes('--platform')) {
      const platformIndex = args.indexOf('--platform')
      const platform = args[platformIndex + 1]
      
      if (platform) {
        await runner.runPlatformSpecificTests(platform)
      } else {
        console.error('请指定平台类型: --platform app-plus|mp-weixin|h5')
        process.exit(1)
      }
    } else if (args.includes('--benchmark')) {
      const benchmarkResults = await runner.runPerformanceBenchmarks()
      console.log('\n⚡ 性能基准测试结果:')
      benchmarkResults.forEach(result => {
        console.log(`  ${result.name}: ${result.status}`)
      })
    } else {
      // 运行所有测试
      await runner.runAllTests()
    }
    
    console.log('\n🎉 跨平台兼容性测试完成!')
    
  } catch (error) {
    console.error('💥 测试执行失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此文件，执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export default CompatibilityTestRunner