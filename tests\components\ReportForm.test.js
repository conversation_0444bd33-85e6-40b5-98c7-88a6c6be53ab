// 报告录入表单组件测试
const mockUni = {
  showModal: jest.fn(),
  showToast: jest.fn()
};

global.uni = mockUni;

describe('ReportForm Component Logic', () => {
  let formComponent;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // 模拟组件实例
    formComponent = {
      formData: {
        hospital: '',
        doctor: '',
        checkDate: '',
        title: '',
        notes: '',
        items: []
      },
      errors: {},
      itemErrors: {},
      categoryOptions: [
        '血常规',
        '生化检查',
        '免疫检查',
        '尿常规',
        '肝功能',
        '肾功能',
        '血脂',
        '血糖',
        '其他'
      ]
    };
  });

  describe('表单验证', () => {
    it('应该验证必填字段', () => {
      const validateField = (fieldName) => {
        const value = formComponent.formData[fieldName];
        
        switch (fieldName) {
          case 'hospital':
            if (!value || value.trim() === '') {
              formComponent.errors[fieldName] = '请输入医院名称';
            } else if (value.length < 2) {
              formComponent.errors[fieldName] = '医院名称至少2个字符';
            } else {
              delete formComponent.errors[fieldName];
            }
            break;
            
          case 'doctor':
            if (!value || value.trim() === '') {
              formComponent.errors[fieldName] = '请输入医生姓名';
            } else if (value.length < 2) {
              formComponent.errors[fieldName] = '医生姓名至少2个字符';
            } else {
              delete formComponent.errors[fieldName];
            }
            break;
            
          case 'checkDate':
            if (!value) {
              formComponent.errors[fieldName] = '请选择检查日期';
            } else {
              const checkDate = new Date(value);
              const today = new Date();
              if (checkDate > today) {
                formComponent.errors[fieldName] = '检查日期不能晚于今天';
              } else {
                delete formComponent.errors[fieldName];
              }
            }
            break;
        }
      };

      // 测试空值验证
      validateField('hospital');
      expect(formComponent.errors.hospital).toBe('请输入医院名称');

      validateField('doctor');
      expect(formComponent.errors.doctor).toBe('请输入医生姓名');

      validateField('checkDate');
      expect(formComponent.errors.checkDate).toBe('请选择检查日期');

      // 测试有效值
      formComponent.formData.hospital = '北京医院';
      formComponent.formData.doctor = '张医生';
      formComponent.formData.checkDate = '2024-01-15';

      validateField('hospital');
      validateField('doctor');
      validateField('checkDate');

      expect(formComponent.errors.hospital).toBeUndefined();
      expect(formComponent.errors.doctor).toBeUndefined();
      expect(formComponent.errors.checkDate).toBeUndefined();
    });

    it('应该验证医院名称长度', () => {
      const validateField = (fieldName) => {
        const value = formComponent.formData[fieldName];
        if (fieldName === 'hospital') {
          if (!value || value.trim() === '') {
            formComponent.errors[fieldName] = '请输入医院名称';
          } else if (value.length < 2) {
            formComponent.errors[fieldName] = '医院名称至少2个字符';
          } else {
            delete formComponent.errors[fieldName];
          }
        }
      };

      formComponent.formData.hospital = '医';
      validateField('hospital');
      expect(formComponent.errors.hospital).toBe('医院名称至少2个字符');

      formComponent.formData.hospital = '北京医院';
      validateField('hospital');
      expect(formComponent.errors.hospital).toBeUndefined();
    });

    it('应该验证检查日期不能晚于今天', () => {
      const validateField = (fieldName) => {
        if (fieldName === 'checkDate') {
          const value = formComponent.formData[fieldName];
          if (!value) {
            formComponent.errors[fieldName] = '请选择检查日期';
          } else {
            const checkDate = new Date(value);
            const today = new Date();
            if (checkDate > today) {
              formComponent.errors[fieldName] = '检查日期不能晚于今天';
            } else {
              delete formComponent.errors[fieldName];
            }
          }
        }
      };

      // 设置未来日期
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      formComponent.formData.checkDate = tomorrow.toISOString().split('T')[0];

      validateField('checkDate');
      expect(formComponent.errors.checkDate).toBe('检查日期不能晚于今天');

      // 设置今天日期
      const today = new Date();
      formComponent.formData.checkDate = today.toISOString().split('T')[0];

      validateField('checkDate');
      expect(formComponent.errors.checkDate).toBeUndefined();
    });
  });

  describe('检查项目验证', () => {
    it('应该验证项目必填字段', () => {
      const validateItem = (index, fieldName) => {
        const item = formComponent.formData.items[index];
        const key = `${index}_${fieldName}`;
        
        switch (fieldName) {
          case 'name':
            if (!item.name || item.name.trim() === '') {
              formComponent.itemErrors[key] = '请输入项目名称';
            } else {
              delete formComponent.itemErrors[key];
            }
            break;
            
          case 'value':
            if (!item.value || item.value.trim() === '') {
              formComponent.itemErrors[key] = '请输入检查结果';
            } else if (isNaN(parseFloat(item.value))) {
              formComponent.itemErrors[key] = '检查结果必须是数字';
            } else {
              delete formComponent.itemErrors[key];
            }
            break;
        }
      };

      // 添加一个空项目
      formComponent.formData.items.push({
        name: '',
        value: '',
        unit: '',
        referenceRange: '',
        category: '',
        isAbnormal: false
      });

      validateItem(0, 'name');
      expect(formComponent.itemErrors['0_name']).toBe('请输入项目名称');

      validateItem(0, 'value');
      expect(formComponent.itemErrors['0_value']).toBe('请输入检查结果');

      // 设置有效值
      formComponent.formData.items[0].name = '白细胞计数';
      formComponent.formData.items[0].value = '5.2';

      validateItem(0, 'name');
      validateItem(0, 'value');

      expect(formComponent.itemErrors['0_name']).toBeUndefined();
      expect(formComponent.itemErrors['0_value']).toBeUndefined();
    });

    it('应该验证检查结果必须是数字', () => {
      const validateItem = (index, fieldName) => {
        if (fieldName === 'value') {
          const item = formComponent.formData.items[index];
          const key = `${index}_${fieldName}`;
          
          if (!item.value || item.value.trim() === '') {
            formComponent.itemErrors[key] = '请输入检查结果';
          } else if (isNaN(parseFloat(item.value))) {
            formComponent.itemErrors[key] = '检查结果必须是数字';
          } else {
            delete formComponent.itemErrors[key];
          }
        }
      };

      formComponent.formData.items.push({
        name: '白细胞计数',
        value: 'abc',
        unit: '',
        referenceRange: '',
        category: '',
        isAbnormal: false
      });

      validateItem(0, 'value');
      expect(formComponent.itemErrors['0_value']).toBe('检查结果必须是数字');

      formComponent.formData.items[0].value = '5.2';
      validateItem(0, 'value');
      expect(formComponent.itemErrors['0_value']).toBeUndefined();
    });
  });

  describe('异常值检测', () => {
    it('应该正确检测异常值', () => {
      const checkAbnormalValue = (index) => {
        const item = formComponent.formData.items[index];
        if (!item.value || !item.referenceRange) {
          item.isAbnormal = false;
          return;
        }

        const value = parseFloat(item.value);
        if (isNaN(value)) {
          item.isAbnormal = false;
          return;
        }

        // 解析参考范围
        const rangeMatch = item.referenceRange.match(/(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/);
        if (rangeMatch) {
          const minValue = parseFloat(rangeMatch[1]);
          const maxValue = parseFloat(rangeMatch[2]);
          
          if (!isNaN(minValue) && !isNaN(maxValue)) {
            item.isAbnormal = value < minValue || value > maxValue;
          }
        }
      };

      // 测试正常值
      formComponent.formData.items.push({
        name: '白细胞计数',
        value: '6.0',
        unit: '10^9/L',
        referenceRange: '3.5-9.5',
        category: '血常规',
        isAbnormal: false
      });

      checkAbnormalValue(0);
      expect(formComponent.formData.items[0].isAbnormal).toBe(false);

      // 测试异常值（过高）
      formComponent.formData.items[0].value = '10.5';
      checkAbnormalValue(0);
      expect(formComponent.formData.items[0].isAbnormal).toBe(true);

      // 测试异常值（过低）
      formComponent.formData.items[0].value = '2.0';
      checkAbnormalValue(0);
      expect(formComponent.formData.items[0].isAbnormal).toBe(true);
    });

    it('应该处理无效的参考范围', () => {
      const checkAbnormalValue = (index) => {
        const item = formComponent.formData.items[index];
        if (!item.value || !item.referenceRange) {
          item.isAbnormal = false;
          return;
        }

        const value = parseFloat(item.value);
        if (isNaN(value)) {
          item.isAbnormal = false;
          return;
        }

        const rangeMatch = item.referenceRange.match(/(\d+\.?\d*)\s*[-~]\s*(\d+\.?\d*)/);
        if (rangeMatch) {
          const minValue = parseFloat(rangeMatch[1]);
          const maxValue = parseFloat(rangeMatch[2]);
          
          if (!isNaN(minValue) && !isNaN(maxValue)) {
            item.isAbnormal = value < minValue || value > maxValue;
          }
        }
      };

      formComponent.formData.items.push({
        name: '白细胞计数',
        value: '6.0',
        unit: '10^9/L',
        referenceRange: '无效范围',
        category: '血常规',
        isAbnormal: false
      });

      checkAbnormalValue(0);
      expect(formComponent.formData.items[0].isAbnormal).toBe(false);
    });
  });

  describe('分类猜测', () => {
    it('应该根据项目名称猜测正确的分类', () => {
      const guessCategory = (itemName) => {
        if (!itemName) return '';
        
        const categoryMap = {
          '血常规': ['白细胞', '红细胞', '血红蛋白', '血小板', '中性粒细胞', '淋巴细胞'],
          '生化检查': ['总蛋白', '白蛋白', '谷丙转氨酶', '谷草转氨酶'],
          '血脂': ['总胆固醇', '甘油三酯', '高密度脂蛋白', '低密度脂蛋白'],
          '血糖': ['血糖', '葡萄糖'],
          '肾功能': ['肌酐', '尿素氮', '尿酸'],
          '尿常规': ['尿蛋白', '尿糖', '尿比重']
        };

        for (const [category, keywords] of Object.entries(categoryMap)) {
          if (keywords.some(keyword => itemName.includes(keyword))) {
            return category;
          }
        }

        return '其他';
      };

      expect(guessCategory('白细胞计数')).toBe('血常规');
      expect(guessCategory('总胆固醇')).toBe('血脂');
      expect(guessCategory('血糖')).toBe('血糖');
      expect(guessCategory('肌酐')).toBe('肾功能');
      expect(guessCategory('尿蛋白')).toBe('尿常规');
      expect(guessCategory('未知项目')).toBe('其他');
    });
  });

  describe('OCR结果应用', () => {
    it('应该正确应用OCR识别结果', () => {
      const applyOCRResult = (ocrData) => {
        if (!ocrData) return;

        // 填充基本信息
        if (ocrData.hospital && !formComponent.formData.hospital) {
          formComponent.formData.hospital = ocrData.hospital;
        }
        if (ocrData.doctor && !formComponent.formData.doctor) {
          formComponent.formData.doctor = ocrData.doctor;
        }
        if (ocrData.checkDate && !formComponent.formData.checkDate) {
          formComponent.formData.checkDate = ocrData.checkDate;
        }

        // 填充检查项目
        if (ocrData.items && ocrData.items.length > 0) {
          formComponent.formData.items = ocrData.items.map(item => ({
            name: item.name || '',
            value: item.value || '',
            unit: item.unit || '',
            referenceRange: item.referenceRange || '',
            category: '血常规', // 简化处理
            isAbnormal: item.isAbnormal || false
          }));
        }
      };

      const mockOCRData = {
        hospital: '北京医院',
        doctor: '张医生',
        checkDate: '2024-01-15',
        items: [
          {
            name: '白细胞计数',
            value: '5.2',
            unit: '10^9/L',
            referenceRange: '3.5-9.5',
            isAbnormal: false
          }
        ]
      };

      applyOCRResult(mockOCRData);

      expect(formComponent.formData.hospital).toBe('北京医院');
      expect(formComponent.formData.doctor).toBe('张医生');
      expect(formComponent.formData.checkDate).toBe('2024-01-15');
      expect(formComponent.formData.items).toHaveLength(1);
      expect(formComponent.formData.items[0].name).toBe('白细胞计数');
      expect(formComponent.formData.items[0].value).toBe('5.2');
    });

    it('不应该覆盖已有的基本信息', () => {
      const applyOCRResult = (ocrData) => {
        if (!ocrData) return;

        if (ocrData.hospital && !formComponent.formData.hospital) {
          formComponent.formData.hospital = ocrData.hospital;
        }
        if (ocrData.doctor && !formComponent.formData.doctor) {
          formComponent.formData.doctor = ocrData.doctor;
        }
      };

      // 先设置已有信息
      formComponent.formData.hospital = '已有医院';
      formComponent.formData.doctor = '已有医生';

      const mockOCRData = {
        hospital: '新医院',
        doctor: '新医生'
      };

      applyOCRResult(mockOCRData);

      // 不应该被覆盖
      expect(formComponent.formData.hospital).toBe('已有医院');
      expect(formComponent.formData.doctor).toBe('已有医生');
    });
  });

  describe('项目管理', () => {
    it('应该能添加新项目', () => {
      const addItem = () => {
        formComponent.formData.items.push({
          name: '',
          value: '',
          unit: '',
          referenceRange: '',
          category: '',
          isAbnormal: false
        });
      };

      expect(formComponent.formData.items).toHaveLength(0);
      
      addItem();
      expect(formComponent.formData.items).toHaveLength(1);
      
      addItem();
      expect(formComponent.formData.items).toHaveLength(2);
    });

    it('应该能删除项目', () => {
      const removeItem = (index) => {
        formComponent.formData.items.splice(index, 1);
        // 清理相关错误信息
        Object.keys(formComponent.itemErrors).forEach(key => {
          if (key.startsWith(`${index}_`)) {
            delete formComponent.itemErrors[key];
          }
        });
      };

      // 添加两个项目
      formComponent.formData.items = [
        { name: '项目1', value: '1.0' },
        { name: '项目2', value: '2.0' }
      ];
      
      // 添加错误信息
      formComponent.itemErrors['0_name'] = '错误信息';

      removeItem(0);
      
      expect(formComponent.formData.items).toHaveLength(1);
      expect(formComponent.formData.items[0].name).toBe('项目2');
      expect(formComponent.itemErrors['0_name']).toBeUndefined();
    });
  });

  describe('表单提交', () => {
    it('应该验证表单完整性', () => {
      const isFormValid = () => {
        return !!(formComponent.formData.hospital && 
                 formComponent.formData.doctor && 
                 formComponent.formData.checkDate &&
                 formComponent.formData.items.length > 0 &&
                 Object.keys(formComponent.errors).length === 0 &&
                 Object.keys(formComponent.itemErrors).length === 0);
      };

      // 空表单应该无效
      expect(isFormValid()).toBe(false);

      // 填充必要信息
      formComponent.formData.hospital = '北京医院';
      formComponent.formData.doctor = '张医生';
      formComponent.formData.checkDate = '2024-01-15';
      formComponent.formData.items.push({
        name: '白细胞计数',
        value: '5.2'
      });

      expect(isFormValid()).toBe(true);

      // 添加错误信息后应该无效
      formComponent.errors.hospital = '错误信息';
      expect(isFormValid()).toBe(false);
    });

    it('应该在提交前进行完整验证', () => {
      const onSave = () => {
        // 验证所有字段的模拟
        const validateAllFields = () => {
          const requiredFields = ['hospital', 'doctor', 'checkDate'];
          let hasErrors = false;

          requiredFields.forEach(field => {
            if (!formComponent.formData[field]) {
              formComponent.errors[field] = `请填写${field}`;
              hasErrors = true;
            }
          });

          formComponent.formData.items.forEach((item, index) => {
            if (!item.name) {
              formComponent.itemErrors[`${index}_name`] = '请输入项目名称';
              hasErrors = true;
            }
            if (!item.value) {
              formComponent.itemErrors[`${index}_value`] = '请输入检查结果';
              hasErrors = true;
            }
          });

          return !hasErrors;
        };

        if (!validateAllFields()) {
          return { success: false, message: '请完善必填信息' };
        }

        return { success: true, data: formComponent.formData };
      };

      // 测试不完整表单
      formComponent.formData.items.push({ name: '', value: '' });
      const result1 = onSave();
      expect(result1.success).toBe(false);

      // 测试完整表单
      formComponent.formData.hospital = '北京医院';
      formComponent.formData.doctor = '张医生';
      formComponent.formData.checkDate = '2024-01-15';
      formComponent.formData.items[0] = { name: '白细胞计数', value: '5.2' };
      formComponent.errors = {};
      formComponent.itemErrors = {};

      const result2 = onSave();
      expect(result2.success).toBe(true);
      expect(result2.data.hospital).toBe('北京医院');
    });
  });
});