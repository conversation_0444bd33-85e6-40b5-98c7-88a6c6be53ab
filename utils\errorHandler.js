/**
 * 全局错误处理器
 * 统一处理应用中的各种错误
 * 集成新的错误处理系统
 */

import { errorHandler, installErrorHandler } from '../core/errors/ErrorHandler.js'
import { Constants } from '../types/index.js'

// 导出错误类型常量（保持向后兼容）
export const ERROR_TYPES = Constants.ERROR_TYPES

class GlobalErrorHandler {
  constructor() {
    this.coreErrorHandler = errorHandler
    this.setupUniAppHandlers()
  }
  
  // 设置uni-app特定的错误处理器
  setupUniAppHandlers() {
    // 网络错误处理器
    this.coreErrorHandler.registerHandler(ERROR_TYPES.NETWORK_ERROR, (error, context) => {
      uni.showToast({
        title: '网络连接异常',
        icon: 'none',
        duration: 2000
      })
      
      return {
        shouldRetry: true,
        retryDelay: 3000,
        userMessage: '网络连接异常，请检查网络设置'
      }
    })
    
    // 认证错误处理器
    this.coreErrorHandler.registerHandler(ERROR_TYPES.AUTH_ERROR, (error, context) => {
      // 清除本地认证信息
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 跳转到登录页面
      uni.reLaunch({
        url: '/pages/auth/login'
      })
      
      return {
        shouldRetry: false,
        requiresReauth: true,
        userMessage: '身份验证失败，请重新登录'
      }
    })
    
    // 验证错误处理器
    this.coreErrorHandler.registerHandler(ERROR_TYPES.VALIDATION_ERROR, (error, context) => {
      uni.showToast({
        title: error.message || '输入数据格式不正确',
        icon: 'none',
        duration: 2000
      })
      
      return {
        shouldRetry: false,
        userMessage: error.message || '输入数据格式不正确',
        validationErrors: error.validationErrors || []
      }
    })
    
    // OCR错误处理器
    this.coreErrorHandler.registerHandler(ERROR_TYPES.OCR_ERROR, (error, context) => {
      uni.showModal({
        title: '识别失败',
        content: '图片识别失败，请重新拍摄或手动输入',
        showCancel: true,
        cancelText: '重新拍摄',
        confirmText: '手动输入',
        success: (res) => {
          if (res.confirm) {
            // 触发手动输入
            uni.$emit('ocr-fallback-manual')
          } else {
            // 触发重新拍摄
            uni.$emit('ocr-fallback-retake')
          }
        }
      })
      
      return {
        shouldRetry: true,
        retryDelay: 1000,
        fallbackAction: 'manual_input',
        userMessage: '图片识别失败，请重新拍摄或手动输入'
      }
    })
    
    // 存储错误处理器
    this.coreErrorHandler.registerHandler(ERROR_TYPES.STORAGE_ERROR, (error, context) => {
      uni.showToast({
        title: '数据保存失败',
        icon: 'none',
        duration: 2000
      })
      
      return {
        shouldRetry: true,
        retryDelay: 2000,
        userMessage: '数据保存失败，请稍后重试'
      }
    })
    
    // 权限错误处理器
    this.coreErrorHandler.registerHandler(ERROR_TYPES.PERMISSION_ERROR, (error, context) => {
      uni.showModal({
        title: '权限不足',
        content: '应用需要相关权限才能正常使用，请在设置中开启',
        showCancel: true,
        cancelText: '取消',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            // 打开系统设置
            // #ifdef APP-PLUS
            plus.runtime.openURL('app-settings:')
            // #endif
          }
        }
      })
      
      return {
        shouldRetry: false,
        requiresPermission: true,
        userMessage: '权限不足，请检查应用权限设置'
      }
    })
    
    // 添加错误监听器，保存错误日志到本地存储
    this.coreErrorHandler.addListener((errorResult) => {
      this.saveErrorLog(errorResult)
    })
  }
  
  // 保存错误日志到本地存储
  saveErrorLog(errorResult) {
    try {
      const errorLogs = uni.getStorageSync('errorLogs') || []
      
      const logEntry = {
        timestamp: errorResult.timestamp,
        error: {
          type: errorResult.error.type,
          message: errorResult.error.message,
          code: errorResult.error.code,
          stack: errorResult.error.stack
        },
        context: errorResult.context,
        userMessage: errorResult.userMessage,
        handled: errorResult.handled,
        userAgent: navigator.userAgent || 'Unknown',
        url: window.location?.href || 'Unknown'
      }
      
      errorLogs.push(logEntry)
      
      // 只保留最近100条错误日志
      if (errorLogs.length > 100) {
        errorLogs.splice(0, errorLogs.length - 100)
      }
      
      uni.setStorageSync('errorLogs', errorLogs)
    } catch (storageError) {
      console.error('保存错误日志失败:', storageError)
    }
  }
  
  // 主要错误处理方法（保持向后兼容）
  handleError(error, context = {}) {
    return this.coreErrorHandler.handleError(error, context)
  }
  
  // 注册自定义错误处理器（保持向后兼容）
  registerHandler(errorType, handler) {
    this.coreErrorHandler.registerHandler(errorType, handler)
  }
  
  // 创建错误对象（保持向后兼容）
  createError(type, message, details = {}) {
    return this.coreErrorHandler.createError(type, message, details)
  }
  
  // 异步操作错误包装器（保持向后兼容）
  async wrapAsync(asyncFn, context = {}) {
    return this.coreErrorHandler.wrapAsync(asyncFn, context)
  }
  
  // 重试机制（保持向后兼容）
  async retryOperation(operation, maxRetries = 3, delay = 1000) {
    return this.coreErrorHandler.retryWithErrorHandling(operation, {
      maxRetries,
      baseDelay: delay
    })
  }
  
  // 获取错误日志
  getErrorLogs() {
    try {
      return uni.getStorageSync('errorLogs') || []
    } catch (error) {
      console.error('获取错误日志失败:', error)
      return []
    }
  }
  
  // 清空错误日志
  clearErrorLogs() {
    try {
      uni.removeStorageSync('errorLogs')
    } catch (error) {
      console.error('清空错误日志失败:', error)
    }
  }
  
  // 上报错误日志
  async reportErrorLogs() {
    try {
      const logs = this.getErrorLogs()
      if (logs.length === 0) return
      
      // TODO: 实现错误日志上报逻辑
      console.log('上报错误日志:', logs)
      
      // 上报成功后清空本地日志
      this.clearErrorLogs()
    } catch (error) {
      console.error('上报错误日志失败:', error)
    }
  }
  
  // 安装错误处理器（保持向后兼容）
  install(app) {
    // 使用核心错误处理器的安装函数
    installErrorHandler(app)
    
    // uni-app错误处理
    if (typeof uni !== 'undefined') {
      uni.onError((error) => {
        this.handleError(error, { context: 'uni-app' })
      })
    }
    
    // 挂载到全局
    if (app && app.config) {
      app.config.globalProperties.$errorHandler = this
    }
  }
}

// 创建全局实例
const globalErrorHandler = new GlobalErrorHandler()

// Vue.js 错误处理器安装
export function install(app) {
  globalErrorHandler.install(app)
}

export default {
  install,
  handler: globalErrorHandler,
  ERROR_TYPES
}