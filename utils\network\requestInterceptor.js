/**
 * 网络请求拦截器
 * 自动添加token、处理token刷新、统一错误处理
 */

import tokenService from '../../services/auth/tokenService.js'
import sessionService from '../../services/auth/sessionService.js'

class RequestInterceptor {
  constructor() {
    this.isRefreshing = false
    this.refreshSubscribers = []
    this.setupInterceptors()
  }

  /**
   * 设置请求拦截器
   */
  setupInterceptors() {
    // 保存原始的uni.request方法
    const originalRequest = uni.request

    // 重写uni.request方法
    uni.request = (options) => {
      return this.interceptRequest(originalRequest, options)
    }
  }

  /**
   * 拦截请求
   * @param {Function} originalRequest 原始请求方法
   * @param {Object} options 请求选项
   */
  async interceptRequest(originalRequest, options) {
    try {
      // 添加认证头
      options = await this.addAuthHeaders(options)
      
      // 执行请求
      return new Promise((resolve, reject) => {
        originalRequest({
          ...options,
          success: (res) => {
            this.handleResponse(res, resolve, reject, originalRequest, options)
          },
          fail: (err) => {
            this.handleError(err, reject)
          }
        })
      })
    } catch (error) {
      console.error('请求拦截失败:', error)
      throw error
    }
  }

  /**
   * 添加认证头
   * @param {Object} options 请求选项
   */
  async addAuthHeaders(options) {
    try {
      // 如果是认证相关的请求，不需要添加token
      const authUrls = ['/auth/login', '/auth/register', '/auth/send-code', '/auth/refresh-token']
      const isAuthRequest = authUrls.some(url => options.url.includes(url))
      
      if (!isAuthRequest) {
        // 确保token有效
        const token = await tokenService.ensureValidToken()
        
        // 添加认证头
        options.header = {
          ...options.header,
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
      
      // 添加通用头
      options.header = {
        ...options.header,
        'X-Requested-With': 'XMLHttpRequest',
        'X-Client-Version': '1.0.0',
        'X-Platform': this.getPlatform()
      }
      
      return options
    } catch (error) {
      console.error('添加认证头失败:', error)
      return options
    }
  }

  /**
   * 处理响应
   * @param {Object} res 响应对象
   * @param {Function} resolve Promise resolve
   * @param {Function} reject Promise reject
   * @param {Function} originalRequest 原始请求方法
   * @param {Object} options 请求选项
   */
  async handleResponse(res, resolve, reject, originalRequest, options) {
    try {
      // 检查HTTP状态码
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // 检查业务状态码
        if (res.data && res.data.code !== undefined) {
          if (res.data.code === 0 || res.data.code === 200) {
            // 成功
            resolve(res)
          } else if (res.data.code === 401) {
            // token过期，尝试刷新
            await this.handleTokenExpired(originalRequest, options, resolve, reject)
          } else {
            // 其他业务错误
            reject(new Error(res.data.message || '请求失败'))
          }
        } else {
          // 没有业务状态码，直接返回
          resolve(res)
        }
      } else if (res.statusCode === 401) {
        // HTTP 401，token过期
        await this.handleTokenExpired(originalRequest, options, resolve, reject)
      } else {
        // 其他HTTP错误
        reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`))
      }
    } catch (error) {
      console.error('处理响应失败:', error)
      reject(error)
    }
  }

  /**
   * 处理token过期
   * @param {Function} originalRequest 原始请求方法
   * @param {Object} options 请求选项
   * @param {Function} resolve Promise resolve
   * @param {Function} reject Promise reject
   */
  async handleTokenExpired(originalRequest, options, resolve, reject) {
    try {
      if (!this.isRefreshing) {
        this.isRefreshing = true
        
        try {
          // 刷新token
          const refreshResult = await tokenService.refreshToken()
          
          if (refreshResult.success) {
            // 刷新成功，通知所有等待的请求
            this.onTokenRefreshed(refreshResult.token)
            
            // 重新发起当前请求
            const newOptions = await this.addAuthHeaders(options)
            originalRequest({
              ...newOptions,
              success: resolve,
              fail: reject
            })
          } else {
            // 刷新失败，跳转到登录页
            this.onTokenRefreshFailed()
            reject(new Error('登录已过期，请重新登录'))
          }
        } finally {
          this.isRefreshing = false
        }
      } else {
        // 正在刷新token，将请求加入队列
        this.addRefreshSubscriber((token) => {
          options.header.Authorization = `Bearer ${token}`
          originalRequest({
            ...options,
            success: resolve,
            fail: reject
          })
        })
      }
    } catch (error) {
      console.error('处理token过期失败:', error)
      reject(error)
    }
  }

  /**
   * 处理请求错误
   * @param {Object} err 错误对象
   * @param {Function} reject Promise reject
   */
  handleError(err, reject) {
    console.error('网络请求失败:', err)
    
    let errorMessage = '网络请求失败'
    
    // 根据错误类型提供更具体的错误信息
    if (err.errMsg) {
      if (err.errMsg.includes('timeout')) {
        errorMessage = '请求超时，请检查网络连接'
      } else if (err.errMsg.includes('fail')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else {
        errorMessage = err.errMsg
      }
    }
    
    reject(new Error(errorMessage))
  }

  /**
   * 添加token刷新订阅者
   * @param {Function} callback 回调函数
   */
  addRefreshSubscriber(callback) {
    this.refreshSubscribers.push(callback)
  }

  /**
   * token刷新成功回调
   * @param {string} token 新token
   */
  onTokenRefreshed(token) {
    this.refreshSubscribers.forEach(callback => callback(token))
    this.refreshSubscribers = []
  }

  /**
   * token刷新失败回调
   */
  onTokenRefreshFailed() {
    this.refreshSubscribers = []
    
    // 销毁会话
    sessionService.destroySession()
    
    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/auth/login'
    })
  }

  /**
   * 获取平台信息
   */
  getPlatform() {
    // #ifdef H5
    return 'h5'
    // #endif
    
    // #ifdef MP-WEIXIN
    return 'mp-weixin'
    // #endif
    
    // #ifdef MP-ALIPAY
    return 'mp-alipay'
    // #endif
    
    // #ifdef APP-PLUS
    return 'app'
    // #endif
    
    return 'unknown'
  }
}

// 创建并导出拦截器实例
export default new RequestInterceptor()