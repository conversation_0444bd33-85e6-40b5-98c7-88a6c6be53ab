<template>
  <view class="compare-analysis">
    <uni-nav-bar 
      title="指标对比" 
      left-icon="back" 
      @clickLeft="goBack"
      background-color="#007AFF"
      color="#ffffff"
    />
    
    <!-- 对比模式选择 -->
    <view class="compare-mode">
      <view class="mode-tabs">
        <view 
          class="mode-tab"
          :class="{ active: compareMode === 'time' }"
          @click="setCompareMode('time')"
        >
          时间对比
        </view>
        <view 
          class="mode-tab"
          :class="{ active: compareMode === 'indicator' }"
          @click="setCompareMode('indicator')"
        >
          指标对比
        </view>
      </view>
    </view>
    
    <!-- 时间对比模式 -->
    <view v-if="compareMode === 'time'" class="time-compare">
      <!-- 指标选择 -->
      <view class="selector-section">
        <text class="selector-title">选择指标</text>
        <picker 
          :value="selectedIndicatorIndex" 
          :range="indicatorNames" 
          @change="onIndicatorChange"
        >
          <view class="picker-item">
            {{ getCurrentIndicatorName() }}
            <uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
          </view>
        </picker>
      </view>
      
      <!-- 时间点选择 -->
      <view class="time-selector">
        <text class="selector-title">选择对比时间点</text>
        <view class="time-points">
          <view 
            v-for="(point, index) in selectedTimePoints" 
            :key="index"
            class="time-point-item"
          >
            <picker 
              mode="date" 
              :value="point.date" 
              @change="(e) => updateTimePoint(index, e.detail.value)"
            >
              <view class="date-picker">
                {{ formatDisplayDate(point.date) }}
              </view>
            </picker>
            <button 
              v-if="selectedTimePoints.length > 2"
              class="remove-btn" 
              @click="removeTimePoint(index)"
            >
              ×
            </button>
          </view>
          <button 
            v-if="selectedTimePoints.length < 5"
            class="add-time-btn" 
            @click="addTimePoint"
          >
            + 添加时间点
          </button>
        </view>
      </view>
      
      <!-- 时间对比图表 -->
      <view class="chart-container">
        <HealthChart
          :canvas-id="'timeCompareChart'"
          :chart-data="timeCompareData"
          :chart-type="'bar'"
          :width="350"
          :height="250"
          :show-grid="true"
          :show-legend="true"
          :normal-range="currentNormalRange"
        />
      </view>
    </view>
    
    <!-- 指标对比模式 -->
    <view v-if="compareMode === 'indicator'" class="indicator-compare">
      <!-- 时间选择 -->
      <view class="selector-section">
        <text class="selector-title">选择时间</text>
        <picker 
          mode="date" 
          :value="compareDate" 
          @change="onCompareDateChange"
        >
          <view class="picker-item">
            {{ formatDisplayDate(compareDate) }}
            <uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
          </view>
        </picker>
      </view>
      
      <!-- 指标选择 -->
      <view class="indicator-selector">
        <text class="selector-title">选择对比指标</text>
        <view class="indicator-checkboxes">
          <label 
            v-for="indicator in availableIndicators" 
            :key="indicator.key"
            class="checkbox-item"
          >
            <checkbox 
              :value="indicator.key" 
              :checked="selectedIndicators.includes(indicator.key)"
              @change="(e) => toggleIndicator(indicator.key, e.detail.value)"
            />
            <text class="checkbox-label">{{ indicator.name }}</text>
          </label>
        </view>
      </view>
      
      <!-- 指标对比图表 -->
      <view class="chart-container">
        <HealthChart
          :canvas-id="'indicatorCompareChart'"
          :chart-data="indicatorCompareData"
          :chart-type="'bar'"
          :width="350"
          :height="250"
          :show-grid="true"
          :show-legend="false"
          :normal-range="{ min: null, max: null }"
        />
      </view>
    </view>
    
    <!-- 对比结果分析 -->
    <view class="compare-results">
      <view class="results-header">
        <text class="results-title">对比分析</text>
      </view>
      
      <view class="analysis-cards">
        <view 
          v-for="analysis in compareAnalysis" 
          :key="analysis.title"
          class="analysis-card"
        >
          <view class="card-header">
            <text class="card-title">{{ analysis.title }}</text>
            <view class="trend-indicator" :class="analysis.trend">
              <uni-icons 
                :type="analysis.trend === 'up' ? 'arrowup' : analysis.trend === 'down' ? 'arrowdown' : 'minus'" 
                size="16"
              ></uni-icons>
            </view>
          </view>
          <text class="card-content">{{ analysis.description }}</text>
          <text v-if="analysis.suggestion" class="card-suggestion">
            建议：{{ analysis.suggestion }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import HealthChart from '@/components/common/HealthChart.vue'

export default {
  name: 'CompareAnalysis',
  components: {
    HealthChart
  },
  data() {
    return {
      compareMode: 'time', // 'time' 或 'indicator'
      selectedIndicatorIndex: 0,
      selectedTimePoints: [
        { date: '2024-03-01' },
        { date: '2024-04-01' }
      ],
      compareDate: '2024-04-01',
      selectedIndicators: ['blood_pressure_systolic', 'heart_rate'],
      availableIndicators: [
        { key: 'blood_pressure_systolic', name: '收缩压', unit: 'mmHg', normalRange: { min: 90, max: 140 } },
        { key: 'blood_pressure_diastolic', name: '舒张压', unit: 'mmHg', normalRange: { min: 60, max: 90 } },
        { key: 'heart_rate', name: '心率', unit: 'bpm', normalRange: { min: 60, max: 100 } },
        { key: 'blood_glucose', name: '血糖', unit: 'mmol/L', normalRange: { min: 3.9, max: 6.1 } },
        { key: 'cholesterol', name: '胆固醇', unit: 'mmol/L', normalRange: { min: 0, max: 5.2 } },
        { key: 'triglycerides', name: '甘油三酯', unit: 'mmol/L', normalRange: { min: 0, max: 1.7 } }
      ],
      healthReports: [],
      timeCompareData: [],
      indicatorCompareData: [],
      compareAnalysis: []
    }
  },
  computed: {
    indicatorNames() {
      return this.availableIndicators.map(indicator => indicator.name)
    },
    
    currentIndicator() {
      return this.availableIndicators[this.selectedIndicatorIndex]
    },
    
    currentNormalRange() {
      return this.currentIndicator ? this.currentIndicator.normalRange : { min: null, max: null }
    }
  },
  onLoad() {
    this.loadHealthReports()
  },
  methods: {
    async loadHealthReports() {
      try {
        // 模拟从数据库加载数据
        this.healthReports = await this.getHealthReportsFromDB()
        this.updateCompareData()
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'error'
        })
      }
    },
    
    async getHealthReportsFromDB() {
      // 模拟数据
      return [
        {
          id: 1,
          date: '2024-01-01',
          indicators: {
            blood_pressure_systolic: 120,
            blood_pressure_diastolic: 80,
            heart_rate: 72,
            blood_glucose: 5.2,
            cholesterol: 4.8,
            triglycerides: 1.2
          }
        },
        {
          id: 2,
          date: '2024-02-01',
          indicators: {
            blood_pressure_systolic: 125,
            blood_pressure_diastolic: 82,
            heart_rate: 75,
            blood_glucose: 5.4,
            cholesterol: 4.9,
            triglycerides: 1.3
          }
        },
        {
          id: 3,
          date: '2024-03-01',
          indicators: {
            blood_pressure_systolic: 135,
            blood_pressure_diastolic: 85,
            heart_rate: 78,
            blood_glucose: 5.8,
            cholesterol: 5.1,
            triglycerides: 1.5
          }
        },
        {
          id: 4,
          date: '2024-04-01',
          indicators: {
            blood_pressure_systolic: 145,
            blood_pressure_diastolic: 92,
            heart_rate: 82,
            blood_glucose: 6.5,
            cholesterol: 5.5,
            triglycerides: 1.8
          }
        }
      ]
    },
    
    setCompareMode(mode) {
      this.compareMode = mode
      this.updateCompareData()
    },
    
    onIndicatorChange(e) {
      this.selectedIndicatorIndex = e.detail.value
      this.updateCompareData()
    },
    
    onCompareDateChange(e) {
      this.compareDate = e.detail.value
      this.updateCompareData()
    },
    
    addTimePoint() {
      this.selectedTimePoints.push({ date: '2024-04-01' })
      this.updateCompareData()
    },
    
    removeTimePoint(index) {
      this.selectedTimePoints.splice(index, 1)
      this.updateCompareData()
    },
    
    updateTimePoint(index, date) {
      this.selectedTimePoints[index].date = date
      this.updateCompareData()
    },
    
    toggleIndicator(indicatorKey, checked) {
      if (checked) {
        if (!this.selectedIndicators.includes(indicatorKey)) {
          this.selectedIndicators.push(indicatorKey)
        }
      } else {
        const index = this.selectedIndicators.indexOf(indicatorKey)
        if (index > -1) {
          this.selectedIndicators.splice(index, 1)
        }
      }
      this.updateCompareData()
    },
    
    updateCompareData() {
      if (this.compareMode === 'time') {
        this.updateTimeCompareData()
      } else {
        this.updateIndicatorCompareData()
      }
      this.generateCompareAnalysis()
    },
    
    updateTimeCompareData() {
      const indicatorKey = this.currentIndicator.key
      
      this.timeCompareData = this.selectedTimePoints.map(point => {
        const report = this.healthReports.find(r => r.date === point.date)
        return {
          date: this.formatDisplayDate(point.date),
          value: report ? (report.indicators[indicatorKey] || 0) : 0
        }
      })
    },
    
    updateIndicatorCompareData() {
      const report = this.healthReports.find(r => r.date === this.compareDate)
      
      if (!report) {
        this.indicatorCompareData = []
        return
      }
      
      this.indicatorCompareData = this.selectedIndicators.map(indicatorKey => {
        const indicator = this.availableIndicators.find(ind => ind.key === indicatorKey)
        return {
          date: indicator.name,
          value: report.indicators[indicatorKey] || 0
        }
      })
    },
    
    generateCompareAnalysis() {
      this.compareAnalysis = []
      
      if (this.compareMode === 'time' && this.timeCompareData.length >= 2) {
        this.generateTimeAnalysis()
      } else if (this.compareMode === 'indicator' && this.indicatorCompareData.length >= 2) {
        this.generateIndicatorAnalysis()
      }
    },
    
    generateTimeAnalysis() {
      const data = this.timeCompareData
      const firstValue = data[0].value
      const lastValue = data[data.length - 1].value
      const change = lastValue - firstValue
      const changePercent = ((change / firstValue) * 100).toFixed(1)
      
      let trend = 'stable'
      let description = ''
      let suggestion = ''
      
      if (Math.abs(change) > firstValue * 0.1) { // 变化超过10%
        trend = change > 0 ? 'up' : 'down'
        description = `${this.currentIndicator.name}从${firstValue}${this.currentIndicator.unit}变化到${lastValue}${this.currentIndicator.unit}，变化幅度${changePercent}%`
        
        if (this.isAbnormalValue(lastValue)) {
          suggestion = '当前数值异常，建议咨询医生'
        } else if (Math.abs(parseFloat(changePercent)) > 20) {
          suggestion = '变化幅度较大，建议持续关注'
        }
      } else {
        description = `${this.currentIndicator.name}保持相对稳定，变化幅度${changePercent}%`
      }
      
      this.compareAnalysis.push({
        title: '趋势变化',
        trend,
        description,
        suggestion
      })
    },
    
    generateIndicatorAnalysis() {
      const abnormalCount = this.indicatorCompareData.filter(item => {
        const indicator = this.availableIndicators.find(ind => ind.name === item.date)
        return indicator && this.isAbnormalValueForIndicator(item.value, indicator)
      }).length
      
      let description = ''
      let suggestion = ''
      let trend = 'stable'
      
      if (abnormalCount === 0) {
        description = '所有选中指标均在正常范围内'
        trend = 'stable'
      } else {
        description = `${abnormalCount}项指标超出正常范围`
        trend = 'up'
        suggestion = '建议关注异常指标，必要时咨询医生'
      }
      
      this.compareAnalysis.push({
        title: '指标状态',
        trend,
        description,
        suggestion
      })
    },
    
    isAbnormalValue(value) {
      const { min, max } = this.currentNormalRange
      if (min === null || max === null) return false
      return value < min || value > max
    },
    
    isAbnormalValueForIndicator(value, indicator) {
      const { min, max } = indicator.normalRange
      if (min === null || max === null) return false
      return value < min || value > max
    },
    
    getCurrentIndicatorName() {
      return this.currentIndicator ? this.currentIndicator.name : ''
    },
    
    formatDisplayDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    },
    
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.compare-analysis {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.compare-mode {
  background-color: #ffffff;
  padding: 15px;
  margin-bottom: 10px;
}

.mode-tabs {
  display: flex;
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 4px;
}

.mode-tab {
  flex: 1;
  text-align: center;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  color: #666666;
}

.mode-tab.active {
  background-color: #007AFF;
  color: #ffffff;
}

.selector-section {
  background-color: #ffffff;
  padding: 15px;
  margin-bottom: 10px;
}

.selector-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10px;
  display: block;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 6px;
  font-size: 16px;
  color: #333333;
}

.time-selector {
  background-color: #ffffff;
  padding: 15px;
  margin-bottom: 10px;
}

.time-points {
  margin-top: 10px;
}

.time-point-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.date-picker {
  flex: 1;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 6px;
  font-size: 16px;
  color: #333333;
}

.remove-btn {
  width: 30px;
  height: 30px;
  margin-left: 10px;
  background-color: #FF3B30;
  color: #ffffff;
  border: none;
  border-radius: 15px;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-time-btn {
  width: 100%;
  padding: 12px;
  background-color: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 16px;
}

.indicator-selector {
  background-color: #ffffff;
  padding: 15px;
  margin-bottom: 10px;
}

.indicator-checkboxes {
  margin-top: 10px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.checkbox-item:last-child {
  border-bottom: none;
}

.checkbox-label {
  margin-left: 10px;
  font-size: 16px;
  color: #333333;
}

.chart-container {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 10px;
}

.compare-results {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 10px;
}

.results-header {
  margin-bottom: 15px;
}

.results-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.analysis-cards {
  margin-top: 15px;
}

.analysis-card {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.trend-indicator {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-indicator.up {
  background-color: #FF3B30;
  color: #ffffff;
}

.trend-indicator.down {
  background-color: #34C759;
  color: #ffffff;
}

.trend-indicator.stable {
  background-color: #007AFF;
  color: #ffffff;
}

.card-content {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  display: block;
  margin-bottom: 8px;
}

.card-suggestion {
  font-size: 14px;
  color: #FF3B30;
  font-weight: bold;
  display: block;
}
</style>