/**
 * 用户注册功能集成测试
 * 测试注册流程的核心逻辑
 */

// Mock uni-app API
global.uni = {
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn().mockReturnValue(null),
  removeStorageSync: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  navigateTo: jest.fn(),
  reLaunch: jest.fn(),
  request: jest.fn()
}

// 模拟认证服务的核心功能
class MockAuthService {
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  validatePassword(password) {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/
    return passwordRegex.test(password)
  }

  validateVerificationCode(code) {
    const codeRegex = /^\d{6}$/
    return codeRegex.test(code)
  }

  async checkPhoneExists(phone) {
    // 模拟某些手机号已存在
    const existingPhones = ['13800138000', '13900139000']
    return existingPhones.includes(phone)
  }

  async sendVerificationCode(phone, type = 'login') {
    try {
      if (!this.validatePhone(phone)) {
        throw new Error('手机号格式不正确')
      }

      // 检查发送频率限制
      const lastSendTime = uni.getStorageSync(`last_send_code_${phone}`)
      const now = Date.now()
      if (lastSendTime && (now - lastSendTime) < 60000) {
        throw new Error('验证码发送过于频繁，请稍后再试')
      }

      // 模拟API调用成功
      uni.setStorageSync(`last_send_code_${phone}`, now)

      return {
        success: true,
        message: '验证码已发送',
        data: {
          codeId: 'code_' + Date.now(),
          expiry: Date.now() + 5 * 60 * 1000
        }
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '发送验证码失败'
      }
    }
  }

  async registerWithPhone(registerData) {
    try {
      const { phone, code, password, nickname } = registerData

      // 验证必填字段
      if (!phone || !code || !password) {
        throw new Error('请填写完整信息')
      }

      if (!this.validatePhone(phone)) {
        throw new Error('手机号格式不正确')
      }

      if (!this.validatePassword(password)) {
        throw new Error('密码格式不正确，至少8位包含字母和数字')
      }

      if (!this.validateVerificationCode(code)) {
        throw new Error('验证码格式不正确')
      }

      // 检查手机号是否已注册
      const phoneExists = await this.checkPhoneExists(phone)
      if (phoneExists) {
        throw new Error('该手机号已注册，请直接登录')
      }

      // 模拟验证码验证（123456为有效验证码）
      if (code !== '123456') {
        throw new Error('验证码错误')
      }

      return {
        success: true,
        message: '注册成功',
        data: {
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now(),
          tokenExpiry: Date.now() + 2 * 60 * 60 * 1000,
          userInfo: {
            id: 'user_' + Date.now(),
            phone: phone,
            nickname: nickname || phone,
            avatar: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            settings: {
              biometricEnabled: false,
              autoSync: true,
              notificationEnabled: true,
              theme: 'light'
            }
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '注册失败'
      }
    }
  }
}

describe('用户注册功能集成测试', () => {
  let authService

  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks()
    authService = new MockAuthService()
  })

  describe('表单验证测试', () => {
    test('应该验证手机号格式', () => {
      // 测试有效手机号
      expect(authService.validatePhone('13800138000')).toBe(true)
      expect(authService.validatePhone('15912345678')).toBe(true)
      
      // 测试无效手机号
      expect(authService.validatePhone('1380013800')).toBe(false) // 少一位
      expect(authService.validatePhone('138001380001')).toBe(false) // 多一位
      expect(authService.validatePhone('12800138000')).toBe(false) // 第二位不是3-9
      expect(authService.validatePhone('abc12345678')).toBe(false) // 包含字母
      expect(authService.validatePhone('')).toBe(false) // 空字符串
    })

    test('应该验证密码格式', () => {
      // 测试有效密码
      expect(authService.validatePassword('abc12345')).toBe(true)
      expect(authService.validatePassword('Test123456')).toBe(true)
      expect(authService.validatePassword('myPassword1')).toBe(true)
      
      // 测试无效密码
      expect(authService.validatePassword('12345678')).toBe(false) // 只有数字
      expect(authService.validatePassword('abcdefgh')).toBe(false) // 只有字母
      expect(authService.validatePassword('abc123')).toBe(false) // 少于8位
      expect(authService.validatePassword('')).toBe(false) // 空字符串
    })

    test('应该验证验证码格式', () => {
      // 测试有效验证码
      expect(authService.validateVerificationCode('123456')).toBe(true)
      expect(authService.validateVerificationCode('000000')).toBe(true)
      
      // 测试无效验证码
      expect(authService.validateVerificationCode('12345')).toBe(false) // 少于6位
      expect(authService.validateVerificationCode('1234567')).toBe(false) // 多于6位
      expect(authService.validateVerificationCode('12345a')).toBe(false) // 包含字母
      expect(authService.validateVerificationCode('')).toBe(false) // 空字符串
    })
  })

  describe('验证码发送测试', () => {
    test('应该成功发送验证码', async () => {
      const phone = '13800138001'
      const result = await authService.sendVerificationCode(phone, 'register')
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('验证码已发送')
      expect(result.data).toHaveProperty('codeId')
      expect(result.data).toHaveProperty('expiry')
    })

    test('应该拒绝无效手机号', async () => {
      const invalidPhone = '1234567890'
      const result = await authService.sendVerificationCode(invalidPhone, 'register')
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('手机号格式不正确')
    })

    test('应该限制验证码发送频率', async () => {
      const phone = '13800138002'
      
      // 模拟上次发送时间为30秒前
      uni.getStorageSync.mockReturnValue(Date.now() - 30000)
      
      const result = await authService.sendVerificationCode(phone, 'register')
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('验证码发送过于频繁，请稍后再试')
    })
  })

  describe('用户注册测试', () => {
    test('应该成功注册新用户', async () => {
      const registerData = {
        phone: '13800138003',
        code: '123456',
        password: 'test123456',
        nickname: '测试用户'
      }
      
      const result = await authService.registerWithPhone(registerData)
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('注册成功')
      expect(result.data).toHaveProperty('token')
      expect(result.data).toHaveProperty('refreshToken')
      expect(result.data).toHaveProperty('tokenExpiry')
      expect(result.data).toHaveProperty('userInfo')
      expect(result.data.userInfo.phone).toBe(registerData.phone)
      expect(result.data.userInfo.nickname).toBe(registerData.nickname)
    })

    test('应该拒绝无效的注册数据', async () => {
      // 测试缺少必填字段
      const incompleteData = {
        phone: '13800138004',
        code: '123456'
        // 缺少password
      }
      
      const result = await authService.registerWithPhone(incompleteData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('请填写完整信息')
    })

    test('应该拒绝无效手机号注册', async () => {
      const invalidPhoneData = {
        phone: '1234567890',
        code: '123456',
        password: 'test123456'
      }
      
      const result = await authService.registerWithPhone(invalidPhoneData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('手机号格式不正确')
    })

    test('应该拒绝无效密码注册', async () => {
      const invalidPasswordData = {
        phone: '13800138005',
        code: '123456',
        password: '123456' // 只有数字，不符合要求
      }
      
      const result = await authService.registerWithPhone(invalidPasswordData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('密码格式不正确，至少8位包含字母和数字')
    })

    test('应该拒绝错误的验证码', async () => {
      const wrongCodeData = {
        phone: '13800138006',
        code: '654321', // 错误的验证码
        password: 'test123456'
      }
      
      const result = await authService.registerWithPhone(wrongCodeData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('验证码错误')
    })

    test('应该拒绝已存在的手机号注册', async () => {
      const existingPhoneData = {
        phone: '13800138000', // 这个号码在mock中设置为已存在
        code: '123456',
        password: 'test123456'
      }
      
      const result = await authService.registerWithPhone(existingPhoneData)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('该手机号已注册，请直接登录')
    })
  })

  describe('完整注册流程测试', () => {
    test('完整的注册流程应该成功', async () => {
      const phone = '13800138009'
      const code = '123456'
      const password = 'test123456'
      const nickname = '集成测试用户'
      
      // 重置mock，确保没有频率限制
      uni.getStorageSync.mockReturnValue(null)
      
      // 1. 发送验证码
      const sendCodeResult = await authService.sendVerificationCode(phone, 'register')
      expect(sendCodeResult.success).toBe(true)
      
      // 2. 注册用户
      const registerResult = await authService.registerWithPhone({
        phone,
        code,
        password,
        nickname
      })
      expect(registerResult.success).toBe(true)
      
      // 3. 验证返回的数据结构
      expect(registerResult.data.userInfo.phone).toBe(phone)
      expect(registerResult.data.userInfo.nickname).toBe(nickname)
      expect(registerResult.data.token).toBeTruthy()
      expect(registerResult.data.refreshToken).toBeTruthy()
      expect(registerResult.data.tokenExpiry).toBeGreaterThan(Date.now())
    })
  })
})