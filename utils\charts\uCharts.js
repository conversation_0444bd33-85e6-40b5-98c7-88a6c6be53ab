/**
 * uCharts 图表库适配器
 * 提供跨平台兼容的图表绘制功能
 */

class UChartsAdapter {
  constructor(options = {}) {
    this.canvasId = options.canvasId || 'chart'
    this.width = options.width || 350
    this.height = options.height || 250
    this.ctx = null
    this.pixelRatio = 1
    
    // 图表配置
    this.config = {
      type: options.type || 'line',
      background: options.background || '#ffffff',
      padding: options.padding || [20, 20, 20, 20],
      enableScroll: options.enableScroll || false,
      enableMarkLine: options.enableMarkLine || false,
      ...options
    }
  }

  /**
   * 初始化图表
   */
  init() {
    return new Promise((resolve, reject) => {
      try {
        // #ifdef APP-PLUS || H5
        this.ctx = uni.createCanvasContext(this.canvasId)
        this.pixelRatio = uni.getSystemInfoSync().pixelRatio || 1
        resolve(this.ctx)
        // #endif
        
        // #ifdef MP-WEIXIN
        const query = uni.createSelectorQuery()
        query.select(`#${this.canvasId}`)
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0]) {
              const canvas = res[0].node
              this.ctx = canvas.getContext('2d')
              this.pixelRatio = uni.getSystemInfoSync().pixelRatio || 1
              canvas.width = res[0].width * this.pixelRatio
              canvas.height = res[0].height * this.pixelRatio
              this.ctx.scale(this.pixelRatio, this.pixelRatio)
              resolve(this.ctx)
            } else {
              reject(new Error('Canvas节点获取失败'))
            }
          })
        // #endif
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 绘制折线图
   */
  drawLineChart(data, options = {}) {
    if (!this.ctx || !data || data.length === 0) return

    const {
      normalRange = { min: null, max: null },
      showGrid = true,
      showLegend = true,
      colors = ['#007AFF', '#FF3B30', '#34C759', '#FF9500'],
      lineWidth = 2
    } = options

    this.clearCanvas()

    const padding = this.config.padding
    const chartWidth = this.width - padding[1] - padding[3]
    const chartHeight = this.height - padding[0] - padding[2]

    // 计算数据范围
    const values = data.map(item => item.value)
    const minValue = Math.min(...values)
    const maxValue = Math.max(...values)
    const valueRange = maxValue - minValue || 1

    // 绘制背景
    this.ctx.setFillStyle(this.config.background)
    this.ctx.fillRect(0, 0, this.width, this.height)

    // 绘制网格
    if (showGrid) {
      this.drawGrid(padding, chartWidth, chartHeight)
    }

    // 绘制坐标轴
    this.drawAxes(padding, chartWidth, chartHeight)

    // 绘制正常值范围
    if (normalRange.min !== null && normalRange.max !== null) {
      this.drawNormalRange(padding, chartWidth, chartHeight, minValue, valueRange, normalRange)
    }

    // 绘制折线
    this.ctx.beginPath()
    this.ctx.setStrokeStyle(colors[0])
    this.ctx.setLineWidth(lineWidth)

    data.forEach((item, index) => {
      const x = padding[3] + (index / (data.length - 1)) * chartWidth
      const y = padding[0] + chartHeight - ((item.value - minValue) / valueRange) * chartHeight

      if (index === 0) {
        this.ctx.moveTo(x, y)
      } else {
        this.ctx.lineTo(x, y)
      }
    })

    this.ctx.stroke()

    // 绘制数据点
    data.forEach((item, index) => {
      const x = padding[3] + (index / (data.length - 1)) * chartWidth
      const y = padding[0] + chartHeight - ((item.value - minValue) / valueRange) * chartHeight

      // 判断是否为异常值
      const isAbnormal = this.isAbnormalValue(item.value, normalRange)

      this.ctx.beginPath()
      this.ctx.arc(x, y, 4, 0, 2 * Math.PI)
      this.ctx.setFillStyle(isAbnormal ? colors[1] : colors[0])
      this.ctx.fill()

      // 显示数值
      this.ctx.setFillStyle('#333333')
      this.ctx.setFontSize(10)
      this.ctx.fillText(item.value.toString(), x - 10, y - 10)
    })

    // 绘制图例
    if (showLegend) {
      this.drawLegend(colors)
    }

    // #ifndef MP-WEIXIN
    this.ctx.draw()
    // #endif
  }

  /**
   * 绘制柱状图
   */
  drawBarChart(data, options = {}) {
    if (!this.ctx || !data || data.length === 0) return

    const {
      normalRange = { min: null, max: null },
      showGrid = true,
      colors = ['#007AFF', '#FF3B30', '#34C759', '#FF9500']
    } = options

    this.clearCanvas()

    const padding = this.config.padding
    const chartWidth = this.width - padding[1] - padding[3]
    const chartHeight = this.height - padding[0] - padding[2]

    const maxValue = Math.max(...data.map(item => item.value))
    const barWidth = chartWidth / data.length * 0.6
    const barSpacing = chartWidth / data.length * 0.4

    // 绘制背景
    this.ctx.setFillStyle(this.config.background)
    this.ctx.fillRect(0, 0, this.width, this.height)

    // 绘制网格
    if (showGrid) {
      this.drawGrid(padding, chartWidth, chartHeight)
    }

    // 绘制坐标轴
    this.drawAxes(padding, chartWidth, chartHeight)

    // 绘制柱状图
    data.forEach((item, index) => {
      const x = padding[3] + index * (barWidth + barSpacing) + barSpacing / 2
      const barHeight = (item.value / maxValue) * chartHeight
      const y = padding[0] + chartHeight - barHeight

      const isAbnormal = this.isAbnormalValue(item.value, normalRange)

      this.ctx.setFillStyle(isAbnormal ? colors[1] : colors[0])
      this.ctx.fillRect(x, y, barWidth, barHeight)

      // 显示数值
      this.ctx.setFillStyle('#333333')
      this.ctx.setFontSize(10)
      this.ctx.fillText(item.value.toString(), x + barWidth / 2 - 10, y - 5)
    })

    // #ifndef MP-WEIXIN
    this.ctx.draw()
    // #endif
  }

  /**
   * 清理画布
   */
  clearCanvas() {
    this.ctx.clearRect(0, 0, this.width, this.height)
  }

  /**
   * 绘制坐标轴
   */
  drawAxes(padding, chartWidth, chartHeight) {
    this.ctx.beginPath()
    this.ctx.setStrokeStyle('#CCCCCC')
    this.ctx.setLineWidth(1)

    // Y轴
    this.ctx.moveTo(padding[3], padding[0])
    this.ctx.lineTo(padding[3], padding[0] + chartHeight)

    // X轴
    this.ctx.moveTo(padding[3], padding[0] + chartHeight)
    this.ctx.lineTo(padding[3] + chartWidth, padding[0] + chartHeight)

    this.ctx.stroke()
  }

  /**
   * 绘制网格线
   */
  drawGrid(padding, chartWidth, chartHeight) {
    this.ctx.beginPath()
    this.ctx.setStrokeStyle('#F0F0F0')
    this.ctx.setLineWidth(0.5)

    // 绘制水平网格线
    for (let i = 1; i < 5; i++) {
      const y = padding[0] + (chartHeight / 5) * i
      this.ctx.moveTo(padding[3], y)
      this.ctx.lineTo(padding[3] + chartWidth, y)
    }

    // 绘制垂直网格线
    const gridCount = Math.min(10, 10)
    for (let i = 1; i < gridCount; i++) {
      const x = padding[3] + (chartWidth / gridCount) * i
      this.ctx.moveTo(x, padding[0])
      this.ctx.lineTo(x, padding[0] + chartHeight)
    }

    this.ctx.stroke()
  }

  /**
   * 绘制正常值范围
   */
  drawNormalRange(padding, chartWidth, chartHeight, minValue, valueRange, normalRange) {
    const normalMinY = padding[0] + chartHeight - ((normalRange.min - minValue) / valueRange) * chartHeight
    const normalMaxY = padding[0] + chartHeight - ((normalRange.max - minValue) / valueRange) * chartHeight

    this.ctx.setFillStyle('rgba(0, 122, 255, 0.1)')
    this.ctx.fillRect(padding[3], normalMaxY, chartWidth, normalMinY - normalMaxY)

    // 绘制正常范围边界线
    this.ctx.beginPath()
    this.ctx.setStrokeStyle('rgba(0, 122, 255, 0.3)')
    this.ctx.setLineWidth(1)
    this.ctx.moveTo(padding[3], normalMinY)
    this.ctx.lineTo(padding[3] + chartWidth, normalMinY)
    this.ctx.moveTo(padding[3], normalMaxY)
    this.ctx.lineTo(padding[3] + chartWidth, normalMaxY)
    this.ctx.stroke()
  }

  /**
   * 绘制图例
   */
  drawLegend(colors) {
    const legendY = this.height - 20

    this.ctx.setFillStyle(colors[0])
    this.ctx.fillRect(10, legendY, 10, 10)
    this.ctx.setFillStyle('#333333')
    this.ctx.setFontSize(12)
    this.ctx.fillText('正常值', 25, legendY + 8)

    this.ctx.setFillStyle(colors[1])
    this.ctx.fillRect(80, legendY, 10, 10)
    this.ctx.fillText('异常值', 95, legendY + 8)
  }

  /**
   * 判断是否为异常值
   */
  isAbnormalValue(value, normalRange) {
    if (!normalRange || normalRange.min === null || normalRange.max === null) {
      return false
    }
    return value < normalRange.min || value > normalRange.max
  }

  /**
   * 更新图表数据
   */
  updateData(data, options = {}) {
    if (this.config.type === 'line') {
      this.drawLineChart(data, options)
    } else if (this.config.type === 'bar') {
      this.drawBarChart(data, options)
    }
  }

  /**
   * 销毁图表
   */
  destroy() {
    this.ctx = null
  }
}

export default UChartsAdapter