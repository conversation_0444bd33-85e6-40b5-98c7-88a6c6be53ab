/**
 * 平台检测工具函数单元测试
 */

import { 
  getCurrentPlatform, 
  isApp, 
  isWeixinMp, 
  isMp, 
  isH5,
  getPlatformConfig
} from '../../utils/platform/detector.js'

import { PLATFORM_TYPES } from '../../utils/platform/constants.js'

// Mock uni对象
global.uni = {
  getSystemInfo: jest.fn()
}

describe('平台检测工具函数测试', () => {
  
  describe('getCurrentPlatform', () => {
    test('应该返回正确的平台类型', () => {
      const platform = getCurrentPlatform()
      expect(typeof platform).toBe('string')
      expect(platform).not.toBe('')
    })
  })

  describe('平台判断函数', () => {
    test('isApp应该返回布尔值', () => {
      const result = isApp()
      expect(typeof result).toBe('boolean')
    })

    test('isWeixinMp应该返回布尔值', () => {
      const result = isWeixinMp()
      expect(typeof result).toBe('boolean')
    })

    test('isMp应该返回布尔值', () => {
      const result = isMp()
      expect(typeof result).toBe('boolean')
    })

    test('isH5应该返回布尔值', () => {
      const result = isH5()
      expect(typeof result).toBe('boolean')
    })
  })

  describe('getPlatformConfig', () => {
    test('应该返回平台配置对象', () => {
      const config = getPlatformConfig()
      
      expect(config).toHaveProperty('supportCamera')
      expect(config).toHaveProperty('supportShare')
      expect(config).toHaveProperty('supportBiometric')
      expect(config).toHaveProperty('maxImageSize')
      expect(config).toHaveProperty('supportedImageFormats')
      
      expect(typeof config.supportCamera).toBe('boolean')
      expect(typeof config.supportShare).toBe('boolean')
      expect(typeof config.supportBiometric).toBe('boolean')
      expect(typeof config.maxImageSize).toBe('number')
      expect(Array.isArray(config.supportedImageFormats)).toBe(true)
    })

    test('配置应该包含合理的默认值', () => {
      const config = getPlatformConfig()
      
      expect(config.maxImageSize).toBeGreaterThan(0)
      expect(config.supportedImageFormats.length).toBeGreaterThan(0)
    })
  })

  describe('getSystemInfo', () => {
    beforeEach(() => {
      uni.getSystemInfo.mockClear()
    })

    test('应该返回Promise', () => {
      uni.getSystemInfo.mockImplementation(({ success }) => {
        success({
          platform: 'android',
          system: 'Android 10',
          version: '10',
          model: 'Test Device',
          pixelRatio: 2,
          screenWidth: 375,
          screenHeight: 667,
          windowWidth: 375,
          windowHeight: 667,
          statusBarHeight: 24,
          safeArea: {},
          safeAreaInsets: {}
        })
      })

      const promise = require('../../utils/platform/detector.js').getSystemInfo()
      expect(promise).toBeInstanceOf(Promise)
    })

    test('成功时应该返回系统信息', async () => {
      const mockSystemInfo = {
        platform: 'android',
        system: 'Android 10',
        version: '10',
        model: 'Test Device',
        pixelRatio: 2,
        screenWidth: 375,
        screenHeight: 667,
        windowWidth: 375,
        windowHeight: 667,
        statusBarHeight: 24,
        safeArea: {},
        safeAreaInsets: {}
      }

      uni.getSystemInfo.mockImplementation(({ success }) => {
        success(mockSystemInfo)
      })

      const { getSystemInfo } = require('../../utils/platform/detector.js')
      const result = await getSystemInfo()
      
      expect(result).toEqual(mockSystemInfo)
    })

    test('失败时应该抛出错误', async () => {
      const mockError = new Error('获取系统信息失败')
      
      uni.getSystemInfo.mockImplementation(({ fail }) => {
        fail(mockError)
      })

      const { getSystemInfo } = require('../../utils/platform/detector.js')
      
      await expect(getSystemInfo()).rejects.toEqual(mockError)
    })
  })
})